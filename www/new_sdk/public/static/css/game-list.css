.rec{
    width: 1200px;
    margin: auto;
    overflow: hidden;
}
.rec>ul>li{
    float: left;
    list-style: none;
    margin-left: 2px;
    position: relative;
    overflow: hidden;
}
.rec>ul>li:first-child{
    margin-left: 0;
}
.rec>ul>li:first-child+li>div:first-child{
    margin-bottom: 2px;
}
.rec>ul>li:first-child+li>div{
    position: relative;
    overflow: hidden;
}
.warp_public_nav{border-bottom: 1px solid #eee;}
/* 游戏分类 */

.shgame{
    overflow: auto;
    padding-top: 40px;
}
.allgames ul.shgame li {
    width: 100%;
    float: left;
    font-size: 14px;
    color: #494949;
    background: #fff;
    margin-bottom: 24px;

}
.allgames ul.shgame li .tit {
    float: left;
    width: 68px;
    height: 100%;
    overflow: hidden;
    font-size: 16px;
    line-height: 30px;
    margin-left: 20px;
}
.allgames ul.shgame li .tit input{display: none;}
.gametype>div{
    float: left;
    text-align: center;
    width: 90px;
    height: 30px;
    font-size: 16px;
    margin-left:7px;
    border-right: 1px solid #EEEEEE;
    display: flex;
}
.gametype>div a{
    width: 90px;
    height: 30px;
    line-height: 30px;
}
.gametype>div:last-child{border: none;}
.gametype .selon{
}
.tit{
  margin-right:24px ;
}
.selon a{   
    color: #fff;
    width: 90px;
    height: 30px;
    line-height: 30px;
    background: #51a7ff;
    border-radius: 5px;
}
.gametype a:hover{
    color: #fff;
    width: 90px;
    height: 30px;
    background: #51a7ff;
    border-radius: 5px;
}
/* 游戏列表 */
.list{  overflow: auto;    width: 1228px;
}
.list li{
    width: 227px;
    height: 289px;
    margin-right: 16px;
    text-align: center;
    float: left;
    margin-bottom: 16px;
    background: #fff;
    border: 1px solid #eee;
}
.list li:hover{
    background: #F6F7F7  ;
}
.game_img{height: 130px;}
.list li img{
    width: 86px;  
    height: 86px;
    margin: 30px 0 10px 0;
}
.list li a{
    width:151px;
   font-size:16px;
   font-weight: bold;
}
.list li .game_name a{
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.list li a:hover{color: #51a7ff;text-decoration: underline;}
.list .game_name{
    width: 151px;
    height: 27px;
    margin: auto;
    padding-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.list p{
    font-size: 14px;
    color: #B2B2B2 ;
    line-height: 24px;
}
.list span{
    padding: 0 5px;
}
.list li div:first-child{
height: 130px;
}
.code_url .download{
    width: 118px;
    height: 36px;
    margin: auto;
    font-size: 16px;
    margin-top: 22px;
    cursor: pointer;
    background: #fff;
    padding-top: 0px !important;
    color: #51a7ff !important;
    line-height: 36px !important;
    border: 1px solid #51a7ff;
    border-radius: 29px;
}
.download:active,.download:focus,.no_download:active,.no_download:focus{
    outline: none;
    box-shadow:none;
}
.code_url .download:hover{
    color: #fff !important;
    background-color: #51a7ff;
}
button{
    font-size: 16px;
    border:0px;background-color:transparent;
}
.code_url .no_download{
    width: 118px;
    height: 36px;
    margin: auto;
    font-size: 16px;
    margin-top: 22px;
    cursor: pointer;
    background: #fff;
    padding-top: 0px !important;
    color: #B2B2B2 !important;
    line-height: 36px !important;
    border: 1px solid #B2B2B2;
    border-radius: 6px;
}
.code_url .no_download:hover{
    background: #b2b2b2;
    color: #fff !important;
}
.allgames .no_content{
    padding: 118px;
}
/* 分页 */
.page{
    width: 100%;
    margin-top: 24px;
    text-align: center;
}
.prev,.end,.next{
    font-size: 14px;
    padding: 6px 12px;
    color: #494949;
    border-radius: 4px;
    margin: 0 5px;
    border: 1px solid #eee;
}
.num,.current{
    font-size: 14px;
    padding: 6px 13px;
    color: #494949;
    border-radius: 4px;
    border: 1px solid #eee;
    margin: 0 5px;
}
.current,.num:hover{
    background: #51a7ff;
    color: #fff;
    border:  1px solid #51a7ff;
}
.rows{
    margin: 24px 0 44px 0;
    color: #B2B2B2;
    font-size: 14px;
}
.page span{
    color: #494949;
    margin: 0 3px;
}

/* 加速游戏 */
.warp_fastgame{
    overflow: hidden;
    margin-bottom: 20px;
}

.fastgame_left {
    width: 854px;
    float: left;
}
.fastgame_left h2 {
    font-size: 16px;
    font-weight: 600;
    border-left: 4px solid #51a7ff;
    line-height: 16px;
    padding-left: 10px;
}
.warp_fastgame_cont {
    overflow: auto;
}
.fastgame_cont {
    width: 394px;
    float: left;
    border: 1px solid #eee;
    margin-top: 12px;
    padding: 12px;
}
.warp_fastgame_cont .fastgame_cont:nth-child(odd){
    margin-right: 12px;
}
.fastgame_cont .title {
    overflow: hidden;
}
.fastgame_cont .title img {
    width: 77px;
    height: 77px;
    float: left;
}
.title div {
    float: left;
    margin-left: 10px;
}
.fastgame_cont .title h3 {
    font-size: 16px;
    font-weight: bold;
    line-height: 40px;
    width: 200px;
    height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.fastgame_cont .title p {
    font-size: 12px;
    line-height: 28px;
    color: #b2b2b2;
}
.fastgame_cont .title .download {
    float: right;
    margin-top: 32px;
    margin-right: 14px;
}
.fastgame_cont .title .download a {
    font-size: 16px;
    color: #FC5351;
    border: 1px solid #FC5351;
    border-radius: 20px;
    padding: 3px 23px;
}
.fastgame_cont .title .download a:hover{
    color: #fff;
    background: #FC5351;
}
.fastgame_cont .title .no_download{
    float: right;
    margin-top: 32px;
    margin-right: 14px;
}
.fastgame_cont .title .no_download a{
    font-size: 16px;
    color: #ccc;
    border: 1px solid #ccc;
    border-radius: 20px;
    padding: 3px 23px;
}
.fastgame_cont .title .no_download a:hover{
    color: #fff;
    background: #ccc;
}
.fastgame_txt {
    height: 181px;
    margin-top: 20px;
    overflow: auto;
    padding-right: 50px;
}
.fastgame_txt::-webkit-scrollbar{
    width: 5px;
    height: 5px;
  }
  .fastgame_txt::-webkit-scrollbar-thumb{
    border-radius: 1em;
    background-color: rgba(50,50,50,.3);
  }
  .fastgame_txt::-webkit-scrollbar-track{
    border-radius: 1em;
    background-color: rgba(50,50,50,.1);
  }
.fastgame_txt h4 {
    font-size: 12px;
    font-weight: bold;
}
/* .fastgame_txt p {
    font-size: 12px;
    color: #b2b2b2;
    line-height: 18px;
    margin: 5px 0 16px;
} */
.fastgame_right {
    width: 330px;
    float: right;
    margin-top: -13px;
}
.download button{
    font-size: 14px;
}

/* 分页 */
.pagination {
    height: 33px;
    margin: auto;
    width: max-content;
    width:-moz-fit-content;
    width:-ms-fit-content;
    display:inline-block;
    *display:inline;
    *zoom:1;
}
.pagination li{
    float: left;
}
.pagination li a,.pagination li span{
    font-size: 14px;
    padding: 6px 13px;
    color: #494949;
    border-radius: 4px;
    border: 1px solid #eee;
    margin: 0 5px;
    cursor: pointer;
}

.pagination li a:hover{
    color: #fff;
    background: #51a7ff;
    border: 1px solid #51a7ff;
}

.pagination .active span{
    color: #fff;
    background: #51a7ff;
    border: 1px solid #51a7ff;
}
.pagination .disabled span{
    color: #fff;
    background: #eee;
}
.pagination .disabled span:hover{
    color: #fff;
    background: #eee;
} 
.rows {
    margin: 24px 0 44px 0;
    color: #B2B2B2;
    font-size: 14px;
}
/* 右侧广告 */
.wx_img{
    width: 330px; 
    height: 151px;
    margin-top:23px;
}
.wx_img img{
    height: 100%;
    width: 100%;
}
.article_adv {
    width: 330px;
    height: 135px;
    margin-top:23px;
    position: relative;
    overflow: hidden;
}
.article_adv div {
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    position: absolute;
    width: 100%;
    height: 28px;
    font-size: 14px;
    bottom: -28px;
    filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#7F000000,endcolorstr=#7F000000)
}
.article_adv div span {
    line-height: 28px;
    padding-left: 12px;
    display: block;
    width: 310px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.article_adv img {
    width: 100%;
    height: 100%;
}