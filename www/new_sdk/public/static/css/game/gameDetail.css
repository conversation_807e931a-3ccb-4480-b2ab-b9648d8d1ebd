.gameDetailPage {
    height: 100vh;
    width: 100vw;
    position: relative;

    max-width: 750px;
    margin: 0 auto;
    min-width: 300px;

}

.gameDetailPageContent {
    height: 100%;
    width: 100%;
    overflow-y: scroll;
    padding-bottom: 1.66rem;
    box-sizing: border-box;
}

/* 返回按钮 */
.backContent {
    position: absolute;
    top: 0.2rem;
    left: 0.2rem;
    width: 0.76rem;
    height: 0.76rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.38rem;
    -webkit-border-radius: 0.38rem;
    -moz-border-radius: 0.38rem;
    -ms-border-radius: 0.38rem;
    -o-border-radius: 0.38rem;
    z-index: 10;

}

.backContent img {
    width: 0.48rem;
    height: 0.48rem;
    object-fit: cover;
}


/* 图片展示 */
.swiperContent {
    width: 100%;
    height: 4.18rem;
    position: relative;
}


.swiperContent .swiperItem {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    opacity: 0;
    transition: all 1s;
}

.swiperContent .swiperItem img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.swiperContent .active {
    z-index: 2;
    opacity: 1 !important;
}


/* 图片展示切换 */
.swiperNavContent {
    display: flex;
    align-items: center;
}

.swiperNavContent .navContent {
    flex-grow: 1;
    display: flex;
    align-items: center;
    overflow-x: scroll;
    padding: 0rem 0.2rem;
}

.swiperNavContent .navContent::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}

.swiperNavContent .navContent .nav_item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.2rem;
    box-sizing: border-box;

}

.swiperNavContent .navContent .active {
    border-radius: 0.06rem;
    overflow: hidden;
    box-sizing: border-box;
    -webkit-border-radius: 0.06rem;
    -moz-border-radius: 0.06rem;
    -ms-border-radius: 0.06rem;
    -o-border-radius: 0.06rem;
}

.swiperNavContent .navContent .active img {
    border: 0.03rem solid #3269FE;
    font-size: 0px;
    box-sizing: border-box;
    background: #eee;
}

.swiperNavContent .navContent .nav_item img {
    height: 0.68rem;
    width: 1.2rem;
    border-radius: 0.06rem;
    object-fit: cover;
    -webkit-border-radius: 0.06rem;
    -moz-border-radius: 0.06rem;
    -ms-border-radius: 0.06rem;
    -o-border-radius: 0.06rem;
}


.swiperNavContent .moreCut {
    flex-shrink: 0;
    width: 1.2rem;
    height: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.24rem;
    color: #999;
}

.swiperNavContent .moreCut img {
    height: 0.48rem;
    margin-right: 0.02rem;
}



/* 游戏基本信息 */
.gameBasicDetail {
    display: flex;
    box-sizing: border-box;
    padding: 0.28rem;
    align-items: center;

}

.gameBasicDetail .game_icon {
    width: 1.4rem;
    height: 1.4rem;
    padding-right: 0.2rem;
    flex-shrink: 0;
}

.gameBasicDetail .game_icon img {
    height: 1.4rem;
    width: 1.4rem;
    object-fit: cover;
}

.gameBasicDetail .gameDetail {}

.gameBasicDetail .gameDetail .title {

    font-size: 0.32rem;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.gameBasicDetail .gameDetail .gameLable {
    font-size: 0.26rem;
    color: #999999;
    margin-top: 0.15rem;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.gameBasicDetail .gameDetail .updateTime {
    font-size: 0.26rem;
    color: #999999;
    margin-top: 0.15rem;
}


/* 游戏基本信息2 */
.hintContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #999999;
    font-size: 0.26rem;
    height: 1.02rem;
    background: #F5F5F5;
    margin: 0rem 0.28rem;
    padding: 0px 0.25rem;
    box-sizing: border-box;
    border-radius: 0.08rem;
    -webkit-border-radius: 0.08rem;
    -moz-border-radius: 0.08rem;
    -ms-border-radius: 0.08rem;
    -o-border-radius: 0.08rem;
}

/* 游戏详情  */
.gameIntroductory {
    margin-top: 0.28rem;
}

.gameIntroductory .title {
    height: 0.66rem;
    border-bottom: 0.01rem solid #E9E9E9;
    display: flex;
    align-items: center;
    font-size: 0.3rem;
    padding: 0rem 0.28rem;
    box-sizing: border-box;
    position: relative;

}

.gameIntroductory .title img {
    display: block;
    position: absolute;
    bottom: -0.04rem;
    height: 0.08rem;
    width: 1.2rem;
    left: 0.28rem;
}

.gameIntroductory .htmlContent {
    font-size: 0.28rem;
    box-sizing: border-box;
    padding: 0.28rem;
    text-indent: 2em;
    color: #999999;
    line-height: 0.44rem;
}

.gameIntroductory .htmlContent img {
    width: 100%;
}

/* 底部按钮 */
.cutContent {
    position: absolute;
    bottom: 0px;
    left: 0px;
    right: 0px;
    background: #fff;
    display: flex;
    align-items: center;
    padding-bottom: 0.7rem;
    padding-top: 0.2rem;
    box-sizing: border-box;
    box-shadow: 0rem -0.03rem 0.06rem 0.01rem rgba(0, 0, 0, 0.16);

}

.cutContent .shareCut {
    width: 1.18rem;
    display: flex;
    align-items: center;
    justify-content: center;

}

.cutContent .shareCut .cut {
    color: #999999;
    font-size: 0.22rem;
    text-align: center;
}

.cutContent .shareCut img {
    height: 0.48rem;
}

.cutContent .download {
    flex-grow: 1;
    padding-left: 0.28rem;
    font-size: 0.36rem;
    color: #fff;
    padding-right: 0.28rem;

}

.cutContent .download .cut {
    background: linear-gradient(270deg, #3269FE 0%, #5F7CFA 100%);

    border-radius: 0.4rem 0.4rem 0.4rem 0.4rem;
    height: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;


}

/* 微信提示框 */
.weixinHint {
    position: fixed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 100;
}

.weixinHint img {
    position: absolute;
    top: 0.17rem;
    right: 0.33rem;
    width: 5.68rem;
}