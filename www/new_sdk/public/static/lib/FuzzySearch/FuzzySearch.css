* {
    box-sizing: border-box;
}

.FuzzySearch_Container {
    position: relative;
    width: 190px;
    margin-right: 5px;
}

/* input */

.FuzzySearch_Container div input[type="text"] {
    width: 100%;
    height: 38px;
    line-height: 1;
    padding: 9px 8px;
    border: 1px solid #ddd;
}

.FuzzySearch_Container div:nth-of-type(2) {
    width: 100%;
    max-height: 300px;
    border: 1px solid #ddd;
    border-top: 0;
    overflow-y: scroll;
    position: absolute;
    top: 38px;
    z-index: 99999;
    background: #fff;
    display: none;
}

.FuzzySearch_Container div:nth-of-type(2) a {
    text-decoration: none;
    display: block;
    padding: 10px;
    color: #333;
}

.FuzzySearch_Container div:nth-of-type(2) a:hover {
    color: #fff;
    background-color: #ddd;
    background-color: #5FB878;
}

.FuzzySearch_Container div:nth-of-type(1)::after {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #999;
    content: "";
    position: absolute;
    width: 0;
    right: 5%;
    top: 45%;
}



.FuzzySearch_Container div:nth-of-type(1){
    height: 38px;
}

.FuzzySearch_Container div:nth-of-type(1) .cross {
    position: absolute;
    right: 22px;
    top: 12px;
    display: inline-block;
    font-size: 20px;
    color:#000;
    width: 14px;
    height: 14px;
    text-align: center;
    line-height: 14px;
}

.FuzzySearch_Container div:nth-of-type(1) .cross:hover {
    cursor: pointer;
}