 // url生成二维码
 var qrcode = new QRCode(document.getElementById("qrcode"), {
    width : 100,
    height : 100
     });
       $(".download").click( function() {
      var p_text = $(this).find("#text-1").text(); 
      var p_title = $(this).find("#text-2").text(); 
      var p_page = $(this).find("#download-page").text();
      qrcode.makeCode(p_page);
      $('.az_hover a').attr('href',p_text);
      $('.download_popup p').html(p_title);
      });
      
  
      $(function () {
          $("#J_rank_list").load('/Layout/gameRankList');
          $(".public_nav li").removeClass('current_page').eq(4).addClass('current_page')
      });
  
      $(".no_colgift").click(function(){
          $("#no_giftpack").show();
      });
  
      function kf_tip(id,kfkc){
          $.ajax({
              type: "POST",
              timeOut: 10000,
              url: "/service/subscribe",
              data: {
                  "id": id,
                  "type": kfkc
              },
              async: false,
              success: function (res) {
                  // console.log(res);
                  if (res.code == 1 || res.code == 4 || res.code == 5) {
                      $(".success").show();
                      $("#success_txt").text(res.msg)
                  }else if (res.code == 2){
                      window.location.href = res.data;
                  }
                  else if (res.code == 3){
                      $("#bind_phone_href").attr('href',res.data);
                      $("#bind_phone_txt").text(res.msg)
                      $("#bind_phone_href").text("去绑定");
                      $(".bind_phone").show();
                  }
                  /*else if (res.code == 4){
                      $(".order").show();
                  }else if (res.code == 5){
                      $(".overtime").show();
                  }*/else {
                      alert(res.msg);
                  }
              },
              error: function () {
                  layer.msg('网络错误，请刷新页面重试');
              }
          });
      }