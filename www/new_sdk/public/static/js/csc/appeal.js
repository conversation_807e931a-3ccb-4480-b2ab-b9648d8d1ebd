// 判断选中手机还是邮箱

$("#radio1").click(function () {
    if ($('#radio1:checked').length) {
        $(".phonenum").show();
        $(".mailnum").hide();
    }
});
$("#radio2").click(function () {
    if ($('#radio2:checked').length) {
        $(".phonenum").hide();
        $(".mailnum").show();
    }
});
if ($('#radio1:checked').length) {
        $(".phonenum").show();
        $(".mailnum").hide();
    }
if ($('#radio2:checked').length) {
    $(".phonenum").hide();
    $(".mailnum").show();
}

// 点击下一步
$(".next").click(function () {
    var preg = /^1[3456789]{1}\d{9}$/;
    var sszh = $('input[name=sszh]').val();
    var phonenum = $('input[name=phonenum]').val();
    var reasons = $('.select').val();
    var mailnum = $('input[name=mailnum]').val();
    var reg = /^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i;
    var type = '';
    // 手机号
    if ($('#radio1:checked').length) {
        type = 'mobile';
        if (sszh == "" || phonenum == "" || !preg.test(phonenum) || reasons == 0 || $("#but1").prop("checked") == false) {
            if (sszh == "") {
                $(".no_account").show();
                $(".no_account").text("请输入申诉账号");
                $("#sszh").css("border", "1px solid #FC6A68")
            }

            if (phonenum == "" || !preg.test(phonenum)) {
                $(".phone_error").show();
                $(".phone_error").text("请输入正确的手机号");
                $("#phonenum").css("border", "1px solid #FC6A68")
            }
            if (reasons == 0) {
                $(".no_select").show();
                $(".no_select").text("请选择申诉原因"); 
                $(".select").css("border", "1px solid #FC6A68")
            }
            if ($("#but1").prop("checked") == false) {
                $(".no_agree").show();
            }

        } else {
            // 跳转手机号验证
            toverify(sszh,type,phonenum,reasons,$("#but1").prop("checked"));
        }

    }


    // 邮箱
    if ($('#radio2:checked').length) {
        type = 'email';
        if (sszh == "" || mailnum == "" || !reg.test(mailnum) || reasons == 0 || $("#but1").prop("checked") == false) {
            if (sszh == "") {
                $(".no_account").show();
                $(".no_account").text("请输入申诉账号");
                $("#sszh").css("border", "1px solid #FC6A68")
            }

            if (mailnum == "" || !reg.test(mailnum)) {
                $(".mail_error").show();
                $(".mail_error").text("请输入正确的邮箱"); 
                $("#mailnum").css("border", "1px solid #FC6A68")
            }
            if (reasons == 0) {
                $(".no_select").show();
                $(".no_select").text("请选择申诉原因"); 
                $(".select").css("border", "1px solid #FC6A68")
            }
            if ($("#but1").prop("checked") == false) {
                $(".no_agree").show();
            }
        } else {
            //   跳转邮箱验证
            toverify(sszh,type,mailnum,reasons,$("#but1").prop("checked"));
        }
    }


    if ($("#but1").prop("checked") == true) {
        $(".no_agree").hide();
    }


})


// 失去焦点

$("#sszh").on('blur', function () {
    var sszh = $('input[name=sszh]').val();
    if (sszh == "") {
        $(".no_account").show();
        $(".no_account").text("请输入申诉账号");
        $("#sszh").css("border", "1px solid #FC6A68")
    } else {
        $(".no_account").hide();
        $("#sszh").css("border", "1px solid #eee")
    }

})

$("#phonenum").on('blur', function () {
    var preg = /^1[3456789]{1}\d{9}$/;
    var phonenum = $('input[name=phonenum]').val();
    if (phonenum == "" || !preg.test(phonenum)) {
        $(".phone_error").show();
        $(".phone_error").text("请输入正确的手机号");
        $("#phonenum").css("border", "1px solid #FC6A68")
    } else {
        $(".phone_error").hide();
        $("#phonenum").css("border", "1px solid #eee")
    }
})

$("#mailnum").on('blur', function () {
    var reg = /^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i;
    var mailnum = $('input[name=mailnum]').val();
    if (mailnum == "" || !reg.test(mailnum)) {
        $(".mail_error").show();
        $(".mail_error").text("请输入正确的邮箱"); 
        $("#mailnum").css("border", "1px solid #FC6A68")
    } else {
        $(".mail_error").hide();
        $("#mailnum").css("border", "1px solid #eee")
    }
})

$(".select").on('blur', function () {
    var reasons = $(this).val();
    if (reasons == 0) {
        $(".no_select").show();
        $(".no_select").text("请选择申诉原因"); 
        $(".select").css("border", "1px solid #FC6A68")
    } else {
        $(".no_select").hide();
        $(".select").css("border", "1px solid #eee")
    }
})


function toverify(sszh,type,number,reason,agreexy) {
    $.ajax({
        type: 'POST',
        url: "/kefu/zhss",
        dataType: 'json',
        data: {type:type,username:sszh,number:number,reason:reason,$agreexy:agreexy},
        success: function(res) {
            //console.log(res);
            if (!res.code){
                layer.alert(res.msg);
                return;
            }
            window.location.href = res.url;
        },
        error: function () {
            layer.alert('网络错误，请刷新页面重试');
        }
    });
}

  //判断IE浏览器版本（IE8及以下）  
  var DEFAULT_VERSION = 8.0;  
var ua = navigator.userAgent.toLowerCase();  
var isIE = ua.indexOf("msie")>-1;  
var safariVersion;  
if(isIE){  
safariVersion =  ua.match(/msie ([\d.]+)/)[1];  
}  
if(safariVersion <= DEFAULT_VERSION ){ 
    $(".remember input").show();
    $(".remember label").hide(); 
}; 