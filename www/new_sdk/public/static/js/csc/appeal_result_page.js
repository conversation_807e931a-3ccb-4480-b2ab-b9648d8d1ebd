   // 点击申诉通过的   确认
   $(".ss_complete").click(function () {
    var voucher = $(".voucher").val();
    if (voucher == "") {
        $(".voucher_error").show();
        $(".voucher_error").text("请输入成功凭证"); 
    } else {
        //  判断凭证是否正确
    }

})


// 凭证输入框失去焦点
$(".voucher").on('blur', function () {
    var voucher = $(this).val();
    if (voucher == "") {
        $(".voucher_error").show();
        $(".voucher_error").text("请输入成功凭证"); 
    } else {
        $(".voucher_error").hide();
    }
})

function toVoucher() {
    var code = $(".voucher").val();
    $.ajax({
        type: 'POST',
        url: '/Csc/appealResultPage',
        dataType: 'json',
        data: {certificate:code},
        success: function(res) {
            //console.log(res);
            if (!res.code){
                $(".voucher_error").show();
                $(".voucher_error").text(res.msg); 
                // layer.alert(res.msg);
                return;
            }
            window.location.href = res.url;
        },
        error: function () {
            layer.alert('网络错误，请刷新页面重试');
        }
    });
}
