    //判断是PC还是移动设备 true是PC，false是移动设备
    function IsPC() {
        var userAgentInfo = navigator.userAgent;
        var Agents = ["Android", "iPhone",
            "SymbianOS", "Windows Phone",
            "iPad", "iPod"
        ];
        var flag = true;
        for (var v = 0; v < Agents.length; v++) {
            if (userAgentInfo.indexOf(Agents[v]) > 0) {
                flag = false;
                break;
            }
        }
        return flag;
    }

if(!IsPC()){
    window.location.href="http://m.weilongwl.com/";
}

//菜单栏移入效果
$(function () {
    $(".public_nav>ul>li").hover(function () { 
        if($(this).hasClass("current_page")){
            } else{
        $(this).find('span').stop(true, true).animate({ width: "100%" }, 200);
    }
    }, function () {
        if($(this).hasClass("current_page")){
            }  else{
                $(this).find('span').stop(true, true).animate({ width: "0px" }, 200);}
       
    });

    
})


  
//   搜索栏


// $('body').bind('click', function(event) {
//     // IE支持 event.srcElement ， FF支持 event.target    
//     var evt = event.srcElement ? event.srcElement : event.target;    
//     if(evt.id == 'input_btn' )  {
//         return; // 如果是元素本身，则返回
//     }   
//     else {
//         $('#search ul ').hide(); // 如不是则隐藏元素
//     }   
// });

var count = 0, timer = '', val = '';



// 头部登录状态下 个人中心弹窗

$(".user_popup").hover(function () { 
    $(".land_popup").show();
},function(){
    $(".land_popup").hide();
});

// 去掉ie浏览器 回车键 默认效果
document.onkeydown = function(e) {
    var e = e || event;
    if(e.keyCode == 13) {
        e.preventDefault ? e.preventDefault() : (e.returnValue = false);
       }
    }