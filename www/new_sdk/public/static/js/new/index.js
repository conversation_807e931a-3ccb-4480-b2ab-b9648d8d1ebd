// 手游排行控件显示隐藏
$(function () {
    $('.rank_list>div').eq(0).find('.big').show()
    $('.rank_list>div').eq(0).find('.small').hide()
    $('.rank_list').hover(function () {
        $('.rank_list>div').hover(function () {
            $(this).find('.big').show().parent().siblings().find('.big').hide();
            $(this).find('.small').hide().parent().siblings().find('.small').show();
        });
    });
  
})

 $(function () {
    $(".public_nav li").removeClass('current_page').eq(2).addClass('current_page');
    $("#J_recommend_gift").load('/Layout/gameRankList');
});

//url转二维码
if($("#qrcode").length>0){
var qrcode = new QRCode(document.getElementById("qrcode"), {
    width : 100,
    height : 100
 });
}

         // url生成二维码
         if($(".az_code_right").length>0){
            var qrcode_title = new QRCode(document.getElementById("qrcode_title"), {
                 width : 100,
                 height : 100
             });
           }
		   
		   if($(".az_code_de").length>0){
		      var qrcode_tit = new QRCode(document.getElementById("qrcode_tit"), {
		           width : 100,
		           height : 100
		       });
		     }

             $(function () {
               var p_text = $("#code_url").text(); 
               if($(".az_code_right").length>0){
                   qrcode_title.makeCode(p_text);
               }
			   if($(".az_code_de").length>0){
			       qrcode_tit.makeCode(p_text);
			   }
             });
           

//  设置默认游戏icon  
$(".content_left img,.hot_news_img img").error(function () {
$(this).attr("src", "/static/images/icon/990-625.png");
})


 // 安卓下载按钮
 $(".az_code_right .az_ed_download,.az_code_de .az_ed_download").hover(function(){
    $(this).find("img").attr("src","/static/images/home/<USER>");
    },function(){
     $(this).find("img").attr("src","/static/images/home/<USER>");
    });

    $(".az_no_link .az_no_download").hover(function(){
     $(".az_no_download img").attr("src","/static/images/home/<USER>");
    },function(){
     $(".az_no_download img").attr("src","/static/images/home/<USER>");
    });
      
    $(".az_no_download").click(function(){
        $(".warp_no_download_popup").show();
    })
   