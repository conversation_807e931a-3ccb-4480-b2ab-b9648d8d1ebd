!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";function e(t){var e={},n={},i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return i&&(n.firefox=!0,n.version=i[1]),r&&(n.ie=!0,n.version=r[1]),a&&(n.edge=!0,n.version=a[1]),o&&(n.weChat=!0),{browser:n,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!n.ie&&!n.edge,pointerEventsSupported:"onpointerdown"in window&&(n.edge||n.ie&&n.version>=11),domSupported:"undefined"!=typeof document}}function n(t,e){"createCanvas"===t&&(kf=null),Tf[t]=e}function i(t){if(null==t||"object"!=typeof t)return t;var e=t,n=_f.call(t);if("[object Array]"===n){if(!E(t)){e=[];for(var r=0,a=t.length;a>r;r++)e[r]=i(t[r])}}else if(yf[n]){if(!E(t)){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(var r=0,a=t.length;a>r;r++)e[r]=i(t[r])}}}else if(!mf[n]&&!E(t)&&!T(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=i(t[s]))}return e}function r(t,e,n){if(!S(e)||!S(t))return n?i(e):t;for(var a in e)if(e.hasOwnProperty(a)){var o=t[a],s=e[a];!S(s)||!S(o)||x(s)||x(o)||T(s)||T(o)||M(s)||M(o)||E(s)||E(o)?!n&&a in t||(t[a]=i(e[a],!0)):r(o,s,n)}return t}function a(t,e){for(var n=t[0],i=1,a=t.length;a>i;i++)n=r(n,t[i],e);return n}function o(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function s(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}function l(){return kf||(kf=If().getContext("2d")),kf}function u(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;i>n;n++)if(t[n]===e)return n}return-1}function h(t,e){function n(){}var i=t.prototype;n.prototype=e.prototype,t.prototype=new n;for(var r in i)i.hasOwnProperty(r)&&(t.prototype[r]=i[r]);t.prototype.constructor=t,t.superClass=e}function c(t,e,n){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,s(t,e,n)}function d(t){return t?"string"==typeof t?!1:"number"==typeof t.length:void 0}function f(t,e,n){if(t&&e)if(t.forEach&&t.forEach===wf)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;r>i;i++)e.call(n,t[i],i,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(n,t[a],a,t)}function p(t,e,n){if(t&&e){if(t.map&&t.map===Mf)return t.map(e,n);for(var i=[],r=0,a=t.length;a>r;r++)i.push(e.call(n,t[r],r,t));return i}}function g(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===Cf)return t.reduce(e,n,i);for(var r=0,a=t.length;a>r;r++)n=e.call(i,n,t[r],r,t);return n}}function v(t,e,n){if(t&&e){if(t.filter&&t.filter===bf)return t.filter(e,n);for(var i=[],r=0,a=t.length;a>r;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function m(t,e,n){if(t&&e)for(var i=0,r=t.length;r>i;i++)if(e.call(n,t[i],i,t))return t[i]}function y(t,e){var n=Sf.call(arguments,2);return function(){return t.apply(e,n.concat(Sf.call(arguments)))}}function _(t){var e=Sf.call(arguments,1);return function(){return t.apply(this,e.concat(Sf.call(arguments)))}}function x(t){return"[object Array]"===_f.call(t)}function w(t){return"function"==typeof t}function b(t){return"[object String]"===_f.call(t)}function S(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function M(t){return!!mf[_f.call(t)]}function C(t){return!!yf[_f.call(t)]}function T(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function I(t){return t!==t}function k(){for(var t=0,e=arguments.length;e>t;t++)if(null!=arguments[t])return arguments[t]}function D(t,e){return null!=t?t:e}function A(t,e,n){return null!=t?t:null!=e?e:n}function P(){return Function.call.apply(Sf,arguments)}function L(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function O(t,e){if(!t)throw new Error(e)}function B(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function z(t){t[Df]=!0}function E(t){return t[Df]}function R(t){function e(t,e){n?i.set(t,e):i.set(e,t)}var n=x(t);this.data={};var i=this;t instanceof R?t.each(e):t&&f(t,e)}function F(t){return new R(t)}function N(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function H(){}function W(t,e){var n=new Pf(2);return null==t&&(t=0),null==e&&(e=0),n[0]=t,n[1]=e,n}function V(t,e){return t[0]=e[0],t[1]=e[1],t}function G(t){var e=new Pf(2);return e[0]=t[0],e[1]=t[1],e}function X(t,e,n){return t[0]=e,t[1]=n,t}function Y(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function U(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function j(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function q(t){return Math.sqrt(Z(t))}function Z(t){return t[0]*t[0]+t[1]*t[1]}function $(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t}function K(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t}function Q(t,e){return t[0]*e[0]+t[1]*e[1]}function J(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function te(t,e){var n=q(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function ee(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function ne(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function ie(t,e){return t[0]=-e[0],t[1]=-e[1],t}function re(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function ae(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function oe(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function se(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}function le(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function ue(t,e){return{target:t,topTarget:e&&e.topTarget}}function he(t,e){var n=t._$eventProcessor;return null!=e&&n&&n.normalizeQuery&&(e=n.normalizeQuery(e)),e}function ce(t,e,n,i,r,a){var o=t._$handlers;if("function"==typeof n&&(r=i,i=n,n=null),!i||!e)return t;n=he(t,n),o[e]||(o[e]=[]);for(var s=0;s<o[e].length;s++)if(o[e][s].h===i)return t;var l={h:i,one:a,query:n,ctx:r||t,callAtLast:i.zrEventfulCallAtLast},u=o[e].length-1,h=o[e][u];return h&&h.callAtLast?o[e].splice(u,0,l):o[e].push(l),t}function de(t,e,n,i,r,a){var o=i+"-"+r,s=t.length;if(a.hasOwnProperty(o))return a[o];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Nf);return t[n][l]}for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,d=0,f=0;s>d;d++){var p=1<<d;p&r||(c+=(f%2?-1:1)*t[n][d]*de(t,e-1,h,u,r|p,a),f++)}return a[o]=c,c}function fe(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=de(n,8,0,0,0,i);if(0!==r){for(var a=[],o=0;8>o;o++)for(var s=0;8>s;s++)null==a[s]&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*de(n,7,0===o?1:0,1<<o,1<<s,i)/r*e[o];return function(t,e,n){var i=e*a[6]+n*a[7]+1;t[0]=(e*a[0]+n*a[1]+a[2])/i,t[1]=(e*a[3]+n*a[4]+a[5])/i}}}function pe(t,e,n,i,r){return ge(Wf,e,i,r,!0)&&ge(t,n,Wf[0],Wf[1])}function ge(t,e,n,i,r){if(e.getBoundingClientRect&&vf.domSupported&&!ye(e)){var a=e[Hf]||(e[Hf]={}),o=ve(e,a),s=me(o,a,r);if(s)return s(t,n,i),!0}return!1}function ve(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],a=0;4>a;a++){var o=document.createElement("div"),s=o.style,l=a%2,u=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(o),n.push(o)}return n}function me(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],a=e.srcCoords,o=!0,s=[],l=[],u=0;4>u;u++){var h=t[u].getBoundingClientRect(),c=2*u,d=h.left,f=h.top;s.push(d,f),o=o&&a&&d===a[c]&&f===a[c+1],l.push(t[u].offsetLeft,t[u].offsetTop)}return o&&r?r:(e.srcCoords=s,e[i]=n?fe(l,s):fe(s,l))}function ye(t){return"CANVAS"===t.nodeName.toUpperCase()}function _e(t,e,n,i){return n=n||{},i||!vf.canvasSupported?xe(t,e,n):vf.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):xe(t,e,n),n}function xe(t,e,n){if(vf.domSupported&&t.getBoundingClientRect){var i=e.clientX,r=e.clientY;if(ye(t)){var a=t.getBoundingClientRect();return n.zrX=i-a.left,void(n.zrY=r-a.top)}if(ge(Xf,t,i,r))return n.zrX=Xf[0],void(n.zrY=Xf[1])}n.zrX=n.zrY=0}function we(t){return t||window.event}function be(t,e,n){if(e=we(e),null!=e.zrX)return e;var i=e.type,r=i&&i.indexOf("touch")>=0;if(r){var a="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];a&&_e(t,a,e,n)}else _e(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&Gf.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function Se(t,e,n,i){Vf?t.addEventListener(e,n,i):t.attachEvent("on"+e,n)}function Me(t,e,n,i){Vf?t.removeEventListener(e,n,i):t.detachEvent("on"+e,n)}function Ce(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}function Te(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function Ie(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:ke}}function ke(){Yf(this.event)}function De(){}function Ae(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return i?qf:!0}return!1}function Pe(t,e,n){var i=t.painter;return 0>e||e>i.getWidth()||0>n||n>i.getHeight()}function Le(){var t=new Kf(6);return Oe(t),t}function Oe(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Be(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function ze(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],a=e[0]*n[2]+e[2]*n[3],o=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t}function Ee(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function Re(t,e,n){var i=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],u=Math.sin(n),h=Math.cos(n);return t[0]=i*h+o*u,t[1]=-i*u+o*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*a+u*l,t[5]=h*l-u*a,t}function Fe(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function Ne(t,e){var n=e[0],i=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=n*o-a*i;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-o*r)*l,t[5]=(a*r-n*s)*l,t):null}function He(t){var e=Le();return Be(e,t),e}function We(t){return t>tp||-tp>t}function Ve(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}function Ge(t){return t=Math.round(t),0>t?0:t>255?255:t}function Xe(t){return t=Math.round(t),0>t?0:t>360?360:t}function Ye(t){return 0>t?0:t>1?1:t}function Ue(t){return Ge(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function je(t){return Ye(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function qe(t,e,n){return 0>n?n+=1:n>1&&(n-=1),1>6*n?t+(e-t)*n*6:1>2*n?e:2>3*n?t+(e-t)*(2/3-n)*6:t}function Ze(t,e,n){return t+(e-t)*n}function $e(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Ke(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function Qe(t,e){pp&&Ke(pp,e),pp=fp.put(t,pp||e.slice())}function Je(t,e){if(t){e=e||[];var n=fp.get(t);if(n)return Ke(e,n);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in dp)return Ke(e,dp[i]),Qe(t,e),e;if("#"!==i.charAt(0)){var r=i.indexOf("("),a=i.indexOf(")");if(-1!==r&&a+1===i.length){var o=i.substr(0,r),s=i.substr(r+1,a-(r+1)).split(","),l=1;switch(o){case"rgba":if(4!==s.length)return void $e(e,0,0,0,1);l=je(s.pop());case"rgb":return 3!==s.length?void $e(e,0,0,0,1):($e(e,Ue(s[0]),Ue(s[1]),Ue(s[2]),l),Qe(t,e),e);case"hsla":return 4!==s.length?void $e(e,0,0,0,1):(s[3]=je(s[3]),tn(s,e),Qe(t,e),e);case"hsl":return 3!==s.length?void $e(e,0,0,0,1):(tn(s,e),Qe(t,e),e);default:return}}$e(e,0,0,0,1)}else{if(4===i.length){var u=parseInt(i.substr(1),16);return u>=0&&4095>=u?($e(e,(3840&u)>>4|(3840&u)>>8,240&u|(240&u)>>4,15&u|(15&u)<<4,1),Qe(t,e),e):void $e(e,0,0,0,1)}if(7===i.length){var u=parseInt(i.substr(1),16);return u>=0&&16777215>=u?($e(e,(16711680&u)>>16,(65280&u)>>8,255&u,1),Qe(t,e),e):void $e(e,0,0,0,1)}}}}function tn(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=je(t[1]),r=je(t[2]),a=.5>=r?r*(i+1):r+i-r*i,o=2*r-a;return e=e||[],$e(e,Ge(255*qe(o,a,n+1/3)),Ge(255*qe(o,a,n)),Ge(255*qe(o,a,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function en(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(i,r,a),s=Math.max(i,r,a),l=s-o,u=(s+o)/2;if(0===l)e=0,n=0;else{n=.5>u?l/(s+o):l/(2-s-o);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-a)/6+l/2)/l;i===s?e=d-c:r===s?e=1/3+h-d:a===s&&(e=2/3+c-h),0>e&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}function nn(t,e){var n=Je(t);if(n){for(var i=0;3>i;i++)n[i]=0>e?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:t[i]<0&&(n[i]=0);return un(n,4===n.length?"rgba":"rgb")}}function rn(t){var e=Je(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function an(t,e,n){if(e&&e.length&&t>=0&&1>=t){n=n||[];var i=t*(e.length-1),r=Math.floor(i),a=Math.ceil(i),o=e[r],s=e[a],l=i-r;return n[0]=Ge(Ze(o[0],s[0],l)),n[1]=Ge(Ze(o[1],s[1],l)),n[2]=Ge(Ze(o[2],s[2],l)),n[3]=Ye(Ze(o[3],s[3],l)),n}}function on(t,e,n){if(e&&e.length&&t>=0&&1>=t){var i=t*(e.length-1),r=Math.floor(i),a=Math.ceil(i),o=Je(e[r]),s=Je(e[a]),l=i-r,u=un([Ge(Ze(o[0],s[0],l)),Ge(Ze(o[1],s[1],l)),Ge(Ze(o[2],s[2],l)),Ye(Ze(o[3],s[3],l))],"rgba");return n?{color:u,leftIndex:r,rightIndex:a,value:i}:u}}function sn(t,e,n,i){return t=Je(t),t?(t=en(t),null!=e&&(t[0]=Xe(e)),null!=n&&(t[1]=je(n)),null!=i&&(t[2]=je(i)),un(tn(t),"rgba")):void 0}function ln(t,e){return t=Je(t),t&&null!=e?(t[3]=Ye(e),un(t,"rgba")):void 0}function un(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(n+=","+t[3]),e+"("+n+")"}}function hn(t,e){return t[e]}function cn(t,e,n){t[e]=n}function dn(t,e,n){return(e-t)*n+t}function fn(t,e,n){return n>.5?e:t}function pn(t,e,n,i,r){var a=t.length;if(1===r)for(var o=0;a>o;o++)i[o]=dn(t[o],e[o],n);else for(var s=a&&t[0].length,o=0;a>o;o++)for(var l=0;s>l;l++)i[o][l]=dn(t[o][l],e[o][l],n)}function gn(t,e,n){var i=t.length,r=e.length;if(i!==r){var a=i>r;if(a)t.length=r;else for(var o=i;r>o;o++)t.push(1===n?e[o]:yp.call(e[o]))}for(var s=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===n)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;s>l;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function vn(t,e,n){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===n){for(var r=0;i>r;r++)if(t[r]!==e[r])return!1}else for(var a=t[0].length,r=0;i>r;r++)for(var o=0;a>o;o++)if(t[r][o]!==e[r][o])return!1;return!0}function mn(t,e,n,i,r,a,o,s,l){var u=t.length;if(1===l)for(var h=0;u>h;h++)s[h]=yn(t[h],e[h],n[h],i[h],r,a,o);else for(var c=t[0].length,h=0;u>h;h++)for(var d=0;c>d;d++)s[h][d]=yn(t[h][d],e[h][d],n[h][d],i[h][d],r,a,o)}function yn(t,e,n,i,r,a,o){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*o+(-3*(e-n)-2*s-l)*a+s*r+e}function _n(t){if(d(t)){var e=t.length;if(d(t[0])){for(var n=[],i=0;e>i;i++)n.push(yp.call(t[i]));return n}return yp.call(t)}return t}function xn(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function wn(t){var e=t[t.length-1].value;return d(e&&e[0])?2:1}function bn(t,e,n,i,r,a){var o=t._getter,s=t._setter,l="spline"===e,u=i.length;if(u){var h,c=i[0].value,f=d(c),p=!1,g=!1,v=f?wn(i):0;i.sort(function(t,e){return t.time-e.time}),h=i[u-1].time;for(var m=[],y=[],_=i[0].value,x=!0,w=0;u>w;w++){m.push(i[w].time/h);var b=i[w].value;if(f&&vn(b,_,v)||!f&&b===_||(x=!1),_=b,"string"==typeof b){var S=Je(b);S?(b=S,p=!0):g=!0}y.push(b)}if(a||!x){for(var M=y[u-1],w=0;u-1>w;w++)f?gn(y[w],M,v):!isNaN(y[w])||isNaN(M)||g||p||(y[w]=M);f&&gn(o(t._target,r),M,v);var C,T,I,k,D,A,P=0,L=0;if(p)var O=[0,0,0,0];var B=function(t,e){var n;if(0>e)n=0;else if(L>e){for(C=Math.min(P+1,u-1),n=C;n>=0&&!(m[n]<=e);n--);n=Math.min(n,u-2)}else{for(n=P;u>n&&!(m[n]>e);n++);n=Math.min(n-1,u-2)}P=n,L=e;var i=m[n+1]-m[n];if(0!==i)if(T=(e-m[n])/i,l)if(k=y[n],I=y[0===n?n:n-1],D=y[n>u-2?u-1:n+1],A=y[n>u-3?u-1:n+2],f)mn(I,k,D,A,T,T*T,T*T*T,o(t,r),v);else{var a;if(p)a=mn(I,k,D,A,T,T*T,T*T*T,O,1),a=xn(O);else{if(g)return fn(k,D,T);a=yn(I,k,D,A,T,T*T,T*T*T)}s(t,r,a)}else if(f)pn(y[n],y[n+1],T,o(t,r),v);else{var a;if(p)pn(y[n],y[n+1],T,O,1),a=xn(O);else{if(g)return fn(y[n],y[n+1],T);a=dn(y[n],y[n+1],T)}s(t,r,a)}},z=new Ve({target:t._target,life:h,loop:t._loop,delay:t._delay,onframe:B,ondestroy:n});return e&&"spline"!==e&&(z.easing=e),z}}}function Sn(t,e,n,i,r,a,o,s){function l(){h--,h||a&&a()}b(i)?(a=r,r=i,i=0):w(r)?(a=r,r="linear",i=0):w(i)?(a=i,i=0):w(n)?(a=n,n=500):n||(n=500),t.stopAnimation(),Mn(t,"",t,e,n,i,s);var u=t.animators.slice(),h=u.length;h||a&&a();for(var c=0;c<u.length;c++)u[c].done(l).start(r,o)}function Mn(t,e,n,i,r,a,o){var s={},l=0;for(var u in i)i.hasOwnProperty(u)&&(null!=n[u]?S(i[u])&&!d(i[u])?Mn(t,e?e+"."+u:u,n[u],i[u],r,a,o):(o?(s[u]=n[u],Cn(t,e,u,i[u])):s[u]=i[u],l++):null==i[u]||o||Cn(t,e,u,i[u]));l>0&&t.animate(e,!1).when(null==r?500:r,s).delay(a||0)}function Cn(t,e,n,i){if(e){var r={};r[e]={},r[e][n]=i,t.attr(r)}else t.attr(n,i)}function Tn(t,e,n,i){0>n&&(t+=n,n=-n),0>i&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}function In(t){for(var e=0;t>=Pp;)e|=1&t,t>>=1;return t+e}function kn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;n>r&&i(t[r],t[r-1])<0;)r++;Dn(t,e,r)}else for(;n>r&&i(t[r],t[r-1])>=0;)r++;return r-e}function Dn(t,e,n){for(n--;n>e;){var i=t[e];t[e++]=t[n],t[n--]=i}}function An(t,e,n,i,r){for(i===e&&i++;n>i;i++){for(var a,o=t[i],s=e,l=i;l>s;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=o}}function Pn(t,e,n,i,r,a){var o=0,s=0,l=1;if(a(t,e[n+r])>0){for(s=i-r;s>l&&a(t,e[n+r+l])>0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;s>l&&a(t,e[n+r-l])<=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}for(o++;l>o;){var h=o+(l-o>>>1);a(t,e[n+h])>0?o=h+1:l=h}return l}function Ln(t,e,n,i,r,a){var o=0,s=0,l=1;if(a(t,e[n+r])<0){for(s=r+1;s>l&&a(t,e[n+r-l])<0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}else{for(s=i-r;s>l&&a(t,e[n+r+l])>=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}for(o++;l>o;){var h=o+(l-o>>>1);a(t,e[n+h])<0?l=h:o=h+1}return l}function On(t,e){function n(t,e){l[c]=t,u[c]=e,c+=1}function i(){for(;c>1;){var t=c-2;if(t>=1&&u[t-1]<=u[t]+u[t+1]||t>=2&&u[t-2]<=u[t]+u[t-1])u[t-1]<u[t+1]&&t--;else if(u[t]>u[t+1])break;a(t)}}function r(){for(;c>1;){var t=c-2;t>0&&u[t-1]<u[t+1]&&t--,a(t)}}function a(n){var i=l[n],r=u[n],a=l[n+1],h=u[n+1];u[n]=r+h,n===c-3&&(l[n+1]=l[n+2],u[n+1]=u[n+2]),c--;var d=Ln(t[a],t,i,r,0,e);i+=d,r-=d,0!==r&&(h=Pn(t[i+r-1],t,a,h,h-1,e),0!==h&&(h>=r?o(i,r,a,h):s(i,r,a,h)))}function o(n,i,r,a){var o=0;for(o=0;i>o;o++)d[o]=t[n+o];var s=0,l=r,u=n;if(t[u++]=t[l++],0!==--a){if(1===i){for(o=0;a>o;o++)t[u+o]=t[l+o];return void(t[u+a]=d[s])}for(var c,f,p,g=h;;){c=0,f=0,p=!1;do if(e(t[l],d[s])<0){if(t[u++]=t[l++],f++,c=0,0===--a){p=!0;break}}else if(t[u++]=d[s++],c++,f=0,1===--i){p=!0;break}while(g>(c|f));if(p)break;do{if(c=Ln(t[l],d,s,i,0,e),0!==c){for(o=0;c>o;o++)t[u+o]=d[s+o];if(u+=c,s+=c,i-=c,1>=i){p=!0;break}}if(t[u++]=t[l++],0===--a){p=!0;break}if(f=Pn(d[s],t,l,a,0,e),0!==f){for(o=0;f>o;o++)t[u+o]=t[l+o];if(u+=f,l+=f,a-=f,0===a){p=!0;break}}if(t[u++]=d[s++],1===--i){p=!0;break}g--}while(c>=Lp||f>=Lp);if(p)break;0>g&&(g=0),g+=2}if(h=g,1>h&&(h=1),1===i){for(o=0;a>o;o++)t[u+o]=t[l+o];t[u+a]=d[s]}else{if(0===i)throw new Error;for(o=0;i>o;o++)t[u+o]=d[s+o]}}else for(o=0;i>o;o++)t[u+o]=d[s+o]}function s(n,i,r,a){var o=0;for(o=0;a>o;o++)d[o]=t[r+o];var s=n+i-1,l=a-1,u=r+a-1,c=0,f=0;if(t[u--]=t[s--],0!==--i){if(1===a){for(u-=i,s-=i,f=u+1,c=s+1,o=i-1;o>=0;o--)t[f+o]=t[c+o];return void(t[u]=d[l])}for(var p=h;;){var g=0,v=0,m=!1;do if(e(d[l],t[s])<0){if(t[u--]=t[s--],g++,v=0,0===--i){m=!0;break}}else if(t[u--]=d[l--],v++,g=0,1===--a){m=!0;break}while(p>(g|v));if(m)break;do{if(g=i-Ln(d[l],t,n,i,i-1,e),0!==g){for(u-=g,s-=g,i-=g,f=u+1,c=s+1,o=g-1;o>=0;o--)t[f+o]=t[c+o];if(0===i){m=!0;break}}if(t[u--]=d[l--],1===--a){m=!0;break}if(v=a-Pn(t[s],d,0,a,a-1,e),0!==v){for(u-=v,l-=v,a-=v,f=u+1,c=l+1,o=0;v>o;o++)t[f+o]=d[c+o];if(1>=a){m=!0;break}}if(t[u--]=t[s--],0===--i){m=!0;break}p--}while(g>=Lp||v>=Lp);if(m)break;0>p&&(p=0),p+=2}if(h=p,1>h&&(h=1),1===a){for(u-=i,s-=i,f=u+1,c=s+1,o=i-1;o>=0;o--)t[f+o]=t[c+o];t[u]=d[l]}else{if(0===a)throw new Error;for(c=u-(a-1),o=0;a>o;o++)t[c+o]=d[o]}}else for(c=u-(a-1),o=0;a>o;o++)t[c+o]=d[o]}var l,u,h=Lp,c=0,d=[];l=[],u=[],this.mergeRuns=i,this.forceMergeRuns=r,this.pushRun=n}function Bn(t,e,n,i){n||(n=0),i||(i=t.length);var r=i-n;if(!(2>r)){var a=0;if(Pp>r)return a=kn(t,n,i,e),void An(t,n,i,n+a,e);var o=new On(t,e),s=In(r);do{if(a=kn(t,n,i,e),s>a){var l=r;l>s&&(l=s),An(t,n,n+l,n+a,e),a=l}o.pushRun(n,a),o.mergeRuns(),r-=a,n+=a}while(0!==r);o.forceMergeRuns()}}function zn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function En(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(i=i*n.width+n.x,r=r*n.width+n.x,a=a*n.height+n.y,o=o*n.height+n.y),i=isNaN(i)?0:i,r=isNaN(r)?1:r,a=isNaN(a)?0:a,o=isNaN(o)?0:o;var s=t.createLinearGradient(i,a,r,o);return s}function Rn(t,e,n){var i=n.width,r=n.height,a=Math.min(i,r),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(o=o*i+n.x,s=s*r+n.y,l*=a);var u=t.createRadialGradient(o,s,0,o,s,l);return u}function Fn(){return!1}function Nn(t,e,n){var i=If(),r=e.getWidth(),a=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left=0,o.top=0,o.width=r+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=a*n,i}function Hn(t){if("string"==typeof t){var e=Up.get(t);return e&&e.image}return t}function Wn(t,e,n,i,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var a=Up.get(t),o={hostEl:n,cb:i,cbPayload:r};return a?(e=a.image,!Gn(e)&&a.pending.push(o)):(e=new Image,e.onload=e.onerror=Vn,Up.put(t,e.__cachedImgObj={image:e,pending:[o]}),e.src=e.__zrImageSrc=t),e}return t}return e}function Vn(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function Gn(t){return t&&t.width&&t.height}function Xn(t,e){e=e||Kp;var n=t+":"+e;if(jp[n])return jp[n];for(var i=(t+"").split("\n"),r=0,a=0,o=i.length;o>a;a++)r=Math.max(ni(i[a],e).width,r);return qp>Zp&&(qp=0,jp={}),qp++,jp[n]=r,r}function Yn(t,e,n,i,r,a,o,s){return o?jn(t,e,n,i,r,a,o,s):Un(t,e,n,i,r,a,s)}function Un(t,e,n,i,r,a,o){var s=ii(t,e,r,a,o),l=Xn(t,e);r&&(l+=r[1]+r[3]);var u=s.outerHeight,h=qn(0,l,n),c=Zn(0,u,i),d=new Tn(h,c,l,u);return d.lineHeight=s.lineHeight,d}function jn(t,e,n,i,r,a,o,s){var l=ri(t,{rich:o,truncate:s,font:e,textAlign:n,textPadding:r,textLineHeight:a}),u=l.outerWidth,h=l.outerHeight,c=qn(0,u,n),d=Zn(0,h,i);return new Tn(c,d,u,h)}function qn(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Zn(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function $n(t,e,n){var i=e.textPosition,r=e.textDistance,a=n.x,o=n.y;r=r||0;var s=n.height,l=n.width,u=s/2,h="left",c="top";switch(i){case"left":a-=r,o+=u,h="right",c="middle";break;case"right":a+=r+l,o+=u,c="middle";break;case"top":a+=l/2,o-=r,h="center",c="bottom";break;case"bottom":a+=l/2,o+=s+r,h="center";break;case"inside":a+=l/2,o+=u,h="center",c="middle";break;case"insideLeft":a+=r,o+=u,c="middle";break;case"insideRight":a+=l-r,o+=u,h="right",c="middle";break;case"insideTop":a+=l/2,o+=r,h="center";break;case"insideBottom":a+=l/2,o+=s-r,h="center",c="bottom";break;case"insideTopLeft":a+=r,o+=r;break;case"insideTopRight":a+=l-r,o+=r,h="right";break;case"insideBottomLeft":a+=r,o+=s-r,c="bottom";break;case"insideBottomRight":a+=l-r,o+=s-r,h="right",c="bottom"}return t=t||{},t.x=a,t.y=o,t.textAlign=h,t.textVerticalAlign=c,t}function Kn(t,e,n,i,r){if(!e)return"";var a=(t+"").split("\n");r=Qn(e,n,i,r);for(var o=0,s=a.length;s>o;o++)a[o]=Jn(a[o],r);return a.join("\n")}function Qn(t,e,n,i){i=o({},i),i.font=e;var n=D(n,"...");i.maxIterations=D(i.maxIterations,2);var r=i.minChar=D(i.minChar,0);i.cnCharWidth=Xn("国",e);var a=i.ascCharWidth=Xn("a",e);i.placeholder=D(i.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;r>l&&s>=a;l++)s-=a;var u=Xn(n,e);return u>s&&(n="",u=0),s=t-u,i.ellipsis=n,i.ellipsisWidth=u,i.contentWidth=s,i.containerWidth=t,i}function Jn(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var a=Xn(t,i);if(n>=a)return t;for(var o=0;;o++){if(r>=a||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?ti(t,r,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*r/a):0;t=t.substr(0,s),a=Xn(t,i)}return""===t&&(t=e.placeholder),t}function ti(t,e,n,i){for(var r=0,a=0,o=t.length;o>a&&e>r;a++){var s=t.charCodeAt(a);r+=s>=0&&127>=s?n:i}return a}function ei(t){return Xn("国",t)}function ni(t,e){return Qp.measureText(t,e)}function ii(t,e,n,i,r){null!=t&&(t+="");var a=D(i,ei(e)),o=t?t.split("\n"):[],s=o.length*a,l=s,u=!0;if(n&&(l+=n[0]+n[2]),t&&r){u=!1;var h=r.outerHeight,c=r.outerWidth;if(null!=h&&l>h)t="",o=[];else if(null!=c)for(var d=Qn(c-(n?n[1]+n[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),f=0,p=o.length;p>f;f++)o[f]=Jn(o[f],d)}return{lines:o,height:s,outerHeight:l,lineHeight:a,canCacheByTextString:u}}function ri(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;for(var i,r=$p.lastIndex=0;null!=(i=$p.exec(t));){var a=i.index;a>r&&ai(n,t.substring(r,a)),ai(n,i[2],i[1]),r=$p.lastIndex}r<t.length&&ai(n,t.substring(r,t.length));var o=n.lines,s=0,l=0,u=[],h=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;h&&(null!=d&&(d-=h[1]+h[3]),null!=f&&(f-=h[0]+h[2]));for(var p=0;p<o.length;p++){for(var g=o[p],v=0,m=0,y=0;y<g.tokens.length;y++){var _=g.tokens[y],x=_.styleName&&e.rich[_.styleName]||{},w=_.textPadding=x.textPadding,b=_.font=x.font||e.font,S=_.textHeight=D(x.textHeight,ei(b));if(w&&(S+=w[0]+w[2]),_.height=S,_.lineHeight=A(x.textLineHeight,e.textLineHeight,S),_.textAlign=x&&x.textAlign||e.textAlign,_.textVerticalAlign=x&&x.textVerticalAlign||"middle",null!=f&&s+_.lineHeight>f)return{lines:[],width:0,height:0};_.textWidth=Xn(_.text,b);var M=x.textWidth,C=null==M||"auto"===M;if("string"==typeof M&&"%"===M.charAt(M.length-1))_.percentWidth=M,u.push(_),M=0;else{if(C){M=_.textWidth;var T=x.textBackgroundColor,I=T&&T.image;I&&(I=Hn(I),Gn(I)&&(M=Math.max(M,I.width*S/I.height)))}var k=w?w[1]+w[3]:0;M+=k;var P=null!=d?d-m:null;null!=P&&M>P&&(!C||k>P?(_.text="",_.textWidth=M=0):(_.text=Kn(_.text,P-k,b,c.ellipsis,{minChar:c.minChar}),_.textWidth=Xn(_.text,b),M=_.textWidth+k))}m+=_.width=M,x&&(v=Math.max(v,_.lineHeight))}g.width=m,g.lineHeight=v,s+=v,l=Math.max(l,m)}n.outerWidth=n.width=D(e.textWidth,l),n.outerHeight=n.height=D(e.textHeight,s),h&&(n.outerWidth+=h[1]+h[3],n.outerHeight+=h[0]+h[2]);for(var p=0;p<u.length;p++){var _=u[p],L=_.percentWidth;_.width=parseInt(L,10)/100*l}return n}function ai(t,e,n){for(var i=""===e,r=e.split("\n"),a=t.lines,o=0;o<r.length;o++){var s=r[o],l={styleName:n,text:s,isLineHolder:!s&&!i};if(o)a.push({tokens:[l]});else{var u=(a[a.length-1]||(a[0]={tokens:[]})).tokens,h=u.length;1===h&&u[0].isLineHolder?u[0]=l:(s||!h||i)&&u.push(l)}}}function oi(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&B(e)||t.textFont||t.font}function si(t,e){var n,i,r,a,o=e.x,s=e.y,l=e.width,u=e.height,h=e.r;0>l&&(o+=l,l=-l),0>u&&(s+=u,u=-u),"number"==typeof h?n=i=r=a=h:h instanceof Array?1===h.length?n=i=r=a=h[0]:2===h.length?(n=r=h[0],i=a=h[1]):3===h.length?(n=h[0],i=a=h[1],r=h[2]):(n=h[0],i=h[1],r=h[2],a=h[3]):n=i=r=a=0;var c;n+i>l&&(c=n+i,n*=l/c,i*=l/c),r+a>l&&(c=r+a,r*=l/c,a*=l/c),i+r>u&&(c=i+r,i*=u/c,r*=u/c),n+a>u&&(c=n+a,n*=u/c,a*=u/c),t.moveTo(o+n,s),t.lineTo(o+l-i,s),0!==i&&t.arc(o+l-i,s+i,i,-Math.PI/2,0),t.lineTo(o+l,s+u-r),0!==r&&t.arc(o+l-r,s+u-r,r,0,Math.PI/2),t.lineTo(o+a,s+u),0!==a&&t.arc(o+a,s+u-a,a,Math.PI/2,Math.PI),t.lineTo(o,s+n),0!==n&&t.arc(o+n,s+n,n,Math.PI,1.5*Math.PI)}function li(t){return ui(t),f(t.rich,ui),t}function ui(t){if(t){t.font=oi(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||tg[e]?e:"left";var n=t.textVerticalAlign||t.textBaseline;"center"===n&&(n="middle"),t.textVerticalAlign=null==n||eg[n]?n:"top";var i=t.textPadding;i&&(t.textPadding=L(t.textPadding))}}function hi(t,e,n,i,r,a){i.rich?di(t,e,n,i,r,a):ci(t,e,n,i,r,a)}function ci(t,e,n,i,r,a){var o,s=vi(i),l=!1,u=e.__attrCachedBy===Ep.PLAIN_TEXT;a!==Rp?(a&&(o=a.style,l=!s&&u&&o),e.__attrCachedBy=s?Ep.NONE:Ep.PLAIN_TEXT):u&&(e.__attrCachedBy=Ep.NONE);var h=i.font||Jp;l&&h===(o.font||Jp)||(e.font=h);var c=t.__computedFont;t.__styleFont!==h&&(t.__styleFont=h,c=t.__computedFont=e.font);var d=i.textPadding,f=i.textLineHeight,p=t.__textCotentBlock;(!p||t.__dirtyText)&&(p=t.__textCotentBlock=ii(n,c,d,f,i.truncate));var g=p.outerHeight,v=p.lines,m=p.lineHeight,y=_i(rg,t,i,r),_=y.baseX,x=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;pi(e,i,r,_,x);var S=Zn(x,g,b),M=_,C=S;if(s||d){var T=Xn(n,c),I=T;d&&(I+=d[1]+d[3]);var k=qn(_,I,w);s&&mi(t,e,i,k,S,I,g),d&&(M=Mi(_,w,d),C+=d[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=i.opacity||1;for(var D=0;D<ng.length;D++){var A=ng[D],P=A[0],L=A[1],O=i[P];l&&O===o[P]||(e[L]=zp(e,L,O||A[2]))}C+=m/2;var B=i.textStrokeWidth,z=l?o.textStrokeWidth:null,E=!l||B!==z,R=!l||E||i.textStroke!==o.textStroke,F=wi(i.textStroke,B),N=bi(i.textFill);if(F&&(E&&(e.lineWidth=B),R&&(e.strokeStyle=F)),N&&(l&&i.textFill===o.textFill||(e.fillStyle=N)),1===v.length)F&&e.strokeText(v[0],M,C),N&&e.fillText(v[0],M,C);else for(var D=0;D<v.length;D++)F&&e.strokeText(v[D],M,C),N&&e.fillText(v[D],M,C),C+=m}function di(t,e,n,i,r,a){a!==Rp&&(e.__attrCachedBy=Ep.NONE);var o=t.__textCotentBlock;(!o||t.__dirtyText)&&(o=t.__textCotentBlock=ri(n,i)),fi(t,e,o,i,r)}function fi(t,e,n,i,r){var a=n.width,o=n.outerWidth,s=n.outerHeight,l=i.textPadding,u=_i(rg,t,i,r),h=u.baseX,c=u.baseY,d=u.textAlign,f=u.textVerticalAlign;pi(e,i,r,h,c);var p=qn(h,o,d),g=Zn(c,s,f),v=p,m=g;l&&(v+=l[3],m+=l[0]);var y=v+a;vi(i)&&mi(t,e,i,p,g,o,s);for(var _=0;_<n.lines.length;_++){for(var x,w=n.lines[_],b=w.tokens,S=b.length,M=w.lineHeight,C=w.width,T=0,I=v,k=y,D=S-1;S>T&&(x=b[T],!x.textAlign||"left"===x.textAlign);)gi(t,e,x,i,M,m,I,"left"),C-=x.width,I+=x.width,T++;for(;D>=0&&(x=b[D],"right"===x.textAlign);)gi(t,e,x,i,M,m,k,"right"),C-=x.width,k-=x.width,D--;for(I+=(a-(I-v)-(y-k)-C)/2;D>=T;)x=b[T],gi(t,e,x,i,M,m,I+x.width/2,"center"),I+=x.width,T++;m+=M}}function pi(t,e,n,i,r){if(n&&e.textRotation){var a=e.textOrigin;"center"===a?(i=n.width/2+n.x,r=n.height/2+n.y):a&&(i=a[0]+n.x,r=a[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r)}}function gi(t,e,n,i,r,a,o,s){var l=i.rich[n.styleName]||{};l.text=n.text;var u=n.textVerticalAlign,h=a+r/2;
"top"===u?h=a+n.height/2:"bottom"===u&&(h=a+r-n.height/2),!n.isLineHolder&&vi(l)&&mi(t,e,l,"right"===s?o-n.width:"center"===s?o-n.width/2:o,h-n.height/2,n.width,n.height);var c=n.textPadding;c&&(o=Mi(o,s,c),h-=n.height/2-c[2]-n.textHeight/2),xi(e,"shadowBlur",A(l.textShadowBlur,i.textShadowBlur,0)),xi(e,"shadowColor",l.textShadowColor||i.textShadowColor||"transparent"),xi(e,"shadowOffsetX",A(l.textShadowOffsetX,i.textShadowOffsetX,0)),xi(e,"shadowOffsetY",A(l.textShadowOffsetY,i.textShadowOffsetY,0)),xi(e,"textAlign",s),xi(e,"textBaseline","middle"),xi(e,"font",n.font||Jp);var d=wi(l.textStroke||i.textStroke,p),f=bi(l.textFill||i.textFill),p=D(l.textStrokeWidth,i.textStrokeWidth);d&&(xi(e,"lineWidth",p),xi(e,"strokeStyle",d),e.strokeText(n.text,o,h)),f&&(xi(e,"fillStyle",f),e.fillText(n.text,o,h))}function vi(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function mi(t,e,n,i,r,a,o){var s=n.textBackgroundColor,l=n.textBorderWidth,u=n.textBorderColor,h=b(s);if(xi(e,"shadowBlur",n.textBoxShadowBlur||0),xi(e,"shadowColor",n.textBoxShadowColor||"transparent"),xi(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),xi(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),h||l&&u){e.beginPath();var c=n.textBorderRadius;c?si(e,{x:i,y:r,width:a,height:o,r:c}):e.rect(i,r,a,o),e.closePath()}if(h)if(xi(e,"fillStyle",s),null!=n.fillOpacity){var d=e.globalAlpha;e.globalAlpha=n.fillOpacity*n.opacity,e.fill(),e.globalAlpha=d}else e.fill();else if(S(s)){var f=s.image;f=Wn(f,null,t,yi,s),f&&Gn(f)&&e.drawImage(f,i,r,a,o)}if(l&&u)if(xi(e,"lineWidth",l),xi(e,"strokeStyle",u),null!=n.strokeOpacity){var d=e.globalAlpha;e.globalAlpha=n.strokeOpacity*n.opacity,e.stroke(),e.globalAlpha=d}else e.stroke()}function yi(t,e){e.image=t}function _i(t,e,n,i){var r=n.x||0,a=n.y||0,o=n.textAlign,s=n.textVerticalAlign;if(i){var l=n.textPosition;if(l instanceof Array)r=i.x+Si(l[0],i.width),a=i.y+Si(l[1],i.height);else{var u=e&&e.calculateTextPosition?e.calculateTextPosition(ig,n,i):$n(ig,n,i);r=u.x,a=u.y,o=o||u.textAlign,s=s||u.textVerticalAlign}var h=n.textOffset;h&&(r+=h[0],a+=h[1])}return t=t||{},t.baseX=r,t.baseY=a,t.textAlign=o,t.textVerticalAlign=s,t}function xi(t,e,n){return t[e]=zp(t,e,n),t[e]}function wi(t,e){return null==t||0>=e||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function bi(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Si(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Mi(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Ci(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Ti(t){t=t||{},Tp.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new Np(t.style,this),this._rect=null,this.__clipPaths=null}function Ii(t){Ti.call(this,t)}function ki(t){return parseInt(t,10)}function Di(t){return t?t.__builtin__?!0:"function"!=typeof t.resize||"function"!=typeof t.refresh?!1:!0:!1}function Ai(t,e,n){return cg.copy(t.getBoundingRect()),t.transform&&cg.applyTransform(t.transform),dg.width=e,dg.height=n,!cg.intersect(dg)}function Pi(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}function Li(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}function Oi(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}function Bi(t){return"mousewheel"===t&&vf.browser.firefox?"DOMMouseScroll":t}function zi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Ei(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout(function(){t.touching=!1,t.touchTimer=null},700)}function Ri(t){t&&(t.zrByTouch=!0)}function Fi(t,e){return be(t.dom,new Hi(t,e),!0)}function Ni(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}function Hi(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}function Wi(t,e){var n=e.domHandlers;vf.pointerEventsSupported?f(mg.pointer,function(i){Gi(e,i,function(e){n[i].call(t,e)})}):(vf.touchEventsSupported&&f(mg.touch,function(i){Gi(e,i,function(r){n[i].call(t,r),Ei(e)})}),f(mg.mouse,function(i){Gi(e,i,function(r){r=we(r),e.touching||n[i].call(t,r)})}))}function Vi(t,e){function n(n){function i(i){i=we(i),Ni(t,i.target)||(i=Fi(t,i),e.domHandlers[n].call(t,i))}Gi(e,n,i,{capture:!0})}vf.pointerEventsSupported?f(yg.pointer,n):vf.touchEventsSupported||f(yg.mouse,n)}function Gi(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,Se(t.domTarget,Bi(e),n,i)}function Xi(t){var e=t.mounted;for(var n in e)e.hasOwnProperty(n)&&Me(t.domTarget,Bi(n),e[n],t.listenerOpts[n]);t.mounted={}}function Yi(t,e){if(t._mayPointerCapture=null,vg&&t._pointerCapturing^e){t._pointerCapturing=e;var n=t._globalHandlerScope;e?Vi(t,n):Xi(n)}}function Ui(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function ji(t,e){Ff.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new Ui(t,xg),vg&&(this._globalHandlerScope=new Ui(document,wg)),this._pointerCapturing=!1,this._mayPointerCapture=null,Wi(this,this._localHandlerScope)}function qi(t,e){var n=new Ig(pf(),t,e);return Cg[n.id]=n,n}function Zi(t){if(t)t.dispose();else{for(var e in Cg)Cg.hasOwnProperty(e)&&Cg[e].dispose();Cg={}}return this}function $i(t){return Cg[t]}function Ki(t,e){Mg[t]=e}function Qi(t){delete Cg[t]}function Ji(t){return t instanceof Array?t:null==t?[]:[t]}function tr(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;r>i;i++){var a=n[i];!t.emphasis[e].hasOwnProperty(a)&&t[e].hasOwnProperty(a)&&(t.emphasis[e][a]=t[e][a])}}}function er(t){return!Ag(t)||Pg(t)||t instanceof Date?t:t.value}function nr(t){return Ag(t)&&!(t instanceof Array)}function ir(t,e){e=(e||[]).slice();var n=p(t||[],function(t){return{exist:t}});return Dg(e,function(t,i){if(Ag(t)){for(var r=0;r<n.length;r++)if(!n[r].option&&null!=t.id&&n[r].exist.id===t.id+"")return n[r].option=t,void(e[i]=null);for(var r=0;r<n.length;r++){var a=n[r].exist;if(!(n[r].option||null!=a.id&&null!=t.id||null==t.name||or(t)||or(a)||a.name!==t.name+""))return n[r].option=t,void(e[i]=null)}}}),Dg(e,function(t){if(Ag(t)){for(var e=0;e<n.length;e++){var i=n[e].exist;if(!n[e].option&&!or(i)&&null==t.id){n[e].option=t;break}}e>=n.length&&n.push({option:t})}}),n}function rr(t){var e=F();Dg(t,function(t){var n=t.exist;n&&e.set(n.id,t)}),Dg(t,function(t){var n=t.option;O(!n||null==n.id||!e.get(n.id)||e.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&e.set(n.id,t),!t.keyInfo&&(t.keyInfo={})}),Dg(t,function(t,n){var i=t.exist,r=t.option,a=t.keyInfo;if(Ag(r)){if(a.name=null!=r.name?r.name+"":i?i.name:Lg+n,i)a.id=i.id;else if(null!=r.id)a.id=r.id+"";else{var o=0;do a.id="\x00"+a.name+"\x00"+o++;while(e.get(a.id))}e.set(a.id,t)}})}function ar(t){var e=t.name;return!(!e||!e.indexOf(Lg))}function or(t){return Ag(t)&&t.id&&0===(t.id+"").indexOf("\x00_ec_\x00")}function sr(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?x(e.dataIndex)?p(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?x(e.name)?p(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function lr(){var t="__\x00ec_inner_"+Bg++ +"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}function ur(t,e,n){if(b(e)){var i={};i[e+"Index"]=0,e=i}var r=n&&n.defaultMainType;!r||hr(e,r+"Index")||hr(e,r+"Id")||hr(e,r+"Name")||(e[r+"Index"]=0);var a={};return Dg(e,function(i,r){var i=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(a[r]=i);var o=r.match(/^(\w+)(Index|Id|Name)$/)||[],s=o[1],l=(o[2]||"").toLowerCase();if(!(!s||!l||null==i||"index"===l&&"none"===i||n&&n.includeMainTypes&&u(n.includeMainTypes,s)<0)){var h={mainType:s};("index"!==l||"all"!==i)&&(h[l]=i);var c=t.queryComponents(h);a[s+"Models"]=c,a[s+"Model"]=c[0]}}),a}function hr(t,e){return t&&t.hasOwnProperty(e)}function cr(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function dr(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function fr(t){return"auto"===t?vf.domSupported?"html":"richText":t||"html"}function pr(t){var e={main:"",sub:""};return t&&(t=t.split(zg),e.main=t[0]||"",e.sub=t[1]||""),e}function gr(t){O(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function vr(t){t.$constructor=t,t.extend=function(t){var e=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return o(n.prototype,t),n.extend=this.extend,n.superCall=yr,n.superApply=_r,h(n,this),n.superClass=e,n}}function mr(t){var e=["__\x00is_clz",Rg++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function yr(t,e){var n=P(arguments,2);return this.superClass.prototype[e].apply(t,n)}function _r(t,e,n){return this.superClass.prototype[e].apply(t,n)}function xr(t,e){function n(t){var e=i[t.main];return e&&e[Eg]||(e=i[t.main]={},e[Eg]=!0),e}e=e||{};var i={};if(t.registerClass=function(t,e){if(e)if(gr(e),e=pr(e),e.sub){if(e.sub!==Eg){var r=n(e);r[e.sub]=t}}else i[e.main]=t;return t},t.getClass=function(t,e,n){var r=i[t];if(r&&r[Eg]&&(r=e?r[e]:null),n&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=pr(t);var e=[],n=i[t.main];return n&&n[Eg]?f(n,function(t,n){n!==Eg&&e.push(t)}):e.push(n),e},t.hasClass=function(t){return t=pr(t),!!i[t.main]},t.getAllClassMainTypes=function(){var t=[];return f(i,function(e,n){t.push(n)}),t},t.hasSubTypes=function(t){t=pr(t);var e=i[t.main];return e&&e[Eg]},t.parseClassType=pr,e.registerWhenExtend){var r=t.extend;r&&(t.extend=function(e){var n=r.call(this,e);return t.registerClass(n,e.type)})}return t}function wr(t){return t>-Yg&&Yg>t}function br(t){return t>Yg||-Yg>t}function Sr(t,e,n,i,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*i+3*a*n)}function Mr(t,e,n,i,r){var a=1-r;return 3*(((e-t)*a+2*(n-e)*r)*a+(i-n)*r*r)}function Cr(t,e,n,i,r,a){var o=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*o*l,c=s*l-9*o*u,d=l*l-3*s*u,f=0;if(wr(h)&&wr(c))if(wr(s))a[0]=0;else{var p=-l/s;p>=0&&1>=p&&(a[f++]=p)}else{var g=c*c-4*h*d;if(wr(g)){var v=c/h,p=-s/o+v,m=-v/2;p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m)}else if(g>0){var y=Xg(g),_=h*s+1.5*o*(-c+y),x=h*s+1.5*o*(-c-y);_=0>_?-Gg(-_,qg):Gg(_,qg),x=0>x?-Gg(-x,qg):Gg(x,qg);var p=(-s-(_+x))/(3*o);p>=0&&1>=p&&(a[f++]=p)}else{var w=(2*h*s-3*o*c)/(2*Xg(h*h*h)),b=Math.acos(w)/3,S=Xg(h),M=Math.cos(b),p=(-s-2*S*M)/(3*o),m=(-s+S*(M+jg*Math.sin(b)))/(3*o),C=(-s+S*(M-jg*Math.sin(b)))/(3*o);p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m),C>=0&&1>=C&&(a[f++]=C)}}return f}function Tr(t,e,n,i,r){var a=6*n-12*e+6*t,o=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(wr(o)){if(br(a)){var u=-s/a;u>=0&&1>=u&&(r[l++]=u)}}else{var h=a*a-4*o*s;if(wr(h))r[0]=-a/(2*o);else if(h>0){var c=Xg(h),u=(-a+c)/(2*o),d=(-a-c)/(2*o);u>=0&&1>=u&&(r[l++]=u),d>=0&&1>=d&&(r[l++]=d)}}return l}function Ir(t,e,n,i,r,a){var o=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-o)*r+o,h=(l-s)*r+s,c=(h-u)*r+u;a[0]=t,a[1]=o,a[2]=u,a[3]=c,a[4]=c,a[5]=h,a[6]=l,a[7]=i}function kr(t,e,n,i,r,a,o,s,l,u,h){var c,d,f,p,g,v=.005,m=1/0;Zg[0]=l,Zg[1]=u;for(var y=0;1>y;y+=.05)$g[0]=Sr(t,n,r,o,y),$g[1]=Sr(e,i,a,s,y),p=zf(Zg,$g),m>p&&(c=y,m=p);m=1/0;for(var _=0;32>_&&!(Ug>v);_++)d=c-v,f=c+v,$g[0]=Sr(t,n,r,o,d),$g[1]=Sr(e,i,a,s,d),p=zf($g,Zg),d>=0&&m>p?(c=d,m=p):(Kg[0]=Sr(t,n,r,o,f),Kg[1]=Sr(e,i,a,s,f),g=zf(Kg,Zg),1>=f&&m>g?(c=f,m=g):v*=.5);return h&&(h[0]=Sr(t,n,r,o,c),h[1]=Sr(e,i,a,s,c)),Xg(m)}function Dr(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Ar(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Pr(t,e,n,i,r){var a=t-2*e+n,o=2*(e-t),s=t-i,l=0;if(wr(a)){if(br(o)){var u=-s/o;u>=0&&1>=u&&(r[l++]=u)}}else{var h=o*o-4*a*s;if(wr(h)){var u=-o/(2*a);u>=0&&1>=u&&(r[l++]=u)}else if(h>0){var c=Xg(h),u=(-o+c)/(2*a),d=(-o-c)/(2*a);u>=0&&1>=u&&(r[l++]=u),d>=0&&1>=d&&(r[l++]=d)}}return l}function Lr(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function Or(t,e,n,i,r){var a=(e-t)*i+t,o=(n-e)*i+e,s=(o-a)*i+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=n}function Br(t,e,n,i,r,a,o,s,l){var u,h=.005,c=1/0;Zg[0]=o,Zg[1]=s;for(var d=0;1>d;d+=.05){$g[0]=Dr(t,n,r,d),$g[1]=Dr(e,i,a,d);var f=zf(Zg,$g);c>f&&(u=d,c=f)}c=1/0;for(var p=0;32>p&&!(Ug>h);p++){var g=u-h,v=u+h;$g[0]=Dr(t,n,r,g),$g[1]=Dr(e,i,a,g);var f=zf($g,Zg);if(g>=0&&c>f)u=g,c=f;else{Kg[0]=Dr(t,n,r,v),Kg[1]=Dr(e,i,a,v);var m=zf(Kg,Zg);1>=v&&c>m?(u=v,c=m):h*=.5}}return l&&(l[0]=Dr(t,n,r,u),l[1]=Dr(e,i,a,u)),Xg(c)}function zr(t,e,n){if(0!==t.length){var i,r=t[0],a=r[0],o=r[0],s=r[1],l=r[1];for(i=1;i<t.length;i++)r=t[i],a=Qg(a,r[0]),o=Jg(o,r[0]),s=Qg(s,r[1]),l=Jg(l,r[1]);e[0]=a,e[1]=s,n[0]=o,n[1]=l}}function Er(t,e,n,i,r,a){r[0]=Qg(t,n),r[1]=Qg(e,i),a[0]=Jg(t,n),a[1]=Jg(e,i)}function Rr(t,e,n,i,r,a,o,s,l,u){var h,c=Tr,d=Sr,f=c(t,n,r,o,ov);for(l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0,h=0;f>h;h++){var p=d(t,n,r,o,ov[h]);l[0]=Qg(p,l[0]),u[0]=Jg(p,u[0])}for(f=c(e,i,a,s,sv),h=0;f>h;h++){var g=d(e,i,a,s,sv[h]);l[1]=Qg(g,l[1]),u[1]=Jg(g,u[1])}l[0]=Qg(t,l[0]),u[0]=Jg(t,u[0]),l[0]=Qg(o,l[0]),u[0]=Jg(o,u[0]),l[1]=Qg(e,l[1]),u[1]=Jg(e,u[1]),l[1]=Qg(s,l[1]),u[1]=Jg(s,u[1])}function Fr(t,e,n,i,r,a,o,s){var l=Lr,u=Dr,h=Jg(Qg(l(t,n,r),1),0),c=Jg(Qg(l(e,i,a),1),0),d=u(t,n,r,h),f=u(e,i,a,c);o[0]=Qg(t,r,d),o[1]=Qg(e,a,f),s[0]=Jg(t,r,d),s[1]=Jg(e,a,f)}function Nr(t,e,n,i,r,a,o,s,l){var u=oe,h=se,c=Math.abs(r-a);if(1e-4>c%nv&&c>1e-4)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(iv[0]=ev(r)*n+t,iv[1]=tv(r)*i+e,rv[0]=ev(a)*n+t,rv[1]=tv(a)*i+e,u(s,iv,rv),h(l,iv,rv),r%=nv,0>r&&(r+=nv),a%=nv,0>a&&(a+=nv),r>a&&!o?a+=nv:a>r&&o&&(r+=nv),o){var d=a;a=r,r=d}for(var f=0;a>f;f+=Math.PI/2)f>r&&(av[0]=ev(f)*n+t,av[1]=tv(f)*i+e,u(s,av,s),h(l,av,l))}function Hr(t,e,n,i,r,a,o){if(0===r)return!1;var s=r,l=0,u=t;if(o>e+s&&o>i+s||e-s>o&&i-s>o||a>t+s&&a>n+s||t-s>a&&n-s>a)return!1;if(t===n)return Math.abs(a-t)<=s/2;l=(e-i)/(t-n),u=(t*i-n*e)/(t-n);var h=l*a-o+u,c=h*h/(l*l+1);return s/2*s/2>=c}function Wr(t,e,n,i,r,a,o,s,l,u,h){if(0===l)return!1;var c=l;if(h>e+c&&h>i+c&&h>a+c&&h>s+c||e-c>h&&i-c>h&&a-c>h&&s-c>h||u>t+c&&u>n+c&&u>r+c&&u>o+c||t-c>u&&n-c>u&&r-c>u&&o-c>u)return!1;var d=kr(t,e,n,i,r,a,o,s,u,h,null);return c/2>=d}function Vr(t,e,n,i,r,a,o,s,l){if(0===o)return!1;var u=o;if(l>e+u&&l>i+u&&l>a+u||e-u>l&&i-u>l&&a-u>l||s>t+u&&s>n+u&&s>r+u||t-u>s&&n-u>s&&r-u>s)return!1;var h=Br(t,e,n,i,r,a,s,l,null);return u/2>=h}function Gr(t){return t%=wv,0>t&&(t+=wv),t}function Xr(t,e,n,i,r,a,o,s,l){if(0===o)return!1;var u=o;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(h-u>n||n>h+u)return!1;if(Math.abs(i-r)%bv<1e-4)return!0;if(a){var c=i;i=Gr(r),r=Gr(c)}else i=Gr(i),r=Gr(r);i>r&&(r+=bv);var d=Math.atan2(l,s);return 0>d&&(d+=bv),d>=i&&r>=d||d+bv>=i&&r>=d+bv}function Yr(t,e,n,i,r,a){if(a>e&&a>i||e>a&&i>a)return 0;if(i===e)return 0;var o=e>i?1:-1,s=(a-e)/(i-e);(1===s||0===s)&&(o=e>i?.5:-.5);var l=s*(n-t)+t;return l===r?1/0:l>r?o:0}function Ur(t,e){return Math.abs(t-e)<Cv}function jr(){var t=Iv[0];Iv[0]=Iv[1],Iv[1]=t}function qr(t,e,n,i,r,a,o,s,l,u){if(u>e&&u>i&&u>a&&u>s||e>u&&i>u&&a>u&&s>u)return 0;var h=Cr(e,i,a,s,u,Tv);if(0===h)return 0;for(var c,d,f=0,p=-1,g=0;h>g;g++){var v=Tv[g],m=0===v||1===v?.5:1,y=Sr(t,n,r,o,v);l>y||(0>p&&(p=Tr(e,i,a,s,Iv),Iv[1]<Iv[0]&&p>1&&jr(),c=Sr(e,i,a,s,Iv[0]),p>1&&(d=Sr(e,i,a,s,Iv[1]))),f+=2===p?v<Iv[0]?e>c?m:-m:v<Iv[1]?c>d?m:-m:d>s?m:-m:v<Iv[0]?e>c?m:-m:c>s?m:-m)}return f}function Zr(t,e,n,i,r,a,o,s){if(s>e&&s>i&&s>a||e>s&&i>s&&a>s)return 0;var l=Pr(e,i,a,s,Tv);if(0===l)return 0;var u=Lr(e,i,a);if(u>=0&&1>=u){for(var h=0,c=Dr(e,i,a,u),d=0;l>d;d++){var f=0===Tv[d]||1===Tv[d]?.5:1,p=Dr(t,n,r,Tv[d]);o>p||(h+=Tv[d]<u?e>c?f:-f:c>a?f:-f)}return h}var f=0===Tv[0]||1===Tv[0]?.5:1,p=Dr(t,n,r,Tv[0]);return o>p?0:e>a?f:-f}function $r(t,e,n,i,r,a,o,s){if(s-=e,s>n||-n>s)return 0;var l=Math.sqrt(n*n-s*s);Tv[0]=-l,Tv[1]=l;var u=Math.abs(i-r);if(1e-4>u)return 0;if(1e-4>u%Mv){i=0,r=Mv;var h=a?1:-1;return o>=Tv[0]+t&&o<=Tv[1]+t?h:0}if(a){var l=i;i=Gr(r),r=Gr(l)}else i=Gr(i),r=Gr(r);i>r&&(r+=Mv);for(var c=0,d=0;2>d;d++){var f=Tv[d];if(f+t>o){var p=Math.atan2(s,f),h=a?1:-1;0>p&&(p=Mv+p),(p>=i&&r>=p||p+Mv>=i&&r>=p+Mv)&&(p>Math.PI/2&&p<1.5*Math.PI&&(h=-h),c+=h)}}return c}function Kr(t,e,n,i,r){for(var a=0,o=0,s=0,l=0,u=0,h=0;h<t.length;){var c=t[h++];switch(c===Sv.M&&h>1&&(n||(a+=Yr(o,s,l,u,i,r))),1===h&&(o=t[h],s=t[h+1],l=o,u=s),c){case Sv.M:l=t[h++],u=t[h++],o=l,s=u;break;case Sv.L:if(n){if(Hr(o,s,t[h],t[h+1],e,i,r))return!0}else a+=Yr(o,s,t[h],t[h+1],i,r)||0;o=t[h++],s=t[h++];break;case Sv.C:if(n){if(Wr(o,s,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],e,i,r))return!0}else a+=qr(o,s,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],i,r)||0;o=t[h++],s=t[h++];break;case Sv.Q:if(n){if(Vr(o,s,t[h++],t[h++],t[h],t[h+1],e,i,r))return!0}else a+=Zr(o,s,t[h++],t[h++],t[h],t[h+1],i,r)||0;o=t[h++],s=t[h++];break;case Sv.A:var d=t[h++],f=t[h++],p=t[h++],g=t[h++],v=t[h++],m=t[h++];h+=1;var y=1-t[h++],_=Math.cos(v)*p+d,x=Math.sin(v)*g+f;h>1?a+=Yr(o,s,_,x,i,r):(l=_,u=x);var w=(i-d)*g/p+d;if(n){if(Xr(d,f,g,v,v+m,y,e,w,r))return!0}else a+=$r(d,f,g,v,v+m,y,w,r);o=Math.cos(v+m)*p+d,s=Math.sin(v+m)*g+f;break;case Sv.R:l=o=t[h++],u=s=t[h++];var b=t[h++],S=t[h++],_=l+b,x=u+S;if(n){if(Hr(l,u,_,u,e,i,r)||Hr(_,u,_,x,e,i,r)||Hr(_,x,l,x,e,i,r)||Hr(l,x,l,u,e,i,r))return!0}else a+=Yr(_,u,_,x,i,r),a+=Yr(l,x,l,u,i,r);break;case Sv.Z:if(n){if(Hr(o,s,l,u,e,i,r))return!0}else a+=Yr(o,s,l,u,i,r);o=l,s=u}}return n||Ur(s,u)||(a+=Yr(o,s,l,u,i,r)||0),0!==a}function Qr(t,e,n){return Kr(t,0,!1,e,n)}function Jr(t,e,n,i){return Kr(t,e,!0,n,i)}function ta(t){Ti.call(this,t),this.path=null}function ea(t,e,n,i,r,a,o,s,l,u,h){var c=l*(Nv/180),d=Fv(c)*(t-n)/2+Rv(c)*(e-i)/2,f=-1*Rv(c)*(t-n)/2+Fv(c)*(e-i)/2,p=d*d/(o*o)+f*f/(s*s);p>1&&(o*=Ev(p),s*=Ev(p));var g=(r===a?-1:1)*Ev((o*o*s*s-o*o*f*f-s*s*d*d)/(o*o*f*f+s*s*d*d))||0,v=g*o*f/s,m=g*-s*d/o,y=(t+n)/2+Fv(c)*v-Rv(c)*m,_=(e+i)/2+Rv(c)*v+Fv(c)*m,x=Vv([1,0],[(d-v)/o,(f-m)/s]),w=[(d-v)/o,(f-m)/s],b=[(-1*d-v)/o,(-1*f-m)/s],S=Vv(w,b);Wv(w,b)<=-1&&(S=Nv),Wv(w,b)>=1&&(S=0),0===a&&S>0&&(S-=2*Nv),1===a&&0>S&&(S+=2*Nv),h.addData(u,y,_,o,s,x,S,c,a)}function na(t){if(!t)return new xv;for(var e,n=0,i=0,r=n,a=i,o=new xv,s=xv.CMD,l=t.match(Gv),u=0;u<l.length;u++){for(var h,c=l[u],d=c.charAt(0),f=c.match(Xv)||[],p=f.length,g=0;p>g;g++)f[g]=parseFloat(f[g]);for(var v=0;p>v;){var m,y,_,x,w,b,S,M=n,C=i;switch(d){case"l":n+=f[v++],i+=f[v++],h=s.L,o.addData(h,n,i);break;case"L":n=f[v++],i=f[v++],h=s.L,o.addData(h,n,i);break;case"m":n+=f[v++],i+=f[v++],h=s.M,o.addData(h,n,i),r=n,a=i,d="l";break;case"M":n=f[v++],i=f[v++],h=s.M,o.addData(h,n,i),r=n,a=i,d="L";break;case"h":n+=f[v++],h=s.L,o.addData(h,n,i);break;case"H":n=f[v++],h=s.L,o.addData(h,n,i);break;case"v":i+=f[v++],h=s.L,o.addData(h,n,i);break;case"V":i=f[v++],h=s.L,o.addData(h,n,i);break;case"C":h=s.C,o.addData(h,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),n=f[v-2],i=f[v-1];break;case"c":h=s.C,o.addData(h,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i),n+=f[v-2],i+=f[v-1];break;case"S":m=n,y=i;var T=o.len(),I=o.data;e===s.C&&(m+=n-I[T-4],y+=i-I[T-3]),h=s.C,M=f[v++],C=f[v++],n=f[v++],i=f[v++],o.addData(h,m,y,M,C,n,i);break;case"s":m=n,y=i;var T=o.len(),I=o.data;e===s.C&&(m+=n-I[T-4],y+=i-I[T-3]),h=s.C,M=n+f[v++],C=i+f[v++],n+=f[v++],i+=f[v++],o.addData(h,m,y,M,C,n,i);break;case"Q":M=f[v++],C=f[v++],n=f[v++],i=f[v++],h=s.Q,o.addData(h,M,C,n,i);break;case"q":M=f[v++]+n,C=f[v++]+i,n+=f[v++],i+=f[v++],h=s.Q,o.addData(h,M,C,n,i);break;case"T":m=n,y=i;var T=o.len(),I=o.data;e===s.Q&&(m+=n-I[T-4],y+=i-I[T-3]),n=f[v++],i=f[v++],h=s.Q,o.addData(h,m,y,n,i);break;case"t":m=n,y=i;var T=o.len(),I=o.data;e===s.Q&&(m+=n-I[T-4],y+=i-I[T-3]),n+=f[v++],i+=f[v++],h=s.Q,o.addData(h,m,y,n,i);break;case"A":_=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],M=n,C=i,n=f[v++],i=f[v++],h=s.A,ea(M,C,n,i,b,S,_,x,w,h,o);break;case"a":_=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],M=n,C=i,n+=f[v++],i+=f[v++],h=s.A,ea(M,C,n,i,b,S,_,x,w,h,o)}}("z"===d||"Z"===d)&&(h=s.Z,o.addData(h),n=r,i=a),e=h}return o.toStatic(),o}function ia(t,e){var n=na(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;n.rebuildPath(e)}},e.applyTransform=function(t){zv(n,t),this.dirty(!0)},e}function ra(t,e){return new ta(ia(t,e))}function aa(t,e){return ta.extend(ia(t,e))}function oa(t,e){for(var n=[],i=t.length,r=0;i>r;r++){var a=t[r];a.path||a.createPathProxy(),a.__dirtyPath&&a.buildPath(a.path,a.shape,!0),n.push(a.path)}var o=new ta(e);return o.createPathProxy(),o.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},o}function sa(t,e,n,i,r,a,o){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*o+(-3*(e-n)-2*s-l)*a+s*r+e}function la(t,e,n){var i=e.points,r=e.smooth;if(i&&i.length>=2){if(r&&"spline"!==r){var a=Qv(i,r,n,e.smoothConstraint);t.moveTo(i[0][0],i[0][1]);for(var o=i.length,s=0;(n?o:o-1)>s;s++){var l=a[2*s],u=a[2*s+1],h=i[(s+1)%o];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{"spline"===r&&(i=Kv(i,n)),t.moveTo(i[0][0],i[0][1]);for(var s=1,c=i.length;c>s;s++)t.lineTo(i[s][0],i[s][1])}n&&t.closePath()}}function ua(t,e,n){if(e){var i=e.x1,r=e.x2,a=e.y1,o=e.y2;t.x1=i,t.x2=r,t.y1=a,t.y2=o;var s=n&&n.lineWidth;s&&(em(2*i)===em(2*r)&&(t.x1=t.x2=ca(i,s,!0)),em(2*a)===em(2*o)&&(t.y1=t.y2=ca(a,s,!0)))}}function ha(t,e,n){if(e){var i=e.x,r=e.y,a=e.width,o=e.height;t.x=i,t.y=r,t.width=a,t.height=o;var s=n&&n.lineWidth;s&&(t.x=ca(i,s,!0),t.y=ca(r,s,!0),t.width=Math.max(ca(i+a,s,!1)-t.x,0===a?0:1),t.height=Math.max(ca(r+o,s,!1)-t.y,0===o?0:1))}}function ca(t,e,n){if(!e)return t;var i=em(2*t);return(i+em(e))%2===0?i/2:(i+(n?1:-1))/2}function da(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?Mr:Sr)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?Mr:Sr)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Ar:Dr)(t.x1,t.cpx1,t.x2,e),(n?Ar:Dr)(t.y1,t.cpy1,t.y2,e)]}function fa(t){Ti.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}function pa(t){return ta.extend(t)}function ga(t,e){return aa(t,e)}function va(t,e){Sm[t]=e}function ma(t){return Sm.hasOwnProperty(t)?Sm[t]:void 0}function ya(t,e,n,i){var r=ra(t,e);return n&&("center"===i&&(n=xa(n,r.getBoundingRect())),wa(r,n)),r}function _a(t,e,n){var i=new Ii({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(xa(e,r))}}});return i}function xa(t,e){var n,i=e.width/e.height,r=t.height*i;r<=t.width?n=t.height:(r=t.width,n=r/i);var a=t.x+t.width/2,o=t.y+t.height/2;return{x:a-r/2,y:o-n/2,width:r,height:n}}function wa(t,e){if(t.applyTransform){var n=t.getBoundingRect(),i=n.calculateTransform(e);t.applyTransform(i)}}function ba(t){return ua(t.shape,t.shape,t.style),t}function Sa(t){return ha(t.shape,t.shape,t.style),t}function Ma(t){return null!=t&&"none"!==t}function Ca(t){if("string"!=typeof t)return t;var e=Tm.get(t);return e||(e=nn(t,-.1),1e4>Im&&(Tm.set(t,e),Im++)),e}function Ta(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(!e)return void(t.__cachedNormalStl=t.__cachedNormalZ2=null);var n=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var i=t.style;for(var r in e)null!=e[r]&&(n[r]=i[r]);n.fill=i.fill,n.stroke=i.stroke}}function Ia(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var n=t.__zr,i=t.useHoverLayer&&n&&"canvas"===n.painter.type;if(t.__highlighted=i?"layer":"plain",!(t.isGroup||!n&&t.useHoverLayer)){var r=t,a=t.style;i&&(r=n.addHover(t),a=r.style),$a(a),i||Ta(r),a.extendFrom(e),ka(a,e,"fill"),ka(a,e,"stroke"),Za(a),i||(t.dirty(!1),t.z2+=mm)}}}function ka(t,e,n){!Ma(e[n])&&Ma(t[n])&&(t[n]=Ca(t[n]))}function Da(t){var e=t.__highlighted;if(e&&(t.__highlighted=!1,!t.isGroup))if("layer"===e)t.__zr&&t.__zr.removeHover(t);else{var n=t.style,i=t.__cachedNormalStl;i&&($a(n),t.setStyle(i),Za(n));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===mm&&(t.z2=r)}}function Aa(t,e,n){var i,r=xm,a=xm;t.__highlighted&&(r=_m,i=!0),e(t,n),t.__highlighted&&(a=_m,i=!0),t.isGroup&&t.traverse(function(t){!t.isGroup&&e(t,n)}),i&&t.__highDownOnUpdate&&t.__highDownOnUpdate(r,a)}function Pa(t,e){e=t.__hoverStl=e!==!1&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,Da(t),Ia(t))}function La(t){!Ea(this,t)&&!this.__highByOuter&&Aa(this,Ia)}function Oa(t){!Ea(this,t)&&!this.__highByOuter&&Aa(this,Da)}function Ba(t){this.__highByOuter|=1<<(t||0),Aa(this,Ia)}function za(t){!(this.__highByOuter&=~(1<<(t||0)))&&Aa(this,Da)}function Ea(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Ra(t,e){Fa(t,!0),Aa(t,Pa,e)}function Fa(t,e){var n=e===!1;if(t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,!n||t.__highDownDispatcher){var i=n?"off":"on";t[i]("mouseover",La)[i]("mouseout",Oa),t[i]("emphasis",Ba)[i]("normal",za),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!n}}function Na(t){return!(!t||!t.__highDownDispatcher)}function Ha(t){var e=bm[t];return null==e&&32>=wm&&(e=bm[t]=wm++),e}function Wa(t,e,n,i,r,a,o){r=r||vm;var s,l=r.labelFetcher,u=r.labelDataIndex,h=r.labelDimIndex,c=r.labelProp,d=n.getShallow("show"),f=i.getShallow("show");(d||f)&&(l&&(s=l.getFormattedLabel(u,"normal",null,h,c)),null==s&&(s=w(r.defaultText)?r.defaultText(u,r):r.defaultText));var p=d?s:null,g=f?D(l?l.getFormattedLabel(u,"emphasis",null,h,c):null,s):null;(null!=p||null!=g)&&(Ga(t,n,a,r),Ga(e,i,o,r,!0)),t.text=p,e.text=g}function Va(t,e,n){var i=t.style;e&&($a(i),t.setStyle(e),Za(i)),i=t.__hoverStl,n&&i&&($a(i),o(i,n),Za(i))}function Ga(t,e,n,i,r){return Ya(t,e,i,r),n&&o(t,n),t}function Xa(t,e,n){var i,r={isRectText:!0};n===!1?i=!0:r.autoColor=n,Ya(t,e,r,i)}function Ya(t,e,n,i){if(n=n||vm,n.isRectText){var r;n.getTextPosition?r=n.getTextPosition(e,i):(r=e.getShallow("position")||(i?null:"inside"),"outside"===r&&(r="top")),t.textPosition=r,t.textOffset=e.getShallow("offset");var a=e.getShallow("rotate");null!=a&&(a*=Math.PI/180),t.textRotation=a,t.textDistance=D(e.getShallow("distance"),i?null:5)}var o,s=e.ecModel,l=s&&s.option.textStyle,u=Ua(e);if(u){o={};for(var h in u)if(u.hasOwnProperty(h)){var c=e.getModel(["rich",h]);ja(o[h]={},c,l,n,i)}}return t.rich=o,ja(t,e,l,n,i,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function Ua(t){for(var e;t&&t!==t.ecModel;){var n=(t.option||vm).rich;if(n){e=e||{};for(var i in n)n.hasOwnProperty(i)&&(e[i]=1)}t=t.parentModel}return e}function ja(t,e,n,i,r,a){n=!r&&n||vm,t.textFill=qa(e.getShallow("color"),i)||n.color,t.textStroke=qa(e.getShallow("textBorderColor"),i)||n.textBorderColor,t.textStrokeWidth=D(e.getShallow("textBorderWidth"),n.textBorderWidth),r||(a&&(t.insideRollbackOpt=i,Za(t)),null==t.textFill&&(t.textFill=i.autoColor)),t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),a&&i.disableBox||(t.textBackgroundColor=qa(e.getShallow("backgroundColor"),i),t.textPadding=e.getShallow("padding"),t.textBorderColor=qa(e.getShallow("borderColor"),i),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function qa(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Za(t){var e,n=t.textPosition,i=t.insideRollbackOpt;if(i&&null==t.textFill){var r=i.autoColor,a=i.isRectText,o=i.useInsideStyle,s=o!==!1&&(o===!0||a&&n&&"string"==typeof n&&n.indexOf("inside")>=0),l=!s&&null!=r;(s||l)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),s&&(t.textFill="#fff",null==t.textStroke&&(t.textStroke=r,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),l&&(t.textFill=r)}t.insideRollback=e}function $a(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function Ka(t,e){var n=e&&e.getModel("textStyle");return B([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}function Qa(t,e,n,i,r,a){"function"==typeof r&&(a=r,r=null);var o=i&&i.isAnimationEnabled();if(o){var s=t?"Update":"",l=i.getShallow("animationDuration"+s),u=i.getShallow("animationEasing"+s),h=i.getShallow("animationDelay"+s);"function"==typeof h&&(h=h(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(n,l,h||0,u,a,!!a):(e.stopAnimation(),e.attr(n),a&&a())}else e.stopAnimation(),e.attr(n),a&&a()}function Ja(t,e,n,i,r){Qa(!0,t,e,n,i,r)}function to(t,e,n,i,r){Qa(!1,t,e,n,i,r)}function eo(t,e){for(var n=Oe([]);t&&t!==e;)ze(n,t.getLocalTransform(),n),t=t.parent;return n}function no(t,e,n){return e&&!d(e)&&(e=ep.getLocalTransform(e)),n&&(e=Ne([],e)),ae([],t,e)}function io(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return a=no(a,e,n),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function ro(t,e,n){function i(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:G(t.position),rotation:t.rotation};return t.shape&&(e.shape=o({},t.shape)),e}if(t&&e){var a=i(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var i=r(t);t.attr(r(e)),Ja(t,i,n,t.dataIndex)}}})}}function ao(t,e){return p(t,function(t){var n=t[0];n=pm(n,e.x),n=gm(n,e.x+e.width);var i=t[1];return i=pm(i,e.y),i=gm(i,e.y+e.height),[n,i]})}function oo(t,e){var n=pm(t.x,e.x),i=gm(t.x+t.width,e.x+e.width),r=pm(t.y,e.y),a=gm(t.y+t.height,e.y+e.height);return i>=n&&a>=r?{x:n,y:r,width:i-n,height:a-r}:void 0}function so(t,e,n){e=o({rectHover:!0},e);var i=e.style={strokeNoScale:!0};return n=n||{x:-1,y:-1,width:2,height:2},t?0===t.indexOf("image://")?(i.image=t.slice(8),s(i,n),new Ii(e)):ya(t.replace("path://",""),e,n,"center"):void 0}function lo(t,e,n,i,r){for(var a=0,o=r[r.length-1];a<r.length;a++){var s=r[a];if(uo(t,e,n,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function uo(t,e,n,i,r,a,o,s){var l=n-t,u=i-e,h=o-r,c=s-a,d=ho(h,c,l,u);if(co(d))return!1;var f=t-r,p=e-a,g=ho(f,p,l,u)/d;if(0>g||g>1)return!1;var v=ho(f,p,h,c)/d;return 0>v||v>1?!1:!0}function ho(t,e,n,i){return t*i-n*e}function co(t){return 1e-6>=t&&t>=-1e-6}function fo(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function po(t,e,n){for(var i=0;i<e.length&&(!e[i]||(t=t&&"object"==typeof t?t[e[i]]:null,null!=t));i++);return null==t&&n&&(t=n.get(e)),t}function go(t,e){var n=Bm(t).getParent;return n?n.call(t,e):t.parentModel}function vo(t){return[t||"",zm++,Math.random().toFixed(5)].join("_")}function mo(t){var e={};return t.registerSubTypeDefaulter=function(t,n){t=pr(t),e[t.main]=n
},t.determineSubType=function(n,i){var r=i.type;if(!r){var a=pr(n).main;t.hasSubTypes(n)&&e[a]&&(r=e[a](i))}return r},t}function yo(t,e){function n(t){var n={},a=[];return f(t,function(o){var s=i(n,o),l=s.originalDeps=e(o),h=r(l,t);s.entryCount=h.length,0===s.entryCount&&a.push(o),f(h,function(t){u(s.predecessor,t)<0&&s.predecessor.push(t);var e=i(n,t);u(e.successor,t)<0&&e.successor.push(o)})}),{graph:n,noEntryList:a}}function i(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function r(t,e){var n=[];return f(t,function(t){u(e,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,e,i,r){function a(t){l[t].entryCount--,0===l[t].entryCount&&u.push(t)}function o(t){h[t]=!0,a(t)}if(t.length){var s=n(e),l=s.graph,u=s.noEntryList,h={};for(f(t,function(t){h[t]=!0});u.length;){var c=u.pop(),d=l[c],p=!!h[c];p&&(i.call(r,c,d.originalDeps.slice()),delete h[c]),f(d.successor,p?o:a)}f(h,function(){throw new Error("Circle dependency may exists")})}}}function _o(t){return t.replace(/^\s+|\s+$/g,"")}function xo(t,e,n,i){var r=e[1]-e[0],a=n[1]-n[0];if(0===r)return 0===a?n[0]:(n[0]+n[1])/2;if(i)if(r>0){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*a+n[0]}function wo(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?_o(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?0/0:+t}function bo(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function So(t){return t.sort(function(t,e){return t-e}),t}function Mo(t){if(t=+t,isNaN(t))return 0;for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n}function Co(t){var e=t.toString(),n=e.indexOf("e");if(n>0){var i=+e.slice(n+1);return 0>i?-i:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}function To(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),a=Math.round(n(Math.abs(e[1]-e[0]))/i),o=Math.min(Math.max(-r+a,0),20);return isFinite(o)?o:20}function Io(t,e,n){if(!t[e])return 0;var i=g(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var r=Math.pow(10,n),a=p(t,function(t){return(isNaN(t)?0:t)/i*r*100}),o=100*r,s=p(a,function(t){return Math.floor(t)}),l=g(s,function(t,e){return t+e},0),u=p(a,function(t,e){return t-s[e]});o>l;){for(var h=Number.NEGATIVE_INFINITY,c=null,d=0,f=u.length;f>d;++d)u[d]>h&&(h=u[d],c=d);++s[c],u[c]=0,++l}return s[e]/r}function ko(t){var e=2*Math.PI;return(t%e+e)%e}function Do(t){return t>-Em&&Em>t}function Ao(t){if(t instanceof Date)return t;if("string"==typeof t){var e=Fm.exec(t);if(!e)return new Date(0/0);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return new Date(null==t?0/0:Math.round(t))}function Po(t){return Math.pow(10,Lo(t))}function Lo(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function Oo(t,e){var n,i=Lo(t),r=Math.pow(10,i),a=t/r;return n=e?1.5>a?1:2.5>a?2:4>a?3:7>a?5:10:1>a?1:2>a?2:3>a?3:5>a?5:10,t=n*r,i>=-20?+t.toFixed(0>i?-i:0):t}function Bo(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],a=n-i;return a?r+a*(t[i]-r):r}function zo(t){function e(t,n,i){return t.interval[i]<n.interval[i]||t.interval[i]===n.interval[i]&&(t.close[i]-n.close[i]===(i?-1:1)||!i&&e(t,n,1))}t.sort(function(t,n){return e(t,n,0)?-1:1});for(var n=-1/0,i=1,r=0;r<t.length;){for(var a=t[r].interval,o=t[r].close,s=0;2>s;s++)a[s]<=n&&(a[s]=n,o[s]=s?1:1-i),n=a[s],i=o[s];a[0]===a[1]&&o[0]*o[1]!==1?t.splice(r,1):r++}return t}function Eo(t){return t-parseFloat(t)>=0}function Ro(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function Fo(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}function No(t){return null==t?"":(t+"").replace(Wm,function(t,e){return Vm[e]})}function Ho(t,e,n){x(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],a=0;a<r.length;a++){var o=Gm[a];t=t.replace(Xm(o),Xm(o,0))}for(var s=0;i>s;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Xm(Gm[l],s),n?No(u):u)}return t}function Wo(t,e,n){return f(e,function(e,i){t=t.replace("{"+i+"}",n?No(e):e)}),t}function Vo(t,e){t=b(t)?{color:t,extraCssText:e}:t||{};var n=t.color,i=t.type,e=t.extraCssText,r=t.renderMode||"html",a=t.markerId||"X";return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+No(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+No(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+a+"|}  ",style:{color:n}}:""}function Go(t,e){return t+="","0000".substr(0,e-t.length)+t}function Xo(t,e,n){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var i=Ao(e),r=n?"UTC":"",a=i["get"+r+"FullYear"](),o=i["get"+r+"Month"]()+1,s=i["get"+r+"Date"](),l=i["get"+r+"Hours"](),u=i["get"+r+"Minutes"](),h=i["get"+r+"Seconds"](),c=i["get"+r+"Milliseconds"]();return t=t.replace("MM",Go(o,2)).replace("M",o).replace("yyyy",a).replace("yy",a%100).replace("dd",Go(s,2)).replace("d",s).replace("hh",Go(l,2)).replace("h",l).replace("mm",Go(u,2)).replace("m",u).replace("ss",Go(h,2)).replace("s",h).replace("SSS",Go(c,3))}function Yo(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}function Uo(t){return Yn(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)}function jo(t,e,n,i,r,a,o,s){return Yn(t,e,n,i,r,s,a,o)}function qo(t,e){if("_blank"===e||"blank"===e){var n=window.open();n.opener=null,n.location=t}else window.open(t,e)}function Zo(t,e,n,i,r){var a=0,o=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,u){var h,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(u+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(g?-g.x+f.x:0);h=a+v,h>i||l.newline?(a=0,h=v,o+=s+n,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(g?-g.y+f.y:0);c=o+m,c>r||l.newline?(a+=s+n,o=0,c=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=a,d[1]=o,"horizontal"===t?a=h+n:o=c+n)})}function $o(t,e,n){n=Hm(n||0);var i=e.width,r=e.height,a=wo(t.left,i),o=wo(t.top,r),s=wo(t.right,i),l=wo(t.bottom,r),u=wo(t.width,i),h=wo(t.height,r),c=n[2]+n[0],d=n[1]+n[3],f=t.aspect;switch(isNaN(u)&&(u=i-s-d-a),isNaN(h)&&(h=r-l-c-o),null!=f&&(isNaN(u)&&isNaN(h)&&(f>i/r?u=.8*i:h=.8*r),isNaN(u)&&(u=f*h),isNaN(h)&&(h=u/f)),isNaN(a)&&(a=i-s-u-d),isNaN(o)&&(o=r-l-h-c),t.left||t.right){case"center":a=i/2-u/2-n[3];break;case"right":a=i-u-d}switch(t.top||t.bottom){case"middle":case"center":o=r/2-h/2-n[0];break;case"bottom":o=r-h-c}a=a||0,o=o||0,isNaN(u)&&(u=i-d-a-(s||0)),isNaN(h)&&(h=r-c-o-(l||0));var p=new Tn(a+n[3],o+n[0],u,h);return p.margin=n,p}function Ko(t,e,n){function i(n,i){var o={},l=0,u={},h=0,c=2;if(jm(n,function(e){u[e]=t[e]}),jm(n,function(t){r(e,t)&&(o[t]=u[t]=e[t]),a(o,t)&&l++,a(u,t)&&h++}),s[i])return a(e,n[1])?u[n[2]]=null:a(e,n[2])&&(u[n[1]]=null),u;if(h!==c&&l){if(l>=c)return o;for(var d=0;d<n.length;d++){var f=n[d];if(!r(o,f)&&r(t,f)){o[f]=t[f];break}}return o}return u}function r(t,e){return t.hasOwnProperty(e)}function a(t,e){return null!=t[e]&&"auto"!==t[e]}function o(t,e,n){jm(t,function(t){e[t]=n[t]})}!S(n)&&(n={});var s=n.ignoreSize;!x(s)&&(s=[s,s]);var l=i(Zm[0],0),u=i(Zm[1],1);o(Zm[0],t,l),o(Zm[1],t,u)}function Qo(t){return Jo({},t)}function Jo(t,e){return e&&t&&jm(qm,function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t}function ts(t){var e=[];return f(Jm.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=p(e,function(t){return pr(t).main}),"dataset"!==t&&u(e,"dataset")<=0&&e.unshift("dataset"),e}function es(t,e){for(var n=t.length,i=0;n>i;i++)if(t[i].length>e)return t[i];return t[n-1]}function ns(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===sy?{}:[]),this.sourceFormat=t.sourceFormat||ly,this.seriesLayoutBy=t.seriesLayoutBy||hy,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&F(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}function is(t){var e=t.option.source,n=ly;if(C(e))n=uy;else if(x(e)){0===e.length&&(n=ay);for(var i=0,r=e.length;r>i;i++){var a=e[i];if(null!=a){if(x(a)){n=ay;break}if(S(a)){n=oy;break}}}}else if(S(e)){for(var o in e)if(e.hasOwnProperty(o)&&d(e[o])){n=sy;break}}else if(null!=e)throw new Error("Invalid data");fy(t).sourceFormat=n}function rs(t){return fy(t).source}function as(t){fy(t).datasetMap=F()}function os(t){var e=t.option,n=e.data,i=C(n)?uy:ry,r=!1,a=e.seriesLayoutBy,o=e.sourceHeader,s=e.dimensions,l=ds(t);if(l){var u=l.option;n=u.source,i=fy(l).sourceFormat,r=!0,a=a||u.seriesLayoutBy,null==o&&(o=u.sourceHeader),s=s||u.dimensions}var h=ss(n,i,a,o,s);fy(t).source=new ns({data:n,fromDataset:r,seriesLayoutBy:a,sourceFormat:i,dimensionsDefine:h.dimensionsDefine,startIndex:h.startIndex,dimensionsDetectCount:h.dimensionsDetectCount,encodeDefine:e.encode})}function ss(t,e,n,i,r){if(!t)return{dimensionsDefine:ls(r)};var a,o;if(e===ay)"auto"===i||null==i?us(function(t){null!=t&&"-"!==t&&(b(t)?null==o&&(o=1):o=0)},n,t,10):o=i?1:0,r||1!==o||(r=[],us(function(t,e){r[e]=null!=t?t:""},n,t)),a=r?r.length:n===cy?t.length:t[0]?t[0].length:null;else if(e===oy)r||(r=hs(t));else if(e===sy)r||(r=[],f(t,function(t,e){r.push(e)}));else if(e===ry){var s=er(t[0]);a=x(s)&&s.length||1}return{startIndex:o,dimensionsDefine:ls(r),dimensionsDetectCount:a}}function ls(t){if(t){var e=F();return p(t,function(t){if(t=o({},S(t)?t:{name:t}),null==t.name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var n=e.get(t.name);return n?t.name+="-"+n.count++:e.set(t.name,{count:1}),t})}}function us(t,e,n,i){if(null==i&&(i=1/0),e===cy)for(var r=0;r<n.length&&i>r;r++)t(n[r]?n[r][0]:null,r);else for(var a=n[0]||[],r=0;r<a.length&&i>r;r++)t(a[r],r)}function hs(t){for(var e,n=0;n<t.length&&!(e=t[n++]););if(e){var i=[];return f(e,function(t,e){i.push(e)}),i}}function cs(t,e,n){function i(t,e,n){for(var i=0;n>i;i++)t.push(e+i)}function r(t){var e=t.dimsDef;return e?e.length:1}var a={},o=ds(e);if(!o||!t)return a;var s,l,u=[],h=[],c=e.ecModel,d=fy(c).datasetMap,p=o.uid+"_"+n.seriesLayoutBy;t=t.slice(),f(t,function(e,n){!S(e)&&(t[n]={name:e}),"ordinal"===e.type&&null==s&&(s=n,l=r(t[n])),a[e.name]=[]});var g=d.get(p)||d.set(p,{categoryWayDim:l,valueWayDim:0});return f(t,function(t,e){var n=t.name,o=r(t);if(null==s){var l=g.valueWayDim;i(a[n],l,o),i(h,l,o),g.valueWayDim+=o}else if(s===e)i(a[n],0,o),i(u,0,o);else{var l=g.categoryWayDim;i(a[n],l,o),i(h,l,o),g.categoryWayDim+=o}}),u.length&&(a.itemName=u),h.length&&(a.seriesName=h),a}function ds(t){var e=t.option,n=e.data;return n?void 0:t.ecModel.getComponent("dataset",e.datasetIndex||0)}function fs(t,e){return ps(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function ps(t,e,n,i,r,a){function o(t){var e=b(t);return null!=t&&isFinite(t)&&""!==t?e?dy.Might:dy.Not:e&&"-"!==t?dy.Must:void 0}var s,l=5;if(C(t))return dy.Not;var u,h;if(i){var c=i[a];S(c)?(u=c.name,h=c.type):b(c)&&(u=c)}if(null!=h)return"ordinal"===h?dy.Must:dy.Not;if(e===ay)if(n===cy){for(var d=t[a],f=0;f<(d||[]).length&&l>f;f++)if(null!=(s=o(d[r+f])))return s}else for(var f=0;f<t.length&&l>f;f++){var p=t[r+f];if(p&&null!=(s=o(p[a])))return s}else if(e===oy){if(!u)return dy.Not;for(var f=0;f<t.length&&l>f;f++){var g=t[f];if(g&&null!=(s=o(g[u])))return s}}else if(e===sy){if(!u)return dy.Not;var d=t[u];if(!d||C(d))return dy.Not;for(var f=0;f<d.length&&l>f;f++)if(null!=(s=o(d[f])))return s}else if(e===ry)for(var f=0;f<t.length&&l>f;f++){var g=t[f],v=er(g);if(!x(v))return dy.Not;if(null!=(s=o(v[a])))return s}return dy.Not}function gs(t,e){if(e){var n=e.seiresIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}function vs(t,e){var n=t.color&&!t.colorLayer;f(e,function(e,a){"colorLayer"===a&&n||Jm.hasClass(a)||("object"==typeof e?t[a]=t[a]?r(t[a],e,!1):i(e):null==t[a]&&(t[a]=e))})}function ms(t){t=t,this.option={},this.option[py]=1,this._componentsMap=F({series:[]}),this._seriesIndices,this._seriesIndicesMap,vs(t,this._theme.option),r(t,ey,!1),this.mergeOption(t)}function ys(t,e){x(e)||(e=e?[e]:[]);var n={};return f(e,function(e){n[e]=(t.get(e)||[]).slice()}),n}function _s(t,e,n){var i=e.type?e.type:n?n.subType:Jm.determineSubType(t,e);return i}function xs(t,e){t._seriesIndicesMap=F(t._seriesIndices=p(e,function(t){return t.componentIndex})||[])}function ws(t,e){return e.hasOwnProperty("subType")?v(t,function(t){return t.subType===e.subType}):t}function bs(t){f(vy,function(e){this[e]=y(t[e],t)},this)}function Ss(){this._coordinateSystems=[]}function Ms(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function Cs(t,e,n){var i,r,a=[],o=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;yy(l,function(t){t&&t.option&&(t.query?o.push(t):i||(i=t))})}return r||(r=t),r.timeline||(r.timeline=s),yy([r].concat(a).concat(p(o,function(t){return t.option})),function(t){yy(e,function(e){e(t,n)})}),{baseOption:r,timelineOptions:a,mediaDefault:i,mediaList:o}}function Ts(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return f(t,function(t,e){var n=e.match(by);if(n&&n[1]&&n[2]){var a=n[1],o=n[2].toLowerCase();Is(i[o],t,a)||(r=!1)}}),r}function Is(t,e,n){return"min"===n?t>=e:"max"===n?e>=t:t===e}function ks(t,e){return t.join(",")===e.join(",")}function Ds(t,e){e=e||{},yy(e,function(e,n){if(null!=e){var i=t[n];if(Jm.hasClass(n)){e=Ji(e),i=Ji(i);var r=ir(i,e);t[n]=xy(r,function(t){return t.option&&t.exist?wy(t.exist,t.option,!0):t.exist||t.option})}else t[n]=wy(i,e,!0)}})}function As(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=Cy.length;i>n;n++){var a=Cy[n],o=e.normal,s=e.emphasis;o&&o[a]&&(t[a]=t[a]||{},t[a].normal?r(t[a].normal,o[a]):t[a].normal=o[a],o[a]=null),s&&s[a]&&(t[a]=t[a]||{},t[a].emphasis?r(t[a].emphasis,s[a]):t[a].emphasis=s[a],s[a]=null)}}function Ps(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,s(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function Ls(t){Ps(t,"itemStyle"),Ps(t,"lineStyle"),Ps(t,"areaStyle"),Ps(t,"label"),Ps(t,"labelLine"),Ps(t,"upperLabel"),Ps(t,"edgeLabel")}function Os(t,e){var n=My(t)&&t[e],i=My(n)&&n.textStyle;if(i)for(var r=0,a=Og.length;a>r;r++){var e=Og[r];i.hasOwnProperty(e)&&(n[e]=i[e])}}function Bs(t){t&&(Ls(t),Os(t,"label"),t.emphasis&&Os(t.emphasis,"label"))}function zs(t){if(My(t)){As(t),Ls(t),Os(t,"label"),Os(t,"upperLabel"),Os(t,"edgeLabel"),t.emphasis&&(Os(t.emphasis,"label"),Os(t.emphasis,"upperLabel"),Os(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(As(e),Bs(e));var n=t.markLine;n&&(As(n),Bs(n));var i=t.markArea;i&&Bs(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var a=t.links||t.edges;if(a&&!C(a))for(var o=0;o<a.length;o++)Bs(a[o]);f(t.categories,function(t){Ls(t)})}if(r&&!C(r))for(var o=0;o<r.length;o++)Bs(r[o]);var e=t.markPoint;if(e&&e.data)for(var s=e.data,o=0;o<s.length;o++)Bs(s[o]);var n=t.markLine;if(n&&n.data)for(var l=n.data,o=0;o<l.length;o++)x(l[o])?(Bs(l[o][0]),Bs(l[o][1])):Bs(l[o]);"gauge"===t.type?(Os(t,"axisLabel"),Os(t,"title"),Os(t,"detail")):"treemap"===t.type?(Ps(t.breadcrumb,"itemStyle"),f(t.levels,function(t){Ls(t)})):"tree"===t.type&&Ls(t.leaves)}}function Es(t){return x(t)?t:t?[t]:[]}function Rs(t){return(x(t)?t[0]:t)||{}}function Fs(t,e){e=e.split(",");for(var n=t,i=0;i<e.length&&(n=n&&n[e[i]],null!=n);i++);return n}function Ns(t,e,n,i){e=e.split(",");for(var r,a=t,o=0;o<e.length-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(i||null==a[e[o]])&&(a[e[o]]=n)}function Hs(t){f(Iy,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}function Ws(t){f(t,function(e,n){var i=[],r=[0/0,0/0],a=[e.stackResultDimension,e.stackedOverDimension],o=e.data,s=e.isStackedByIndex,l=o.map(a,function(a,l,u){var h=o.get(e.stackedDimension,u);if(isNaN(h))return r;var c,d;s?d=o.getRawIndex(u):c=o.get(e.stackedByDimension,u);for(var f=0/0,p=n-1;p>=0;p--){var g=t[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,c)),d>=0){var v=g.data.getByRawIndex(g.stackResultDimension,d);if(h>=0&&v>0||0>=h&&0>v){h+=v,f=v;break}}}return i[0]=h,i[1]=f,i});o.hostModel.setData(l),e.data=l})}function Vs(t,e){ns.isInstance(t)||(t=ns.seriesDataToSource(t)),this._source=t;var n=this._data=t.data,i=t.sourceFormat;i===uy&&(this._offset=0,this._dimSize=e,this._data=n);var r=Ly[i===ay?i+"_"+t.seriesLayoutBy:i];o(this,r)}function Gs(){return this._data.length}function Xs(t){return this._data[t]}function Ys(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Us(t,e,n){return null!=n?t[n]:t}function js(t,e,n,i){return qs(t[i],this._dimensionInfos[e])}function qs(t,e){var n=e&&e.type;if("ordinal"===n){var i=e&&e.ordinalMeta;return i?i.parseAndCollect(t):t}return"time"===n&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+Ao(t)),null==t||""===t?0/0:+t}function Zs(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r,a,o=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(n);return s&&(r=s.name,a=s.index),Oy[o](i,e,a,r)}}}function $s(t){return new Ks(t)}function Ks(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}function Qs(t,e,n,i,r,a){Fy.reset(n,i,r,a),t._callingProgress=e,t._callingProgress({start:n,end:i,count:i-n,next:Fy.next},t.context)}function Js(t,e){t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null;var n,i;!e&&t._reset&&(n=t._reset(t.context),n&&n.progress&&(i=n.forceFirstProgress,n=n.progress),x(n)&&!n.length&&(n=null)),t._progress=n,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),i}function tl(t){var e=t.name;ar(t)||(t.name=el(t)||e)}function el(t){var e=t.getRawData(),n=e.mapDimension("seriesName",!0),i=[];return f(n,function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)}),i.join(" ")}function nl(t){return t.model.getRawData().count()}function il(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),rl}function rl(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function al(t,e){f(t.CHANGABLE_METHODS,function(n){t.wrapMethod(n,_(ol,e))})}function ol(t){var e=sl(t);e&&e.setOutputEnd(this.count())}function sl(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}function ll(){this.group=new Ap,this.uid=vo("viewChart"),this.renderTask=$s({plan:cl,reset:dl}),this.renderTask.context={view:this}}function ul(t,e,n){if(t&&(t.trigger(e,n),t.isGroup&&!Na(t)))for(var i=0,r=t.childCount();r>i;i++)ul(t.childAt(i),e,n)}function hl(t,e,n){var i=sr(t,e),r=e&&null!=e.highlightKey?Ha(e.highlightKey):null;null!=i?f(Ji(i),function(e){ul(t.getItemGraphicEl(e),n,r)}):t.eachItemGraphicEl(function(t){ul(t,n,r)})}function cl(t){return Yy(t.model)}function dl(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,a=e.pipelineContext.progressiveRender,o=t.view,s=r&&Xy(r).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return"render"!==l&&o[l](e,n,i,r),jy[l]}function fl(t,e,n){function i(){h=(new Date).getTime(),c=null,t.apply(o,s||[])}var r,a,o,s,l,u=0,h=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),o=this,s=arguments;var t=l||e,d=l||n;l=null,a=r-(d?u:h)-t,clearTimeout(c),d?c=setTimeout(i,t):a>=0?i():c=setTimeout(i,-a),u=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d}function pl(t,e,n,i){var r=t[e];if(r){var a=r[qy]||r,o=r[$y],s=r[Zy];if(s!==n||o!==i){if(null==n||!i)return t[e]=a;r=t[e]=fl(a,n,"debounce"===i),r[qy]=a,r[$y]=i,r[Zy]=n}return r}}function gl(t,e,n,i){this.ecInstance=t,this.api=e,this.unfinished;var n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice();this._allHandlers=n.concat(i),this._stageTaskMap=F()}function vl(t,e,n,i,r){function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}r=r||{};var o;f(e,function(e){if(!r.visualType||r.visualType===e.visualType){var s=t._stageTaskMap.get(e.uid),l=s.seriesTaskMap,u=s.overallTask;if(u){var h,c=u.agentStubMap;c.each(function(t){a(r,t)&&(t.dirty(),h=!0)}),h&&u.dirty(),i_(u,i);var d=t.getPerformArgs(u,r.block);c.each(function(t){t.perform(d)}),o|=u.perform(d)}else l&&l.each(function(s){a(r,s)&&s.dirty();var l=t.getPerformArgs(s,r.block);l.skip=!e.performRawSeries&&n.isSeriesFiltered(s.context.model),i_(s,i),o|=s.perform(l)})}}),t.unfinished|=o}function ml(t,e,n,i,r){function a(n){var a=n.uid,s=o.get(a)||o.set(a,$s({plan:Sl,reset:Ml,count:Tl}));s.context={model:n,ecModel:i,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},Il(t,n,s)}var o=n.seriesTaskMap||(n.seriesTaskMap=F()),s=e.seriesType,l=e.getTargetSeries;e.createOnAllSeries?i.eachRawSeries(a):s?i.eachRawSeriesByType(s,a):l&&l(i,r).each(a);var u=t._pipelineMap;o.each(function(t,e){u.get(e)||(t.dispose(),o.removeKey(e))})}function yl(t,e,n,i,r){function a(e){var n=e.uid,i=s.get(n);i||(i=s.set(n,$s({reset:xl,onDirty:bl})),o.dirty()),i.context={model:e,overallProgress:h,modifyOutputEnd:c},i.agent=o,i.__block=h,Il(t,e,i)}var o=n.overallTask=n.overallTask||$s({reset:_l});o.context={ecModel:i,api:r,overallReset:e.overallReset,scheduler:t};var s=o.agentStubMap=o.agentStubMap||F(),l=e.seriesType,u=e.getTargetSeries,h=!0,c=e.modifyOutputEnd;l?i.eachRawSeriesByType(l,a):u?u(i,r).each(a):(h=!1,f(i.getSeries(),a));var d=t._pipelineMap;s.each(function(t,e){d.get(e)||(t.dispose(),o.dirty(),s.removeKey(e))})}function _l(t){t.overallReset(t.ecModel,t.api,t.payload)}function xl(t){return t.overallProgress&&wl}function wl(){this.agent.dirty(),this.getDownstream().dirty()}function bl(){this.agent&&this.agent.dirty()}function Sl(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function Ml(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=Ji(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?p(e,function(t,e){return Cl(e)}):r_}function Cl(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var a=e.start;a<e.end;a++)r.dataEach(i,a);else r&&r.progress&&r.progress(e,i)}}function Tl(t){return t.data.count()}function Il(t,e,n){var i=e.uid,r=t._pipelineMap.get(i);!r.head&&(r.head=n),r.tail&&r.tail.pipe(n),r.tail=n,n.__idxInPipeline=r.count++,n.__pipeline=r}function kl(t){a_=null;try{t(o_,s_)}catch(e){}return a_}function Dl(t,e){for(var n in e.prototype)t[n]=H}function Al(t){if(b(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}for(9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}function Pl(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}function Ll(t,e){for(var n=t.firstChild;n;){if(1===n.nodeType){var i=n.getAttribute("offset");i=i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var r=n.getAttribute("stop-color")||"#000000";e.addColorStop(i,r)}n=n.nextSibling}}function Ol(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),s(e.__inheritedStyle,t.__inheritedStyle))}function Bl(t){for(var e=B(t).split(g_),n=[],i=0;i<e.length;i+=2){var r=parseFloat(e[i]),a=parseFloat(e[i+1]);n.push([r,a])}return n}function zl(t,e,n,i){var r=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(Rl(t,e),o(r,Fl(t)),!i))for(var s in y_)if(y_.hasOwnProperty(s)){var l=t.getAttribute(s);null!=l&&(r[y_[s]]=l)}var u=a?"textFill":"fill",h=a?"textStroke":"stroke";e.style=e.style||new Np;var c=e.style;null!=r.fill&&c.set(u,El(r.fill,n)),null!=r.stroke&&c.set(h,El(r.stroke,n)),f(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=r[t]&&c.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),f(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&c.set(t,r[t])}),r.lineDash&&(e.style.lineDash=B(r.lineDash).split(g_)),c[h]&&"none"!==c[h]&&(e[h]=!0),e.__inheritedStyle=r}function El(t,e){var n=e&&t&&t.match(__);if(n){var i=B(n[1]),r=e[i];return r}return t}function Rl(t,e){var n=t.getAttribute("transform");if(n){n=n.replace(/,/g," ");var i=null,r=[];n.replace(x_,function(t,e,n){r.push(e,n)});for(var a=r.length-1;a>0;a-=2){var o=r[a],s=r[a-1];switch(i=i||Le(),s){case"translate":o=B(o).split(g_),Ee(i,i,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=B(o).split(g_),Fe(i,i,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=B(o).split(g_),Re(i,i,parseFloat(o[0]));break;case"skew":o=B(o).split(g_),console.warn("Skew transform is not supported yet");break;case"matrix":var o=B(o).split(g_);i[0]=parseFloat(o[0]),i[1]=parseFloat(o[1]),i[2]=parseFloat(o[2]),i[3]=parseFloat(o[3]),i[4]=parseFloat(o[4]),i[5]=parseFloat(o[5])}}e.setLocalTransform(i)}}function Fl(t){var e=t.getAttribute("style"),n={};if(!e)return n;var i={};w_.lastIndex=0;for(var r;null!=(r=w_.exec(e));)i[r[1]]=r[2];for(var a in y_)y_.hasOwnProperty(a)&&null!=i[a]&&(n[y_[a]]=i[a]);return n}function Nl(t,e,n){var i=e/t.width,r=n/t.height,a=Math.min(i,r),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+n/2];return{scale:o,position:s}}function Hl(t,e){return function(n,i,r){(e||!this._disposed)&&(n=n&&n.toLowerCase(),Ff.prototype[t].call(this,n,i,r))}}function Wl(){Ff.call(this)}function Vl(t,e,n){function r(t,e){return t.__prio-e.__prio}n=n||{},"string"==typeof e&&(e=ix[e]),this.id,this.group,this._dom=t;var a="canvas",o=this._zr=qi(t,{renderer:n.renderer||a,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=fl(y(o.flush,o),17);var e=i(e);e&&Dy(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new Ss;var s=this._api=ou(this);Bn(nx,r),Bn(J_,r),this._scheduler=new gl(this,s,J_,nx),Ff.call(this,this._ecEventProcessor=new su),this._messageCenter=new Wl,this._initEvents(),this.resize=y(this.resize,this),this._pendingActions=[],o.animation.on("frame",this._onframe,this),$l(o,this),z(this)}function Gl(t,e,n){if(!this._disposed){var i,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=ur(r,e);for(var o=0;o<a.length;o++){var s=a[o];if(s[t]&&null!=(i=s[t](r,e,n)))return i}}}function Xl(t){var e=t._model,n=t._scheduler;n.restorePipelines(e),n.prepareStageTasks(),Kl(t,"component",e,n),Kl(t,"chart",e,n),n.plan()}function Yl(t,e,n,i,r){function a(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}var o=t._model;if(!i)return void T_(t._componentsViews.concat(t._chartsViews),a);var s={};s[i+"Id"]=n[i+"Id"],s[i+"Index"]=n[i+"Index"],s[i+"Name"]=n[i+"Name"];var l={mainType:i,query:s};r&&(l.subType=r);var u=n.excludeSeriesId;null!=u&&(u=F(Ji(u))),o&&o.eachComponent(l,function(e){u&&null!=u.get(e.id)||a(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function Ul(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})}function jl(t,e){var n=t.type,i=t.escapeConnect,r=K_[n],a=r.actionInfo,l=(a.update||"update").split(":"),u=l.pop();l=null!=l[0]&&D_(l[0]),this[Y_]=!0;var h=[t],c=!1;t.batch&&(c=!0,h=p(t.batch,function(e){return e=s(o({},e),t),e.batch=null,e}));var d,f=[],g="highlight"===n||"downplay"===n;T_(h,function(t){d=r.action(t,this._model,this._api),d=d||o({},t),d.type=a.event||d.type,f.push(d),g?Yl(this,u,t,"series"):l&&Yl(this,u,t,l.main,l.sub)},this),"none"===u||g||l||(this[U_]?(Xl(this),Z_.update.call(this,t),this[U_]=!1):Z_[u].call(this,t)),d=c?{type:a.event||n,escapeConnect:i,batch:f}:f[0],this[Y_]=!1,!e&&this._messageCenter.trigger(d.type,d)}function ql(t){for(var e=this._pendingActions;e.length;){var n=e.shift();jl.call(this,n,t)}}function Zl(t){!t&&this.trigger("updated")}function $l(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[U_]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}function Kl(t,e,n,i){function r(t){var e="_ec_"+t.id+"_"+t.type,r=s[e];if(!r){var h=D_(t.type),c=a?Wy.getClass(h.main,h.sub):ll.getClass(h.sub);r=new c,r.init(n,u),s[e]=r,o.push(r),l.add(r.group)}t.__viewId=r.__id=e,r.__alive=!0,r.__model=t,r.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!a&&i.prepareView(r,t,n,u)}for(var a="component"===e,o=a?t._componentsViews:t._chartsViews,s=a?t._componentsMap:t._chartsMap,l=t._zr,u=t._api,h=0;h<o.length;h++)o[h].__alive=!1;a?n.eachComponent(function(t,e){"series"!==t&&r(e)}):n.eachSeries(r);for(var h=0;h<o.length;){var c=o[h];c.__alive?h++:(!a&&c.renderTask.dispose(),l.remove(c.group),c.dispose(n,u),o.splice(h,1),delete s[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function Ql(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function Jl(t,e,n,i){tu(t,e,n,i),T_(t._chartsViews,function(t){t.__alive=!1}),eu(t,e,n,i),T_(t._chartsViews,function(t){t.__alive||t.remove(e,n)})}function tu(t,e,n,i,r){T_(r||t._componentsViews,function(t){var r=t.__model;t.render(r,e,n,i),au(r,t)})}function eu(t,e,n,i,r){var a,o=t._scheduler;e.eachSeries(function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var s=n.renderTask;o.updatePayload(s,i),r&&r.get(e.uid)&&s.dirty(),a|=s.perform(o.getPerformArgs(s)),n.group.silent=!!e.get("silent"),au(e,n),ru(e,n)}),o.unfinished|=a,iu(t,e),Jy(t._zr.dom,e)}function nu(t,e){T_(ex,function(n){n(t,e)})}function iu(t,e){var n=t._zr,i=n.storage,r=0;i.traverse(function(){r++}),r>e.get("hoverLayerThreshold")&&!vf.node&&e.eachSeries(function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.group.traverse(function(t){t.useHoverLayer=!0})}})}function ru(t,e){var n=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.style.blend!==n&&t.setStyle("blend",n),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",n)})})}function au(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))})}function ou(t){var e=t._coordSysMgr;return o(new bs(t),{getCoordinateSystems:y(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}}})}function su(){this.eventInfo}function lu(t){function e(t,e){for(var n=0;n<t.length;n++){var i=t[n];i[a]=e}}var n=0,i=1,r=2,a="__connectUpdateStatus";T_(Q_,function(o,s){t._messageCenter.on(s,function(o){if(ox[t.group]&&t[a]!==n){if(o&&o.escapeConnect)return;var s=t.makeActionFromEvent(o),l=[];T_(ax,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,n),T_(l,function(t){t[a]!==i&&t.dispatchAction(s)}),e(l,r)}})})}function uu(t,e,n){var i=fu(t);if(i)return i;var r=new Vl(t,e,n);return r.id="ec_"+sx++,ax[r.id]=r,cr(t,ux,r.id),lu(r),r}function hu(t){if(x(t)){var e=t;t=null,T_(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+lx++,T_(e,function(e){e.group=t})}return ox[t]=!0,t}function cu(t){ox[t]=!1}function du(t){"string"==typeof t?t=ax[t]:t instanceof Vl||(t=fu(t)),t instanceof Vl&&!t.isDisposed()&&t.dispose()}function fu(t){return ax[dr(t,ux)]}function pu(t){return ax[t]}function gu(t,e){ix[t]=e}function vu(t){tx.push(t)}function mu(t,e){Mu(J_,t,e,O_)}function yu(t){ex.push(t)
}function _u(t,e,n){"function"==typeof e&&(n=e,e="");var i=k_(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,C_(j_.test(i)&&j_.test(e)),K_[i]||(K_[i]={action:n,actionInfo:t}),Q_[e]=i}function xu(t,e){Ss.register(t,e)}function wu(t){var e=Ss.get(t);return e?e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice():void 0}function bu(t,e){Mu(nx,t,e,R_,"layout")}function Su(t,e){Mu(nx,t,e,H_,"visual")}function Mu(t,e,n,i,r){(I_(e)||k_(e))&&(n=e,e=i);var a=gl.wrapStageHandler(n,r);return a.__prio=e,a.__raw=n,t.push(a),a}function Cu(t,e){rx[t]=e}function Tu(t){return Jm.extend(t)}function Iu(t){return Wy.extend(t)}function ku(t){return Hy.extend(t)}function Du(t){return ll.extend(t)}function Au(t){n("createCanvas",t)}function Pu(t,e,n){S_.registerMap(t,e,n)}function Lu(t){var e=S_.retrieveMap(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}}function Ou(t){return t}function Bu(t,e,n,i,r){this._old=t,this._new=e,this._oldKeyGetter=n||Ou,this._newKeyGetter=i||Ou,this.context=r}function zu(t,e,n,i,r){for(var a=0;a<t.length;a++){var o="_ec_"+r[i](t[a],a),s=e[o];null==s?(n.push(o),e[o]=a):(s.length||(e[o]=s=[s]),s.push(a))}}function Eu(t){var e={},n=e.encode={},i=F(),r=[],a=[],o=e.userOutput={dimensionNames:t.dimensions.slice(),encode:{}};f(t.dimensions,function(e){var s=t.getDimensionInfo(e),l=s.coordDim;if(l){var u=s.coordDimIndex;Ru(n,l)[u]=e,s.isExtraCoord||(i.set(l,1),Nu(s.type)&&(r[0]=e),Ru(o.encode,l)[u]=s.index),s.defaultTooltip&&a.push(e)}dx.each(function(t,e){var i=Ru(n,e),r=s.otherDims[e];null!=r&&r!==!1&&(i[r]=s.name)})});var s=[],l={};i.each(function(t,e){var i=n[e];l[e]=i[0],s=s.concat(i)}),e.dataDimsOnCoord=s,e.encodeFirstDimNotExtra=l;var u=n.label;u&&u.length&&(r=u.slice());var h=n.tooltip;return h&&h.length?a=h.slice():a.length||(a=r.slice()),n.defaultedLabel=r,n.defaultedTooltip=a,e}function Ru(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function Fu(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function Nu(t){return!("ordinal"===t||"time"===t)}function Hu(t){null!=t&&o(this,t),this.otherDims={}}function Wu(t){return t._rawCount>65535?yx:xx}function Vu(t){var e=t.constructor;return e===Array?t.slice():new e(t)}function Gu(t,e){f(bx.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods,f(Sx,function(n){t[n]=i(e[n])}),t._calculationInfo=o(e._calculationInfo)}function Xu(t,e,n,i,r){var a=mx[e.type],o=i-1,s=e.name,l=t[s][o];if(l&&l.length<n){for(var u=new a(Math.min(r-o*n,n)),h=0;h<l.length;h++)u[h]=l[h];t[s][o]=u}for(var c=i*n;r>c;c+=n)t[s].push(new a(Math.min(r-c,n)))}function Yu(t){var e=t._invertedIndicesMap;f(e,function(n,i){var r=t._dimensionInfos[i],a=r.ordinalMeta;if(a){n=e[i]=new _x(a.categories.length);for(var o=0;o<n.length;o++)n[o]=gx;for(var o=0;o<t._count;o++)n[t.get(i,o)]=o}})}function Uu(t,e,n){var i;if(null!=e){var r=t._chunkSize,a=Math.floor(n/r),o=n%r,s=t.dimensions[e],l=t._storage[s][a];if(l){i=l[o];var u=t._dimensionInfos[s].ordinalMeta;u&&u.categories.length&&(i=u.categories[i])}}return i}function ju(t){return t}function qu(t){return t<this._count&&t>=0?this._indices[t]:-1}function Zu(t,e){var n=t._idList[e];return null==n&&(n=Uu(t,t._idDimIdx,e)),null==n&&(n=vx+e),n}function $u(t){return x(t)||(t=[t]),t}function Ku(t,e){var n=t.dimensions,i=new Mx(p(n,t.getDimensionInfo,t),t.hostModel);Gu(i,t);for(var r=i._storage={},a=t._storage,o=0;o<n.length;o++){var s=n[o];a[s]&&(u(e,s)>=0?(r[s]=Qu(a[s]),i._rawExtent[s]=Ju(),i._extent[s]=null):r[s]=a[s])}return i}function Qu(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=Vu(t[n]);return e}function Ju(){return[1/0,-1/0]}function th(t,e,n){function r(t,e,n){null!=dx.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,u.set(e,!0))}ns.isInstance(e)||(e=ns.seriesDataToSource(e)),n=n||{},t=(t||[]).slice();for(var a=(n.dimsDef||[]).slice(),l=F(),u=F(),h=[],c=eh(e,t,a,n.dimCount),d=0;c>d;d++){var p=a[d]=o({},S(a[d])?a[d]:{name:a[d]}),g=p.name,v=h[d]=new Hu;null!=g&&null==l.get(g)&&(v.name=v.displayName=g,l.set(g,d)),null!=p.type&&(v.type=p.type),null!=p.displayName&&(v.displayName=p.displayName)}var m=n.encodeDef;!m&&n.encodeDefaulter&&(m=n.encodeDefaulter(e,c)),m=F(m),m.each(function(t,e){if(t=Ji(t).slice(),1===t.length&&!b(t[0])&&t[0]<0)return void m.set(e,!1);var n=m.set(e,[]);f(t,function(t,i){b(t)&&(t=l.get(t)),null!=t&&c>t&&(n[i]=t,r(h[t],e,i))})});var y=0;f(t,function(t){var e,t,n,a;if(b(t))e=t,t={};else{e=t.name;var o=t.ordinalMeta;t.ordinalMeta=null,t=i(t),t.ordinalMeta=o,n=t.dimsDef,a=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var l=m.get(e);if(l!==!1){var l=Ji(l);if(!l.length)for(var u=0;u<(n&&n.length||1);u++){for(;y<h.length&&null!=h[y].coordDim;)y++;y<h.length&&l.push(y++)}f(l,function(i,o){var l=h[i];if(r(s(l,t),e,o),null==l.name&&n){var u=n[o];!S(u)&&(u={name:u}),l.name=l.displayName=u.name,l.defaultTooltip=u.defaultTooltip}a&&s(l.otherDims,a)})}});var _=n.generateCoord,x=n.generateCoordCount,w=null!=x;x=_?x||1:0;for(var M=_||"value",C=0;c>C;C++){var v=h[C]=h[C]||new Hu,T=v.coordDim;null==T&&(v.coordDim=nh(M,u,w),v.coordDimIndex=0,(!_||0>=x)&&(v.isExtraCoord=!0),x--),null==v.name&&(v.name=nh(v.coordDim,l)),null!=v.type||fs(e,C,v.name)!==dy.Must&&(!v.isExtraCoord||null==v.otherDims.itemName&&null==v.otherDims.seriesName)||(v.type="ordinal")}return h}function eh(t,e,n,i){var r=Math.max(t.dimensionsDetectCount||1,e.length,n.length,i||0);return f(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}function nh(t,e,n){if(n||null!=e.get(t)){for(var i=0;null!=e.get(t+i);)i++;t+=i}return e.set(t,!0),t}function ih(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=F(),this.categoryAxisMap=F(),this.firstCategoryDimIndex=null}function rh(t){var e=t.get("coordinateSystem"),n=new ih(e),i=kx[e];return i?(i(t,n,n.axisMap,n.categoryAxisMap),n):void 0}function ah(t){return"category"===t.get("type")}function oh(t,e,n){n=n||{};var i,r,a,o,s=n.byIndex,l=n.stackedCoordDimension,u=!(!t||!t.get("stack"));if(f(e,function(t,n){b(t)&&(e[n]=t={name:t}),u&&!t.isExtraCoord&&(s||i||!t.ordinalMeta||(i=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),!r||s||i||(s=!0),r){a="__\x00ecstackresult",o="__\x00ecstackedover",i&&(i.createInvertedIndices=!0);var h=r.coordDim,c=r.type,d=0;f(e,function(t){t.coordDim===h&&d++}),e.push({name:a,coordDim:h,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0}),d++,e.push({name:o,coordDim:o,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:i&&i.name,isStackedByIndex:s,stackedOverDimension:o,stackResultDimension:a}}function sh(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function lh(t,e){return sh(t,e)?t.getCalculationInfo("stackResultDimension"):e}function uh(t,e,n){n=n||{},ns.isInstance(t)||(t=ns.seriesDataToSource(t));var i,r=e.get("coordinateSystem"),a=Ss.get(r),o=rh(e);o&&(i=p(o.coordSysDims,function(t){var e={name:t},n=o.axisMap.get(t);if(n){var i=n.get("type");e.type=Fu(i)}return e})),i||(i=a&&(a.getDimensionsInfo?a.getDimensionsInfo():a.dimensions.slice())||["x","y"]);var s,l,u=Ix(t,{coordDimensions:i,generateCoord:n.generateCoord,encodeDefaulter:n.useEncodeDefaulter?_(cs,i,e):null});o&&f(u,function(t,e){var n=t.coordDim,i=o.categoryAxisMap.get(n);i&&(null==s&&(s=e),t.ordinalMeta=i.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(u[s].otherDims.itemName=0);var h=oh(e,u),c=new Mx(u,e);c.setCalculationInfo(h);var d=null!=s&&hh(t)?function(t,e,n,i){return i===s?n:this.defaultDimValueGetter(t,e,n,i)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function hh(t){if(t.sourceFormat===ry){var e=ch(t.data||[]);return null!=e&&!x(er(e))}}function ch(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function dh(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function fh(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}function ph(t){return t._map||(t._map=F(t.categories))}function gh(t){return S(t)&&null!=t.value?t.value:t+""}function vh(t,e,n,i){var r={},a=t[1]-t[0],o=r.interval=Oo(a/e,!0);null!=n&&n>o&&(o=r.interval=n),null!=i&&o>i&&(o=r.interval=i);var s=r.intervalPrecision=mh(o),l=r.niceTickExtent=[Lx(Math.ceil(t[0]/o)*o,s),Lx(Math.floor(t[1]/o)*o,s)];return _h(l,t),r}function mh(t){return Co(t)+2}function yh(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function _h(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),yh(t,0,e),yh(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function xh(t){return t.get("stack")||zx+t.seriesIndex}function wh(t){return t.dim+t.index}function bh(t,e){var n=[];return e.eachSeriesByType(t,function(t){kh(t)&&!Dh(t)&&n.push(t)}),n}function Sh(t){var e={};f(t,function(t){var n=t.coordinateSystem,i=n.getBaseAxis();if("time"===i.type||"value"===i.type)for(var r=t.getData(),a=i.dim+"_"+i.index,o=r.mapDimension(i.dim),s=0,l=r.count();l>s;++s){var u=r.get(o,s);e[a]?e[a].push(u):e[a]=[u]}});var n=[];for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r){r.sort(function(t,e){return t-e});for(var a=null,o=1;o<r.length;++o){var s=r[o]-r[o-1];s>0&&(a=null===a?s:Math.min(a,s))}n[i]=a}}return n}function Mh(t){var e=Sh(t),n=[];return f(t,function(t){var i,r=t.coordinateSystem,a=r.getBaseAxis(),o=a.getExtent();if("category"===a.type)i=a.getBandWidth();else if("value"===a.type||"time"===a.type){var s=a.dim+"_"+a.index,l=e[s],u=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),c=Math.abs(h[1]-h[0]);i=l?u/c*l:u}else{var d=t.getData();i=Math.abs(o[1]-o[0])/d.count()}var f=wo(t.get("barWidth"),i),p=wo(t.get("barMaxWidth"),i),g=wo(t.get("barMinWidth")||1,i),v=t.get("barGap"),m=t.get("barCategoryGap");n.push({bandWidth:i,barWidth:f,barMaxWidth:p,barMinWidth:g,barGap:v,barCategoryGap:m,axisKey:wh(a),stackId:xh(t)})}),Ch(n)}function Ch(t){var e={};f(t,function(t){var n=t.axisKey,i=t.bandWidth,r=e[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},a=r.stacks;e[n]=r;var o=t.stackId;a[o]||r.autoWidthCount++,a[o]=a[o]||{width:0,maxWidth:0};var s=t.barWidth;s&&!a[o].width&&(a[o].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(a[o].maxWidth=l);var u=t.barMinWidth;u&&(a[o].minWidth=u);var h=t.barGap;null!=h&&(r.gap=h);var c=t.barCategoryGap;null!=c&&(r.categoryGap=c)});var n={};return f(e,function(t,e){n[e]={};var i=t.stacks,r=t.bandWidth,a=wo(t.categoryGap,r),o=wo(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,u=(s-a)/(l+(l-1)*o);u=Math.max(u,0),f(i,function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){var i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,s-=i+o*i,l--}else{var i=u;e&&i>e&&(i=Math.min(e,s)),n&&n>i&&(i=n),i!==u&&(t.width=i,s-=i+o*i,l--)}}),u=(s-a)/(l+(l-1)*o),u=Math.max(u,0);var h,c=0;f(i,function(t){t.width||(t.width=u),h=t,c+=t.width*(1+o)}),h&&(c-=h.width*o);var d=-c/2;f(i,function(t,i){n[e][i]=n[e][i]||{bandWidth:r,offset:d,width:t.width},d+=t.width*(1+o)})}),n}function Th(t,e,n){if(t&&e){var i=t[wh(e)];return null!=i&&null!=n&&(i=i[xh(n)]),i}}function Ih(t,e){var n=bh(t,e),i=Mh(n),r={};f(n,function(t){var e=t.getData(),n=t.coordinateSystem,a=n.getBaseAxis(),o=xh(t),s=i[wh(a)][o],l=s.offset,u=s.width,h=n.getOtherAxis(a),c=t.get("barMinHeight")||0;r[o]=r[o]||[],e.setLayout({bandWidth:s.bandWidth,offset:l,size:u});for(var d=e.mapDimension(h.dim),f=e.mapDimension(a.dim),p=sh(e,d),g=h.isHorizontal(),v=Ah(a,h,p),m=0,y=e.count();y>m;m++){var _=e.get(d,m),x=e.get(f,m),w=_>=0?"p":"n",b=v;p&&(r[o][x]||(r[o][x]={p:v,n:v}),b=r[o][x][w]);var S,M,C,T;if(g){var I=n.dataToPoint([_,x]);S=b,M=I[1]+l,C=I[0]-v,T=u,Math.abs(C)<c&&(C=(0>C?-1:1)*c),isNaN(C)||p&&(r[o][x][w]+=C)}else{var I=n.dataToPoint([x,_]);S=I[0]+l,M=b,C=u,T=I[1]-v,Math.abs(T)<c&&(T=(0>=T?-1:1)*c),isNaN(T)||p&&(r[o][x][w]+=T)}e.setItemLayout(m,{x:S,y:M,width:C,height:T})}},this)}function kh(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function Dh(t){return t.pipelineContext&&t.pipelineContext.large}function Ah(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}function Ph(t,e){return Qx(t,Kx(e))}function Lh(t,e){var n,i,r,a=t.type,o=e.getMin(),s=e.getMax(),l=t.getExtent();"ordinal"===a?n=e.getCategories().length:(i=e.get("boundaryGap"),x(i)||(i=[i||0,i||0]),"boolean"==typeof i[0]&&(i=[0,0]),i[0]=wo(i[0],1),i[1]=wo(i[1],1),r=l[1]-l[0]||Math.abs(l[0])),"dataMin"===o?o=l[0]:"function"==typeof o&&(o=o({min:l[0],max:l[1]})),"dataMax"===s?s=l[1]:"function"==typeof s&&(s=s({min:l[0],max:l[1]}));var u=null!=o,h=null!=s;null==o&&(o="ordinal"===a?n?0:0/0:l[0]-i[0]*r),null==s&&(s="ordinal"===a?n?n-1:0/0:l[1]+i[1]*r),(null==o||!isFinite(o))&&(o=0/0),(null==s||!isFinite(s))&&(s=0/0),t.setBlank(I(o)||I(s)||"ordinal"===a&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(o>0&&s>0&&!u&&(o=0),0>o&&0>s&&!h&&(s=0));var c=e.ecModel;if(c&&"time"===a){var d,p=bh("bar",c);if(f(p,function(t){d|=t.getBaseAxis()===e.axis}),d){var g=Mh(p),v=Oh(o,s,e,g);o=v.min,s=v.max}}return{extent:[o,s],fixMin:u,fixMax:h}}function Oh(t,e,n,i){var r=n.axis.getExtent(),a=r[1]-r[0],o=Th(i,n.axis);if(void 0===o)return{min:t,max:e};var s=1/0;f(o,function(t){s=Math.min(t.offset,s)});var l=-1/0;f(o,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=1-(s+l)/a,d=h/c-h;return e+=d*(l/u),t-=d*(s/u),{min:t,max:e}}function Bh(t,e){var n=Lh(t,e),i=n.extent,r=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var a=t.type;t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:"interval"===a||"time"===a?e.get("minInterval"):null,maxInterval:"interval"===a||"time"===a?e.get("maxInterval"):null});var o=e.get("interval");null!=o&&t.setInterval&&t.setInterval(o)}function zh(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Px(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new Bx;default:return(dh.getClass(e)||Bx).create(t)}}function Eh(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||0>n&&0>i)}function Rh(t){var e=t.getLabelModel().get("formatter"),n="category"===t.type?t.scale.getExtent()[0]:null;return"string"==typeof e?e=function(e){return function(n){return n=t.scale.getLabel(n),e.replace("{value}",null!=n?n:"")}}(e):"function"==typeof e?function(i,r){return null!=n&&(r=i-n),e(Fh(t,i),r)}:function(e){return t.scale.getLabel(e)}}function Fh(t,e){return"category"===t.type?t.scale.getLabel(e):e}function Nh(t){var e=t.model,n=t.scale;if(e.get("axisLabel.show")&&!n.isBlank()){var i,r,a="category"===t.type,o=n.getExtent();a?r=n.count():(i=n.getTicks(),r=i.length);var s,l=t.getLabelModel(),u=Rh(t),h=1;r>40&&(h=Math.ceil(r/40));for(var c=0;r>c;c+=h){var d=i?i[c]:o[0]+c,f=u(d),p=l.getTextRect(f),g=Hh(p,l.get("rotate")||0);s?s.union(g):s=g}return s}}function Hh(t,e){var n=e*Math.PI/180,i=t.plain(),r=i.width,a=i.height,o=r*Math.cos(n)+a*Math.sin(n),s=r*Math.sin(n)+a*Math.cos(n),l=new Tn(i.x,i.y,o,s);return l}function Wh(t){var e=t.get("interval");return null==e?"auto":e}function Vh(t){return"category"===t.type&&0===Wh(t.getLabelModel())}function Gh(t,e){if("image"!==this.type){var n=this.style,i=this.shape;i&&"line"===i.symbolType?n.stroke=t:this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff"):(n.fill&&(n.fill=t),n.stroke&&(n.stroke=t)),this.dirty(!1)}}function Xh(t,e,n,i,r,a,o){var s=0===t.indexOf("empty");s&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var l;return l=0===t.indexOf("image://")?_a(t.slice(8),new Tn(e,n,i,r),o?"center":"cover"):0===t.indexOf("path://")?ya(t.slice(7),{},new Tn(e,n,i,r),o?"center":"cover"):new dw({shape:{symbolType:t,x:e,y:n,width:i,height:r}}),l.__isEmptyBrush=s,l.setColor=Gh,l.setColor(a),l}function Yh(t){return uh(t.getSource(),t)}function Uh(t,e){var n=e;fo.isInstance(e)||(n=new fo(e),c(n,rw));var i=zh(n);return i.setExtent(t[0],t[1]),Bh(i,n),i}function jh(t){c(t,rw)}function qh(t,e){return Math.abs(t-e)<gw}function Zh(t,e,n){var i=0,r=t[0];if(!r)return!1;for(var a=1;a<t.length;a++){var o=t[a];i+=Yr(r[0],r[1],o[0],o[1],e,n),r=o}var s=t[0];return qh(r[0],s[0])&&qh(r[1],s[1])||(i+=Yr(r[0],r[1],s[0],s[1],e,n)),0!==i}function $h(t,e,n){if(this.name=t,this.geometries=e,n)n=[n[0],n[1]];else{var i=this.getBoundingRect();n=[i.x+i.width/2,i.y+i.height/2]}this.center=n}function Kh(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var n=t.features,i=0;i<n.length;i++)for(var r=n[i],a=r.geometry,o=a.coordinates,s=a.encodeOffsets,l=0;l<o.length;l++){var u=o[l];if("Polygon"===a.type)o[l]=Qh(u,s[l],e);else if("MultiPolygon"===a.type)for(var h=0;h<u.length;h++){var c=u[h];u[h]=Qh(c,s[l][h],e)}}return t.UTF8Encoding=!1,t}function Qh(t,e,n){for(var i=[],r=e[0],a=e[1],o=0;o<t.length;o+=2){var s=t.charCodeAt(o)-64,l=t.charCodeAt(o+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),s+=r,l+=a,r=s,a=l,i.push([s/n,l/n])}return i}function Jh(t){return"category"===t.type?ec(t):rc(t)}function tc(t,e){return"category"===t.type?ic(t,e):{ticks:t.scale.getTicks()}}function ec(t){var e=t.getLabelModel(),n=nc(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}function nc(t,e){var n=ac(t,"labels"),i=Wh(e),r=oc(n,i);if(r)return r;var a,o;return w(i)?a=dc(t,i):(o="auto"===i?lc(t):i,a=cc(t,o)),sc(n,i,{labels:a,labelCategoryInterval:o})}function ic(t,e){var n=ac(t,"ticks"),i=Wh(e),r=oc(n,i);if(r)return r;var a,o;if((!e.get("show")||t.scale.isBlank())&&(a=[]),w(i))a=dc(t,i,!0);else if("auto"===i){var s=nc(t,t.getLabelModel());o=s.labelCategoryInterval,a=p(s.labels,function(t){return t.tickValue})}else o=i,a=cc(t,o,!0);return sc(n,i,{ticks:a,tickCategoryInterval:o})}function rc(t){var e=t.scale.getTicks(),n=Rh(t);return{labels:p(e,function(e,i){return{formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e}})}}function ac(t,e){return mw(t)[e]||(mw(t)[e]=[])}function oc(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function sc(t,e,n){return t.push({key:e,value:n}),n}function lc(t){var e=mw(t).autoInterval;return null!=e?e:mw(t).autoInterval=t.calculateCategoryInterval()}function uc(t){var e=hc(t),n=Rh(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,a=r.getExtent(),o=r.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=a[0],u=t.dataToCoord(l+1)-t.dataToCoord(l),h=Math.abs(u*Math.cos(i)),c=Math.abs(u*Math.sin(i)),d=0,f=0;l<=a[1];l+=s){var p=0,g=0,v=Yn(n(l),e.font,"center","top");p=1.3*v.width,g=1.3*v.height,d=Math.max(d,p,7),f=Math.max(f,g,7)}var m=d/h,y=f/c;isNaN(m)&&(m=1/0),isNaN(y)&&(y=1/0);var _=Math.max(0,Math.floor(Math.min(m,y))),x=mw(t.model),w=t.getExtent(),b=x.lastAutoInterval,S=x.lastTickCount;return null!=b&&null!=S&&Math.abs(b-_)<=1&&Math.abs(S-o)<=1&&b>_&&x.axisExtend0===w[0]&&x.axisExtend1===w[1]?_=b:(x.lastTickCount=o,x.lastAutoInterval=_,x.axisExtend0=w[0],x.axisExtend1=w[1]),_}function hc(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function cc(t,e,n){function i(t){l.push(n?t:{formattedLabel:r(t),rawLabel:a.getLabel(t),tickValue:t})}var r=Rh(t),a=t.scale,o=a.getExtent(),s=t.getLabelModel(),l=[],u=Math.max((e||0)+1,1),h=o[0],c=a.count();0!==h&&u>1&&c/u>2&&(h=Math.round(Math.ceil(h/u)*u));var d=Vh(t),f=s.get("showMinLabel")||d,p=s.get("showMaxLabel")||d;f&&h!==o[0]&&i(o[0]);for(var g=h;g<=o[1];g+=u)i(g);return p&&g-u!==o[1]&&i(o[1]),l}function dc(t,e,n){var i=t.scale,r=Rh(t),a=[];return f(i.getTicks(),function(t){var o=i.getLabel(t);e(t,o)&&a.push(n?t:{formattedLabel:r(t),rawLabel:o,tickValue:t})}),a}function fc(t,e){var n=t[1]-t[0],i=e,r=n/i/2;t[0]+=r,t[1]-=r}function pc(t,e,n,i){function r(t,e){return t=bo(t),e=bo(e),d?t>e:e>t}var a=e.length;if(t.onBand&&!n&&a){var o,s,l=t.getExtent();if(1===a)e[0].coord=l[0],o=e[1]={coord:l[0]};else{var u=e[a-1].tickValue-e[0].tickValue,h=(e[a-1].coord-e[0].coord)/u;f(e,function(t){t.coord-=h/2});var c=t.scale.getExtent();s=1+c[1]-e[a-1].tickValue,o={coord:e[a-1].coord+h*s},e.push(o)}var d=l[0]>l[1];r(e[0].coord,l[0])&&(i?e[0].coord=l[0]:e.shift()),i&&r(l[0],e[0].coord)&&e.unshift({coord:l[0]}),r(l[1],o.coord)&&(i?o.coord=l[1]:e.pop()),i&&r(o.coord,l[1])&&e.push({coord:l[1]})}}function gc(t){return this._axes[t]}function vc(t){Sw.call(this,t)}function mc(t,e){return e.type||(e.data?"category":"value")}function yc(t,e){return t.getCoordSysModel()===e}function _c(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}function xc(t,e,n,i){function r(t){return t.dim+"_"+t.index}n.getAxesOnZeroOf=function(){return a?[a]:[]};var a,o=t[e],s=n.model,l=s.get("axisLine.onZero"),u=s.get("axisLine.onZeroAxisIndex");if(l){if(null!=u)wc(o[u])&&(a=o[u]);else for(var h in o)if(o.hasOwnProperty(h)&&wc(o[h])&&!i[r(o[h])]){a=o[h];break}a&&(i[r(a)]=!0)}}function wc(t){return t&&"category"!==t.type&&"time"!==t.type&&Eh(t)}function bc(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}function Sc(t){return p(Lw,function(e){var n=t.getReferringComponents(e)[0];return n})}function Mc(t){return"cartesian2d"===t.get("coordinateSystem")}function Cc(t,e){var n=t.mapDimension("defaultedLabel",!0),i=n.length;if(1===i)return Zs(t,e,n[0]);if(i){for(var r=[],a=0;a<n.length;a++){var o=Zs(t,e,n[a]);r.push(o)}return r.join(" ")}}function Tc(t,e,n,i,r,a){var o=n.getModel("label"),s=n.getModel("emphasis.label");Wa(t,e,o,s,{labelFetcher:r,labelDataIndex:a,defaultText:Cc(r.getData(),a),isRectText:!0,autoColor:i}),Ic(t),Ic(e)}function Ic(t,e){"outside"===t.textPosition&&(t.textPosition=e)}function kc(t,e,n){var i=t.getArea(),r=t.getBaseAxis().isHorizontal(),a=i.x,o=i.y,s=i.width,l=i.height,u=n.get("lineStyle.width")||2;a-=u/2,o-=u/2,s+=u,l+=u,a=Math.floor(a),s=Math.round(s);var h=new im({shape:{x:a,y:o,width:s,height:l}});return e&&(h.shape[r?"width":"height"]=0,to(h,{shape:{width:s,height:l}},n)),h}function Dc(t,e,n){var i=t.getArea(),r=new Zv({shape:{cx:bo(t.cx,1),cy:bo(t.cy,1),r0:bo(i.r0,1),r:bo(i.r,1),startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});return e&&(r.shape.endAngle=i.startAngle,to(r,{shape:{endAngle:i.endAngle}},n)),r}function Ac(t,e,n){return t?"polar"===t.type?Dc(t,e,n):"cartesian2d"===t.type?kc(t,e,n):null:null}function Pc(t,e){var n=t.getArea&&t.getArea();if("cartesian2d"===t.type){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}function Lc(t,e,n){n.style.text=null,Ja(n,{shape:{width:0}},e,t,function(){n.parent&&n.parent.remove(n)})}function Oc(t,e,n){n.style.text=null,Ja(n,{shape:{r:n.shape.r0}},e,t,function(){n.parent&&n.parent.remove(n)})}function Bc(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function zc(t,e,n,i,r,a,o,l){var u=e.getItemVisual(n,"color"),h=e.getItemVisual(n,"opacity"),c=e.getVisual("borderColor"),d=i.getModel("itemStyle"),f=i.getModel("emphasis.itemStyle").getBarItemStyle();l||t.setShape("r",d.get("barBorderRadius")||0),t.useStyle(s({stroke:Bc(r)?"none":c,fill:Bc(r)?"none":u,opacity:h},d.getBarItemStyle()));var p=i.getShallow("cursor");p&&t.attr("cursor",p);var g=o?r.height>0?"bottom":"top":r.width>0?"left":"right";l||Tc(t.style,f,i,u,a,n,g),Bc(r)&&(f.fill=f.stroke="none"),Ra(t,f)}function Ec(t,e){var n=t.get(Rw)||0,i=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),r=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(n,i,r)}function Rc(t,e,n){var i=t.getData(),r=[],a=i.getLayout("valueAxisHorizontal")?1:0;r[1-a]=i.getLayout("valueAxisStart");var o=i.getLayout("largeDataIndices"),s=i.getLayout("barWidth"),l=t.getModel("backgroundStyle"),u=t.get("showBackground",!0);if(u){var h=i.getLayout("largeBackgroundPoints"),c=[];c[1-a]=i.getLayout("backgroundStart");var d=new Xw({shape:{points:h},incremental:!!n,__startPoint:c,__baseDimIdx:a,__largeDataIndices:o,__barWidth:s,silent:!0,z2:0});Hc(d,l,i),e.add(d)}var f=new Xw({shape:{points:i.getLayout("largePoints")},incremental:!!n,__startPoint:r,__baseDimIdx:a,__largeDataIndices:o,__barWidth:s});e.add(f),Nc(f,t,i),f.seriesIndex=t.seriesIndex,t.get("silent")||(f.on("mousedown",Yw),f.on("mousemove",Yw))}function Fc(t,e,n){var i=t.__baseDimIdx,r=1-i,a=t.shape.points,o=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];Fw[0]=e,Fw[1]=n;for(var u=Fw[i],h=Fw[1-i],c=u-s,d=u+s,f=0,p=a.length/2;p>f;f++){var g=2*f,v=a[g+i],m=a[g+r];if(v>=c&&d>=v&&(m>=l?h>=l&&m>=h:h>=m&&l>=h))return o[f]}return-1}function Nc(t,e,n){var i=n.getVisual("borderColor")||n.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=i,t.style.lineWidth=n.getLayout("barWidth")}function Hc(t,e,n){var i=e.get("borderColor")||e.get("color"),r=e.getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=i,t.style.lineWidth=n.getLayout("barWidth")}function Wc(t,e,n){var i,r="polar"===n.type;return i=r?n.getArea():n.grid.getRect(),r?{cx:i.cx,cy:i.cy,r0:t?i.r0:e.r0,r:t?i.r:e.r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}:{x:t?e.x:i.x,y:t?i.y:e.y,width:t?e.width:i.width,height:t?i.height:e.height}}function Vc(t,e,n){var i="polar"===t.type?Zv:im;return new i({shape:Wc(e,n,t),silent:!0,z2:0})}function Gc(t,e,n,i){var r,a,o=ko(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;return Do(o-Uw/2)?(a=l?"bottom":"top",r="center"):Do(o-1.5*Uw)?(a=l?"top":"bottom",r="center"):(a="middle",r=1.5*Uw>o&&o>Uw/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,textVerticalAlign:a}}function Xc(t,e,n){if(!Vh(t.axis)){var i=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],n=n||[];var a=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],h=n[1],c=n[n.length-1],d=n[n.length-2];i===!1?(Yc(a),Yc(u)):Uc(a,o)&&(i?(Yc(o),Yc(h)):(Yc(a),Yc(u))),r===!1?(Yc(s),Yc(c)):Uc(l,s)&&(r?(Yc(l),Yc(d)):(Yc(s),Yc(c)))}}function Yc(t){t&&(t.ignore=!0)}function Uc(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=Oe([]);return Re(r,r,-t.rotation),n.applyTransform(ze([],r,t.getLocalTransform())),i.applyTransform(ze([],r,e.getLocalTransform())),n.intersect(i)}}function jc(t){return"middle"===t||"center"===t}function qc(t,e,n,i,r){for(var a=[],o=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;o[0]=u,o[1]=0,s[0]=u,s[1]=n,e&&(ae(o,o,e),ae(s,s,e));var h=new am({anid:r+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,silent:!0});a.push(h)}return a}function Zc(t,e,n){var i=e.axis,r=e.getModel("axisTick");if(r.get("show")&&!i.scale.isBlank()){for(var a=r.getModel("lineStyle"),o=n.tickDirection*r.get("length"),l=i.getTicksCoords(),u=qc(l,t._transform,o,s(a.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),h=0;h<u.length;h++)t.group.add(u[h]);return u}}function $c(t,e,n){var i=e.axis,r=e.getModel("minorTick");if(r.get("show")&&!i.scale.isBlank()){var a=i.getMinorTicksCoords();if(a.length)for(var o=r.getModel("lineStyle"),l=n.tickDirection*r.get("length"),u=s(o.getLineStyle(),s(e.getModel("axisTick").getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")})),h=0;h<a.length;h++)for(var c=qc(a[h],t._transform,l,u,"minorticks_"+h),d=0;d<c.length;d++)t.group.add(c[d])}}function Kc(t,e,n){var i=e.axis,r=k(n.axisLabelShow,e.get("axisLabel.show"));if(r&&!i.scale.isBlank()){var a=e.getModel("axisLabel"),o=a.get("margin"),s=i.getViewLabels(),l=(k(n.labelRotate,a.get("rotate"))||0)*Uw/180,u=$w(n.rotation,l,n.labelDirection),h=e.getCategories&&e.getCategories(!0),c=[],d=Kw(e),p=e.get("triggerEvent");return f(s,function(r,s){var l=r.tickValue,f=r.formattedLabel,g=r.rawLabel,v=a;h&&h[l]&&h[l].textStyle&&(v=new fo(h[l].textStyle,a,e.ecModel));var m=v.getTextColor()||e.get("axisLine.lineStyle.color"),y=i.dataToCoord(l),_=[y,n.labelOffset+n.labelDirection*o],x=new Yv({anid:"label_"+l,position:_,rotation:u.rotation,silent:d,z2:10});Ga(x.style,v,{text:f,textAlign:v.getShallow("align",!0)||u.textAlign,textVerticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||u.textVerticalAlign,textFill:"function"==typeof m?m("category"===i.type?g:"value"===i.type?l+"":l,s):m}),p&&(x.eventData=Zw(e),x.eventData.targetType="axisLabel",x.eventData.value=g),t._dumbGroup.add(x),x.updateTransform(),c.push(x),t.group.add(x),x.decomposeTransform()}),c}}function Qc(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return Jc(n,t,e),n.seriesInvolved&&ed(n,t),n}function Jc(t,e,n){var i=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),a=r.get("link",!0)||[],o=[];Qw(n.getCoordinateSystems(),function(n){function s(i,s,l){var h=l.model.getModel("axisPointer",r),d=h.get("show");if(d&&("auto"!==d||i||sd(h))){null==s&&(s=h.get("triggerTooltip")),h=i?td(l,c,r,e,i,s):h;var f=h.get("snap"),p=ld(l.model),g=s||f||"category"===l.type,v=t.axesInfo[p]={key:p,axis:l,coordSys:n,axisPointerModel:h,triggerTooltip:s,involveSeries:g,snap:f,useHandle:sd(h),seriesModels:[]};u[p]=v,t.seriesInvolved|=g;var m=nd(a,l);if(null!=m){var y=o[m]||(o[m]={axesInfo:{}});y.axesInfo[p]=v,y.mapper=a[m].mapper,v.linkGroup=y}}}if(n.axisPointerEnabled){var l=ld(n.model),u=t.coordSysAxesInfo[l]={};t.coordSysMap[l]=n;var h=n.model,c=h.getModel("tooltip",i);if(Qw(n.getAxes(),Jw(s,!1,null)),n.getTooltipAxes&&i&&c.get("show")){var d="axis"===c.get("trigger"),f="cross"===c.get("axisPointer.type"),p=n.getTooltipAxes(c.get("axisPointer.axis"));(d||f)&&Qw(p.baseAxes,Jw(s,f?"cross":!0,d)),f&&Qw(p.otherAxes,Jw(s,"cross",!1))}}})}function td(t,e,n,r,a,o){var l=e.getModel("axisPointer"),u={};Qw(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){u[t]=i(l.get(t))}),u.snap="category"!==t.type&&!!o,"cross"===l.get("type")&&(u.type="line");var h=u.label||(u.label={});if(null==h.show&&(h.show=!1),"cross"===a){var c=l.get("label.show");if(h.show=null!=c?c:!0,!o){var d=u.lineStyle=l.get("crossStyle");d&&s(h,d.textStyle)}}return t.model.getModel("axisPointer",new fo(u,n,r))}function ed(t,e){e.eachSeries(function(e){var n=e.coordinateSystem,i=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);n&&"none"!==i&&i!==!1&&"item"!==i&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&Qw(t.coordSysAxesInfo[ld(n.model)],function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function nd(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var a=t[r]||{};if(id(a[i+"AxisId"],n.id)||id(a[i+"AxisIndex"],n.componentIndex)||id(a[i+"AxisName"],n.name))return r}}function id(t,e){return"all"===t||x(t)&&u(t,e)>=0||t===e}function rd(t){var e=ad(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,a=n.get("status"),o=n.get("value");null!=o&&(o=i.parse(o));var s=sd(n);null==a&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==o||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),r.value=o,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function ad(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[ld(t)]}function od(t){var e=ad(t);return e&&e.axisPointerModel}function sd(t){return!!t.get("handle.show")}function ld(t){return t.type+"||"+t.id}function ud(t,e,n,i,r,a){var o=tb.getAxisPointerClass(t.axisPointerClass);if(o){var s=od(e);s?(t._axisPointer||(t._axisPointer=new o)).render(e,s,i,a):hd(t,i)}}function hd(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}function cd(t,e,n){n=n||{};var i=t.coordinateSystem,r=e.axis,a={},o=r.getAxesOnZeroOf()[0],s=r.position,l=o?"onZero":s,u=r.dim,h=i.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===u?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];
if(o){var g=o.toGlobalCoord(o.dataToCoord(0));p[d.onZero]=Math.max(Math.min(g,p[1]),p[0])}a.position=["y"===u?p[d[l]]:c[0],"x"===u?p[d[l]]:c[3]],a.rotation=Math.PI/2*("x"===u?0:1);var v={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=v[s],a.labelOffset=o?p[d[s]]-p[d.onZero]:0,e.get("axisTick.inside")&&(a.tickDirection=-a.tickDirection),k(n.labelInside,e.get("axisLabel.inside"))&&(a.labelDirection=-a.labelDirection);var m=e.get("axisLabel.rotate");return a.labelRotate="top"===l?-m:m,a.z2=1,a}function dd(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var a=n.getModel("splitArea"),o=a.getModel("areaStyle"),l=o.get("color"),u=i.coordinateSystem.getRect(),h=r.getTicksCoords({tickModel:a,clamp:!0});if(h.length){var c=l.length,d=t.__splitAreaColors,f=F(),p=0;if(d)for(var g=0;g<h.length;g++){var v=d.get(h[g].tickValue);if(null!=v){p=(v+(c-1)*g)%c;break}}var m=r.toGlobalCoord(h[0].coord),y=o.getAreaStyle();l=x(l)?l:[l];for(var g=1;g<h.length;g++){var _,w,b,S,M=r.toGlobalCoord(h[g].coord);r.isHorizontal()?(_=m,w=u.y,b=M-_,S=u.height,m=_+b):(_=u.x,w=m,b=u.width,S=M-w,m=w+S);var C=h[g-1].tickValue;null!=C&&f.set(C,p),e.add(new im({anid:null!=C?"area_"+C:null,shape:{x:_,y:w,width:b,height:S},style:s({fill:l[p]},y),silent:!0})),p=(p+1)%c}t.__splitAreaColors=f}}}function fd(t){t.__splitAreaColors=null}function pd(t,e,n){var i,r={},a="toggleSelected"===t;return n.eachComponent("legend",function(n){a&&null!=i?n[i?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?n[t]():(n[t](e.name),i=n.isSelected(e.name));var o=n.getData();f(o,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var i=n.isSelected(e);r[e]=r.hasOwnProperty(e)?r[e]&&i:i}})}),"allSelect"===t||"inverseSelect"===t?{selected:r}:{name:e.name,selected:r}}function gd(t,e){var n=Hm(e.get("padding")),i=e.getItemStyle(["color","opacity"]);i.fill=e.get("backgroundColor");var t=new im({shape:{x:t.x-n[3],y:t.y-n[0],width:t.width+n[1]+n[3],height:t.height+n[0]+n[2],r:e.get("borderRadius")},style:i,silent:!0,z2:-1});return t}function vd(t,e,n,i,r,a){var o;return"line"!==e&&e.indexOf("empty")<0?(o=n.getItemStyle(),t.style.stroke=i,a||(o.stroke=r)):o=n.getItemStyle(["borderWidth","borderColor"]),t.setStyle(o)}function md(t,e,n,i){_d(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),yd(t,e,n,i)}function yd(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function _d(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}function xd(t,e,n){var i=t.getOrient(),r=[1,1];r[i.index]=0,Ko(e,n,{type:"box",ignoreSize:r})}function wd(t,e,n,i,r){var a=t.axis;if(!a.scale.isBlank()&&a.containData(e)){if(!t.involveSeries)return void n.showPointer(t,e);var s=bd(e,t),l=s.payloadBatch,u=s.snapToValue;l[0]&&null==r.seriesIndex&&o(r,l[0]),!i&&t.snap&&a.containData(u)&&null!=u&&(e=u),n.showPointer(t,e,l,r),n.showTooltip(t,s,u)}}function bd(t,e){var n=e.axis,i=n.dim,r=t,a=[],o=Number.MAX_VALUE,s=-1;return _b(e.seriesModels,function(e){var l,u,h=e.getData().mapDimension(i,!0);if(e.getAxisTooltipData){var c=e.getAxisTooltipData(h,t,n);u=c.dataIndices,l=c.nestestValue}else{if(u=e.getData().indicesOfNearest(h[0],t,"category"===n.type?.5:null),!u.length)return;l=e.getData().get(h[0],u[0])}if(null!=l&&isFinite(l)){var d=t-l,f=Math.abs(d);o>=f&&((o>f||d>=0&&0>s)&&(o=f,s=d,r=l,a.length=0),_b(u,function(t){a.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:a,snapToValue:r}}function Sd(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function Md(t,e,n,i){var r=n.payloadBatch,a=e.axis,o=a.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,u=ld(l),h=t.map[u];h||(h=t.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:i,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function Cd(t,e,n){var i=n.axesInfo=[];_b(e,function(e,n){var r=e.axisPointerModel.option,a=t[n];a?(!e.useHandle&&(r.status="show"),r.value=a.value,r.seriesDataIndices=(a.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&i.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function Td(t,e,n,i){if(Ad(e)||!t.list.length)return void i({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}function Id(t,e,n){var i=n.getZr(),r="axisPointerLastHighlights",a=wb(i)[r]||{},o=wb(i)[r]={};_b(t,function(t){var e=t.axisPointerModel.option;"show"===e.status&&_b(e.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;o[e]=t})});var s=[],l=[];f(a,function(t,e){!o[e]&&l.push(t)}),f(o,function(t,e){!a[e]&&s.push(t)}),l.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}function kd(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}function Dd(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function Ad(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function Pd(t,e,n){if(!vf.node){var i=e.getZr();Sb(i).records||(Sb(i).records={}),Ld(i,e);var r=Sb(i).records[t]||(Sb(i).records[t]={});r.handler=n}}function Ld(t,e){function n(n,i){t.on(n,function(n){var r=Ed(e);Mb(Sb(t).records,function(t){t&&i(t,n,r.dispatchAction)}),Od(r.pendings,e)})}Sb(t).initialized||(Sb(t).initialized=!0,n("click",_(zd,"click")),n("mousemove",_(zd,"mousemove")),n("globalout",Bd))}function Od(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]),n&&(n.dispatchAction=null,e.dispatchAction(n))}function Bd(t,e,n){t.handler("leave",null,n)}function zd(t,e,n,i){e.handler(t,n,i)}function Ed(t){var e={showTip:[],hideTip:[]},n=function(i){var r=e[i.type];r?r.push(i):(i.dispatchAction=n,t.dispatchAction(i))};return{dispatchAction:n,pendings:e}}function Rd(t,e){if(!vf.node){var n=e.getZr(),i=(Sb(n).records||{})[t];i&&(Sb(n).records[t]=null)}}function Fd(){}function Nd(t,e,n,i){Hd(Tb(n).lastProp,i)||(Tb(n).lastProp=i,e?Ja(n,i,t):(n.stopAnimation(),n.attr(i)))}function Hd(t,e){if(S(t)&&S(e)){var n=!0;return f(e,function(e,i){n=n&&Hd(t[i],e)}),!!n}return t===e}function Wd(t,e){t[e.get("label.show")?"show":"hide"]()}function Vd(t){return{position:t.position.slice(),rotation:t.rotation||0}}function Gd(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function Xd(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle(),e.fill=null):"shadow"===n&&(e=i.getAreaStyle(),e.stroke=null),e}function Yd(t,e,n,i,r){var a=n.get("value"),o=jd(a,e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),s=n.getModel("label"),l=Hm(s.get("padding")||0),u=s.getFont(),h=Yn(o,u),c=r.position,d=h.width+l[1]+l[3],f=h.height+l[0]+l[2],p=r.align;"right"===p&&(c[0]-=d),"center"===p&&(c[0]-=d/2);var g=r.verticalAlign;"bottom"===g&&(c[1]-=f),"middle"===g&&(c[1]-=f/2),Ud(c,d,f,i);var v=s.get("backgroundColor");v&&"auto"!==v||(v=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:d,height:f,r:s.get("borderRadius")},position:c.slice(),style:{text:o,textFont:u,textFill:s.getTextColor(),textPosition:"inside",textPadding:l,fill:v,stroke:s.get("borderColor")||"transparent",lineWidth:s.get("borderWidth")||0,shadowBlur:s.get("shadowBlur"),shadowColor:s.get("shadowColor"),shadowOffsetX:s.get("shadowOffsetX"),shadowOffsetY:s.get("shadowOffsetY")},z2:10}}function Ud(t,e,n,i){var r=i.getWidth(),a=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,a)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function jd(t,e,n,i,r){t=e.scale.parse(t);var a=e.scale.getLabel(t,{precision:r.precision}),o=r.formatter;if(o){var s={value:Fh(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};f(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,r=e&&e.getDataParams(i);r&&s.seriesData.push(r)}),b(o)?a=o.replace("{value}",a):w(o)&&(a=o(s))}return a}function qd(t,e,n){var i=Le();return Re(i,i,n.rotation),Ee(i,i,n.position),no([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}function Zd(t,e,n,i,r,a){var o=jw.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get("label.margin"),Yd(e,i,r,a,{position:qd(i.axis,t,n),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function $d(t,e,n){return n=n||0,{x1:t[n],y1:t[1-n],x2:e[n],y2:e[1-n]}}function Kd(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}function Qd(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}function Jd(t){return"x"===t.dim?0:1}function tf(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",n="left "+t+"s "+e+",top "+t+"s "+e;return p(Ob,function(t){return t+"transition:"+n}).join(";")}function ef(t){var e=[],n=t.get("fontSize"),i=t.getTextColor();return i&&e.push("color:"+i),e.push("font:"+t.getFont()),n&&e.push("line-height:"+Math.round(3*n/2)+"px"),Pb(["decoration","align"],function(n){var i=t.get(n);i&&e.push("text-"+n+":"+i)}),e.join(";")}function nf(t){var e=[],n=t.get("transitionDuration"),i=t.get("backgroundColor"),r=t.getModel("textStyle"),a=t.get("padding");return n&&e.push(tf(n)),i&&(vf.canvasSupported?e.push("background-Color:"+i):(e.push("background-Color:#"+rn(i)),e.push("filter:alpha(opacity=70)"))),Pb(["width","color","radius"],function(n){var i="border-"+n,r=Lb(i),a=t.get(r);null!=a&&e.push(i+":"+a+("color"===n?"":"px"))}),e.push(ef(r)),null!=a&&e.push("padding:"+Hm(a).join("px ")+"px"),e.join(";")+";"}function rf(t,e,n,i,r){var a=e&&e.painter;if(n){var o=a&&a.getViewportRoot();o&&pe(t,o,document.body,i,r)}else{t[0]=i,t[1]=r;var s=a&&a.getViewportRootOffset();s&&(t[0]+=s.offsetLeft,t[1]+=s.offsetTop)}}function af(t,e,n){if(vf.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var r=this._zr=e.getZr(),a=this._appendToBody=n&&n.appendToBody;this._styleCoord=[0,0],rf(this._styleCoord,r,a,e.getWidth()/2,e.getHeight()/2),a?document.body.appendChild(i):t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var o=this;i.onmouseenter=function(){o._enterable&&(clearTimeout(o._hideTimeout),o._show=!0),o._inContent=!0},i.onmousemove=function(t){if(t=t||window.event,!o._enterable){var e=r.handler,n=r.painter.getViewportRoot();be(n,t,!0),e.dispatch("mousemove",t)}},i.onmouseleave=function(){o._enterable&&o._show&&o.hideLater(o._hideDelay),o._inContent=!1}}function of(t){this._zr=t.getZr(),this._show=!1,this._hideTimeout}function sf(t){for(var e=t.pop();t.length;){var n=t.pop();n&&(fo.isInstance(n)&&(n=n.get("tooltip",!0)),"string"==typeof n&&(n={formatter:n}),e=new fo(n,e,e.ecModel))}return e}function lf(t,e){return t.dispatchAction||y(e.dispatchAction,e)}function uf(t,e,n,i,r,a,o){var s=n.getOuterSize(),l=s.width,u=s.height;return null!=a&&(t+l+a>i?t-=l+a:t+=a),null!=o&&(e+u+o>r?e-=u+o:e+=o),[t,e]}function hf(t,e,n,i,r){var a=n.getOuterSize(),o=a.width,s=a.height;return t=Math.min(t+o,i)-o,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function cf(t,e,n){var i=n[0],r=n[1],a=5,o=0,s=0,l=e.width,u=e.height;switch(t){case"inside":o=e.x+l/2-i/2,s=e.y+u/2-r/2;break;case"top":o=e.x+l/2-i/2,s=e.y-r-a;break;case"bottom":o=e.x+l/2-i/2,s=e.y+u+a;break;case"left":o=e.x-i-a,s=e.y+u/2-r/2;break;case"right":o=e.x+l+a,s=e.y+u/2-r/2}return[o,s]}function df(t){return"center"===t||"middle"===t}var ff=2311,pf=function(){return ff++},gf={};gf="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:e(navigator.userAgent);var vf=gf,mf={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},yf={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},_f=Object.prototype.toString,xf=Array.prototype,wf=xf.forEach,bf=xf.filter,Sf=xf.slice,Mf=xf.map,Cf=xf.reduce,Tf={},If=function(){return Tf.createCanvas()};Tf.createCanvas=function(){return document.createElement("canvas")};var kf,Df="__ec_primitive__";R.prototype={constructor:R,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){void 0!==e&&(t=y(t,e));for(var n in this.data)this.data.hasOwnProperty(n)&&t(this.data[n],n)},removeKey:function(t){delete this.data[t]}};var Af=(Object.freeze||Object)({$override:n,clone:i,merge:r,mergeAll:a,extend:o,defaults:s,createCanvas:If,getContext:l,indexOf:u,inherits:h,mixin:c,isArrayLike:d,each:f,map:p,reduce:g,filter:v,find:m,bind:y,curry:_,isArray:x,isFunction:w,isString:b,isObject:S,isBuiltInObject:M,isTypedArray:C,isDom:T,eqNaN:I,retrieve:k,retrieve2:D,retrieve3:A,slice:P,normalizeCssArray:L,assert:O,trim:B,setAsPrimitive:z,isPrimitive:E,createHashMap:F,concatArray:N,noop:H}),Pf="undefined"==typeof Float32Array?Array:Float32Array,Lf=q,Of=Z,Bf=ee,zf=ne,Ef=(Object.freeze||Object)({create:W,copy:V,clone:G,set:X,add:Y,scaleAndAdd:U,sub:j,len:q,length:Lf,lenSquare:Z,lengthSquare:Of,mul:$,div:K,dot:Q,scale:J,normalize:te,distance:ee,dist:Bf,distanceSquare:ne,distSquare:zf,negate:ie,lerp:re,applyTransform:ae,min:oe,max:se});le.prototype={constructor:le,_dragStart:function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(ue(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,a=i-this._y;this._x=n,this._y=i,e.drift(r,a,t),this.dispatchToElement(ue(e,t),"drag",t.event);var o=this.findHover(n,i,e).target,s=this._dropTarget;this._dropTarget=o,e!==o&&(s&&o!==s&&this.dispatchToElement(ue(s,t),"dragleave",t.event),o&&o!==s&&this.dispatchToElement(ue(o,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(ue(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(ue(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var Rf=Array.prototype.slice,Ff=function(t){this._$handlers={},this._$eventProcessor=t};Ff.prototype={constructor:Ff,one:function(t,e,n,i){return ce(this,t,e,n,i,!0)},on:function(t,e,n,i){return ce(this,t,e,n,i,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,a=n[t].length;a>r;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e){var i=arguments,r=i.length;r>3&&(i=Rf.call(i,1));for(var a=e.length,o=0;a>o;){var s=e[o];if(n&&n.filter&&null!=s.query&&!n.filter(t,s.query))o++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,i[1]);break;case 3:s.h.call(s.ctx,i[1],i[2]);break;default:s.h.apply(s.ctx,i)}s.one?(e.splice(o,1),a--):o++}}}return n&&n.afterTrigger&&n.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e){var i=arguments,r=i.length;r>4&&(i=Rf.call(i,1,i.length-1));for(var a=i[i.length-1],o=e.length,s=0;o>s;){var l=e[s];if(n&&n.filter&&null!=l.query&&!n.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(a);break;case 2:l.h.call(a,i[1]);break;case 3:l.h.call(a,i[1],i[2]);break;default:l.h.apply(a,i)}l.one?(e.splice(s,1),o--):s++}}}return n&&n.afterTrigger&&n.afterTrigger(t),this}};var Nf=Math.log(2),Hf="___zrEVENTSAVED",Wf=[],Vf="undefined"!=typeof window&&!!window.addEventListener,Gf=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Xf=[],Yf=Vf?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},Uf=function(){this._track=[]};Uf.prototype={constructor:Uf,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},a=0,o=i.length;o>a;a++){var s=i[a],l=_e(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in jf)if(jf.hasOwnProperty(e)){var n=jf[e](this._track,t);if(n)return n}}};var jf={pinch:function(t,e){var n=t.length;if(n){var i=(t[n-1]||{}).points,r=(t[n-2]||{}).points||i;if(r&&r.length>1&&i&&i.length>1){var a=Ce(i)/Ce(r);!isFinite(a)&&(a=1),e.pinchScale=a;var o=Te(i);return e.pinchX=o[0],e.pinchY=o[1],{type:"pinch",target:t[0].target,event:e}}}}},qf="silent";De.prototype.dispose=function(){};var Zf=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],$f=function(t,e,n,i){Ff.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new De,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,le.call(this),this.setHandlerProxy(n)};$f.prototype={constructor:$f,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(f(Zf,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,n=t.zrY,i=Pe(this,e,n),r=this._hovered,a=r.target;a&&!a.__zr&&(r=this.findHover(r.x,r.y),a=r.target);var o=this._hovered=i?{x:e,y:n}:this.findHover(e,n),s=o.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),a&&s!==a&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(o,"mousemove",t),s&&s!==a&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,n=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&!n&&this.trigger("globalout",{type:"globalout",event:t})},resize:function(){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){t=t||{};var i=t.target;if(!i||!i.silent){for(var r="on"+e,a=Ie(e,t,n);i&&(i[r]&&(a.cancelBubble=i[r].call(i,a)),i.trigger(e,a),i=i.parent,!a.cancelBubble););a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,a),t.trigger&&t.trigger(e,a)}))}},findHover:function(t,e,n){for(var i=this.storage.getDisplayList(),r={x:t,y:e},a=i.length-1;a>=0;a--){var o;if(i[a]!==n&&!i[a].ignore&&(o=Ae(i[a],t,e))&&(!r.topTarget&&(r.topTarget=i[a]),o!==qf)){r.target=i[a];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new Uf);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r,this.dispatchToElement({target:i.target},r,i.event)}}},f(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){$f.prototype[t]=function(e){var n,i,r=e.zrX,a=e.zrY,o=Pe(this,r,a);if("mouseup"===t&&o||(n=this.findHover(r,a),i=n.target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Bf(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}),c($f,Ff),c($f,le);var Kf="undefined"==typeof Float32Array?Array:Float32Array,Qf=(Object.freeze||Object)({create:Le,identity:Oe,copy:Be,mul:ze,translate:Ee,rotate:Re,scale:Fe,invert:Ne,clone:He}),Jf=Oe,tp=5e-5,ep=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},np=ep.prototype;np.transform=null,np.needLocalTransform=function(){return We(this.rotation)||We(this.position[0])||We(this.position[1])||We(this.scale[0]-1)||We(this.scale[1]-1)};var ip=[];np.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),i=this.transform;if(!n&&!e)return void(i&&Jf(i));i=i||Le(),n?this.getLocalTransform(i):Jf(i),e&&(n?ze(i,t.transform,i):Be(i,t.transform)),this.transform=i;var r=this.globalScaleRatio;if(null!=r&&1!==r){this.getGlobalScale(ip);var a=ip[0]<0?-1:1,o=ip[1]<0?-1:1,s=((ip[0]-a)*r+a)/ip[0]||0,l=((ip[1]-o)*r+o)/ip[1]||0;i[0]*=s,i[1]*=s,i[2]*=l,i[3]*=l}this.invTransform=this.invTransform||Le(),Ne(this.invTransform,i)},np.getLocalTransform=function(t){return ep.getLocalTransform(this,t)},np.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},np.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var rp=[],ap=Le();np.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=this.position,r=this.scale;We(e-1)&&(e=Math.sqrt(e)),We(n-1)&&(n=Math.sqrt(n)),t[0]<0&&(e=-e),t[3]<0&&(n=-n),i[0]=t[4],i[1]=t[5],r[0]=e,r[1]=n,this.rotation=Math.atan2(-t[1]/n,t[0]/e)}},np.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(ze(rp,t.invTransform,e),e=rp);var n=this.origin;n&&(n[0]||n[1])&&(ap[4]=n[0],ap[5]=n[1],ze(rp,e,ap),rp[4]-=n[0],rp[5]-=n[1],e=rp),this.setLocalTransform(e)}},np.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},np.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&ae(n,n,i),n},np.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&ae(n,n,i),n},ep.getLocalTransform=function(t,e){e=e||[],Jf(e);var n=t.origin,i=t.scale||[1,1],r=t.rotation||0,a=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),Fe(e,e,i),r&&Re(e,e,r),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=a[0],e[5]+=a[1],e};var op={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||1>n?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/i)))},elasticOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||1>n?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/i)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||1>n?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?-.5*n*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/i):n*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-op.bounceOut(1-t)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return.5>t?.5*op.bounceIn(2*t):.5*op.bounceOut(2*t-1)+.5}};Ve.prototype={constructor:Ve,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var n=(t-this._startTime-this._pausedTime)/this._life;if(!(0>n)){n=Math.min(n,1);var i=this.easing,r="string"==typeof i?op[i]:i,a="function"==typeof r?r(n):n;return this.fire("frame",a),1===n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var sp=function(){this.head=null,this.tail=null,this._len=0},lp=sp.prototype;lp.insert=function(t){var e=new up(t);return this.insertEntry(e),e},lp.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},lp.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},lp.len=function(){return this._len},lp.clear=function(){this.head=this.tail=null,this._len=0};var up=function(t){this.value=t,this.next,this.prev},hp=function(t){this._list=new sp,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},cp=hp.prototype;cp.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var a=n.len(),o=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}o?o.value=e:o=new up(e),o.key=t,n.insertEntry(o),i[t]=o}return r},cp.get=function(t){var e=this._map[t],n=this._list;return null!=e?(e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value):void 0},cp.clear=function(){this._list.clear(),this._map={}};var dp={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},fp=new hp(20),pp=null,gp=an,vp=on,mp=(Object.freeze||Object)({parse:Je,lift:nn,toHex:rn,fastLerp:an,fastMapToColor:gp,lerp:on,mapToColor:vp,modifyHSL:sn,modifyAlpha:ln,stringify:un}),yp=Array.prototype.slice,_p=function(t,e,n,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||hn,this._setter=i||cn,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};_p.prototype={when:function(t,e){var n=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!n[i]){n[i]=[];var r=this._getter(this._target,i);if(null==r)continue;0!==t&&n[i].push({time:0,value:_n(r)})}n[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;e>n;n++)t[n].call(this)
},start:function(t,e){var n,i=this,r=0,a=function(){r--,r||i._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=bn(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),n=s)}if(n){var l=n.onframe;n.onframe=function(t,e){l(t,e);for(var n=0;n<i._onframeList.length;n++)i._onframeList[n](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var xp=1;"undefined"!=typeof window&&(xp=Math.max(window.devicePixelRatio||1,1));var wp=0,bp=xp,Sp=function(){};1===wp&&(Sp=console.error);var Mp=Sp,Cp=function(){this.animators=[]};Cp.prototype={constructor:Cp,animate:function(t,e){var n,i=!1,r=this,a=this.__zr;if(t){var o=t.split("."),s=r;i="shape"===o[0];for(var l=0,h=o.length;h>l;l++)s&&(s=s[o[l]]);s&&(n=s)}else n=r;if(!n)return void Mp('Property "'+t+'" is not existed in element '+r.id);var c=r.animators,d=new _p(n,e);return d.during(function(){r.dirty(i)}).done(function(){c.splice(u(c,d),1)}),c.push(d),a&&a.animation.addAnimator(d),d},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;n>i;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,a){Sn(this,t,e,n,i,r,a)},animateFrom:function(t,e,n,i,r,a){Sn(this,t,e,n,i,r,a,!0)}};var Tp=function(t){ep.call(this,t),Ff.call(this,t),Cp.call(this,t),this.id=t.id||pf()};Tp.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var n=this[t];n||(n=this[t]=[]),n[0]=e[0],n[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(S(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},c(Tp,Cp),c(Tp,ep),c(Tp,Ff);var Ip=ae,kp=Math.min,Dp=Math.max;Tn.prototype={constructor:Tn,union:function(t){var e=kp(t.x,this.x),n=kp(t.y,this.y);this.width=Dp(t.x+t.width,this.x+this.width)-e,this.height=Dp(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:function(){var t=[],e=[],n=[],i=[];return function(r){if(r){t[0]=n[0]=this.x,t[1]=i[1]=this.y,e[0]=i[0]=this.x+this.width,e[1]=n[1]=this.y+this.height,Ip(t,t,r),Ip(e,e,r),Ip(n,n,r),Ip(i,i,r),this.x=kp(t[0],e[0],n[0],i[0]),this.y=kp(t[1],e[1],n[1],i[1]);var a=Dp(t[0],e[0],n[0],i[0]),o=Dp(t[1],e[1],n[1],i[1]);this.width=a-this.x,this.height=o-this.y}}}(),calculateTransform:function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=Le();return Ee(r,r,[-e.x,-e.y]),Fe(r,r,[n,i]),Ee(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof Tn||(t=Tn.create(t));var e=this,n=e.x,i=e.x+e.width,r=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,l=t.y,u=t.y+t.height;return!(o>i||n>s||l>a||r>u)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new Tn(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},Tn.create=function(t){return new Tn(t.x,t.y,t.width,t.height)};var Ap=function(t){t=t||{},Tp.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};Ap.prototype={constructor:Ap,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof Ap&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,i=this._children,r=u(i,t);return 0>r?this:(i.splice(r,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof Ap&&t.delChildrenFromStorage(n)),e&&e.refresh(),this)},removeAll:function(){var t,e,n=this._children,i=this.__storage;for(e=0;e<n.length;e++)t=n[e],i&&(i.delFromStorage(t),t instanceof Ap&&t.delChildrenFromStorage(i)),t.parent=null;return n.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof Ap&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof Ap&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new Tn(0,0,0,0),i=t||this._children,r=[],a=0;a<i.length;a++){var o=i[a];if(!o.ignore&&!o.invisible){var s=o.getBoundingRect(),l=o.getLocalTransform(r);l?(n.copy(s),n.applyTransform(l),e=e||n.clone(),e.union(n)):(e=e||s.clone(),e.union(s))}}return e||n}},h(Ap,Tp);var Pp=32,Lp=7,Op=function(){this._roots=[],this._displayList=[],this._displayListLen=0};Op.prototype={constructor:Op,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;r>i;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,vf.canvasSupported&&Bn(n,zn)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];for(var r=i,a=t;r;)r.parent=a,r.updateTransform(),e.push(r),a=r,r=r.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var l=o[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof Ap&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof Ap&&n.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,i=t.length;i>e;e++)this.delRoot(t[e]);else{var r=u(this._roots,t);r>=0&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof Ap&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:zn};var Bp={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},zp=function(t,e,n){return Bp.hasOwnProperty(e)?n*=t.dpr:n},Ep={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},Rp=9,Fp=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],Np=function(t){this.extendFrom(t,!1)};Np.prototype={constructor:Np,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){var i=this,r=n&&n.style,a=!r||t.__attrCachedBy!==Ep.STYLE_BIND;t.__attrCachedBy=Ep.STYLE_BIND;for(var o=0;o<Fp.length;o++){var s=Fp[o],l=s[0];(a||i[l]!==r[l])&&(t[l]=zp(t,l,i[l]||s[1]))}if((a||i.fill!==r.fill)&&(t.fillStyle=i.fill),(a||i.stroke!==r.stroke)&&(t.strokeStyle=i.stroke),(a||i.opacity!==r.opacity)&&(t.globalAlpha=null==i.opacity?1:i.opacity),(a||i.blend!==r.blend)&&(t.globalCompositeOperation=i.blend||"source-over"),this.hasStroke()){var u=i.lineWidth;t.lineWidth=u/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||e!==!0&&(e===!1?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var i="radial"===e.type?Rn:En,r=i(t,e,n),a=e.colorStops,o=0;o<a.length;o++)r.addColorStop(a[o].offset,a[o].color);return r}};for(var Hp=Np.prototype,Wp=0;Wp<Fp.length;Wp++){var Vp=Fp[Wp];Vp[0]in Hp||(Hp[Vp[0]]=Vp[1])}Np.getGradient=Hp.getGradient;var Gp=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};Gp.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var Xp=function(t,e,n){var i;n=n||bp,"string"==typeof t?i=Nn(t,e,n):S(t)&&(i=t,t=i.id),this.id=t,this.dom=i;var r=i.style;r&&(i.onselectstart=Fn,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n};Xp.prototype={constructor:Xp,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=Nn("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,a=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,a&&(a.width=t*n,a.height=e*n,1!==n&&this.ctxBack.scale(n,n))},clear:function(t,e){var n=this.dom,i=this.ctx,r=n.width,a=n.height,e=e||this.clearColor,o=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;if(o&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,r/l,a/l)),i.clearRect(0,0,r,a),e&&"transparent"!==e){var u;e.colorStops?(u=e.__canvasGradient||Np.getGradient(i,e,{x:0,y:0,width:r,height:a}),e.__canvasGradient=u):e.image&&(u=Gp.prototype.getCanvasPattern.call(e,i)),i.save(),i.fillStyle=u||e,i.fillRect(0,0,r,a),i.restore()}if(o){var h=this.domBack;i.save(),i.globalAlpha=s,i.drawImage(h,0,0,r,a),i.restore()}}};var Yp="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},Up=new hp(50),jp={},qp=0,Zp=5e3,$p=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,Kp="12px sans-serif",Qp={};Qp.measureText=function(t,e){var n=l();return n.font=e||Kp,n.measureText(t)};var Jp=Kp,tg={left:1,right:1,center:1},eg={top:1,bottom:1,middle:1},ng=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],ig={},rg={},ag=new Tn,og=function(){};og.prototype={constructor:og,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&li(n,!0);var i=n.text;if(null!=i&&(i+=""),Ci(i,n)){t.save();var r=this.transform;n.transformText?this.setTransform(t):r&&(ag.copy(e),ag.applyTransform(r),e=ag),hi(this,t,i,n,e,Rp),t.restore()}}},Ti.prototype={constructor:Ti,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(n[0],n[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?Tp.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new Np(t,this),this.dirty(!1),this},calculateTextPosition:null},h(Ti,Tp),c(Ti,og),Ii.prototype={constructor:Ii,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r=this._image=Wn(i,this._image,this,this.onload);if(r&&Gn(r)){var a=n.x||0,o=n.y||0,s=n.width,l=n.height,u=r.width/r.height;if(null==s&&null!=l?s=l*u:null==l&&null!=s?l=s/u:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),n.sWidth&&n.sHeight){var h=n.sx||0,c=n.sy||0;t.drawImage(r,h,c,n.sWidth,n.sHeight,a,o,s,l)}else if(n.sx&&n.sy){var h=n.sx,c=n.sy,d=s-h,f=l-c;t.drawImage(r,h,c,d,f,a,o,s,l)}else t.drawImage(r,a,o,s,l);null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new Tn(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},h(Ii,Ti);var sg=1e5,lg=314159,ug=.01,hg=.001,cg=new Tn(0,0,0,0),dg=new Tn(0,0,0,0),fg=function(t,e,n){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=o({},n||{}),this.dpr=n.devicePixelRatio||bp,this._singleCanvas=i,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],s=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,i){var l=t.width,u=t.height;null!=n.width&&(l=n.width),null!=n.height&&(u=n.height),this.dpr=n.devicePixelRatio||1,t.width=l*this.dpr,t.height=u*this.dpr,this._width=l,this._height=u;var h=new Xp(t,this,this.dpr);h.__builtin__=!0,h.initContext(),s[lg]=h,h.zlevel=lg,a.push(lg),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=Oi(this._width,this._height);t.appendChild(c)}this._hoverlayer=null,this._hoverElements=[]};fg.prototype={constructor:fg,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<n.length;i++){var r=n[i],a=this._layers[r];if(!a.__builtin__&&a.refresh){var o=0===i?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return n.__from=t,t.__hoverMir=n,e&&n.setStyle(e),this._hoverElements.push(n),n}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=u(n,e);i>=0&&n.splice(i,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t.length;e++){var n=t[e].__from;n&&(n.__hoverMir=null)}t.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){Bn(t,this.storage.displayableSortFunc),n||(n=this._hoverlayer=this.getLayer(sg));var i={};n.ctx.save();for(var r=0;e>r;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,n,!0,i))):(t.splice(r,1),o.__hoverMir=null,e--)}n.ctx.restore()}},getHoverLayer:function(){return this.getLayer(sg)},_paintList:function(t,e,n){if(this._redrawId===n){e=e||!1,this._updateLayerStatus(t);var i=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!i){var r=this;Yp(function(){r._paintList(t,e,n)})}}},_compositeManually:function(){var t=this.getLayer(lg).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer(function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)})},_doPaintList:function(t,e){for(var n=[],i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i],a=this._layers[r];a.__builtin__&&a!==this._hoverlayer&&(a.__dirty||e)&&n.push(a)}for(var o=!0,s=0;s<n.length;s++){var a=n[s],l=a.ctx,u={};l.save();var h=e?a.__startIndex:a.__drawIndex,c=!e&&a.incremental&&Date.now,d=c&&Date.now(),p=a.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(a.__startIndex===a.__endIndex)a.clear(!1,p);else if(h===a.__startIndex){var g=t[h];g.incremental&&g.notClear&&!e||a.clear(!1,p)}-1===h&&(console.error("For some unknown reason. drawIndex is -1"),h=a.__startIndex);for(var v=h;v<a.__endIndex;v++){var m=t[v];if(this._doPaintEl(m,a,e,u),m.__dirty=m.__dirtyText=!1,c){var y=Date.now()-d;if(y>15)break}}a.__drawIndex=v,a.__drawIndex<a.__endIndex&&(o=!1),u.prevElClipPaths&&l.restore(),l.restore()}return vf.wxa&&f(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,n,i){var r=e.ctx,a=t.transform;if(!(!e.__dirty&&!n||t.invisible||0===t.style.opacity||a&&!a[0]&&!a[3]||t.culling&&Ai(t,this._width,this._height))){var o=t.__clipPaths,s=i.prevElClipPaths;(!s||Pi(o,s))&&(s&&(r.restore(),i.prevElClipPaths=null,i.prevEl=null),o&&(r.save(),Li(o,r),i.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,i.prevEl||null),i.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=lg);var n=this._layers[t];return n||(n=new Xp("zr_"+t,this,this.dpr),n.zlevel=t,n.__builtin__=!0,this._layerConfig[t]?r(n,this._layerConfig[t],!0):this._layerConfig[t-ug]&&r(n,this._layerConfig[t-ug],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},insertLayer:function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,a=null,o=-1,s=this._domRoot;if(n[t])return void Mp("ZLevel "+t+" has been used already");if(!Di(e))return void Mp("Layer of zlevel "+t+" is not valid");if(r>0&&t>i[0]){for(o=0;r-1>o&&!(i[o]<t&&i[o+1]>t);o++);a=n[i[o]]}if(i.splice(o+1,0,t),n[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)},eachLayer:function(t,e){var n,i,r=this._zlevelList;for(i=0;i<r.length;i++)n=r[i],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){var n,i,r,a=this._zlevelList;for(r=0;r<a.length;r++)i=a[r],n=this._layers[i],n.__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){var n,i,r,a=this._zlevelList;for(r=0;r<a.length;r++)i=a[r],n=this._layers[i],n.__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){a&&(a.__endIndex!==t&&(a.__dirty=!0),a.__endIndex=t)}if(this.eachBuiltinLayer(function(t){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++){var i=t[n];if(i.zlevel!==t[n-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}for(var r,a=null,o=0,n=0;n<t.length;n++){var s,i=t[n],l=i.zlevel;r!==l&&(r=l,o=0),i.incremental?(s=this.getLayer(l+hg,this._needsManuallyCompositing),s.incremental=!0,o=1):s=this.getLayer(l+(o>0?ug:0),this._needsManuallyCompositing),s.__builtin__||Mp("ZLevel "+l+" has been used by unkown layer "+s.id),s!==a&&(s.__used=!0,s.__startIndex!==n&&(s.__dirty=!0),s.__startIndex=n,s.__drawIndex=s.incremental?-1:n,e(n),a=s),i.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=n))}e(n),this.eachBuiltinLayer(function(t){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?r(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var a=this._zlevelList[i];if(a===t||a===t+ug){var o=this._layers[a];r(o,n[t],!0)}}}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(u(n,t),1))},resize:function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!==t||e!==this._height){n.style.width=t+"px",n.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);f(this._progressiveLayers,function(n){n.resize(t,e)}),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(lg).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[lg].dom;var e=new Xp("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var l=o[s];this._doPaintEl(l,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[i]||ki(s[n])||ki(o.style[n]))-(ki(s[r])||0)-(ki(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),r=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,l=a.shadowOffsetY*e,u=a.hasStroke()?a.lineWidth:0,h=Math.max(u/2,-s+o),c=Math.max(u/2,s+o),d=Math.max(u/2,-l+o),f=Math.max(u/2,l+o),p=r.width+h+c,g=r.height+d+f;n.width=p*e,n.height=g*e,i.scale(e,e),i.clearRect(0,0,p,g),i.dpr=e;var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[h-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);var m=Ii,y=new m({style:{x:0,y:0,image:n}});return null!=v.position&&(y.position=t.position=v.position),null!=v.rotation&&(y.rotation=t.rotation=v.rotation),null!=v.scale&&(y.scale=t.scale=v.scale),y}};var pg=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,Ff.call(this)};pg.prototype={constructor:pg,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=u(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],a=[],o=0;i>o;o++){var s=n[o],l=s.step(t,e);l&&(r.push(l),a.push(s))}for(var o=0;i>o;)n[o]._needsRemove?(n[o]=n[i-1],n.pop(),i--):o++;i=r.length;for(var o=0;i>o;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(Yp(t),!e._paused&&e._update())}var e=this;this._running=!0,Yp(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var n=new _p(t,e.loop,e.getter,e.setter);return this.addAnimator(n),n}},c(pg,Ff);var gg=300,vg=vf.domSupported,mg=function(){var t=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e=["touchstart","touchend","touchmove"],n={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=p(t,function(t){var e=t.replace("mouse","pointer");return n.hasOwnProperty(e)?e:t});return{mouse:t,touch:e,pointer:i}}(),yg={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},_g=Hi.prototype;_g.stopPropagation=_g.stopImmediatePropagation=_g.preventDefault=H;var xg={mousedown:function(t){t=be(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=be(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||Yi(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=be(this.dom,t),Yi(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=be(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=Ni(this,e),this.trigger("mouseout",t)},touchstart:function(t){t=be(this.dom,t),Ri(t),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),xg.mousemove.call(this,t),xg.mousedown.call(this,t)},touchmove:function(t){t=be(this.dom,t),Ri(t),this.handler.processGesture(t,"change"),xg.mousemove.call(this,t)},touchend:function(t){t=be(this.dom,t),Ri(t),this.handler.processGesture(t,"end"),xg.mouseup.call(this,t),+new Date-this._lastTouchMoment<gg&&xg.click.call(this,t)},pointerdown:function(t){xg.mousedown.call(this,t)},pointermove:function(t){zi(t)||xg.mousemove.call(this,t)},pointerup:function(t){xg.mouseup.call(this,t)},pointerout:function(t){zi(t)||xg.mouseout.call(this,t)}};f(["click","mousewheel","dblclick","contextmenu"],function(t){xg[t]=function(e){e=be(this.dom,e),this.trigger(t,e)}});var wg={pointermove:function(t){zi(t)||wg.mousemove.call(this,t)},pointerup:function(t){wg.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;Yi(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}},bg=ji.prototype;bg.dispose=function(){Xi(this._localHandlerScope),vg&&Xi(this._globalHandlerScope)},bg.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},c(ji,Ff);var Sg=!vf.canvasSupported,Mg={canvas:fg},Cg={},Tg="4.3.1",Ig=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,r=new Op,a=n.renderer;if(Sg){if(!Mg.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");a="vml"}else a&&Mg[a]||(a="canvas");var o=new Mg[a](e,r,n,t);this.storage=r,this.painter=o;var s=vf.node||vf.worker?null:new ji(o.getViewportRoot(),o.root);this.handler=new $f(r,o,s,o.root),this.animation=new pg({stage:{update:y(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,u=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(i)},r.addToStorage=function(t){u.call(r,t),t.addSelfToZr(i)}};Ig.prototype={constructor:Ig,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var n=this.painter.addHover(t,e);return this.refreshHover(),n}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,Qi(this.id)}};var kg=(Object.freeze||Object)({version:Tg,init:qi,dispose:Zi,getInstance:$i,registerPainter:Ki}),Dg=f,Ag=S,Pg=x,Lg="series\x00",Og=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],Bg=0,zg=".",Eg="___EC__COMPONENT__CONTAINER___",Rg=0,Fg=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);
return function(e,n,i){for(var r={},a=0;a<t.length;a++){var o=t[a][1];if(!(n&&u(n,o)>=0||i&&u(i,o)<0)){var s=e.getShallow(o);null!=s&&(r[t[a][0]]=s)}}return r}},Ng=Fg([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Hg={getLineStyle:function(t){var e=Ng(this,t);return e.lineDash=this.getLineDash(e.lineWidth),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"===e||null==e?!1:"dashed"===e?[i,i]:[n,n]}},Wg=Fg([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Vg={getAreaStyle:function(t,e){return Wg(this,t,e)}},Gg=Math.pow,Xg=Math.sqrt,Yg=1e-8,Ug=1e-4,jg=Xg(3),qg=1/3,Zg=W(),$g=W(),Kg=W(),Qg=Math.min,Jg=Math.max,tv=Math.sin,ev=Math.cos,nv=2*Math.PI,iv=W(),rv=W(),av=W(),ov=[],sv=[],lv={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},uv=[],hv=[],cv=[],dv=[],fv=Math.min,pv=Math.max,gv=Math.cos,vv=Math.sin,mv=Math.sqrt,yv=Math.abs,_v="undefined"!=typeof Float32Array,xv=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};xv.prototype={constructor:xv,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,n){n=n||0,this._ux=yv(n/bp/t)||0,this._uy=yv(n/bp/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(lv.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=yv(t-this._xi)>this._ux||yv(e-this._yi)>this._uy||this._len<5;return this.addData(lv.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,a){return this.addData(lv.C,t,e,n,i,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,a):this._ctx.bezierCurveTo(t,e,n,i,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,n,i){return this.addData(lv.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,a){return this.addData(lv.A,t,e,n,n,i,r-i,0,a?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,a),this._xi=gv(r)*n+t,this._yi=vv(r)*n+e,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(lv.R,t,e,n,i),this},closePath:function(){this.addData(lv.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!_v||(this.data=new Float32Array(e));for(var n=0;e>n;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;e>r;r++)n+=t[r].len();_v&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(var r=0;e>r;r++)for(var a=t[r].data,o=0;o<a.length;o++)this.data[i++]=a[o];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,u=this._yi,h=t-l,c=e-u,d=mv(h*h+c*c),f=l,p=u,g=o.length;for(h/=d,c/=d,0>a&&(a=r+a),a%=r,f-=a*h,p-=a*c;h>0&&t>=f||0>h&&f>=t||0===h&&(c>0&&e>=p||0>c&&p>=e);)i=this._dashIdx,n=o[i],f+=h*n,p+=c*n,this._dashIdx=(i+1)%g,h>0&&l>f||0>h&&f>l||c>0&&u>p||0>c&&p>u||s[i%2?"moveTo":"lineTo"](h>=0?fv(f,t):pv(f,t),c>=0?fv(p,e):pv(p,e));h=f-t,c=p-e,this._dashOffset=-mv(h*h+c*c)},_dashedBezierTo:function(t,e,n,i,r,a){var o,s,l,u,h,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,v=this._yi,m=Sr,y=0,_=this._dashIdx,x=f.length,w=0;for(0>d&&(d=c+d),d%=c,o=0;1>o;o+=.1)s=m(g,t,n,r,o+.1)-m(g,t,n,r,o),l=m(v,e,i,a,o+.1)-m(v,e,i,a,o),y+=mv(s*s+l*l);for(;x>_&&(w+=f[_],!(w>d));_++);for(o=(w-d)/y;1>=o;)u=m(g,t,n,r,o),h=m(v,e,i,a,o),_%2?p.moveTo(u,h):p.lineTo(u,h),o+=f[_]/y,_=(_+1)%x;_%2!==0&&p.lineTo(r,a),s=r-u,l=a-h,this._dashOffset=-mv(s*s+l*l)},_dashedQuadraticTo:function(t,e,n,i){var r=n,a=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,_v&&(this.data=new Float32Array(t)))},getBoundingRect:function(){uv[0]=uv[1]=cv[0]=cv[1]=Number.MAX_VALUE,hv[0]=hv[1]=dv[0]=dv[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,n=0,i=0,r=0,a=0;a<t.length;){var o=t[a++];switch(1===a&&(e=t[a],n=t[a+1],i=e,r=n),o){case lv.M:i=t[a++],r=t[a++],e=i,n=r,cv[0]=i,cv[1]=r,dv[0]=i,dv[1]=r;break;case lv.L:Er(e,n,t[a],t[a+1],cv,dv),e=t[a++],n=t[a++];break;case lv.C:Rr(e,n,t[a++],t[a++],t[a++],t[a++],t[a],t[a+1],cv,dv),e=t[a++],n=t[a++];break;case lv.Q:Fr(e,n,t[a++],t[a++],t[a],t[a+1],cv,dv),e=t[a++],n=t[a++];break;case lv.A:var s=t[a++],l=t[a++],u=t[a++],h=t[a++],c=t[a++],d=t[a++]+c;a+=1;var f=1-t[a++];1===a&&(i=gv(c)*u+s,r=vv(c)*h+l),Nr(s,l,u,h,c,d,f,cv,dv),e=gv(d)*u+s,n=vv(d)*h+l;break;case lv.R:i=e=t[a++],r=n=t[a++];var p=t[a++],g=t[a++];Er(i,r,i+p,r+g,cv,dv);break;case lv.Z:e=i,n=r}oe(uv,uv,cv),se(hv,hv,dv)}return 0===a&&(uv[0]=uv[1]=hv[0]=hv[1]=0),new Tn(uv[0],uv[1],hv[0]-uv[0],hv[1]-uv[1])},rebuildPath:function(t){for(var e,n,i,r,a,o,s=this.data,l=this._ux,u=this._uy,h=this._len,c=0;h>c;){var d=s[c++];switch(1===c&&(i=s[c],r=s[c+1],e=i,n=r),d){case lv.M:e=i=s[c++],n=r=s[c++],t.moveTo(i,r);break;case lv.L:a=s[c++],o=s[c++],(yv(a-i)>l||yv(o-r)>u||c===h-1)&&(t.lineTo(a,o),i=a,r=o);break;case lv.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case lv.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case lv.A:var f=s[c++],p=s[c++],g=s[c++],v=s[c++],m=s[c++],y=s[c++],_=s[c++],x=s[c++],w=g>v?g:v,b=g>v?1:g/v,S=g>v?v/g:1,M=Math.abs(g-v)>.001,C=m+y;M?(t.translate(f,p),t.rotate(_),t.scale(b,S),t.arc(0,0,w,m,C,1-x),t.scale(1/b,1/S),t.rotate(-_),t.translate(-f,-p)):t.arc(f,p,w,m,C,1-x),1===c&&(e=gv(m)*g+f,n=vv(m)*v+p),i=gv(C)*g+f,r=vv(C)*v+p;break;case lv.R:e=i=s[c],n=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case lv.Z:t.closePath(),i=e,r=n}}}},xv.CMD=lv;var wv=2*Math.PI,bv=2*Math.PI,Sv=xv.CMD,Mv=2*Math.PI,Cv=1e-4,Tv=[-1,-1,-1],Iv=[-1,-1],kv=Gp.prototype.getCanvasPattern,Dv=Math.abs,Av=new xv(!0);ta.prototype={constructor:ta,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var n=this.style,i=this.path||Av,r=n.hasStroke(),a=n.hasFill(),o=n.fill,s=n.stroke,l=a&&!!o.colorStops,u=r&&!!s.colorStops,h=a&&!!o.image,c=r&&!!s.image;if(n.bind(t,this,e),this.setTransform(t),this.__dirty){var d;l&&(d=d||this.getBoundingRect(),this._fillGradient=n.getGradient(t,o,d)),u&&(d=d||this.getBoundingRect(),this._strokeGradient=n.getGradient(t,s,d))}l?t.fillStyle=this._fillGradient:h&&(t.fillStyle=kv.call(o,t)),u?t.strokeStyle=this._strokeGradient:c&&(t.strokeStyle=kv.call(s,t));var f=n.lineDash,p=n.lineDashOffset,g=!!t.setLineDash,v=this.getGlobalScale();if(i.setScale(v[0],v[1],this.segmentIgnoreThreshold),this.__dirtyPath||f&&!g&&r?(i.beginPath(t),f&&!g&&(i.setLineDash(f),i.setLineDashOffset(p)),this.buildPath(i,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a)if(null!=n.fillOpacity){var m=t.globalAlpha;t.globalAlpha=n.fillOpacity*n.opacity,i.fill(t),t.globalAlpha=m}else i.fill(t);if(f&&g&&(t.setLineDash(f),t.lineDashOffset=p),r)if(null!=n.strokeOpacity){var m=t.globalAlpha;t.globalAlpha=n.strokeOpacity*n.opacity,i.stroke(t),t.globalAlpha=m}else i.stroke(t);f&&g&&t.setLineDash([]),null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(){},createPathProxy:function(){this.path=new xv},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var i=this.path;i||(i=this.path=new xv),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){r.copy(t);var a=e.lineWidth,o=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),o>1e-10&&(r.width+=a/o,r.height+=a/o,r.x-=a/o/2,r.y-=a/o/2)}return r}return t},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var a=this.path.data;if(r.hasStroke()){var o=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(r.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),Jr(a,o/s,t,e)))return!0}if(r.hasFill())return Qr(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Ti.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(S(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&Dv(t[0]-1)>1e-10&&Dv(t[3]-1)>1e-10?Math.sqrt(Dv(t[0]*t[3]-t[2]*t[1])):1}},ta.extend=function(t){var e=function(e){ta.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var i=this.shape;for(var r in n)!i.hasOwnProperty(r)&&n.hasOwnProperty(r)&&(i[r]=n[r])}t.init&&t.init.call(this,e)};h(e,ta);for(var n in t)"style"!==n&&"shape"!==n&&(e.prototype[n]=t[n]);return e},h(ta,Ti);var Pv=xv.CMD,Lv=[[],[],[]],Ov=Math.sqrt,Bv=Math.atan2,zv=function(t,e){var n,i,r,a,o,s,l=t.data,u=Pv.M,h=Pv.C,c=Pv.L,d=Pv.R,f=Pv.A,p=Pv.Q;for(r=0,a=0;r<l.length;){switch(n=l[r++],a=r,i=0,n){case u:i=1;break;case c:i=1;break;case h:i=3;break;case p:i=2;break;case f:var g=e[4],v=e[5],m=Ov(e[0]*e[0]+e[1]*e[1]),y=Ov(e[2]*e[2]+e[3]*e[3]),_=Bv(-e[1]/y,e[0]/m);l[r]*=m,l[r++]+=g,l[r]*=y,l[r++]+=v,l[r++]*=m,l[r++]*=y,l[r++]+=_,l[r++]+=_,r+=2,a=r;break;case d:s[0]=l[r++],s[1]=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1],s[0]+=l[r++],s[1]+=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1]}for(o=0;i>o;o++){var s=Lv[o];s[0]=l[r++],s[1]=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1]}}},Ev=Math.sqrt,Rv=Math.sin,Fv=Math.cos,Nv=Math.PI,Hv=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},Wv=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(Hv(t)*Hv(e))},Vv=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Wv(t,e))},Gv=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Xv=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g,Yv=function(t){Ti.call(this,t)};Yv.prototype={constructor:Yv,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&li(n,!0),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;return null!=i&&(i+=""),Ci(i,n)?(this.setTransform(t),hi(this,t,i,n,null,e),void this.restoreTransform(t)):void(t.__attrCachedBy=Ep.NONE)},getBoundingRect:function(){var t=this.style;if(this.__dirty&&li(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var n=Yn(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(n.x+=t.x||0,n.y+=t.y||0,wi(t.textStroke,t.textStrokeWidth)){var i=t.textStrokeWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect}},h(Yv,Ti);var Uv=ta.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),jv=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],qv=function(t){return vf.browser.ie&&vf.browser.version>=11?function(){var e,n=this.__clipPaths,i=this.style;if(n)for(var r=0;r<n.length;r++){var a=n[r],o=a&&a.shape,s=a&&a.type;if(o&&("sector"===s&&o.startAngle===o.endAngle||"rect"===s&&(!o.width||!o.height))){for(var l=0;l<jv.length;l++)jv[l][2]=i[jv[l][0]],i[jv[l][0]]=jv[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(var l=0;l<jv.length;l++)i[jv[l][0]]=jv[l][2]}:t},Zv=ta.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:qv(ta.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(o),h=Math.sin(o);t.moveTo(u*r+n,h*r+i),t.lineTo(u*a+n,h*a+i),t.arc(n,i,a,o,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,o,l),t.closePath()}}),$v=ta.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}}),Kv=function(t,e){for(var n=t.length,i=[],r=0,a=1;n>a;a++)r+=ee(t[a-1],t[a]);var o=r/2;o=n>o?n:o;for(var a=0;o>a;a++){var s,l,u,h=a/(o-1)*(e?n:n-1),c=Math.floor(h),d=h-c,f=t[c%n];e?(s=t[(c-1+n)%n],l=t[(c+1)%n],u=t[(c+2)%n]):(s=t[0===c?c:c-1],l=t[c>n-2?n-1:c+1],u=t[c>n-3?n-1:c+2]);var p=d*d,g=d*p;i.push([sa(s[0],f[0],l[0],u[0],d,p,g),sa(s[1],f[1],l[1],u[1],d,p,g)])}return i},Qv=function(t,e,n,i){var r,a,o,s,l=[],u=[],h=[],c=[];if(i){o=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;f>d;d++)oe(o,o,t[d]),se(s,s,t[d]);oe(o,o,i[0]),se(s,s,i[1])}for(var d=0,f=t.length;f>d;d++){var p=t[d];if(n)r=t[d?d-1:f-1],a=t[(d+1)%f];else{if(0===d||d===f-1){l.push(G(t[d]));continue}r=t[d-1],a=t[d+1]}j(u,a,r),J(u,u,e);var g=ee(p,r),v=ee(p,a),m=g+v;0!==m&&(g/=m,v/=m),J(h,u,-g),J(c,u,v);var y=Y([],p,h),_=Y([],p,c);i&&(se(y,y,o),oe(y,y,s),se(_,_,o),oe(_,_,s)),l.push(y),l.push(_)}return n&&l.push(l.shift()),l},Jv=ta.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){la(t,e,!0)}}),tm=ta.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){la(t,e,!1)}}),em=Math.round,nm={},im=ta.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n,i,r,a;this.subPixelOptimize?(ha(nm,e,this.style),n=nm.x,i=nm.y,r=nm.width,a=nm.height,nm.r=e.r,e=nm):(n=e.x,i=e.y,r=e.width,a=e.height),e.r?si(t,e):t.rect(n,i,r,a),t.closePath()}}),rm={},am=ta.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n,i,r,a;this.subPixelOptimize?(ua(rm,e,this.style),n=rm.x1,i=rm.y1,r=rm.x2,a=rm.y2):(n=e.x1,i=e.y1,r=e.x2,a=e.y2);var o=e.percent;0!==o&&(t.moveTo(n,i),1>o&&(r=n*(1-o)+r*o,a=i*(1-o)+a*o),t.lineTo(r,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),om=[],sm=ta.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,a=e.y2,o=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(n,i),null==l||null==u?(1>h&&(Or(n,o,r,h,om),o=om[1],r=om[2],Or(i,s,a,h,om),s=om[1],a=om[2]),t.quadraticCurveTo(o,s,r,a)):(1>h&&(Ir(n,o,l,r,h,om),o=om[1],l=om[2],r=om[3],Ir(i,s,u,a,h,om),s=om[1],u=om[2],a=om[3]),t.bezierCurveTo(o,s,l,u,r,a)))},pointAt:function(t){return da(this.shape,t,!1)},tangentAt:function(t){var e=da(this.shape,t,!0);return te(e,e)}}),lm=ta.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),u=Math.sin(a);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,a,o,!s)}}),um=ta.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),ta.prototype.getBoundingRect.call(this)}}),hm=function(t){this.colorStops=t||[]};hm.prototype={constructor:hm,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var cm=function(t,e,n,i,r,a){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=a||!1,hm.call(this,r)};cm.prototype={constructor:cm},h(cm,hm);var dm=function(t,e,n,i,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=r||!1,hm.call(this,i)};dm.prototype={constructor:dm},h(dm,hm),fa.prototype.incremental=!0,fa.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},fa.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},fa.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},fa.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},fa.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},fa.prototype.brush=function(t){for(var e=this._cursor;e<this._displayables.length;e++){var n=this._displayables[e];n.beforeBrush&&n.beforeBrush(t),n.brush(t,e===this._cursor?null:this._displayables[e-1]),n.afterBrush&&n.afterBrush(t)}this._cursor=e;for(var e=0;e<this._temporaryDisplayables.length;e++){var n=this._temporaryDisplayables[e];n.beforeBrush&&n.beforeBrush(t),n.brush(t,0===e?null:this._temporaryDisplayables[e-1]),n.afterBrush&&n.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var fm=[];fa.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Tn(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(fm)),t.union(i)}this._rect=t}return this._rect},fa.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();if(i.contain(n[0],n[1]))for(var r=0;r<this._displayables.length;r++){var a=this._displayables[r];if(a.contain(t,e))return!0}return!1},h(fa,Ti);var pm=Math.max,gm=Math.min,vm={},mm=1,ym={color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},_m="emphasis",xm="normal",wm=1,bm={},Sm={},Mm=oa,Cm=ca,Tm=F(),Im=0;va("circle",Uv),va("sector",Zv),va("ring",$v),va("polygon",Jv),va("polyline",tm),va("rect",im),va("line",am),va("bezierCurve",sm),va("arc",lm);var km=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:mm,CACHED_LABEL_STYLE_PROPERTIES:ym,extendShape:pa,extendPath:ga,registerShape:va,getShapeClass:ma,makePath:ya,makeImage:_a,mergePath:Mm,resizePath:wa,subPixelOptimizeLine:ba,subPixelOptimizeRect:Sa,subPixelOptimize:Cm,setElementHoverStyle:Pa,setHoverStyle:Ra,setAsHighDownDispatcher:Fa,isHighDownDispatcher:Na,getHighlightDigit:Ha,setLabelStyle:Wa,modifyLabelStyle:Va,setTextStyle:Ga,setText:Xa,getFont:Ka,updateProps:Ja,initProps:to,getTransform:eo,applyTransform:no,transformDirection:io,groupTransition:ro,clipPointsByRect:ao,clipRectByRect:oo,createIcon:so,linePolygonIntersect:lo,lineLineIntersect:uo,Group:Ap,Image:Ii,Text:Yv,Circle:Uv,Sector:Zv,Ring:$v,Polygon:Jv,Polyline:tm,Rect:im,Line:am,BezierCurve:sm,Arc:lm,IncrementalDisplayable:fa,CompoundPath:um,LinearGradient:cm,RadialGradient:dm,BoundingRect:Tn}),Dm=["textStyle","color"],Am={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(Dm):null)},getFont:function(){return Ka({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return Yn(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},Pm=Fg([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),Lm={getItemStyle:function(t,e){var n=Pm(this,t,e),i=this.getBorderLineDash();return i&&(n.lineDash=i),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},Om=c,Bm=lr();fo.prototype={constructor:fo,init:null,mergeOption:function(t){r(this.option,t,!0)},get:function(t,e){return null==t?this.option:po(this.option,this.parsePath(t),!e&&go(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&go(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n,i=null==t?this.option:po(this.option,t=this.parsePath(t));return e=e||(n=go(this,t))&&n.getModel(t),new fo(i,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(i(this.option))},setReadOnly:function(){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){Bm(this).getParent=t},isAnimationEnabled:function(){if(!vf.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},vr(fo),mr(fo),Om(fo,Hg),Om(fo,Vg),Om(fo,Am),Om(fo,Lm);var zm=0,Em=1e-4,Rm=9007199254740991,Fm=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/,Nm=(Object.freeze||Object)({linearMap:xo,parsePercent:wo,round:bo,asc:So,getPrecision:Mo,getPrecisionSafe:Co,getPixelPrecision:To,getPercentWithPrecision:Io,MAX_SAFE_INTEGER:Rm,remRadian:ko,isRadianAroundZero:Do,parseDate:Ao,quantity:Po,quantityExponent:Lo,nice:Oo,quantile:Bo,reformIntervals:zo,isNumeric:Eo}),Hm=L,Wm=/([&<>"'])/g,Vm={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Gm=["a","b","c","d","e","f","g"],Xm=function(t,e){return"{"+t+(null==e?"":e)+"}"},Ym=Kn,Um=(Object.freeze||Object)({addCommas:Ro,toCamelCase:Fo,normalizeCssArray:Hm,encodeHTML:No,formatTpl:Ho,formatTplSimple:Wo,getTooltipMarker:Vo,formatTime:Xo,capitalFirst:Yo,truncateText:Ym,getTextBoundingRect:Uo,getTextRect:jo,windowOpen:qo}),jm=f,qm=["left","right","top","bottom","width","height"],Zm=[["width","left","right"],["height","top","bottom"]],$m=Zo,Km=(_(Zo,"vertical"),_(Zo,"horizontal"),{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}),Qm=lr(),Jm=fo.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){fo.call(this,t,e,n,i),this.uid=vo("ec_cpt_model")},init:function(t,e,n){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?Qo(t):{},a=e.getTheme();r(t,a.get(this.mainType)),r(t,this.getDefaultOption()),n&&Ko(t,i,n)},mergeOption:function(t){r(this.option,t,!0);var e=this.layoutMode;e&&Ko(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){var t=Qm(this);if(!t.defaultOption){for(var e=[],n=this.constructor;n;){var i=n.prototype.defaultOption;i&&e.push(i),n=n.superClass}for(var a={},o=e.length-1;o>=0;o--)a=r(a,e[o],!0);t.defaultOption=a}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});xr(Jm,{registerWhenExtend:!0}),mo(Jm),yo(Jm,ts),c(Jm,Km);var ty="";"undefined"!=typeof navigator&&(ty=navigator.platform||"");var ey={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:ty.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},ny=lr(),iy={clearColorPalette:function(){ny(this).colorIdx=0,ny(this).colorNameMap={}},getColorFromPalette:function(t,e,n){e=e||this;var i=ny(e),r=i.colorIdx||0,a=i.colorNameMap=i.colorNameMap||{};if(a.hasOwnProperty(t))return a[t];var o=Ji(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=n&&s?es(s,n):o;if(l=l||o,l&&l.length){var u=l[r];return t&&(a[t]=u),i.colorIdx=(r+1)%l.length,u}}},ry="original",ay="arrayRows",oy="objectRows",sy="keyedColumns",ly="unknown",uy="typedArray",hy="column",cy="row";ns.seriesDataToSource=function(t){return new ns({data:t,sourceFormat:C(t)?uy:ry,fromDataset:!1})},mr(ns);var dy={Must:1,Might:2,Not:3},fy=lr(),py="\x00_ec_inner",gy=fo.extend({init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new fo(n),this._optionManager=i},setOption:function(t,e){O(!(py in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var i=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(i)):ms.call(this,i),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=n.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var a=n.getMediaOption(this,this._api);a.length&&f(a,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,i){var r=Ji(t[e]),s=ir(a.get(e),r);rr(s),f(s,function(t){var n=t.option;S(n)&&(t.keyInfo.mainType=e,t.keyInfo.subType=_s(e,n,t.exist))});var l=ys(a,i);n[e]=[],a.set(e,[]),f(s,function(t,i){var r=t.exist,s=t.option;if(O(S(s)||r,"Empty component definition"),s){var u=Jm.getClass(e,t.keyInfo.subType,!0);if(r&&r.constructor===u)r.name=t.keyInfo.name,r.mergeOption(s,this),r.optionUpdated(s,!1);else{var h=o({dependentModels:l,componentIndex:i},t.keyInfo);r=new u(s,this,this,h),o(r,h),r.init(s,this,this,h),r.optionUpdated(null,!0)}}else r.mergeOption({},this),r.optionUpdated({},!1);a.get(e)[i]=r,n[e][i]=r.option},this),"series"===e&&xs(this,a.get("series"))}var n=this.option,a=this._componentsMap,s=[];as(this),f(t,function(t,e){null!=t&&(Jm.hasClass(e)?e&&s.push(e):n[e]=null==n[e]?i(t):r(n[e],t,!0))}),Jm.topologicalTravel(s,Jm.getAllClassMainTypes(),e,this),this._seriesIndicesMap=F(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=i(this.option);return f(t,function(e,n){if(Jm.hasClass(n)){for(var e=Ji(e),i=e.length-1;i>=0;i--)or(e[i])&&e.splice(i,1);t[n]=e}}),delete t[py],t},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);return n?n[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n=t.index,i=t.id,r=t.name,a=this._componentsMap.get(e);if(!a||!a.length)return[];var o;if(null!=n)x(n)||(n=[n]),o=v(p(n,function(t){return a[t]}),function(t){return!!t});else if(null!=i){var s=x(i);o=v(a,function(t){return s&&u(i,t.id)>=0||!s&&t.id===i})}else if(null!=r){var l=x(r);o=v(a,function(t){return l&&u(r,t.name)>=0||!l&&t.name===r})}else o=a.slice();return ws(o,t)},findComponents:function(t){function e(t){var e=r+"Index",n=r+"Id",i=r+"Name";return!t||null==t[e]&&null==t[n]&&null==t[i]?null:{mainType:r,index:t[e],id:t[n],name:t[i]}}function n(e){return t.filter?v(e,t.filter):e}var i=t.query,r=t.mainType,a=e(i),o=a?this.queryComponents(a):this._componentsMap.get(r);return n(ws(o,t))},eachComponent:function(t,e,n){var i=this._componentsMap;if("function"==typeof t)n=e,e=t,i.each(function(t,i){f(t,function(t,r){e.call(n,i,t,r)})});else if(b(t))f(i.get(t),e,n);else if(S(t)){var r=this.findComponents(t);f(r,e,n)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){f(this._seriesIndices,function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)},this)},eachRawSeries:function(t,e){f(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,n){f(this._seriesIndices,function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)},this)},eachRawSeriesByType:function(t,e,n){return f(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){var n=v(this._componentsMap.get("series"),t,e);xs(this,n)},restoreData:function(t){var e=this._componentsMap;xs(this,e.get("series"));var n=[];e.each(function(t,e){n.push(e)}),Jm.topologicalTravel(n,Jm.getAllClassMainTypes(),function(n){f(e.get(n),function(e){("series"!==n||!gs(e,t))&&e.restoreData()})})}});c(gy,iy);var vy=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"],my={};Ss.prototype={constructor:Ss,create:function(t,e){var n=[];f(my,function(i){var r=i.create(t,e);n=n.concat(r||[])}),this._coordinateSystems=n},update:function(t,e){f(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},Ss.register=function(t,e){my[t]=e},Ss.get=function(t){return my[t]};var yy=f,_y=i,xy=p,wy=r,by=/^(min|max)?(.+)$/;Ms.prototype={constructor:Ms,setOption:function(t,e){t&&f(Ji(t.series),function(t){t&&t.data&&C(t.data)&&z(t.data)}),t=_y(t);var n=this._optionBackup,i=Cs.call(this,t,e,!n);this._newBaseOption=i.baseOption,n?(Ds(n.baseOption,i.baseOption),i.timelineOptions.length&&(n.timelineOptions=i.timelineOptions),i.mediaList.length&&(n.mediaList=i.mediaList),i.mediaDefault&&(n.mediaDefault=i.mediaDefault)):this._optionBackup=i},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=xy(e.timelineOptions,_y),this._mediaList=xy(e.mediaList,_y),this._mediaDefault=_y(e.mediaDefault),this._currentMediaIndices=[],_y(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=_y(n[i.getCurrentIndex()],!0))
}return e},getMediaOption:function(){var t=this._api.getWidth(),e=this._api.getHeight(),n=this._mediaList,i=this._mediaDefault,r=[],a=[];if(!n.length&&!i)return a;for(var o=0,s=n.length;s>o;o++)Ts(n[o].query,t,e)&&r.push(o);return!r.length&&i&&(r=[-1]),r.length&&!ks(r,this._currentMediaIndices)&&(a=xy(r,function(t){return _y(-1===t?i.option:n[t].option)})),this._currentMediaIndices=r,a}};var Sy=f,My=S,Cy=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"],Ty=function(t,e){Sy(Es(t.series),function(t){My(t)&&zs(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),Sy(n,function(e){Sy(Es(t[e]),function(t){t&&(Os(t,"axisLabel"),Os(t.axisPointer,"label"))})}),Sy(Es(t.parallel),function(t){var e=t&&t.parallelAxisDefault;Os(e,"axisLabel"),Os(e&&e.axisPointer,"label")}),Sy(Es(t.calendar),function(t){Ps(t,"itemStyle"),Os(t,"dayLabel"),Os(t,"monthLabel"),Os(t,"yearLabel")}),Sy(Es(t.radar),function(t){Os(t,"name")}),Sy(Es(t.geo),function(t){My(t)&&(Bs(t),Sy(Es(t.regions),function(t){Bs(t)}))}),Sy(Es(t.timeline),function(t){Bs(t),Ps(t,"label"),Ps(t,"itemStyle"),Ps(t,"controlStyle",!0);var e=t.data;x(e)&&f(e,function(t){S(t)&&(Ps(t,"label"),Ps(t,"itemStyle"))})}),Sy(Es(t.toolbox),function(t){Ps(t,"iconStyle"),Sy(t.feature,function(t){Ps(t,"iconStyle")})}),Os(Rs(t.axisPointer),"label"),Os(Rs(t.tooltip).axisPointer,"label")},Iy=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],ky=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Dy=function(t,e){Ty(t,e),t.series=Ji(t.series),f(t.series,function(t){if(S(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e)null!=t.clockWise&&(t.clockwise=t.clockWise);else if("gauge"===e){var n=Fs(t,"pointer.color");null!=n&&Ns(t,"itemStyle.color",n)}Hs(t)}}),t.dataRange&&(t.visualMap=t.dataRange),f(ky,function(e){var n=t[e];n&&(x(n)||(n=[n]),f(n,function(t){Hs(t)}))})},Ay=function(t){var e=F();t.eachSeries(function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),a={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!a.stackedDimension||!a.isStackedByIndex&&!a.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(a)}}),e.each(Ws)},Py=Vs.prototype;Py.pure=!1,Py.persistent=!0,Py.getSource=function(){return this._source};var Ly={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:Ys},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],n=this._data,i=0;i<n.length;i++){var r=n[i];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:Gs,getItem:Xs,appendData:Ys},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],n=this._source.dimensionsDefine,i=0;i<n.length;i++){var r=this._data[n[i].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;f(t,function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])})}},original:{count:Gs,getItem:Xs,appendData:Ys},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var n=this._dimSize*t,i=0;i<this._dimSize;i++)e[i]=this._data[n+i];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}},Oy={arrayRows:Us,objectRows:function(t,e,n,i){return null!=n?t[i]:t},keyedColumns:Us,original:function(t,e,n){var i=er(t);return null!=n&&i instanceof Array?i[n]:i},typedArray:Us},By={arrayRows:js,objectRows:function(t,e){return qs(t[e],this._dimensionInfos[e])},keyedColumns:js,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&nr(t)&&(this.hasItemOption=!0),qs(r instanceof Array?r[i]:r,this._dimensionInfos[e])},typedArray:function(t,e,n,i){return t[i]}},zy=/\{@(.+?)\}/g,Ey={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),a=n.getName(t),o=n.getRawDataItem(t),s=n.getItemVisual(t,"color"),l=n.getItemVisual(t,"borderColor"),u=this.ecModel.getComponent("tooltip"),h=u&&u.get("renderMode"),c=fr(h),d=this.mainType,f="series"===d,p=n.userOutput;return{componentType:d,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:f?this.subType:null,seriesIndex:this.seriesIndex,seriesId:f?this.id:null,seriesName:f?this.name:null,name:a,dataIndex:r,data:o,dataType:e,value:i,color:s,borderColor:l,dimensionNames:p?p.dimensionNames:null,encode:p?p.encode:null,marker:Vo({color:s,renderMode:c}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,i,r){e=e||"normal";var a=this.getData(n),o=a.getItemModel(t),s=this.getDataParams(t,n);null!=i&&s.value instanceof Array&&(s.value=s.value[i]);var l=o.get("normal"===e?[r||"label","formatter"]:[e,r||"label","formatter"]);if("function"==typeof l)return s.status=e,s.dimensionIndex=i,l(s);if("string"==typeof l){var u=Ho(l,s);return u.replace(zy,function(e,n){var i=n.length;return"["===n.charAt(0)&&"]"===n.charAt(i-1)&&(n=+n.slice(1,i-1)),Zs(a,t,n)})}},getRawValue:function(t,e){return Zs(this.getData(e),t)},formatTooltip:function(){}},Ry=Ks.prototype;Ry.perform=function(t){function e(t){return!(t>=1)&&(t=1),t}var n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var r=this.context;r.data=r.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!i&&(a=this._plan(this.context));var o=e(this._modBy),s=this._modDataCount||0,l=e(t&&t.modBy),u=t&&t.modDataCount||0;(o!==l||s!==u)&&(a="reset");var h;(this._dirty||"reset"===a)&&(this._dirty=!1,h=Js(this,i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(h||f>d)){var p=this._progress;if(x(p))for(var g=0;g<p.length;g++)Qs(this,p[g],d,f,l,u);else Qs(this,p,d,f,l,u)}this._dueIndex=f;var v=null!=this._settedOutputEnd?this._settedOutputEnd:f;this._outputDueEnd=v}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var Fy=function(){function t(){return n>i?i++:null}function e(){var t=i%o*r+Math.ceil(i/o),e=i>=n?null:a>t?t:i;return i++,e}var n,i,r,a,o,s={reset:function(l,u,h,c){i=l,n=u,r=h,a=c,o=Math.ceil(a/r),s.next=r>1&&a>0?e:t}};return s}();Ry.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},Ry.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},Ry.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},Ry.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},Ry.getUpstream=function(){return this._upstream},Ry.getDownstream=function(){return this._downstream},Ry.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Ny=lr(),Hy=Jm.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=$s({count:nl,reset:il}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),os(this);var i=this.getInitialData(t,n);al(i,this),this.dataTask.context.data=i,Ny(this).dataBeforeProcessed=i,tl(this)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?Qo(t):{},a=this.subType;Jm.hasClass(a)&&(a+="Series"),r(t,e.getTheme().get(this.subType)),r(t,this.getDefaultOption()),tr(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Ko(t,i,n)},mergeOption:function(t,e){t=r(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&Ko(this.option,t,n),os(this);var i=this.getInitialData(t,e);al(i,this),this.dataTask.dirty(),this.dataTask.context.data=i,Ny(this).dataBeforeProcessed=i,tl(this)},fillDataTextStyle:function(t){if(t&&!C(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&tr(t[n],"label",e)},getInitialData:function(){},appendData:function(t){var e=this.getRawData();e.appendData(t.data)},getData:function(t){var e=sl(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return Ny(this).data},setData:function(t){var e=sl(this);if(e){var n=e.context;n.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),n.outputData=t,e!==this.dataTask&&(n.data=t)}Ny(this).data=t},getSource:function(){return rs(this)},getRawData:function(){return Ny(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,n,i){function r(n){function r(t,n){var r=c.getDimensionInfo(n);if(r&&r.otherDims.tooltip!==!1){var d=r.type,f="sub"+o.seriesIndex+"at"+h,p=Vo({color:y,type:"subItem",renderMode:i,markerId:f}),g="string"==typeof p?p:p.content,v=(a?g+No(r.displayName||"-")+": ":"")+No("ordinal"===d?t+"":"time"===d?e?"":Xo("yyyy/MM/dd hh:mm:ss",t):Ro(t));v&&s.push(v),l&&(u[f]=y,++h)}}var a=g(n,function(t,e,n){var i=c.getDimensionInfo(n);return t|=i&&i.tooltip!==!1&&null!=i.displayName},0),s=[];d.length?f(d,function(e){r(Zs(c,t,e),e)}):f(n,r);var p=a?l?"\n":"<br/>":"",v=p+s.join(p||", ");return{renderMode:i,content:v,style:u}}function a(t){return{renderMode:i,content:No(Ro(t)),style:u}}var o=this;i=i||"html";var s="html"===i?"<br/>":"\n",l="richText"===i,u={},h=0,c=this.getData(),d=c.mapDimension("defaultedTooltip",!0),p=d.length,v=this.getRawValue(t),m=x(v),y=c.getItemVisual(t,"color");S(y)&&y.colorStops&&(y=(y.colorStops[0]||{}).color),y=y||"transparent";var _=p>1||m&&!p?r(v):a(p?Zs(c,t,d[0]):m?v[0]:v),w=_.content,b=o.seriesIndex+"at"+h,M=Vo({color:y,type:"item",renderMode:i,markerId:b});u[b]=y,++h;var C=c.getName(t),T=this.name;ar(this)||(T=""),T=T?No(T)+(e?": ":s):"";var I="string"==typeof M?M:M.content,k=e?I+T+w:T+I+(C?No(C)+": "+w:w);return{html:k,markers:u}},isAnimationEnabled:function(){if(vf.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,n){var i=this.ecModel,r=iy.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});c(Hy,Ey),c(Hy,iy);var Wy=function(){this.group=new Ap,this.uid=vo("viewComponent")};Wy.prototype={constructor:Wy,init:function(){},render:function(){},dispose:function(){},filterForExposedEvent:null};var Vy=Wy.prototype;Vy.updateView=Vy.updateLayout=Vy.updateVisual=function(){},vr(Wy),xr(Wy,{registerWhenExtend:!0});var Gy=function(){var t=lr();return function(e){var n=t(e),i=e.pipelineContext,r=n.large,a=n.progressiveRender,o=n.large=i&&i.large,s=n.progressiveRender=i&&i.progressiveRender;return!!(r^o||a^s)&&"reset"}},Xy=lr(),Yy=Gy();ll.prototype={type:"chart",init:function(){},render:function(){},highlight:function(t,e,n,i){hl(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){hl(t.getData(),i,"normal")},remove:function(){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};var Uy=ll.prototype;Uy.updateView=Uy.updateLayout=Uy.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},vr(ll,["dispose"]),xr(ll,{registerWhenExtend:!0}),ll.markUpdateMethod=function(t,e){Xy(t).updateMethod=e};var jy={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},qy="\x00__throttleOriginMethod",Zy="\x00__throttleRate",$y="\x00__throttleType",Ky={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=(t.visualColorAccessPath||"itemStyle.color").split("."),r=t.get(i),a=!w(r)||r instanceof hm?null:r;(!r||a)&&(r=t.getColorFromPalette(t.name,null,e.getSeriesCount())),n.setVisual("color",r);var o=(t.visualBorderColorAccessPath||"itemStyle.borderColor").split("."),s=t.get(o);if(n.setVisual("borderColor",s),!e.isSeriesFiltered(t)){a&&n.each(function(e){n.setItemVisual(e,"color",a(t.getDataParams(e)))});var l=function(t,e){var n=t.getItemModel(e),r=n.get(i,!0),a=n.get(o,!0);null!=r&&t.setItemVisual(e,"color",r),null!=a&&t.setItemVisual(e,"borderColor",a)};return{dataEach:n.hasItemOption?l:null}}}},Qy={legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},Jy=function(t,e){function n(t,e){if("string"!=typeof t)return t;var n=t;return f(e,function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),n}function i(t){var e=o.get(t);if(null==e){for(var n=t.split("."),i=Qy.aria,r=0;r<n.length;++r)i=i[n[r]];return i}return e}function r(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}function a(t){return Qy.series.typeNames[t]||"自定义图"}var o=e.getModel("aria");if(o.get("show")){if(o.get("description"))return void t.setAttribute("aria-label",o.get("description"));var s=0;e.eachSeries(function(){++s},this);var l,u=o.get("data.maxCount")||10,h=o.get("series.maxCount")||10,c=Math.min(s,h);if(!(1>s)){var d=r();l=d?n(i("general.withTitle"),{title:d}):i("general.withoutTitle");var p=[],g=s>1?"series.multiple.prefix":"series.single.prefix";l+=n(i(g),{seriesCount:s}),e.eachSeries(function(t,e){if(c>e){var r,o=t.get("name"),l="series."+(s>1?"multiple":"single")+".";r=i(o?l+"withName":l+"withoutName"),r=n(r,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:a(t.subType)});var h=t.getData();window.data=h,r+=h.count()>u?n(i("data.partialData"),{displayCnt:u}):i("data.allData");for(var d=[],f=0;f<h.count();f++)if(u>f){var g=h.getName(f),v=Zs(h,f);d.push(n(i(g?"data.withName":"data.withoutName"),{name:g,value:v}))}r+=d.join(i("data.separator.middle"))+i("data.separator.end"),p.push(r)}}),l+=p.join(i("series.multiple.separator.middle"))+i("series.multiple.separator.end"),t.setAttribute("aria-label",l)}}},t_=Math.PI,e_=function(t,e){e=e||{},s(e,{text:"loading",textColor:"#000",fontSize:"12px",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#c23531",spinnerRadius:10,lineWidth:5,zlevel:0});var n=new Ap,i=new im({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});n.add(i);var r=e.fontSize+" sans-serif",a=new im({style:{fill:"none",text:e.text,font:r,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});if(n.add(a),e.showSpinner){var o=new lm({shape:{startAngle:-t_/2,endAngle:-t_/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001});o.animateShape(!0).when(1e3,{endAngle:3*t_/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*t_/2}).delay(300).start("circularInOut"),n.add(o)}return n.resize=function(){var n=Xn(e.text,r),s=e.showSpinner?e.spinnerRadius:0,l=(t.getWidth()-2*s-(e.showSpinner&&n?10:0)-n)/2-(e.showSpinner?0:n/2),u=t.getHeight()/2;e.showSpinner&&o.setShape({cx:l,cy:u}),a.setShape({x:l-s,y:u-s,width:2*s,height:2*s}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},n.resize(),n},n_=gl.prototype;n_.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},n_.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex,a=r?n.step:null,o=i&&i.modDataCount,s=null!=o?Math.ceil(o/a):null;return{step:a,modBy:s,modDataCount:o}}},n_.getPipeline=function(t){return this._pipelineMap.get(t)},n_.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData(),r=i.count(),a=n.progressiveEnabled&&e.incrementalPrepareRender&&r>=n.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=n.context={progressiveRender:a,modDataCount:s,large:o}},n_.restorePipelines=function(t){var e=this,n=e._pipelineMap=F();t.eachSeries(function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),Il(e,t,t.dataTask)})},n_.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),n=this.api;f(this._allHandlers,function(i){var r=t.get(i.uid)||t.set(i.uid,[]);i.reset&&ml(this,i,r,e,n),i.overallReset&&yl(this,i,r,e,n)},this)},n_.prepareView=function(t,e,n,i){var r=t.renderTask,a=r.context;a.model=e,a.ecModel=n,a.api=i,r.__block=!t.incrementalPrepareRender,Il(this,e,r)},n_.performDataProcessorTasks=function(t,e){vl(this,this._dataProcessorHandlers,t,e,{block:!0})},n_.performVisualTasks=function(t,e,n){vl(this,this._visualHandlers,t,e,n)},n_.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},n_.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var i_=n_.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},r_=Cl(0);gl.wrapStageHandler=function(t,e){return w(t)&&(t={overallReset:t,seriesType:kl(t)}),t.uid=vo("stageHandler"),e&&(t.visualType=e),t};var a_,o_={},s_={};Dl(o_,gy),Dl(s_,bs),o_.eachSeriesByType=o_.eachRawSeriesByType=function(t){a_=t},o_.eachComponent=function(t){"series"===t.mainType&&t.subType&&(a_=t.subType)};var l_=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],u_={color:l_,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],l_]},h_="#eee",c_=function(){return{axisLine:{lineStyle:{color:h_}},axisTick:{lineStyle:{color:h_}},axisLabel:{textStyle:{color:h_}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:h_}}}},d_=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],f_={color:d_,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:h_},crossStyle:{color:h_},label:{color:"#000"}}},legend:{textStyle:{color:h_}},textStyle:{color:h_},title:{textStyle:{color:h_}},toolbox:{iconStyle:{normal:{borderColor:h_}}},dataZoom:{textStyle:{color:h_}},visualMap:{textStyle:{color:h_}},timeline:{lineStyle:{color:h_},itemStyle:{normal:{color:d_[1]}},label:{normal:{textStyle:{color:h_}}},controlStyle:{normal:{color:h_,borderColor:h_}}},timeAxis:c_(),logAxis:c_(),valueAxis:c_(),categoryAxis:c_(),line:{symbol:"circle"},graph:{color:d_},gauge:{title:{textStyle:{color:h_}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};f_.categoryAxis.splitLine.show=!1,Jm.extend({type:"dataset",defaultOption:{seriesLayoutBy:hy,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){is(this)}}),Wy.extend({type:"dataset"});var p_=ta.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var n=.5522848,i=e.cx,r=e.cy,a=e.rx,o=e.ry,s=a*n,l=o*n;t.moveTo(i-a,r),t.bezierCurveTo(i-a,r-l,i-s,r-o,i,r-o),t.bezierCurveTo(i+s,r-o,i+a,r-l,i+a,r),t.bezierCurveTo(i+a,r+l,i+s,r+o,i,r+o),t.bezierCurveTo(i-s,r+o,i-a,r+l,i-a,r),t.closePath()}}),g_=/[\s,]+/;Pl.prototype.parse=function(t,e){e=e||{};var n=Al(t);if(!n)throw new Error("Illegal svg");var i=new Ap;this._root=i;var r=n.getAttribute("viewBox")||"",a=parseFloat(n.getAttribute("width")||e.width),o=parseFloat(n.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(o)&&(o=null),zl(n,i,null,!0);for(var s=n.firstChild;s;)this._parseNode(s,i),s=s.nextSibling;var l,u;if(r){var h=B(r).split(g_);h.length>=4&&(l={x:parseFloat(h[0]||0),y:parseFloat(h[1]||0),width:parseFloat(h[2]),height:parseFloat(h[3])})}if(l&&null!=a&&null!=o&&(u=Nl(l,a,o),!e.ignoreViewBox)){var c=i;i=new Ap,i.add(c),c.scale=u.scale.slice(),c.position=u.position.slice()}return e.ignoreRootClip||null==a||null==o||i.setClipPath(new im({shape:{x:0,y:0,width:a,height:o}})),{root:i,width:a,height:o,viewBoxRect:l,viewBoxTransform:u}},Pl.prototype._parseNode=function(t,e){var n=t.nodeName.toLowerCase();"defs"===n?this._isDefine=!0:"text"===n&&(this._isText=!0);var i;if(this._isDefine){var r=m_[n];if(r){var a=r.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else{var r=v_[n];r&&(i=r.call(this,t,e),e.add(i))}for(var s=t.firstChild;s;)1===s.nodeType&&this._parseNode(s,i),3===s.nodeType&&this._isText&&this._parseText(s,i),s=s.nextSibling;"defs"===n?this._isDefine=!1:"text"===n&&(this._isText=!1)},Pl.prototype._parseText=function(t,e){if(1===t.nodeType){var n=t.getAttribute("dx")||0,i=t.getAttribute("dy")||0;this._textX+=parseFloat(n),this._textY+=parseFloat(i)}var r=new Yv({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});Ol(e,r),zl(t,r,this._defs);var a=r.style.fontSize;a&&9>a&&(r.style.fontSize=9,r.scale=r.scale||[1,1],r.scale[0]*=a/9,r.scale[1]*=a/9);var o=r.getBoundingRect();return this._textX+=o.width,e.add(r),r};var v_={g:function(t,e){var n=new Ap;return Ol(e,n),zl(t,n,this._defs),n},rect:function(t,e){var n=new im;return Ol(e,n),zl(t,n,this._defs),n.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),n},circle:function(t,e){var n=new Uv;return Ol(e,n),zl(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),n},line:function(t,e){var n=new am;return Ol(e,n),zl(t,n,this._defs),n.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),n},ellipse:function(t,e){var n=new p_;return Ol(e,n),zl(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),n},polygon:function(t,e){var n=t.getAttribute("points");n&&(n=Bl(n));var i=new Jv({shape:{points:n||[]}});return Ol(e,i),zl(t,i,this._defs),i},polyline:function(t,e){var n=new ta;Ol(e,n),zl(t,n,this._defs);var i=t.getAttribute("points");i&&(i=Bl(i));var r=new tm({shape:{points:i||[]}});return r},image:function(t,e){var n=new Ii;return Ol(e,n),zl(t,n,this._defs),n.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),n},text:function(t,e){var n=t.getAttribute("x")||0,i=t.getAttribute("y")||0,r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0;this._textX=parseFloat(n)+parseFloat(r),this._textY=parseFloat(i)+parseFloat(a);var o=new Ap;return Ol(e,o),zl(t,o,this._defs),o},tspan:function(t,e){var n=t.getAttribute("x"),i=t.getAttribute("y");null!=n&&(this._textX=parseFloat(n)),null!=i&&(this._textY=parseFloat(i));var r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0,o=new Ap;return Ol(e,o),zl(t,o,this._defs),this._textX+=r,this._textY+=a,o},path:function(t,e){var n=t.getAttribute("d")||"",i=ra(n);return Ol(e,i),zl(t,i,this._defs),i}},m_={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),n=parseInt(t.getAttribute("y1")||0,10),i=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),a=new cm(e,n,i,r);return Ll(t,a),a},radialgradient:function(){}},y_={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"},__=/url\(\s*#(.*?)\)/,x_=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g,w_=/([^\s:;]+)\s*:\s*([^:;]+)/g,b_=F(),S_={registerMap:function(t,e,n){var i;return x(e)?i=e:e.svg?i=[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(n=e.specialAreas,e=e.geoJson),i=[{type:"geoJSON",source:e,specialAreas:n}]),f(i,function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var n=M_[e];n(t)}),b_.set(t,i)},retrieveMap:function(t){return b_.get(t)}},M_={geoJSON:function(t){var e=t.source;t.geoJSON=b(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=Al(t.source)}},C_=O,T_=f,I_=w,k_=S,D_=Jm.parseClassType,A_="4.8.0",P_={zrender:"4.3.1"},L_=1,O_=1e3,B_=800,z_=900,E_=5e3,R_=1e3,F_=1100,N_=2e3,H_=3e3,W_=3500,V_=4e3,G_=5e3,X_={PROCESSOR:{FILTER:O_,SERIES_FILTER:B_,STATISTIC:E_},VISUAL:{LAYOUT:R_,PROGRESSIVE_LAYOUT:F_,GLOBAL:N_,CHART:H_,POST_CHART_LAYOUT:W_,COMPONENT:V_,BRUSH:G_}},Y_="__flagInMainProcess",U_="__optionUpdated",j_=/^[a-zA-Z0-9_]+$/;Wl.prototype.on=Hl("on",!0),Wl.prototype.off=Hl("off",!0),Wl.prototype.one=Hl("one",!0),c(Wl,Ff);var q_=Vl.prototype;q_._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[U_]){var e=this[U_].silent;this[Y_]=!0,Xl(this),Z_.update.call(this),this[Y_]=!1,this[U_]=!1,ql.call(this,e),Zl.call(this,e)}else if(t.unfinished){var n=L_,i=this._model,r=this._api;t.unfinished=!1;do{var a=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),Ul(this,i),t.performVisualTasks(i),eu(this,this._model,r,"remain"),n-=+new Date-a}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},q_.getDom=function(){return this._dom},q_.getZr=function(){return this._zr},q_.setOption=function(t,e,n){if(!this._disposed){var i;if(k_(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[Y_]=!0,!this._model||e){var r=new Ms(this._api),a=this._theme,o=this._model=new gy;o.scheduler=this._scheduler,o.init(null,null,a,r)}this._model.setOption(t,tx),n?(this[U_]={silent:i},this[Y_]=!1):(Xl(this),Z_.update.call(this),this._zr.flush(),this[U_]=!1,this[Y_]=!1,ql.call(this,i),Zl.call(this,i))}},q_.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},q_.getModel=function(){return this._model},q_.getOption=function(){return this._model&&this._model.getOption()},q_.getWidth=function(){return this._zr.getWidth()},q_.getHeight=function(){return this._zr.getHeight()},q_.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},q_.getRenderedCanvas=function(t){if(vf.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr;return e.painter.getRenderedCanvas(t)}},q_.getSvgDataURL=function(){if(vf.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return f(e,function(t){t.stopAnimation(!0)}),t.painter.toDataURL()}},q_.getDataURL=function(t){if(!this._disposed){t=t||{};var e=t.excludeComponents,n=this._model,i=[],r=this;T_(e,function(t){n.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)})});var a="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return T_(i,function(t){t.group.ignore=!1}),a}},q_.getConnectedDataURL=function(t){if(!this._disposed&&vf.canvasSupported){var e="svg"===t.type,n=this.group,r=Math.min,a=Math.max,o=1/0;if(ox[n]){var s=o,l=o,u=-o,h=-o,c=[],d=t&&t.pixelRatio||1;f(ax,function(o){if(o.group===n){var d=e?o.getZr().painter.getSvgDom().innerHTML:o.getRenderedCanvas(i(t)),f=o.getDom().getBoundingClientRect();s=r(f.left,s),l=r(f.top,l),u=a(f.right,u),h=a(f.bottom,h),c.push({dom:d,left:f.left,top:f.top})}}),s*=d,l*=d,u*=d,h*=d;var p=u-s,g=h-l,v=If(),m=qi(v,{renderer:e?"svg":"canvas"});if(m.resize({width:p,height:g}),e){var y="";return T_(c,function(t){var e=t.left-s,n=t.top-l;y+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),m.painter.getSvgRoot().innerHTML=y,t.connectedBackgroundColor&&m.painter.setBackgroundColor(t.connectedBackgroundColor),m.refreshImmediately(),m.painter.toDataURL()}return t.connectedBackgroundColor&&m.add(new im({shape:{x:0,y:0,width:p,height:g},style:{fill:t.connectedBackgroundColor}})),T_(c,function(t){var e=new Ii({style:{x:t.left*d-s,y:t.top*d-l,image:t.dom}});m.add(e)}),m.refreshImmediately(),v.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},q_.convertToPixel=_(Gl,"convertToPixel"),q_.convertFromPixel=_(Gl,"convertFromPixel"),q_.containPixel=function(t,e){if(!this._disposed){var n,i=this._model;return t=ur(i,t),f(t,function(t,i){i.indexOf("Models")>=0&&f(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n|=!!r.containPoint(e);else if("seriesModels"===i){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(n|=a.containPoint(e,t))}},this)},this),!!n}},q_.getVisual=function(t,e){var n=this._model;t=ur(n,t,{defaultMainType:"series"});var i=t.seriesModel,r=i.getData(),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)},q_.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},q_.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var Z_={prepareAndUpdate:function(t){Xl(this),Z_.update.call(this,t)},update:function(t){var e=this._model,n=this._api,i=this._zr,r=this._coordSysMgr,a=this._scheduler;if(e){a.restoreData(e,t),a.performSeriesTasks(e),r.create(e,n),a.performDataProcessorTasks(e,t),Ul(this,e),r.update(e,n),Ql(e),a.performVisualTasks(e,t),Jl(this,e,n,t);
var o=e.get("backgroundColor")||"transparent";if(vf.canvasSupported)i.setBackgroundColor(o);else{var s=Je(o);o=un(s,"rgb"),0===s[3]&&(o="transparent")}nu(e,n)}},updateTransform:function(t){var e=this._model,n=this,i=this._api;if(e){var r=[];e.eachComponent(function(a,o){var s=n.getViewOfComponentModel(o);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(o,e,i,t);l&&l.update&&r.push(s)}else r.push(s)});var a=F();e.eachSeries(function(r){var o=n._chartsMap[r.__viewId];if(o.updateTransform){var s=o.updateTransform(r,e,i,t);s&&s.update&&a.set(r.uid,1)}else a.set(r.uid,1)}),Ql(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:a}),eu(n,e,i,t,a),nu(e,this._api)}},updateView:function(t){var e=this._model;e&&(ll.markUpdateMethod(t,"updateView"),Ql(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Jl(this,this._model,this._api,t),nu(e,this._api))},updateVisual:function(t){Z_.update.call(this,t)},updateLayout:function(t){Z_.update.call(this,t)}};q_.resize=function(t){if(!this._disposed){this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[Y_]=!0,n&&Xl(this),Z_.update.call(this),this[Y_]=!1,ql.call(this,i),Zl.call(this,i)}}},q_.showLoading=function(t,e){if(!this._disposed&&(k_(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),rx[t])){var n=rx[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},q_.hideLoading=function(){this._disposed||(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},q_.makeActionFromEvent=function(t){var e=o({},t);return e.type=Q_[t.type],e},q_.dispatchAction=function(t,e){if(!this._disposed&&(k_(e)||(e={silent:!!e}),K_[t.type]&&this._model)){if(this[Y_])return void this._pendingActions.push(t);jl.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&vf.browser.weChat&&this._throttledZrFlush(),ql.call(this,e.silent),Zl.call(this,e.silent)}},q_.appendData=function(t){if(!this._disposed){var e=t.seriesIndex,n=this.getModel(),i=n.getSeriesByIndex(e);i.appendData(t),this._scheduler.unfinished=!0}},q_.on=Hl("on",!1),q_.off=Hl("off",!1),q_.one=Hl("one",!1);var $_=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];q_._initEvents=function(){T_($_,function(t){var e=function(e){var n,i=this.getModel(),r=e.target,a="globalout"===t;if(a)n={};else if(r&&null!=r.dataIndex){var s=r.dataModel||i.getSeriesByIndex(r.seriesIndex);n=s&&s.getDataParams(r.dataIndex,r.dataType,r)||{}}else r&&r.eventData&&(n=o({},r.eventData));if(n){var l=n.componentType,u=n.componentIndex;("markLine"===l||"markPoint"===l||"markArea"===l)&&(l="series",u=n.seriesIndex);var h=l&&null!=u&&i.getComponent(l,u),c=h&&this["series"===h.mainType?"_chartsMap":"_componentsMap"][h.__viewId];n.event=e,n.type=t,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:n,model:h,view:c},this.trigger(t,n)}};e.zrEventfulCallAtLast=!0,this._zr.on(t,e,this)},this),T_(Q_,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},q_.isDisposed=function(){return this._disposed},q_.clear=function(){this._disposed||this.setOption({series:[]},!0)},q_.dispose=function(){if(!this._disposed){this._disposed=!0,cr(this.getDom(),ux,"");var t=this._api,e=this._model;T_(this._componentsViews,function(n){n.dispose(e,t)}),T_(this._chartsViews,function(n){n.dispose(e,t)}),this._zr.dispose(),delete ax[this.id]}},c(Vl,Ff),su.prototype={constructor:su,normalizeQuery:function(t){var e={},n={},i={};if(b(t)){var r=D_(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var a=["Index","Name","Id"],o={name:1,dataIndex:1,dataType:1};f(t,function(t,r){for(var s=!1,l=0;l<a.length;l++){var u=a[l],h=r.lastIndexOf(u);if(h>0&&h===r.length-u.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[u.toLowerCase()]=t,s=!0)}}o.hasOwnProperty(r)&&(n[r]=t,s=!0),s||(i[r]=t)})}return{cptQuery:e,dataQuery:n,otherQuery:i}},filter:function(t,e){function n(t,e,n,i){return null==t[n]||e[i||n]===t[n]}var i=this.eventInfo;if(!i)return!0;var r=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return n(l,o,"mainType")&&n(l,o,"subType")&&n(l,o,"index","componentIndex")&&n(l,o,"name")&&n(l,o,"id")&&n(u,a,"name")&&n(u,a,"dataIndex")&&n(u,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,a))},afterTrigger:function(){this.eventInfo=null}};var K_={},Q_={},J_=[],tx=[],ex=[],nx=[],ix={},rx={},ax={},ox={},sx=new Date-0,lx=new Date-0,ux="_echarts_instance_",hx=cu;Su(N_,Ky),vu(Dy),mu(z_,Ay),Cu("default",e_),_u({type:"highlight",event:"highlight",update:"highlight"},H),_u({type:"downplay",event:"downplay",update:"downplay"},H),gu("light",u_),gu("dark",f_);var cx={};Bu.prototype={constructor:Bu,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,n=this._new,i={},r={},a=[],o=[];for(zu(e,i,a,"_oldKeyGetter",this),zu(n,r,o,"_newKeyGetter",this),t=0;t<e.length;t++){var s=a[t],l=r[s];if(null!=l){var u=l.length;u?(1===u&&(r[s]=null),l=l.shift()):r[s]=null,this._update&&this._update(l,t)}else this._remove&&this._remove(t)}for(var t=0;t<o.length;t++){var s=o[t];if(r.hasOwnProperty(s)){var l=r[s];if(null==l)continue;if(l.length)for(var h=0,u=l.length;u>h;h++)this._add&&this._add(l[h]);else this._add&&this._add(l)}}}};var dx=F(["tooltip","label","itemName","itemId","seriesName"]),fx=S,px="undefined",gx=-1,vx="e\x00\x00",mx={"float":typeof Float64Array===px?Array:Float64Array,"int":typeof Int32Array===px?Array:Int32Array,ordinal:Array,number:Array,time:Array},yx=typeof Uint32Array===px?Array:Uint32Array,_x=typeof Int32Array===px?Array:Int32Array,xx=typeof Uint16Array===px?Array:Uint16Array,bx=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],Sx=["_extent","_approximateExtent","_rawExtent"],Mx=function(t,e){t=t||["x","y"];for(var n={},i=[],r={},a=0;a<t.length;a++){var o=t[a];b(o)?o=new Hu({name:o}):o instanceof Hu||(o=new Hu(o));var s=o.name;o.type=o.type||"float",o.coordDim||(o.coordDim=s,o.coordDimIndex=0),o.otherDims=o.otherDims||{},i.push(s),n[s]=o,o.index=a,o.createInvertedIndices&&(r[s]=[])}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=Eu(this),this._invertedIndicesMap=r,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},Cx=Mx.prototype;Cx.type="list",Cx.hasItemOption=!0,Cx.getDimension=function(t){return("number"==typeof t||!isNaN(t)&&!this._dimensionInfos.hasOwnProperty(t))&&(t=this.dimensions[t]),t},Cx.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},Cx.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},Cx.mapDimension=function(t,e){var n=this._dimensionsSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return e===!0?(i||[]).slice():i&&i[e]},Cx.initData=function(t,e,n){var i=ns.isInstance(t)||d(t);i&&(t=new Vs(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},n||(this.hasItemOption=!1),this.defaultDimValueGetter=By[this._rawData.getSource().sourceFormat],this._dimValueGetter=n=n||this.defaultDimValueGetter,this._dimValueGetterArrayRows=By.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},Cx.getProvider=function(){return this._rawData},Cx.appendData=function(t){var e=this._rawData,n=this.count();e.appendData(t);var i=e.count();e.persistent||(i+=n),this._initDataFromProvider(n,i)},Cx.appendValues=function(t,e){for(var n=this._chunkSize,i=this._storage,r=this.dimensions,a=r.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),u=this._chunkCount,h=0;a>h;h++){var c=r[h];o[c]||(o[c]=Ju()),i[c]||(i[c]=[]),Xu(i,this._dimensionInfos[c],n,u,l),this._chunkCount=i[c].length}for(var d=new Array(a),f=s;l>f;f++){for(var p=f-s,g=Math.floor(f/n),v=f%n,m=0;a>m;m++){var c=r[m],y=this._dimValueGetterArrayRows(t[p]||d,c,p,m);i[c][g][v]=y;var _=o[c];y<_[0]&&(_[0]=y),y>_[1]&&(_[1]=y)}e&&(this._nameList[f]=e[p])}this._rawCount=this._count=l,this._extent={},Yu(this)},Cx._initDataFromProvider=function(t,e){if(!(t>=e)){for(var n,i=this._chunkSize,r=this._rawData,a=this._storage,o=this.dimensions,s=o.length,l=this._dimensionInfos,u=this._nameList,h=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;s>p;p++){var g=o[p];c[g]||(c[g]=Ju());var v=l[g];0===v.otherDims.itemName&&(n=this._nameDimIdx=p),0===v.otherDims.itemId&&(this._idDimIdx=p),a[g]||(a[g]=[]),Xu(a,v,i,f,e),this._chunkCount=a[g].length}for(var m=new Array(s),y=t;e>y;y++){m=r.getItem(y,m);for(var _=Math.floor(y/i),x=y%i,w=0;s>w;w++){var g=o[w],b=a[g][_],S=this._dimValueGetter(m,g,y,w);b[x]=S;var M=c[g];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var C=u[y];if(m&&null==C)if(null!=m.name)u[y]=C=m.name;else if(null!=n){var T=o[n],I=a[T][_];if(I){C=I[x];var k=l[T].ordinalMeta;k&&k.categories.length&&(C=k.categories[C])}}var D=null==m?null:m.id;null==D&&null!=C&&(d[C]=d[C]||0,D=C,d[C]>0&&(D+="__ec__"+d[C]),d[C]++),null!=D&&(h[y]=D)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},Yu(this)}},Cx.count=function(){return this._count},Cx.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var r=0;i>r;r++)t[r]=e[r]}else t=new n(e.buffer,0,i)}else for(var n=Wu(this),t=new n(this.count()),r=0;r<t.length;r++)t[r]=r;return t},Cx.get=function(t,e){if(!(e>=0&&e<this._count))return 0/0;var n=this._storage;if(!n[t])return 0/0;e=this.getRawIndex(e);var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=n[t][i],o=a[r];return o},Cx.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return 0/0;var n=this._storage[t];if(!n)return 0/0;var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=n[i];return a[r]},Cx._getFast=function(t,e){var n=Math.floor(e/this._chunkSize),i=e%this._chunkSize,r=this._storage[t][n];return r[i]},Cx.getValues=function(t,e){var n=[];x(t)||(e=t,t=this.dimensions);for(var i=0,r=t.length;r>i;i++)n.push(this.get(t[i],e));return n},Cx.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,n=0,i=e.length;i>n;n++)if(isNaN(this.get(e[n],t)))return!1;return!0},Cx.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],n=Ju();if(!e)return n;var i,r=this.count(),a=!this._indices;if(a)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();i=n;for(var o=i[0],s=i[1],l=0;r>l;l++){var u=this._getFast(t,this.getRawIndex(l));o>u&&(o=u),u>s&&(s=u)}return i=[o,s],this._extent[t]=i,i},Cx.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},Cx.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},Cx.getCalculationInfo=function(t){return this._calculationInfo[t]},Cx.setCalculationInfo=function(t,e){fx(t)?o(this._calculationInfo,t):this._calculationInfo[t]=e},Cx.getSum=function(t){var e=this._storage[t],n=0;if(e)for(var i=0,r=this.count();r>i;i++){var a=this.get(t,i);isNaN(a)||(n+=a)}return n},Cx.getMedian=function(t){var e=[];this.each(t,function(t){isNaN(t)||e.push(t)});var n=[].concat(e).sort(function(t,e){return t-e}),i=this.count();return 0===i?0:i%2===1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},Cx.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t],i=n[e];return null==i||isNaN(i)?gx:i},Cx.indexOfName=function(t){for(var e=0,n=this.count();n>e;e++)if(this.getName(e)===t)return e;return-1},Cx.indexOfRawIndex=function(t){if(t>=this._rawCount||0>t)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;r>=i;){var a=(i+r)/2|0;if(e[a]<t)i=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},Cx.indicesOfNearest=function(t,e,n){var i=this._storage,r=i[t],a=[];if(!r)return a;null==n&&(n=1/0);for(var o=1/0,s=-1,l=0,u=0,h=this.count();h>u;u++){var c=e-this.get(t,u),d=Math.abs(c);n>=d&&((o>d||d===o&&c>=0&&0>s)&&(o=d,s=c,l=0),c===s&&(a[l++]=u))}return a.length=l,a},Cx.getRawIndex=ju,Cx.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],n=0;n<this.dimensions.length;n++){var i=this.dimensions[n];e.push(this.get(i,t))}return e},Cx.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||Uu(this,this._nameDimIdx,e)||""},Cx.getId=function(t){return Zu(this,this.getRawIndex(t))},Cx.each=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=p($u(t),this.getDimension,this);for(var r=t.length,a=0;a<this.count();a++)switch(r){case 0:e.call(n,a);break;case 1:e.call(n,this.get(t[0],a),a);break;case 2:e.call(n,this.get(t[0],a),this.get(t[1],a),a);break;default:for(var o=0,s=[];r>o;o++)s[o]=this.get(t[o],a);s[o]=a,e.apply(n,s)}}},Cx.filterSelf=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=p($u(t),this.getDimension,this);for(var r=this.count(),a=Wu(this),o=new a(r),s=[],l=t.length,u=0,h=t[0],c=0;r>c;c++){var d,f=this.getRawIndex(c);if(0===l)d=e.call(n,c);else if(1===l){var g=this._getFast(h,f);d=e.call(n,g,c)}else{for(var v=0;l>v;v++)s[v]=this._getFast(h,f);s[v]=c,d=e.apply(n,s)}d&&(o[u++]=f)}return r>u&&(this._indices=o),this._count=u,this._extent={},this.getRawIndex=this._indices?qu:ju,this}},Cx.selectRange=function(t){if(this._count){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);var i=e.length;if(i){var r=this.count(),a=Wu(this),o=new a(r),s=0,l=e[0],u=t[l][0],h=t[l][1],c=!1;if(!this._indices){var d=0;if(1===i){for(var f=this._storage[e[0]],p=0;p<this._chunkCount;p++)for(var g=f[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m];(y>=u&&h>=y||isNaN(y))&&(o[s++]=d),d++}c=!0}else if(2===i){for(var f=this._storage[l],_=this._storage[e[1]],x=t[e[1]][0],w=t[e[1]][1],p=0;p<this._chunkCount;p++)for(var g=f[p],b=_[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m],S=b[m];(y>=u&&h>=y||isNaN(y))&&(S>=x&&w>=S||isNaN(S))&&(o[s++]=d),d++}c=!0}}if(!c)if(1===i)for(var m=0;r>m;m++){var M=this.getRawIndex(m),y=this._getFast(l,M);(y>=u&&h>=y||isNaN(y))&&(o[s++]=M)}else for(var m=0;r>m;m++){for(var C=!0,M=this.getRawIndex(m),p=0;i>p;p++){var T=e[p],y=this._getFast(n,M);(y<t[T][0]||y>t[T][1])&&(C=!1)}C&&(o[s++]=this.getRawIndex(m))}return r>s&&(this._indices=o),this._count=s,this._extent={},this.getRawIndex=this._indices?qu:ju,this}}},Cx.mapArray=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n),r},Cx.map=function(t,e,n,i){n=n||i||this,t=p($u(t),this.getDimension,this);var r=Ku(this,t);r._indices=this._indices,r.getRawIndex=r._indices?qu:ju;for(var a=r._storage,o=[],s=this._chunkSize,l=t.length,u=this.count(),h=[],c=r._rawExtent,d=0;u>d;d++){for(var f=0;l>f;f++)h[f]=this.get(t[f],d);h[l]=d;var g=e&&e.apply(n,h);if(null!=g){"object"!=typeof g&&(o[0]=g,g=o);for(var v=this.getRawIndex(d),m=Math.floor(v/s),y=v%s,_=0;_<g.length;_++){var x=t[_],w=g[_],b=c[x],S=a[x];S&&(S[m][y]=w),w<b[0]&&(b[0]=w),w>b[1]&&(b[1]=w)}}}return r},Cx.downSample=function(t,e,n,i){for(var r=Ku(this,[t]),a=r._storage,o=[],s=Math.floor(1/e),l=a[t],u=this.count(),h=this._chunkSize,c=r._rawExtent[t],d=new(Wu(this))(u),f=0,p=0;u>p;p+=s){s>u-p&&(s=u-p,o.length=s);for(var g=0;s>g;g++){var v=this.getRawIndex(p+g),m=Math.floor(v/h),y=v%h;o[g]=l[m][y]}var _=n(o),x=this.getRawIndex(Math.min(p+i(o,_)||0,u-1)),w=Math.floor(x/h),b=x%h;l[w][b]=_,_<c[0]&&(c[0]=_),_>c[1]&&(c[1]=_),d[f++]=x}return r._count=f,r._indices=d,r.getRawIndex=qu,r},Cx.getItemModel=function(t){var e=this.hostModel;return new fo(this.getRawDataItem(t),e,e&&e.ecModel)},Cx.diff=function(t){var e=this;return new Bu(t?t.getIndices():[],this.getIndices(),function(e){return Zu(t,e)},function(t){return Zu(e,t)})},Cx.getVisual=function(t){var e=this._visual;return e&&e[t]},Cx.setVisual=function(t,e){if(fx(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},Cx.setLayout=function(t,e){if(fx(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},Cx.getLayout=function(t){return this._layout[t]},Cx.getItemLayout=function(t){return this._itemLayouts[t]},Cx.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?o(this._itemLayouts[t]||{},e):e},Cx.clearItemLayouts=function(){this._itemLayouts.length=0},Cx.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},Cx.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=i,fx(e))for(var a in e)e.hasOwnProperty(a)&&(i[a]=e[a],r[a]=!0);else i[e]=n,r[e]=!0},Cx.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var Tx=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};Cx.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse(Tx,e)),this._graphicEls[t]=e},Cx.getItemGraphicEl=function(t){return this._graphicEls[t]},Cx.eachItemGraphicEl=function(t,e){f(this._graphicEls,function(n,i){n&&t&&t.call(e,n,i)})},Cx.cloneShallow=function(t){if(!t){var e=p(this.dimensions,this.getDimensionInfo,this);t=new Mx(e,this.hostModel)}if(t._storage=this._storage,Gu(t,this),this._indices){var n=this._indices.constructor;t._indices=new n(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?qu:ju,t},Cx.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(P(arguments)))})},Cx.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],Cx.CHANGABLE_METHODS=["filterSelf","selectRange"];var Ix=function(t,e){return e=e||{},th(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})},kx={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis")[0],a=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",a),ah(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),ah(a)&&(i.set("y",a),null==e.firstCategoryDimIndex&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],n.set("single",r),ah(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar")[0],a=r.findAxisModel("radiusAxis"),o=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",a),n.set("angle",o),ah(a)&&(i.set("radius",a),e.firstCategoryDimIndex=0),ah(o)&&(i.set("angle",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,a=r.getComponent("parallel",t.get("parallelIndex")),o=e.coordSysDims=a.dimensions.slice();f(a.parallelAxisIndex,function(t,a){var s=r.getComponent("parallelAxis",t),l=o[a];n.set(l,s),ah(s)&&null==e.firstCategoryDimIndex&&(i.set(l,s),e.firstCategoryDimIndex=a)})}};dh.prototype.parse=function(t){return t},dh.prototype.getSetting=function(t){return this._setting[t]},dh.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},dh.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},dh.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},dh.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},dh.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},dh.prototype.getExtent=function(){return this._extent.slice()},dh.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},dh.prototype.isBlank=function(){return this._isBlank},dh.prototype.setBlank=function(t){this._isBlank=t},dh.prototype.getLabel=null,vr(dh),xr(dh,{registerWhenExtend:!0}),fh.createByAxisModel=function(t){var e=t.option,n=e.data,i=n&&p(n,gh);return new fh({categories:i,needCollect:!i,deduplication:e.dedplication!==!1})};var Dx=fh.prototype;Dx.getOrdinal=function(t){return ph(this).get(t)},Dx.parseAndCollect=function(t){var e,n=this._needCollect;if("string"!=typeof t&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=ph(this);return e=i.get(t),null==e&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=0/0),e};var Ax=dh.prototype,Px=dh.extend({type:"ordinal",init:function(t,e){(!t||x(t))&&(t=new fh({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),Ax.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return Ax.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(Ax.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push(n),n++;return t},getLabel:function(t){return this.isBlank()?void 0:this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:H,niceExtent:H});Px.create=function(){return new Px};var Lx=bo,Ox=bo,Bx=dh.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),Bx.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=mh(t)},getTicks:function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,a=[];if(!e)return a;var o=1e4;n[0]<i[0]&&a.push(t?Ox(i[0]-e,r):n[0]);for(var s=i[0];s<=i[1]&&(a.push(s),s=Ox(s+e,r),s!==a[a.length-1]);)if(a.length>o)return[];var l=a.length?a[a.length-1]:i[1];return n[1]>l&&a.push(t?Ox(l+e,r):n[1]),a},getMinorTicks:function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var a=e[r],o=e[r-1],s=0,l=[],u=a-o,h=u/t;t-1>s;){var c=bo(o+(s+1)*h);c>i[0]&&c<i[1]&&l.push(c),s++}n.push(l)}return n},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=Co(t)||0:"auto"===n&&(n=this._intervalPrecision),t=Ox(t,n,!0),Ro(t)},niceTicks:function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){0>r&&(r=-r,i.reverse());var a=vh(i,t,e,n);this._intervalPrecision=a.intervalPrecision,this._interval=a.interval,this._niceExtent=a.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax?e[0]-=n/2:(e[1]+=n/2,e[0]-=n/2)}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=Ox(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=Ox(Math.ceil(e[1]/r)*r))}});Bx.create=function(){return new Bx};var zx="__ec_stack_",Ex=.5,Rx="undefined"!=typeof Float32Array?Float32Array:Array,Fx={seriesType:"bar",plan:Gy(),reset:function(t){function e(t,e){for(var n,d=t.count,f=new Rx(2*d),p=new Rx(2*d),g=new Rx(d),v=[],m=[],y=0,_=0;null!=(n=t.next());)m[h]=e.get(s,n),m[1-h]=e.get(l,n),v=i.dataToPoint(m,null,v),p[y]=u?r.x+r.width:v[0],f[y++]=v[0],p[y]=u?v[1]:r.y+r.height,f[y++]=v[1],g[_++]=n;e.setLayout({largePoints:f,largeDataIndices:g,largeBackgroundPoints:p,barWidth:c,valueAxisStart:Ah(a,o,!1),backgroundStart:u?r.x:r.y,valueAxisHorizontal:u})}if(kh(t)&&Dh(t)){var n=t.getData(),i=t.coordinateSystem,r=i.grid.getRect(),a=i.getBaseAxis(),o=i.getOtherAxis(a),s=n.mapDimension(o.dim),l=n.mapDimension(a.dim),u=o.isHorizontal(),h=u?0:1,c=Th(Mh([t]),a,t).width;return c>Ex||(c=Ex),{progress:e}}}},Nx=Bx.prototype,Hx=Math.ceil,Wx=Math.floor,Vx=1e3,Gx=60*Vx,Xx=60*Gx,Yx=24*Xx,Ux=function(t,e,n,i){for(;i>n;){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n},jx=Bx.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return Xo(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=Yx,e[1]+=Yx),e[1]===-1/0&&1/0===e[0]){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-Yx}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(e[0]=bo(Wx(e[0]/i)*i)),t.fixMax||(e[1]=bo(Hx(e[1]/i)*i))},niceTicks:function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0],a=r/t;null!=e&&e>a&&(a=e),null!=n&&a>n&&(a=n);var o=qx.length,s=Ux(qx,a,0,o),l=qx[Math.min(s,o-1)],u=l[1];if("year"===l[0]){var h=r/u,c=Oo(h/t,!0);u*=c}var d=this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3,f=[Math.round(Hx((i[0]-d)/u)*u+d),Math.round(Wx((i[1]-d)/u)*u+d)];_h(f,i),this._stepLvl=l,this._interval=u,this._niceExtent=f},parse:function(t){return+Ao(t)}});f(["contain","normalize"],function(t){jx.prototype[t]=function(e){return Nx[t].call(this,this.parse(e))}});var qx=[["hh:mm:ss",Vx],["hh:mm:ss",5*Vx],["hh:mm:ss",10*Vx],["hh:mm:ss",15*Vx],["hh:mm:ss",30*Vx],["hh:mm\nMM-dd",Gx],["hh:mm\nMM-dd",5*Gx],["hh:mm\nMM-dd",10*Gx],["hh:mm\nMM-dd",15*Gx],["hh:mm\nMM-dd",30*Gx],["hh:mm\nMM-dd",Xx],["hh:mm\nMM-dd",2*Xx],["hh:mm\nMM-dd",6*Xx],["hh:mm\nMM-dd",12*Xx],["MM-dd\nyyyy",Yx],["MM-dd\nyyyy",2*Yx],["MM-dd\nyyyy",3*Yx],["MM-dd\nyyyy",4*Yx],["MM-dd\nyyyy",5*Yx],["MM-dd\nyyyy",6*Yx],["week",7*Yx],["MM-dd\nyyyy",10*Yx],["week",14*Yx],["week",21*Yx],["month",31*Yx],["week",42*Yx],["month",62*Yx],["week",70*Yx],["quarter",95*Yx],["month",31*Yx*4],["month",31*Yx*5],["half-year",380*Yx/2],["month",31*Yx*8],["month",31*Yx*10],["year",380*Yx]];jx.create=function(t){return new jx({useUTC:t.ecModel.get("useUTC")})};var Zx=dh.prototype,$x=Bx.prototype,Kx=Co,Qx=bo,Jx=Math.floor,tw=Math.ceil,ew=Math.pow,nw=Math.log,iw=dh.extend({type:"log",base:10,$constructor:function(){dh.apply(this,arguments),this._originalScale=new Bx},getTicks:function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return p($x.getTicks.call(this,t),function(t){var r=bo(ew(this.base,t));return r=t===n[0]&&e.__fixMin?Ph(r,i[0]):r,r=t===n[1]&&e.__fixMax?Ph(r,i[1]):r},this)},getMinorTicks:$x.getMinorTicks,getLabel:$x.getLabel,scale:function(t){return t=Zx.scale.call(this,t),ew(this.base,t)},setExtent:function(t,e){var n=this.base;t=nw(t)/nw(n),e=nw(e)/nw(n),$x.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=Zx.getExtent.call(this);e[0]=ew(t,e[0]),e[1]=ew(t,e[1]);var n=this._originalScale,i=n.getExtent();return n.__fixMin&&(e[0]=Ph(e[0],i[0])),n.__fixMax&&(e[1]=Ph(e[1],i[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=nw(t[0])/nw(e),t[1]=nw(t[1])/nw(e),Zx.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(1/0===n||0>=n)){var i=Po(n),r=t/n*i;for(.5>=r&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var a=[bo(tw(e[0]/i)*i),bo(Jx(e[1]/i)*i)];this._interval=i,this._niceExtent=a}},niceExtent:function(t){$x.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});f(["contain","normalize"],function(t){iw.prototype[t]=function(e){return e=nw(e)/nw(this.base),Zx[t].call(this,e)}}),iw.create=function(){return new iw};var rw={getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!=typeof n&&!I(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!=typeof n&&!I(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},getCoordSysModel:H,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},aw=pa({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,a=e.height/2;t.moveTo(n,i-a),t.lineTo(n+r,i+a),t.lineTo(n-r,i+a),t.closePath()}}),ow=pa({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,a=e.height/2;t.moveTo(n,i-a),t.lineTo(n+r,i),t.lineTo(n,i+a),t.lineTo(n-r,i),t.closePath()}}),sw=pa({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,a=Math.max(r,e.height),o=r/2,s=o*o/(a-o),l=i-a+o+s,u=Math.asin(s/o),h=Math.cos(u)*o,c=Math.sin(u),d=Math.cos(u),f=.6*o,p=.7*o;t.moveTo(n-h,l+s),t.arc(n,l,o,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*f,l+s+d*f,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-h+c*f,l+s+d*f,n-h,l+s),t.closePath()}}),lw=pa({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,a=e.y,o=i/3*2;t.moveTo(r,a),t.lineTo(r+o,a+n),t.lineTo(r,a+n/4*3),t.lineTo(r-o,a+n),t.lineTo(r,a),t.closePath()}}),uw={line:am,rect:im,roundRect:im,square:im,circle:Uv,diamond:ow,pin:sw,arrow:lw,triangle:aw},hw={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var a=Math.min(n,i);r.x=t,r.y=e,r.width=a,r.height=a},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},cw={};f(uw,function(t,e){cw[e]=new t});var dw=pa({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=$n(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.textPosition&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=cw[i];r||(i="rect",r=cw[i]),hw[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}}),fw={isDimensionStacked:sh,enableDataStack:oh,getStackedDimension:lh},pw=(Object.freeze||Object)({createList:Yh,getLayoutRect:$o,dataStack:fw,createScale:Uh,mixinAxisModelCommonMethods:jh,completeDimensions:th,createDimensions:Ix,createSymbol:Xh}),gw=1e-8;$h.prototype={constructor:$h,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,n=[e,e],i=[-e,-e],r=[],a=[],o=this.geometries,s=0;s<o.length;s++)if("polygon"===o[s].type){var l=o[s].exterior;zr(l,r,a),oe(n,n,r),se(i,i,a)}return 0===s&&(n[0]=n[1]=i[0]=i[1]=0),this._rect=new Tn(n[0],n[1],i[0]-n[0],i[1]-n[1])},contain:function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;r>i;i++)if("polygon"===n[i].type){var a=n[i].exterior,o=n[i].interiors;
if(Zh(a,t[0],t[1])){for(var s=0;s<(o?o.length:0);s++)if(Zh(o[s]))continue t;return!0}}return!1},transformTo:function(t,e,n,i){var r=this.getBoundingRect(),a=r.width/r.height;n?i||(i=n/a):n=a*i;for(var o=new Tn(t,e,n,i),s=r.calculateTransform(o),l=this.geometries,u=0;u<l.length;u++)if("polygon"===l[u].type){for(var h=l[u].exterior,c=l[u].interiors,d=0;d<h.length;d++)ae(h[d],h[d],s);for(var f=0;f<(c?c.length:0);f++)for(var d=0;d<c[f].length;d++)ae(c[f][d],c[f][d],s)}r=this._rect,r.copy(o),this.center=[r.x+r.width/2,r.y+r.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new $h(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};var vw=function(t,e){return Kh(t),p(v(t.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var n=t.properties,i=t.geometry,r=i.coordinates,a=[];"Polygon"===i.type&&a.push({type:"polygon",exterior:r[0],interiors:r.slice(1)}),"MultiPolygon"===i.type&&f(r,function(t){t[0]&&a.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var o=new $h(n[e||"name"],a,n.cp);return o.properties=n,o})},mw=lr(),yw=[0,1],_w=function(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1};_w.prototype={constructor:_w,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&i>=t},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return To(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&(n=n.slice(),fc(n,i.count())),xo(t,yw,n,e)},coordToData:function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&(n=n.slice(),fc(n,i.count()));var r=xo(t,n,yw,e);return this.scale.scale(r)},pointToData:function(){},getTicksCoords:function(t){t=t||{};var e=t.tickModel||this.getTickModel(),n=tc(this,e),i=n.ticks,r=p(i,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this),a=e.get("alignWithLabel");return pc(this,r,a,t.clamp),r},getMinorTicksCoords:function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&100>e||(e=5);var n=this.scale.getMinorTicks(e),i=p(n,function(t){return p(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this);return i},getViewLabels:function(){return Jh(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return uc(this)}};var xw=vw,ww={};f(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){ww[t]=Af[t]});var bw={};f(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){bw[t]=km[t]});var Sw=function(t){this._axes={},this._dimList=[],this.name=t||""};Sw.prototype={constructor:Sw,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return p(this._dimList,gc,this)},getAxesByScale:function(t){return t=t.toLowerCase(),v(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var a=n[r],o=this._axes[a];i[a]=o[e](t[a])}return i}},vc.prototype={constructor:vc,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,n){var i=this.getAxis("x"),r=this.getAxis("y");return n=n||[],n[0]=i.toGlobalCoord(i.dataToCoord(t[0])),n[1]=r.toGlobalCoord(r.dataToCoord(t[1])),n},clampData:function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),a=i.getExtent(),o=n.parse(t[0]),s=i.parse(t[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(r[0],r[1]),o),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(a[0],a[1]),s),Math.max(a[0],a[1])),e},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return e=e||[],e[0]=n.coordToData(n.toLocalCoord(t[0])),e[1]=i.coordToData(i.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]),r=Math.max(t[0],t[1])-n,a=Math.max(e[0],e[1])-i,o=new Tn(n,i,r,a);return o}},h(vc,Sw);var Mw=function(t,e,n,i,r){_w.call(this,t,e,n),this.type=i||"value",this.position=r||"bottom"};Mw.prototype={constructor:Mw,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},h(Mw,_w);var Cw={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},Tw={};Tw.categoryAxis=r({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Cw),Tw.valueAxis=r({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},Cw),Tw.timeAxis=s({scale:!0,min:"dataMin",max:"dataMax"},Tw.valueAxis),Tw.logAxis=s({scale:!0,logBase:10},Tw.valueAxis);var Iw=["value","category","time","log"],kw=function(t,e,n,i){f(Iw,function(o){e.extend({type:t+"Axis."+o,mergeDefaultAndTheme:function(e,i){var a=this.layoutMode,s=a?Qo(e):{},l=i.getTheme();r(e,l.get(o+"Axis")),r(e,this.getDefaultOption()),e.type=n(t,e),a&&Ko(e,s,a)},optionUpdated:function(){var t=this.option;"category"===t.type&&(this.__ordinalMeta=fh.createByAxisModel(this))},getCategories:function(t){var e=this.option;return"category"===e.type?t?e.data:this.__ordinalMeta.categories:void 0},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:a([{},Tw[o+"Axis"],i],!0)})}),Jm.registerSubTypeDefaulter(t+"Axis",_(n,t))},Dw=Jm.extend({type:"cartesian2dAxis",axis:null,init:function(){Dw.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){Dw.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){Dw.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});r(Dw.prototype,rw);var Aw={offset:0};kw("x",Dw,mc,Aw),kw("y",Dw,mc,Aw),Jm.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var Pw=_c.prototype;Pw.type="grid",Pw.axisPointerEnabled=!0,Pw.getRect=function(){return this._rect},Pw.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),f(n.x,function(t){Bh(t.scale,t.model)}),f(n.y,function(t){Bh(t.scale,t.model)});var i={};f(n.x,function(t){xc(n,"y",t,i)}),f(n.y,function(t){xc(n,"x",t,i)}),this.resize(this.model,e)},Pw.resize=function(t,e,n){function i(){f(a,function(t){var e=t.isHorizontal(),n=e?[0,r.width]:[0,r.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),bc(t,e?r.x:r.y)})}var r=$o(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var a=this._axesList;i(),!n&&t.get("containLabel")&&(f(a,function(t){if(!t.model.get("axisLabel.inside")){var e=Nh(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get("axisLabel.margin");r[n]-=e[n]+i,"top"===t.position?r.y+=e.height+i:"left"===t.position&&(r.x+=e.width+i)}}}),i())},Pw.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},Pw.getAxes=function(){return this._axesList.slice()},Pw.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}S(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},Pw.getCartesians=function(){return this._coordsList.slice()},Pw.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},Pw.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},Pw._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)n=r.coordinateSystem,u(l,n)<0&&(n=null);else if(a&&o)n=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)i=this.getAxis("x",a.componentIndex);else if(o)i=this.getAxis("y",o.componentIndex);else if(s){var h=s.coordinateSystem;h===this&&(n=this._coordsList[0])}return{cartesian:n,axis:i}},Pw.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},Pw._initCartesian=function(t,e){function n(n){return function(o,s){if(yc(o,t,e)){var l=o.get("position");"x"===n?"top"!==l&&"bottom"!==l&&(l=i.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=i.left?"right":"left"),i[l]=!0;var u=new Mw(n,zh(o),[0,0],o.get("type"),l),h="category"===u.type;u.onBand=h&&o.get("boundaryGap"),u.inverse=o.get("inverse"),o.axis=u,u.model=o,u.grid=this,u.index=s,this._axesList.push(u),r[n][s]=u,a[n]++}}}var i={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},a={x:0,y:0};return e.eachComponent("xAxis",n("x"),this),e.eachComponent("yAxis",n("y"),this),a.x&&a.y?(this._axesMap=r,void f(r.x,function(e,n){f(r.y,function(i,r){var a="x"+n+"y"+r,o=new vc(a);o.grid=this,o.model=t,this._coordsMap[a]=o,this._coordsList.push(o),o.addAxis(e),o.addAxis(i)},this)},this)):(this._axesMap={},void(this._axesList=[]))},Pw._updateScale=function(t,e){function n(t,e){f(t.mapDimension(e.dim,!0),function(n){e.scale.unionExtentFromData(t,lh(t,n))})}f(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(i){if(Mc(i)){var r=Sc(i,t),a=r[0],o=r[1];if(!yc(a,e,t)||!yc(o,e,t))return;var s=this.getCartesian(a.componentIndex,o.componentIndex),l=i.getData(),u=s.getAxis("x"),h=s.getAxis("y");"list"===l.type&&(n(l,u,i),n(l,h,i))}},this)},Pw.getTooltipAxes=function(t){var e=[],n=[];return f(this.getCartesians(),function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),a=i.getOtherAxis(r);u(e,r)<0&&e.push(r),u(n,a)<0&&n.push(a)}),{baseAxes:e,otherAxes:n}};var Lw=["xAxis","yAxis"];_c.create=function(t,e){var n=[];return t.eachComponent("grid",function(i,r){var a=new _c(i,t,e);a.name="grid_"+r,a.resize(i,e,!0),i.coordinateSystem=a,n.push(a)}),t.eachSeries(function(e){if(Mc(e)){var n=Sc(e,t),i=n[0],r=n[1],a=i.getCoordSysModel(),o=a.coordinateSystem;e.coordinateSystem=o.getCartesian(i.componentIndex,r.componentIndex)}}),n},_c.dimensions=_c.prototype.dimensions=vc.prototype.dimensions,Ss.register("cartesian2d",_c);var Ow=Hy.extend({type:"series.__base_bar__",getInitialData:function(){return uh(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var n=e.dataToPoint(e.clampData(t)),i=this.getData(),r=i.getLayout("offset"),a=i.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return n[o]+=r+a/2,n}return[0/0,0/0]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});Ow.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return this.get("large")?this.get("progressive"):!1},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},defaultOption:{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1}}});var Bw=Fg([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),zw={getBarItemStyle:function(t){var e=Bw(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(e.lineDash=n)}return e}},Ew=pa({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=.5*(a-r),s=r+o,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=Math.cos(l),d=Math.sin(l),f=Math.cos(u),p=Math.sin(u),g=h?u-l<2*Math.PI:l-u<2*Math.PI;g&&(t.moveTo(c*r+n,d*r+i),t.arc(c*s+n,d*s+i,o,-Math.PI+l,l,!h)),t.arc(n,i,a,l,u,!h),t.moveTo(f*a+n,p*a+i),t.arc(f*s+n,p*s+i,o,u-2*Math.PI,u-Math.PI,!h),0!==r&&(t.arc(n,i,r,u,l,h),t.moveTo(c*r+n,p*r+i)),t.closePath()}}),Rw=["itemStyle","barBorderWidth"],Fw=[0,0];o(fo.prototype,zw),Du({type:"bar",render:function(t,e,n){this._updateDrawMode(t);var i=t.get("coordinateSystem");return("cartesian2d"===i||"polar"===i)&&(this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n)),this.group},incrementalPrepareRender:function(t){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(t){var e,n=this.group,i=t.getData(),r=this._data,a=t.coordinateSystem,o=a.getBaseAxis();"cartesian2d"===a.type?e=o.isHorizontal():"polar"===a.type&&(e="angle"===o.dim);var s=t.isAnimationEnabled()?t:null,l=t.get("clip",!0),u=Pc(a,i);n.removeClipPath();var h=t.get("roundCap",!0),c=t.get("showBackground",!0),d=t.getModel("backgroundStyle"),f=d.get("barBorderRadius")||0,p=[],g=this._backgroundEls||[];i.diff(r).add(function(r){var o=i.getItemModel(r),g=Gw[a.type](i,r,o);if(c){var v=Gw[a.type](i,r),m=Vc(a,e,v);m.useStyle(d.getBarItemStyle()),"cartesian2d"===a.type&&m.setShape("r",f),p[r]=m}if(i.hasValue(r)){if(l){var y=Ww[a.type](u,g);if(y)return void n.remove(_)}var _=Vw[a.type](r,g,e,s,!1,h);i.setItemGraphicEl(r,_),n.add(_),zc(_,i,r,o,g,t,e,"polar"===a.type)}}).update(function(o,v){var m=i.getItemModel(o),y=Gw[a.type](i,o,m);if(c){var _=g[v];_.useStyle(d.getBarItemStyle()),"cartesian2d"===a.type&&_.setShape("r",f),p[o]=_;var x=Gw[a.type](i,o),w=Wc(e,x,a);Ja(_,{shape:w},s,o)}var b=r.getItemGraphicEl(v);if(!i.hasValue(o))return void n.remove(b);if(l){var S=Ww[a.type](u,y);if(S)return void n.remove(b)}b?Ja(b,{shape:y},s,o):b=Vw[a.type](o,y,e,s,!0,h),i.setItemGraphicEl(o,b),n.add(b),zc(b,i,o,m,y,t,e,"polar"===a.type)}).remove(function(t){var e=r.getItemGraphicEl(t);"cartesian2d"===a.type?e&&Lc(t,s,e):e&&Oc(t,s,e)}).execute();var v=this._backgroundGroup||(this._backgroundGroup=new Ap);v.removeAll();for(var m=0;m<p.length;++m)v.add(p[m]);n.add(v),this._backgroundEls=p,this._data=i},_renderLarge:function(t){this._clear(),Rc(t,this.group);var e=t.get("clip",!0)?Ac(t.coordinateSystem,!1,t):null;e?this.group.setClipPath(e):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){this._removeBackground(),Rc(e,this.group,!0)},dispose:H,remove:function(t){this._clear(t)},_clear:function(t){var e=this.group,n=this._data;t&&t.get("animation")&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl(function(e){"sector"===e.type?Oc(e.dataIndex,t,e):Lc(e.dataIndex,t,e)})):e.removeAll(),this._data=null},_removeBackground:function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null}});var Nw=Math.max,Hw=Math.min,Ww={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;0>n&&(e.x+=e.width,e.width=-e.width),0>i&&(e.y+=e.height,e.height=-e.height);var r=Nw(e.x,t.x),a=Hw(e.x+e.width,t.x+t.width),o=Nw(e.y,t.y),s=Hw(e.y+e.height,t.y+t.height);e.x=r,e.y=o,e.width=a-r,e.height=s-o;var l=e.width<0||e.height<0;return 0>n&&(e.x+=e.width,e.width=-e.width),0>i&&(e.y+=e.height,e.height=-e.height),l},polar:function(){return!1}},Vw={cartesian2d:function(t,e,n,i,r){var a=new im({shape:o({},e),z2:1});if(a.name="item",i){var s=a.shape,l=n?"height":"width",u={};s[l]=0,u[l]=e[l],km[r?"updateProps":"initProps"](a,{shape:u},i,t)}return a},polar:function(t,e,n,i,r,a){var o=e.startAngle<e.endAngle,l=!n&&a?Ew:Zv,u=new l({shape:s({clockwise:o},e),z2:1});if(u.name="item",i){var h=u.shape,c=n?"r":"endAngle",d={};h[c]=n?0:e.startAngle,d[c]=e[c],km[r?"updateProps":"initProps"](u,{shape:d},i,t)}return u}},Gw={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=n?Ec(n,i):0,a=i.width>0?1:-1,o=i.height>0?1:-1;return{x:i.x+a*r/2,y:i.y+o*r/2,width:i.width-a*r,height:i.height-o*r}},polar:function(t,e){var n=t.getItemLayout(e);return{cx:n.cx,cy:n.cy,r0:n.r0,r:n.r,startAngle:n.startAngle,endAngle:n.endAngle}}},Xw=ta.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var n=e.points,i=this.__startPoint,r=this.__baseDimIdx,a=0;a<n.length;a+=2)i[r]=n[a+r],t.moveTo(i[0],i[1]),t.lineTo(n[a],n[a+1])}}),Yw=fl(function(t){var e=this,n=Fc(e,t.offsetX,t.offsetY);e.dataIndex=n>=0?n:null},30,!1),Uw=Math.PI,jw=function(t,e){this.opt=e,this.axisModel=t,s(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new Ap;var n=new Ap({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n};jw.prototype={constructor:jw,hasBuilder:function(t){return!!qw[t]},add:function(t){qw[t].call(this)},getGroup:function(){return this.group}};var qw={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var n=this.axisModel.axis.getExtent(),i=this._transform,r=[n[0],0],a=[n[1],0];i&&(ae(r,r,i),ae(a,a,i));var s=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new am({anid:"line",subPixelOptimize:!0,shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:s,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1}));var l=e.get("axisLine.symbol"),u=e.get("axisLine.symbolSize"),h=e.get("axisLine.symbolOffset")||0;if("number"==typeof h&&(h=[h,h]),null!=l){"string"==typeof l&&(l=[l,l]),("string"==typeof u||"number"==typeof u)&&(u=[u,u]);var c=u[0],d=u[1];f([{rotate:t.rotation+Math.PI/2,offset:h[0],r:0},{rotate:t.rotation-Math.PI/2,offset:h[1],r:Math.sqrt((r[0]-a[0])*(r[0]-a[0])+(r[1]-a[1])*(r[1]-a[1]))}],function(e,n){if("none"!==l[n]&&null!=l[n]){var i=Xh(l[n],-c/2,-d/2,c,d,s.stroke,!0),a=e.r+e.offset,o=[r[0]+a*Math.cos(t.rotation),r[1]-a*Math.sin(t.rotation)];i.attr({rotation:e.rotate,position:o,silent:!0,z2:11}),this.group.add(i)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,n=Zc(this,t,e),i=Kc(this,t,e);Xc(t,i,n),$c(this,t,e)},axisName:function(){var t=this.opt,e=this.axisModel,n=k(t.axisName,e.get("name"));if(n){var i,r=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,u=this.axisModel.axis.getExtent(),h=u[0]>u[1]?-1:1,c=["start"===r?u[0]-h*l:"end"===r?u[1]+h*l:(u[0]+u[1])/2,jc(r)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*Uw/180);var f;jc(r)?i=$w(t.rotation,null!=d?d:t.rotation,a):(i=Gc(t,r,d||0,u),f=t.axisNameAvailableWidth,null!=f&&(f=Math.abs(f/Math.sin(i.rotation)),!isFinite(f)&&(f=null)));var p=s.getFont(),g=e.get("nameTruncate",!0)||{},v=g.ellipsis,m=k(t.nameTruncateMaxWidth,g.maxWidth,f),y=null!=v&&null!=m?Ym(n,m,p,v,{minChar:2,placeholder:g.placeholder}):n,_=e.get("tooltip",!0),x=e.mainType,w={componentType:x,name:n,$vars:["name"]};w[x+"Index"]=e.componentIndex;var b=new Yv({anid:"name",__fullText:n,__truncatedText:y,position:c,rotation:i.rotation,silent:Kw(e),z2:1,tooltip:_&&_.show?o({content:n,formatter:function(){return n},formatterParams:w},_):null});Ga(b.style,s,{text:y,textFont:p,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:s.get("align")||i.textAlign,textVerticalAlign:s.get("verticalAlign")||i.textVerticalAlign}),e.get("triggerEvent")&&(b.eventData=Zw(e),b.eventData.targetType="axisName",b.eventData.name=n),this._dumbGroup.add(b),b.updateTransform(),this.group.add(b),b.decomposeTransform()}}},Zw=jw.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},$w=jw.innerTextLayout=function(t,e,n){var i,r,a=ko(e-t);return Do(a)?(r=n>0?"top":"bottom",i="center"):Do(a-Uw)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=a>0&&Uw>a?n>0?"right":"left":n>0?"left":"right"),{rotation:a,textAlign:i,textVerticalAlign:r}},Kw=jw.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},Qw=f,Jw=_,tb=Iu({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,i){this.axisPointerClass&&rd(t),tb.superApply(this,"render",arguments),ud(this,t,e,n,i,!0)},updateAxisPointer:function(t,e,n,i){ud(this,t,e,n,i,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),tb.superApply(this,"remove",arguments)},dispose:function(t,e){hd(this,e),tb.superApply(this,"dispose",arguments)}}),eb=[];tb.registerAxisPointerClass=function(t,e){eb[t]=e},tb.getAxisPointerClass=function(t){return t&&eb[t]};var nb=["axisLine","axisTickLabel","axisName"],ib=["splitArea","splitLine","minorSplitLine"],rb=tb.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,n,i){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new Ap,this.group.add(this._axisGroup),t.get("show")){var a=t.getCoordSysModel(),o=cd(a,t),s=new jw(t,o);f(nb,s.add,s),this._axisGroup.add(s.getGroup()),f(ib,function(e){t.get(e+".show")&&this["_"+e](t,a)},this),ro(r,this._axisGroup,t),rb.superCall(this,"render",t,e,n,i)}},remove:function(){fd(this)},_splitLine:function(t,e){var n=t.axis;if(!n.scale.isBlank()){var i=t.getModel("splitLine"),r=i.getModel("lineStyle"),a=r.get("color");a=x(a)?a:[a];for(var o=e.coordinateSystem.getRect(),l=n.isHorizontal(),u=0,h=n.getTicksCoords({tickModel:i}),c=[],d=[],f=r.getLineStyle(),p=0;p<h.length;p++){var g=n.toGlobalCoord(h[p].coord);l?(c[0]=g,c[1]=o.y,d[0]=g,d[1]=o.y+o.height):(c[0]=o.x,c[1]=g,d[0]=o.x+o.width,d[1]=g);var v=u++%a.length,m=h[p].tickValue;this._axisGroup.add(new am({anid:null!=m?"line_"+h[p].tickValue:null,subPixelOptimize:!0,shape:{x1:c[0],y1:c[1],x2:d[0],y2:d[1]},style:s({stroke:a[v]},f),silent:!0}))}}},_minorSplitLine:function(t,e){var n=t.axis,i=t.getModel("minorSplitLine"),r=i.getModel("lineStyle"),a=e.coordinateSystem.getRect(),o=n.isHorizontal(),s=n.getMinorTicksCoords();if(s.length)for(var l=[],u=[],h=r.getLineStyle(),c=0;c<s.length;c++)for(var d=0;d<s[c].length;d++){var f=n.toGlobalCoord(s[c][d].coord);o?(l[0]=f,l[1]=a.y,u[0]=f,u[1]=a.y+a.height):(l[0]=a.x,l[1]=f,u[0]=a.x+a.width,u[1]=f),this._axisGroup.add(new am({anid:"minor_line_"+s[c][d].tickValue,subPixelOptimize:!0,shape:{x1:l[0],y1:l[1],x2:u[0],y2:u[1]},style:h,silent:!0}))}},_splitArea:function(t,e){dd(this,this._axisGroup,t,e)}});rb.extend({type:"xAxis"}),rb.extend({type:"yAxis"}),Iu({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new im({shape:t.coordinateSystem.getRect(),style:s({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),vu(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),bu(X_.VISUAL.LAYOUT,_(Ih,"bar")),bu(X_.VISUAL.PROGRESSIVE_LAYOUT,Fx),Su({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}}),Tu({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),Iu({type:"title",render:function(t,e,n){if(this.group.removeAll(),t.get("show")){var i=this.group,r=t.getModel("textStyle"),a=t.getModel("subtextStyle"),o=t.get("textAlign"),s=D(t.get("textBaseline"),t.get("textVerticalAlign")),l=new Yv({style:Ga({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),u=l.getBoundingRect(),h=t.get("subtext"),c=new Yv({style:Ga({},a,{text:h,textFill:a.getTextColor(),y:u.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink"),p=t.get("triggerEvent",!0);l.silent=!d&&!p,c.silent=!f&&!p,d&&l.on("click",function(){qo(d,"_"+t.get("target"))}),f&&c.on("click",function(){qo(d,"_"+t.get("subtarget"))}),l.eventData=c.eventData=p?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(l),h&&i.add(c);var g=i.getBoundingRect(),v=t.getBoxLayoutParams();v.width=g.width,v.height=g.height;var m=$o(v,{width:n.getWidth(),height:n.getHeight()},t.get("padding"));o||(o=t.get("left")||t.get("right"),"middle"===o&&(o="center"),"right"===o?m.x+=m.width:"center"===o&&(m.x+=m.width/2)),s||(s=t.get("top")||t.get("bottom"),"center"===s&&(s="middle"),"bottom"===s?m.y+=m.height:"middle"===s&&(m.y+=m.height/2),s=s||"top"),i.attr("position",[m.x,m.y]);var y={textAlign:o,textVerticalAlign:s};l.setStyle(y),c.setStyle(y),g=i.getBoundingRect();var _=m.margin,x=t.getItemStyle(["color","opacity"]);x.fill=t.get("backgroundColor");var w=new im({shape:{x:g.x-_[3],y:g.y-_[0],width:g.width+_[1]+_[3],height:g.height+_[0]+_[2],r:t.get("borderRadius")},style:x,subPixelOptimize:!0,silent:!0});i.add(w)}}});var ab=Qy.legend.selector,ob={all:{type:"all",title:i(ab.all)},inverse:{type:"inverse",title:i(ab.inverse)}},sb=Tu({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},mergeOption:function(t){sb.superCall(this,"mergeOption",t),this._updateSelector(t)},_updateSelector:function(t){var e=t.selector;e===!0&&(e=t.selector=["all","inverse"]),x(e)&&f(e,function(t,n){b(t)&&(t={type:t}),e[n]=r(t,ob[t.type])})},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=[],n=[];t.eachRawSeries(function(i){var r=i.name;n.push(r);var a;if(i.legendVisualProvider){var o=i.legendVisualProvider,s=o.getAllNames();t.isSeriesFiltered(i)||(n=n.concat(s)),s.length?e=e.concat(s):a=!0}else a=!0;a&&ar(i)&&e.push(i.name)}),this._availableNames=n;var i=this.get("data")||e,r=p(i,function(t){return("string"==typeof t||"number"==typeof t)&&(t={name:t}),new fo(t,this,this.ecModel)},this);this._data=r},getData:function(){return this._data},select:function(t){var e=this.option.selected,n=this.get("selectedMode");if("single"===n){var i=this._data;f(i,function(t){e[t.get("name")]=!1})}e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},allSelect:function(){var t=this._data,e=this.option.selected;f(t,function(t){e[t.get("name",!0)]=!0})},inverseSelect:function(){var t=this._data,e=this.option.selected;f(t,function(t){var n=t.get("name",!0);e.hasOwnProperty(n)||(e[n]=!0),e[n]=!e[n]})},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&u(this._availableNames,t)>=0},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",itemStyle:{borderWidth:0},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:" sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}}});_u("legendToggleSelect","legendselectchanged",_(pd,"toggleSelected")),_u("legendAllSelect","legendselectall",_(pd,"allSelect")),_u("legendInverseSelect","legendinverseselect",_(pd,"inverseSelect")),_u("legendSelect","legendselected",_(pd,"select")),_u("legendUnSelect","legendunselected",_(pd,"unSelect"));var lb=_,ub=f,hb=Ap,cb=Iu({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new hb),this._backgroundEl,this.group.add(this._selectorGroup=new hb),this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},getSelectorGroup:function(){return this._selectorGroup},render:function(t,e,n){var i=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align"),a=t.get("orient");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===a?"right":"left");var o=t.get("selector",!0),l=t.get("selectorPosition",!0);!o||l&&"auto"!==l||(l="horizontal"===a?"end":"start"),this.renderInner(r,t,e,n,o,a,l);var u=t.getBoxLayoutParams(),h={width:n.getWidth(),height:n.getHeight()},c=t.get("padding"),d=$o(u,h,c),f=this.layoutInner(t,r,d,i,o,l),p=$o(s({width:f.width,height:f.height},u),h,c);this.group.attr("position",[p.x-f.x,p.y-f.y]),this.group.add(this._backgroundEl=gd(f,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},renderInner:function(t,e,n,i,r,a,o){var s=this.getContentGroup(),l=F(),u=e.get("selectedMode"),h=[];n.eachRawSeries(function(t){!t.get("legendHoverLink")&&h.push(t.id)}),ub(e.getData(),function(r,a){var o=r.get("name");if(!this.newlineDisabled&&(""===o||"\n"===o))return void s.add(new hb({newline:!0}));
var c=n.getSeriesByName(o)[0];if(!l.get(o))if(c){var d=c.getData(),f=d.getVisual("color"),p=d.getVisual("borderColor");"function"==typeof f&&(f=f(c.getDataParams(0))),"function"==typeof p&&(p=p(c.getDataParams(0)));var g=d.getVisual("legendSymbol")||"roundRect",v=d.getVisual("symbol"),m=this._createItem(o,a,r,e,g,v,t,f,p,u);m.on("click",lb(md,o,null,i,h)).on("mouseover",lb(yd,c.name,null,i,h)).on("mouseout",lb(_d,c.name,null,i,h)),l.set(o,!0)}else n.eachRawSeries(function(n){if(!l.get(o)&&n.legendVisualProvider){var s=n.legendVisualProvider;if(!s.containName(o))return;var c=s.indexOfName(o),d=s.getItemVisual(c,"color"),f=s.getItemVisual(c,"borderColor"),p="roundRect",g=this._createItem(o,a,r,e,p,null,t,d,f,u);g.on("click",lb(md,null,o,i,h)).on("mouseover",lb(yd,null,o,i,h)).on("mouseout",lb(_d,null,o,i,h)),l.set(o,!0)}},this)},this),r&&this._createSelector(r,e,i,a,o)},_createSelector:function(t,e,n){function i(t){var i=t.type,a=new Yv({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:"all"===i?"legendAllSelect":"legendInverseSelect"})}});r.add(a);var o=e.getModel("selectorLabel"),s=e.getModel("emphasis.selectorLabel");Wa(a.style,a.hoverStyle={},o,s,{defaultText:t.title,isRectText:!1}),Ra(a)}var r=this.getSelectorGroup();ub(t,function(t){i(t)})},_createItem:function(t,e,n,i,r,a,s,l,u,h){var c=i.get("itemWidth"),d=i.get("itemHeight"),f=i.get("inactiveColor"),p=i.get("inactiveBorderColor"),g=i.get("symbolKeepAspect"),v=i.getModel("itemStyle"),m=i.isSelected(t),y=new hb,_=n.getModel("textStyle"),x=n.get("icon"),w=n.getModel("tooltip"),b=w.parentModel;r=x||r;var S=Xh(r,0,0,c,d,m?l:f,null==g?!0:g);if(y.add(vd(S,r,v,u,p,m)),!x&&a&&(a!==r||"none"===a)){var M=.8*d;"none"===a&&(a="circle");var C=Xh(a,(c-M)/2,(d-M)/2,M,M,m?l:f,null==g?!0:g);y.add(vd(C,a,v,u,p,m))}var T="left"===s?c+5:-5,I=s,k=i.get("formatter"),D=t;"string"==typeof k&&k?D=k.replace("{name}",null!=t?t:""):"function"==typeof k&&(D=k(t)),y.add(new Yv({style:Ga({},_,{text:D,x:T,y:d/2,textFill:m?_.getTextColor():f,textAlign:I,textVerticalAlign:"middle"})}));var A=new im({shape:y.getBoundingRect(),invisible:!0,tooltip:w.get("show")?o({content:t,formatter:b.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},w.option):null});return y.add(A),y.eachChild(function(t){t.silent=!0}),A.silent=!h,this.getContentGroup().add(y),Ra(y),y.__legendDataIndex=e,y},layoutInner:function(t,e,n,i,r,a){var o=this.getContentGroup(),s=this.getSelectorGroup();$m(t.get("orient"),o,t.get("itemGap"),n.width,n.height);var l=o.getBoundingRect(),u=[-l.x,-l.y];if(r){$m("horizontal",s,t.get("selectorItemGap",!0));var h=s.getBoundingRect(),c=[-h.x,-h.y],d=t.get("selectorButtonGap",!0),f=t.getOrient().index,p=0===f?"width":"height",g=0===f?"height":"width",v=0===f?"y":"x";"end"===a?c[f]+=l[p]+d:u[f]+=h[p]+d,c[1-f]+=l[g]/2-h[g]/2,s.attr("position",c),o.attr("position",u);var m={x:0,y:0};return m[p]=l[p]+d+h[p],m[g]=Math.max(l[g],h[g]),m[v]=Math.min(0,h[v]+c[1-f]),m}return o.attr("position",u),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}}),db=function(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var n=0;n<e.length;n++)if(!e[n].isSelected(t.name))return!1;return!0})};mu(X_.PROCESSOR.SERIES_FILTER,db),Jm.registerSubTypeDefaulter("legend",function(){return"plain"});var fb=sb.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,n,i){var r=Qo(t);fb.superCall(this,"init",t,e,n,i),xd(this,t,r)},mergeOption:function(t,e){fb.superCall(this,"mergeOption",t,e),xd(this,this.option,t)}}),pb=Ap,gb=["width","height"],vb=["x","y"],mb=cb.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){mb.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new pb),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new pb),this._showController},resetInner:function(){mb.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,e,n,i,r,a,o){function s(t,n){var r=t+"DataIndex",a=so(e.get("pageIcons",!0)[e.getOrient().name][n],{onclick:y(l._pageGo,l,r,e,i)},{x:-h[0]/2,y:-h[1]/2,width:h[0],height:h[1]});a.name=t,u.add(a)}var l=this;mb.superCall(this,"renderInner",t,e,n,i,r,a,o);var u=this._controllerGroup,h=e.get("pageIconSize",!0);x(h)||(h=[h,h]),s("pagePrev",0);var c=e.getModel("pageTextStyle");u.add(new Yv({name:"pageText",style:{textFill:c.getTextColor(),font:c.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),s("pageNext",1)},layoutInner:function(t,e,n,r,a,o){var s=this.getSelectorGroup(),l=t.getOrient().index,u=gb[l],h=vb[l],c=gb[1-l],d=vb[1-l];a&&$m("horizontal",s,t.get("selectorItemGap",!0));var f=t.get("selectorButtonGap",!0),p=s.getBoundingRect(),g=[-p.x,-p.y],v=i(n);a&&(v[u]=n[u]-p[u]-f);var m=this._layoutContentAndController(t,r,v,l,u,c,d);if(a){if("end"===o)g[l]+=m[u]+f;else{var y=p[u]+f;g[l]-=y,m[h]-=y}m[u]+=p[u]+f,g[1-l]+=m[d]+m[c]/2-p[c]/2,m[c]=Math.max(m[c],p[c]),m[d]=Math.min(m[d],p[d]+g[1-l]),s.attr("position",g)}return m},_layoutContentAndController:function(t,e,n,i,r,a,o){var s=this.getContentGroup(),l=this._containerGroup,u=this._controllerGroup;$m(t.get("orient"),s,t.get("itemGap"),i?n.width:null,i?null:n.height),$m("horizontal",u,t.get("pageButtonItemGap",!0));var h=s.getBoundingRect(),c=u.getBoundingRect(),d=this._showController=h[r]>n[r],f=[-h.x,-h.y];e||(f[i]=s.position[i]);var p=[0,0],g=[-c.x,-c.y],v=D(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(d){var m=t.get("pageButtonPosition",!0);"end"===m?g[i]+=n[r]-c[r]:p[i]+=c[r]+v}g[1-i]+=h[a]/2-c[a]/2,s.attr("position",f),l.attr("position",p),u.attr("position",g);var y={x:0,y:0};if(y[r]=d?n[r]:h[r],y[a]=Math.max(h[a],c[a]),y[o]=Math.min(0,c[o]+g[1-i]),l.__rectSize=n[r],d){var _={x:0,y:0};_[r]=Math.max(n[r]-c[r]-v,0),_[a]=y[a],l.setClipPath(new im({shape:_})),l.__rectSize=_[r]}else u.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var x=this._getPageInfo(t);return null!=x.pageIndex&&Ja(s,{position:x.contentPosition},d?t:!1),this._updatePageInfoView(t,x),y},_pageGo:function(t,e,n){var i=this._getPageInfo(e)[t];null!=i&&n.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:e.id})},_updatePageInfoView:function(t,e){var n=this._controllerGroup;f(["pagePrev","pageNext"],function(i){var r=null!=e[i+"DataIndex"],a=n.childOfName(i);a&&(a.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),a.cursor=r?"pointer":"default")});var i=n.childOfName("pageText"),r=t.get("pageFormatter"),a=e.pageIndex,o=null!=a?a+1:0,s=e.pageCount;i&&r&&i.setStyle("text",b(r)?r.replace("{current}",o).replace("{total}",s):r({current:o,total:s}))},_getPageInfo:function(t){function e(t){if(t){var e=t.getBoundingRect(),n=e[l]+t.position[o];return{s:n,e:n+e[s],i:t.__legendDataIndex}}}function n(t,e){return t.e>=e&&t.s<=e+a}var i=t.get("scrollDataIndex",!0),r=this.getContentGroup(),a=this._containerGroup.__rectSize,o=t.getOrient().index,s=gb[o],l=vb[o],u=this._findTargetItemIndex(i),h=r.children(),c=h[u],d=h.length,f=d?1:0,p={contentPosition:r.position.slice(),pageCount:f,pageIndex:f-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!c)return p;var g=e(c);p.contentPosition[o]=-g.s;for(var v=u+1,m=g,y=g,_=null;d>=v;++v)_=e(h[v]),(!_&&y.e>m.s+a||_&&!n(_,m.s))&&(m=y.i>m.i?y:_,m&&(null==p.pageNextDataIndex&&(p.pageNextDataIndex=m.i),++p.pageCount)),y=_;for(var v=u-1,m=g,y=g,_=null;v>=-1;--v)_=e(h[v]),_&&n(y,_.s)||!(m.i<y.i)||(y=m,null==p.pagePrevDataIndex&&(p.pagePrevDataIndex=m.i),++p.pageCount,++p.pageIndex),m=_;return p},_findTargetItemIndex:function(t){if(!this._showController)return 0;var e,n,i=this.getContentGroup();return i.eachChild(function(i,r){var a=i.__legendDataIndex;null==n&&null!=a&&(n=r),a===t&&(e=r)}),null!=e?e:n}});_u("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})});var yb=function(t,e){var n,i=[],r=t.seriesIndex;if(null==r||!(n=e.getSeriesByIndex(r)))return{point:[]};var a=n.getData(),o=sr(a,t);if(null==o||0>o||x(o))return{point:[]};var s=a.getItemGraphicEl(o),l=n.coordinateSystem;if(n.getTooltipPosition)i=n.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)i=l.dataToPoint(a.getValues(p(l.dimensions,function(t){return a.mapDimension(t)}),o,!0))||[];else if(s){var u=s.getBoundingRect().clone();u.applyTransform(s.transform),i=[u.x+u.width/2,u.y+u.height/2]}return{point:i,el:s}},_b=f,xb=_,wb=lr(),bb=function(t,e,n){var i=t.currTrigger,r=[t.x,t.y],a=t,o=t.dispatchAction||y(n.dispatchAction,n),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){Ad(r)&&(r=yb({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},e).point);var l=Ad(r),u=a.axesInfo,h=s.axesInfo,c="leave"===i||Ad(r),d={},f={},p={list:[],map:{}},g={showPointer:xb(Sd,f),showTooltip:xb(Md,p)};_b(s.coordSysMap,function(t,e){var n=l||t.containPoint(r);_b(s.coordSysAxesInfo[e],function(t){var e=t.axis,i=kd(u,t);if(!c&&n&&(!u||i)){var a=i&&i.value;null!=a||l||(a=e.pointToData(r)),null!=a&&wd(t,a,g,!1,d)}})});var v={};return _b(h,function(t,e){var n=t.linkGroup;n&&!f[e]&&_b(n.axesInfo,function(e,i){var r=f[i];if(e!==t&&r){var a=r.value;n.mapper&&(a=t.axis.scale.parse(n.mapper(a,Dd(e),Dd(t)))),v[t.key]=a}})}),_b(v,function(t,e){wd(h[e],t,g,!0,d)}),Cd(f,h,d),Td(p,r,t,o),Id(h,o,n),d}},Sb=(Tu({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),lr()),Mb=f,Cb=Iu({type:"axisPointer",render:function(t,e,n){var i=e.getComponent("tooltip"),r=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";Pd("axisPointer",n,function(t,e,n){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){Rd(e.getZr(),"axisPointer"),Cb.superApply(this._model,"remove",arguments)},dispose:function(t,e){Rd("axisPointer",e),Cb.superApply(this._model,"dispose",arguments)}}),Tb=lr(),Ib=i,kb=y;Fd.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,i){var r=e.get("value"),a=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,i||this._lastValue!==r||this._lastStatus!==a){this._lastValue=r,this._lastStatus=a;var o=this._group,s=this._handle;if(!a||"hide"===a)return o&&o.hide(),void(s&&s.hide());o&&o.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,n);var u=l.graphicKey;u!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=u;var h=this._moveAnimation=this.determineAnimation(t,e);if(o){var c=_(Nd,e,h);this.updatePointerEl(o,l,c,e),this.updateLabelEl(o,l,c,e)}else o=this._group=new Ap,this.createPointerEl(o,l,t,e),this.createLabelEl(o,l,t,e),n.getZr().add(o);Gd(o,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,a=e.get("snap");if(!a&&!r)return!1;if("auto"===n||null==n){var o=this.animationThreshold;if(r&&i.getBandWidth()>o)return!0;if(a){var s=ad(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/s>o}return!1}return n===!0},makeElOption:function(){},createPointerEl:function(t,e){var n=e.pointer;if(n){var i=Tb(t).pointerEl=new km[n.type](Ib(e.pointer));t.add(i)}},createLabelEl:function(t,e,n,i){if(e.label){var r=Tb(t).labelEl=new im(Ib(e.label));t.add(r),Wd(r,i)}},updatePointerEl:function(t,e,n){var i=Tb(t).pointerEl;i&&e.pointer&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){var r=Tb(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{shape:e.label.shape,position:e.label.position}),Wd(r,i))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),a=e.get("status");if(!r.get("show")||!a||"hide"===a)return i&&n.remove(i),void(this._handle=null);var o;this._handle||(o=!0,i=this._handle=so(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){Yf(t.event)},onmousedown:kb(this._onHandleDragMove,this,0,0),drift:kb(this._onHandleDragMove,this),ondragend:kb(this._onHandleDragEnd,this)}),n.add(i)),Gd(i,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];i.setStyle(r.getItemStyle(null,s));var l=r.get("size");x(l)||(l=[l,l]),i.attr("scale",[l[0]/2,l[1]/2]),pl(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)}},_moveHandleToValue:function(t,e){Nd(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Vd(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(Vd(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(Vd(i)),Tb(n).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,n=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:n.axis.dim,axisIndex:n.componentIndex}]})}},_onHandleDragEnd:function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}},Fd.prototype.constructor=Fd,vr(Fd);var Db=Fd.extend({makeElOption:function(t,e,n,i,r){var a=n.axis,o=a.grid,s=i.get("type"),l=Qd(o,a).getOtherAxis(a).getGlobalExtent(),u=a.toGlobalCoord(a.dataToCoord(e,!0));if(s&&"none"!==s){var h=Xd(i),c=Ab[s](a,u,l);c.style=h,t.graphicKey=c.type,t.pointer=c}var d=cd(o.model,n);Zd(e,t,d,n,i,r)},getHandleTransform:function(t,e,n){var i=cd(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:qd(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n){var i=n.axis,r=i.grid,a=i.getGlobalExtent(!0),o=Qd(r,i).getOtherAxis(i).getGlobalExtent(),s="x"===i.dim?0:1,l=t.position;l[s]+=e[s],l[s]=Math.min(a[1],l[s]),l[s]=Math.max(a[0],l[s]);var u=(o[1]+o[0])/2,h=[u,u];h[s]=l[s];var c=[{verticalAlign:"middle"},{align:"center"}];return{position:l,rotation:t.rotation,cursorPoint:h,tooltipOption:c[s]}}}),Ab={line:function(t,e,n){var i=$d([e,n[0]],[e,n[1]],Jd(t));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:Kd([e-i/2,n[0]],[i,r],Jd(t))}}};tb.registerAxisPointerClass("CartesianAxisPointer",Db),vu(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!x(e)&&(t.axisPointer.link=[e])}}),mu(X_.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=Qc(t,e)}),_u({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},bb),Tu({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var Pb=f,Lb=Fo,Ob=["","-webkit-","-moz-","-o-"],Bb="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";af.prototype={constructor:af,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),n=t.style;"absolute"!==n.position&&"absolute"!==e.position&&(n.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el,n=this._styleCoord;e.style.cssText=Bb+nf(t)+";left:"+n[0]+"px;top:"+n[1]+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var n=this._styleCoord;rf(n,this._zr,this._appendToBody,t,e);var i=this.el.style;i.left=n[0]+"px",i.top=n[1]+"px"},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){this.el.parentNode.removeChild(this.el)},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var n=document.defaultView.getComputedStyle(this.el);n&&(t+=parseInt(n.borderLeftWidth,10)+parseInt(n.borderRightWidth,10),e+=parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10))}return{width:t,height:e}}},of.prototype={constructor:of,_enterable:!0,update:function(){},show:function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,n){this.el&&this._zr.remove(this.el);for(var i={},r=t,a="{marker",o="|}",s=r.indexOf(a);s>=0;){var l=r.indexOf(o),u=r.substr(s+a.length,l-s-a.length);i["marker"+u]=u.indexOf("sub")>-1?{textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[u],textOffset:[3,0]}:{textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[u]},r=r.substr(l+1),s=r.indexOf("{marker")}this.el=new Yv({style:{rich:i,text:t,textLineHeight:20,textBackgroundColor:n.get("backgroundColor"),textBorderRadius:n.get("borderRadius"),textFill:n.get("textStyle.color"),textPadding:n.get("padding")},z:n.get("z")}),this._zr.add(this.el);var h=this;this.el.on("mouseover",function(){h._enterable&&(clearTimeout(h._hideTimeout),h._show=!0),h._inContent=!0}),this.el.on("mouseout",function(){h._enterable&&h._show&&h.hideLater(h._hideDelay),h._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){this.el&&this.el.attr("position",[t,e])},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var zb=y,Eb=f,Rb=wo,Fb=new im({shape:{x:-1,y:-1,width:2,height:2}});Iu({type:"tooltip",init:function(t,e){if(!vf.node){var n=t.getComponent("tooltip"),i=n.get("renderMode");this._renderMode=fr(i);var r;"html"===this._renderMode?(r=new af(e.getDom(),e,{appendToBody:n.get("appendToBody",!0)}),this._newLine="<br/>"):(r=new of(e),this._newLine="\n"),this._tooltipContent=r}},render:function(t,e,n){if(!vf.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var i=this._tooltipContent;i.update(),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");Pd("itemTooltip",this._api,zb(function(t,n,i){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(n,i):"leave"===t&&this._hide(i))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,n=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var i=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&i.manuallyShowTip(t,e,n,{x:i._lastX,y:i._lastY})})}},manuallyShowTip:function(t,e,n,i){if(i.from!==this.uid&&!vf.node){var r=lf(i,n);this._ticket="";var a=i.dataByCoordSys;if(i.tooltip&&null!=i.x&&null!=i.y){var o=Fb;o.position=[i.x,i.y],o.update(),o.tooltip=i.tooltip,this._tryShow({offsetX:i.x,offsetY:i.y,target:o},r)}else if(a)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:i.dataByCoordSys,tooltipOption:i.tooltipOption},r);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var s=yb(i,e),l=s.point[0],u=s.point[1];null!=l&&null!=u&&this._tryShow({offsetX:l,offsetY:u,position:i.position,target:s.el},r)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r))}},manuallyHideTip:function(t,e,n,i){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,i.from!==this.uid&&this._hide(lf(i,n))},_manuallyAxisShowTip:function(t,e,n,i){var r=i.seriesIndex,a=i.dataIndex,o=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=a&&null!=o){var s=e.getSeriesByIndex(r);if(s){var l=s.getData(),t=sf([l.getItemModel(a),s,(s.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:a,position:i.position}),!0}}},_tryShow:function(t,e){var n=t.target,i=this._tooltipModel;if(i){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):n&&null!=n.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,n,e)):n&&n.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,n,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var n=t.get("showDelay");e=y(e,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(e,n):e()},_showAxisTooltip:function(t,e){var n=this._ecModel,i=this._tooltipModel,a=[e.offsetX,e.offsetY],o=[],s=[],l=sf([e.tooltipOption,i]),u=this._renderMode,h=this._newLine,c={};Eb(t,function(t){Eb(t.dataByAxis,function(t){var e=n.getComponent(t.axisDim+"Axis",t.axisIndex),i=t.value,a=[];if(e&&null!=i){var l=jd(i,e.axis,n,t.seriesDataIndices,t.valueLabelOpt);f(t.seriesDataIndices,function(o){var h=n.getSeriesByIndex(o.seriesIndex),d=o.dataIndexInside,f=h&&h.getDataParams(d);if(f.axisDim=t.axisDim,f.axisIndex=t.axisIndex,f.axisType=t.axisType,f.axisId=t.axisId,f.axisValue=Fh(e.axis,i),f.axisValueLabel=l,f){s.push(f);var p,g=h.formatTooltip(d,!0,null,u);if(S(g)){p=g.html;var v=g.markers;r(c,v)}else p=g;a.push(p)}});var d=l;o.push("html"!==u?a.join(h):(d?No(d)+h:"")+a.join(h))}})},this),o.reverse(),o=o.join(this._newLine+this._newLine);var d=e.position;this._showOrMove(l,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(l,d,a[0],a[1],this._tooltipContent,s):this._showTooltipContent(l,o,s,Math.random(),a[0],a[1],d,void 0,c)})},_showSeriesItemTooltip:function(t,e,n){var i=this._ecModel,r=e.seriesIndex,a=i.getSeriesByIndex(r),o=e.dataModel||a,s=e.dataIndex,l=e.dataType,u=o.getData(l),h=sf([u.getItemModel(s),o,a&&(a.coordinateSystem||{}).model,this._tooltipModel]),c=h.get("trigger");if(null==c||"item"===c){var d,f,p=o.getDataParams(s,l),g=o.formatTooltip(s,!1,l,this._renderMode);S(g)?(d=g.html,f=g.markers):(d=g,f=null);var v="item_"+o.name+"_"+s;this._showOrMove(h,function(){this._showTooltipContent(h,d,p,v,t.offsetX,t.offsetY,t.position,t.target,f)}),n({type:"showTip",dataIndexInside:s,dataIndex:u.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,n){var i=e.tooltip;if("string"==typeof i){var r=i;i={content:r,formatter:r}}var a=new fo(i,this._tooltipModel,this._ecModel),o=a.get("content"),s=Math.random();this._showOrMove(a,function(){this._showTooltipContent(a,o,a.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),n({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,n,i,r,a,o,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var u=this._tooltipContent,h=t.get("formatter");o=o||t.get("position");var c=e;if(h&&"string"==typeof h)c=Ho(h,n,!0);else if("function"==typeof h){var d=zb(function(e,i){e===this._ticket&&(u.setContent(i,l,t),this._updatePosition(t,o,r,a,u,n,s))},this);this._ticket=i,c=h(n,i,d)}u.setContent(c,l,t),u.show(t),this._updatePosition(t,o,r,a,u,n,s)}},_updatePosition:function(t,e,n,i,r,a,o){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var u=r.getSize(),h=t.get("align"),c=t.get("verticalAlign"),d=o&&o.getBoundingRect().clone();if(o&&d.applyTransform(o.transform),"function"==typeof e&&(e=e([n,i],a,r.el,d,{viewSize:[s,l],contentSize:u.slice()})),x(e))n=Rb(e[0],s),i=Rb(e[1],l);else if(S(e)){e.width=u[0],e.height=u[1];var f=$o(e,{width:s,height:l});n=f.x,i=f.y,h=null,c=null}else if("string"==typeof e&&o){var p=cf(e,d,u);n=p[0],i=p[1]}else{var p=uf(n,i,r,s,l,h?null:20,c?null:20);n=p[0],i=p[1]}if(h&&(n-=df(h)?u[0]/2:"right"===h?u[0]:0),c&&(i-=df(c)?u[1]/2:"bottom"===c?u[1]:0),t.get("confine")){var p=hf(n,i,r,s,l);n=p[0],i=p[1]}r.moveTo(n,i)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,n=!!e&&e.length===t.length;return n&&Eb(e,function(e,i){var r=e.dataByAxis||{},a=t[i]||{},o=a.dataByAxis||[];n&=r.length===o.length,n&&Eb(r,function(t,e){var i=o[e]||{},r=t.seriesDataIndices||[],a=i.seriesDataIndices||[];n&=t.value===i.value&&t.axisType===i.axisType&&t.axisId===i.axisId&&r.length===a.length,n&&Eb(r,function(t,e){var i=a[e];n&=t.seriesIndex===i.seriesIndex&&t.dataIndex===i.dataIndex})})}),this._lastDataByCoordSys=t,!!n},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){vf.node||(this._tooltipContent.dispose(),Rd("itemTooltip",e))}}),_u({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),_u({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){}),t.version=A_,t.dependencies=P_,t.PRIORITY=X_,t.init=uu,t.connect=hu,t.disConnect=cu,t.disconnect=hx,t.dispose=du,t.getInstanceByDom=fu,t.getInstanceById=pu,t.registerTheme=gu,t.registerPreprocessor=vu,t.registerProcessor=mu,t.registerPostUpdate=yu,t.registerAction=_u,t.registerCoordinateSystem=xu,t.getCoordinateSystemDimensions=wu,t.registerLayout=bu,t.registerVisual=Su,t.registerLoading=Cu,t.extendComponentModel=Tu,t.extendComponentView=Iu,t.extendSeriesModel=ku,t.extendChartView=Du,t.setCanvasCreator=Au,t.registerMap=Pu,t.getMap=Lu,t.dataTool=cx,t.zrender=kg,t.number=Nm,t.format=Um,t.throttle=fl,t.helper=pw,t.matrix=Qf,t.vector=Ef,t.color=mp,t.parseGeoJSON=vw,t.parseGeoJson=xw,t.util=ww,t.graphic=bw,t.List=Mx,t.Model=fo,t.Axis=_w,t.env=vf});