



    //时间范围插件配置
    var locale = {
        "format": 'YYYY-MM-DD', //HH:MM:SS
        "separator": " - ", //  
        "applyLabel": "确定",
        "cancelLabel": "取消",
        "fromLabel": "起始时间",
        "toLabel": "结束时间'",
        "customRangeLabel": "自定义",
        "weekLabel": "W",
        "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"], //不写会变成英文
        "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"], //不写会变成英文
        "firstDay": 1
    };











    //获取昨天时间
    function yesterdayAry() {
        return moment().subtract('days', 1).startOf('day').format('YYYY-MM-DD') + " - " + moment().subtract('days', 1).endOf('day').format('YYYY-MM-DD');
    }














    //获取当前时间几天前的
    function getDay(day) {
        var today = new Date();
        var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
        today.setTime(targetday_milliseconds); //注意，这行是关键代码
        var tYear = today.getFullYear();
        var tMonth = today.getMonth();
        var tDate = today.getDate();
        tMonth = doHandleMonth(tMonth + 1);
        tDate = doHandleMonth(tDate);
        return tYear + "-" + tMonth + "-" + tDate;
    }

    function doHandleMonth(month) {
        var m = month;
        if (month.toString().length == 1) {
            m = "0" + month;
        }
        return m;
    }


    function sevenDaysAgo() {
        return getDay(-6) + " - " + getDay(0);
    }

    function setSevenDaysAgo(elm) {
        $(elm).val(getDay(-6) + " - " + getDay(0))
    }




    //比较两个日期中间相隔了多少天，传入的格式为2016-05-12,可以只传一个参数
    function compareDate(d1, d2) {
        //d1为更远之前的时间,d2为较新的时间
        var s1 = new Date(d1.replace(/-/g, "/"));
        var now = new Date(); //当前日期

        //如果有传入d2
        if (d2) {
            // console.log("有传入第二个参数");
            var s2 = new Date(d2.replace(/-/g, "/"));
            var days = s2.getTime() - s1.getTime();
        } else {
            var days = now.getTime() - s1.getTime();
        }

        var HowManyDays = parseInt(days / (1000 * 60 * 60 * 24));
        return HowManyDays
    }


    //将时间范围分隔出来，赋值给start_date和end_date
    function splitDate(range, mark) {
        return range.split(mark)
    }





    //获取指定年分下，指定月份有几天
    function GetDateCount(year, month) {
        if (month > 12 || month < 1) {
            alert("月份输入有误");
            return false
        }
        var d = new Date(year, month, 0);
        return d.getDate();
    }



    //获取指定年分下，指定月份有几天,并返回对应天数的数组
    function GetDateCountAry(year, month) {
        if (month > 12 || month < 1) {
            alert("月份输入有误");
            return false
        }
        var d = new Date(year, month, 0);
        var arr = [];
        for (var i = 1; i <= d.getDate(); i++) {
            arr.push(i);
        }
        return arr;
    }



    //这里参数两个和三个的情况，是最大值和最小值是否都有传值，count个数是都要传的
    function randomNum(minNum, maxNum, count) {
        switch (arguments.length) {
            case 2:
                var arr = [];
                for (var i = 0; i < count; i++) {
                    var random = parseInt(Math.random() * minNum + 1, 10);
                    arr.push(random);
                }
                return arr;
                break;
            case 3:
                var arr = [];
                for (var i = 0; i < count; i++) {
                    var random = parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
                    arr.push(random);
                }

                return arr;
                break;
            default:
                return 0;
                break;
        }
    }




    function getLastDay(year, month) {
        var new_year = year; //取当前的年份   
        var new_month = month++; //取下一个月的第一天，方便计算（最后一天不固定）   
        if (month > 12) { //如果当前大于12月，则年份转到下一年   
            new_month -= 12; //月份减   
            new_year++; //年份增   
        }
        var new_date = new Date(new_year, new_month, 1); //取当年当月中的第一天   
        return (new Date(new_date.getTime() - 1000 * 60 * 60 * 24)).getDate(); //获取当月最后一天日期   
    }







    //时间戳转换
    function format(timeStamp) {
        var time = new Date(parseInt(timeStamp));
        var y = time.getFullYear();
        var m = (time.getMonth() + 1) < 10 ? '0' + (time.getMonth() + 1) : (time.getMonth() + 1);
        var d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate();
        var h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours();
        // var min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes();
        // var s = time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds();
        // return y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + s;
        return y + '-' + m + '-' + d;
    }



    //将key不是text的数据改为text
    function textFormat(param) {
        var format = $.map(param, function (obj) {
            obj.text = obj.text || obj.name; // replace name with the property used for the text
            return obj;
        });
        return format;
    }


    function objTextFormat(param) {
        var arr = []
        var keyMap = {
            "name": "text"
        };

        for (var key in param) {
            var newKey = keyMap[key];
            if (newKey) {
                param[newKey] = param[key];
                delete param[key];
            }
        }
        arr.push(param)

        return arr

    }


    function getOption(data) {
        var str = [];
        data.forEach(function (element) {
            str.push('<option value="' + element.id + '">' + element.text + '</option>');
        });
        return str.join("");
    }





























    function removerLastStr(str) {
        if (Object.prototype.toString.call(str) == "[object String]") {
            var newStr = str.substring(0, str.length - 1);
            return newStr
        } else {
            alert("请传入字符串");
            return false
        }
    }



    //获取全数字数组（可以是字符串类型，也可以是数值类型）的平均值
    function getAva(paramAry) {
        if (Object.prototype.toString.call(paramAry) == "[object Array]") {
            var sum = 0;
            for (var i = 0; i < paramAry.length; i++) {
                sum += parseFloat(paramAry[i]);
            }
            var ava = sum / paramAry.length;
            return ava

        } else {
            alert("请传入数组");
            return false
        }
    }


    //获取%号前的数值
    function getNumber(str, separate) {
        if (str.length && separate.length) {
            var ary = str.split(separate);
            //将保留以为小数的字符串转为数值
            var num = Math.round(ary[0] * 10) / 10;
            return num
        } else {
            return false
        }
    }


    //比较日期相隔多少天
    function disparityDay(prevDay, latestDay) {
        var s1 = new Date(prevDay.replace(/-/g, "/"));
        var s2 = new Date(latestDay.replace(/-/g, "/"));
        var days = s2.getTime() - s1.getTime();
        var apart = parseInt(days / (1000 * 60 * 60 * 24));
        return apart
    }



    function getNowFormatDate() {
        var date = new Date();
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + seperator1 + month + seperator1 + strDate;
        return currentdate;
    }


    //截取格式化后的时间 后半部分
    function InterceptStr(str) {
        var ipos = str.indexOf(" ");
        var str1 = str.substring(0, ipos); //获取空格前的部分
        var str2 = str.substring(ipos, str.length); //取后部分

        return removePosStr(str2, 3).trim();
    }

    function removePosStr(str, pos) {
        var num = parseFloat(pos);
        var s1 = str.substring(0, str.length - num);
        return s1;
    }






































    //获取最新的游戏
    function ajaxGetLastGame(parmUrl, whichKind) {

        $.ajax({
            url: parmUrl,
            type: "GET",
            async: false,
            dataType: "json",
            headers: {
                "X-Requested-With": "xmlhttprequest"
            },
            success: function (res) {

                if (res.code == 1) {
                    // localStorage.setItem("lastGame", res.data.id);

                    var gameSelectBox = $('.gameSelect').select2({
                        placeholder: "请选择游戏", // 空值提示内容，选项值为 null
                        allowClear: true, //是否允许清空选中
                        width: '170px',
                        language: "zh-CN",
                        data: objTextFormat(res.data), //如果key不是id和text，则进行转换
                    });

                    // whichGame = $(".gameSelect").select2("val");

                    // localStorage.setItem("lastGame", $(".gameSelect").select2("val"));   
                    localStorage.setItem(whichKind, $(".gameSelect").select2("val"));

                    // console.log(localStorage.getItem(whichKind));

                    // if (localStorage.getItem("whichGame")) {
                    //     gameSelectBox.val(localStorage.getItem("whichGame")).trigger("change");
                    // } else if (localStorage.getItem("whichGame") == null) {
                    //     gameSelectBox.val(localStorage.getItem("lastGame")).trigger("change");
                    // }

                    localStorage.setItem(whichKind + "Text", $(".gameSelect").select2("data")[0].text);

                    // whichGameText=$(".gameSelect").select2("data")[0].text
                    // localStorage.setItem("whichGameText", $(".gameSelect").select2("data")[0].text)

                }
            }
        })

    }





    // AppLastGame
    // AppLastGameText

    // JHLastGame
    // JHLastGameText




















    //模糊搜索渠道和游戏
    function ajaxGameAndChannelList(gameUrl, channelUrl, whichOne) {

        var gameSelectBox = $('.gameSelect').select2({
            placeholder: "请选择游戏", // 空值提示内容，选项值为 null
            allowClear: true, //是否允许清空选中
            ajax: {
                url: gameUrl,
                type: "POST",
                dataType: 'json',
                data: function (params) {
                    var query = {
                        name: params.term,
                    }
                    return query;
                },
                processResults: function (data) {
                    return {
                        results: textFormat(data.data)
                    };
                },
                cache: true
            }
        });

        // gameSelectBox.val(localStorage.getItem("lastGame")).trigger("change");
        // localStorage.setItem("whichGameText", $(".gameSelect").select2("data")[0].text)
        // whichGameText=$(".gameSelect").select2("data")[0].text;

        /*
        whichGame,whichGameText
        whichAppGame,whichAppGameText
        whichJHGame,whichJHGameText

        */

        //初始化后如果存在这个缓存则赋值
        if (localStorage.getItem(whichOne)) {
            $('.gameSelect').html("<option value='" + localStorage.getItem(whichOne) + "'>" + localStorage.getItem(whichOne + "Text") + "</option>");
        }


        $('.channelSelect').select2({
            placeholder: "请选择渠道", // 空值提示内容，选项值为 null
            allowClear: true, //是否允许清空选中
            ajax: {
                url: channelUrl,
                type: "POST",
                dataType: 'json',
                data: function (params) {
                    var query = {
                        name: params.term,
                    }
                    return query;
                },
                processResults: function (data) {

                    return {
                        results: textFormat(data.data)
                    };
                },
                cache: true
            }
        });

        // $(".channelSelect").prepend("<option value='0'>空渠道</option>");


    }











    //聚合用按需搜索请求渠道
    function ajaxJHGame(gameUrl, whichOne) {

        var gameSelectBox = $('.gameSelect').select2({
            placeholder: "请选择游戏", // 空值提示内容，选项值为 null
            allowClear: true, //是否允许清空选中
            ajax: {
                url: gameUrl,
                type: "POST",
                dataType: 'json',
                data: function (params) {
                    var query = {
                        name: params.term,
                    }
                    return query;
                },
                processResults: function (data) {
                    return {
                        results: textFormat(data.data)
                    };
                },
                cache: true
            }
        });




        //初始化后如果存在这个缓存则赋值
        if (localStorage.getItem(whichOne)) {
            $('.gameSelect').html("<option value='" + localStorage.getItem(whichOne) + "'>" + localStorage.getItem(whichOne + "Text") + "</option>");
        }



    }








    //聚合用一次性请求全部渠道
    function ajaxJHChannel(paramUrl) {

        $.ajax({
            url: paramUrl,
            type: "GET",
            async: false,
            dataType: "json",
            headers: {
                "X-Requested-With": "xmlhttprequest"
            },
            success: function (res) {
                // console.log(res.data);
                // console.log(textFormat(res.data));
                
                $('.channelSelect').select2({
                    placeholder: "请选择渠道", // 空值提示内容，选项值为 null
                    allowClear: true, //是否允许清空选中
                    width: '170px',
                    language: "zh-CN",
                    data: textFormat(res.data), //如果key不是id和text，则进行转换
                });

            },
            error:function(){

            }

        })
        


       
    }





















    //获取当前时间
    function getNow() {
        var timeStamp = new Date().getTime();
        var currentDate = new Date();
        var yy = currentDate.getFullYear();
        var mon = (currentDate.getMonth() + 1) < 10 ? '0' + (currentDate.getMonth() + 1) : (currentDate.getMonth() + 1);
        var dd = currentDate.getDate() < 10 ? '0' + currentDate.getDate() : currentDate.getDate();
        var hh = currentDate.getHours() < 10 ? '0' + currentDate.getHours() : currentDate.getHours();
        var min = currentDate.getMinutes() < 10 ? '0' + currentDate.getMinutes() : currentDate.getMinutes();
        var ss = currentDate.getSeconds() < 10 ? '0' + currentDate.getSeconds() : currentDate.getSeconds();
        return yy + '-' + mon + '-' + dd + ' ' + hh + ':' + min + ':' + ss;
    }


    function getNowYMD() {
        var timeStamp = new Date().getTime();
        var currentDate = new Date();
        var yy = currentDate.getFullYear();
        var mon = (currentDate.getMonth() + 1) < 10 ? '0' + (currentDate.getMonth() + 1) : (currentDate.getMonth() + 1);
        var dd = currentDate.getDate() < 10 ? '0' + currentDate.getDate() : currentDate.getDate();
        var hh = currentDate.getHours() < 10 ? '0' + currentDate.getHours() : currentDate.getHours();
        var min = currentDate.getMinutes() < 10 ? '0' + currentDate.getMinutes() : currentDate.getMinutes();
        var ss = currentDate.getSeconds() < 10 ? '0' + currentDate.getSeconds() : currentDate.getSeconds();
        return yy + '-' + mon + '-' + dd;
    }


    function getNowHourMin() {
        var currentDate = new Date();
        var hh = currentDate.getHours() < 10 ? '0' + currentDate.getHours() : currentDate.getHours();
        var min = currentDate.getMinutes() < 10 ? '0' + currentDate.getMinutes() : currentDate.getMinutes();
        return hh + ':' + min;
    }

    function SeparateHourStr(str) {
        var ipos = str.indexOf(":");
        var hh = str.substring(0, ipos); //获取空格前的部分
        var temp = str.substring(ipos, str.length); //取后部分
        var min = temp.substring(1, temp.length)
        return hh
    }

    function SeparateMinStr(str) {
        var ipos = str.indexOf(":");
        var hh = str.substring(0, ipos); //获取空格前的部分
        var temp = str.substring(ipos, str.length); //取后部分
        var min = temp.substring(1, temp.length)
        return min
    }










        //时间戳格式化
        function formatTimeStamp(timeStamp) {
            var time = new Date(parseInt(timeStamp));
            var y = time.getFullYear();
            var m = (time.getMonth() + 1) < 10 ? '0' + (time.getMonth() + 1) : (time.getMonth() + 1);
            var d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate();
            var h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours();
            var min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes();
            var s = time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds();
            return y + '-' + m + '-' + d;
        }
    
        //标准时间格式化
        function standardTimeformat(standard) {
            var y = standard.getFullYear();
            var m = (standard.getMonth() + 1) < 10 ? '0' + (standard.getMonth() + 1) : (standard.getMonth() + 1);
            var d = standard.getDate() < 10 ? '0' + standard.getDate() : standard.getDate();
            var h = standard.getHours() < 10 ? '0' + standard.getHours() : standard.getHours();
            var min = standard.getMinutes() < 10 ? '0' + standard.getMinutes() : standard.getMinutes();
            var s = standard.getSeconds() < 10 ? '0' + standard.getSeconds() : standard.getSeconds();
            return y + '-' + m + '-' + d;
        }
    
    
        //昨天
        function yesterday() {
            var day = new Date();
            day.setTime(day.getTime() - 24 * 60 * 60 * 1000); //24小时，60分钟，60秒，再转为毫秒
            var yy = day.getFullYear();
            var mm = (day.getMonth() + 1) < 10 ? '0' + (day.getMonth() + 1) : (day.getMonth() + 1);
            var dd = day.getDate() < 10 ? '0' + day.getDate() : day.getDate();
            var yesterday = yy + "-" + mm + "-" + dd;
         
            return yesterday
        }



    
    
        //本周
        function getCurrentWeek() {
            //起止日期数组  
            var startStop = new Array();
            //获取当前时间  
            var currentDate = new Date();
            //返回date是一周中的某一天  
            var week = currentDate.getDay();
            //返回date是一个月中的某一天  
            var month = currentDate.getDate();
    
            //一天的毫秒数  
            var millisecond = 1000 * 60 * 60 * 24;
            //减去的天数  
            var minusDay = week != 0 ? week - 1 : 6;
            //alert(minusDay);  
            //本周 周一  
            var mondayStandard = new Date(currentDate.getTime() - (minusDay * millisecond));
            //本周 周日  
            var sundayStandard = new Date(mondayStandard.getTime() + (6 * millisecond));
    
            //添加本周时间  
            startStop.push(standardTimeformat(mondayStandard)); //本周起始时间  
            //添加本周最后一天时间  
            startStop.push(standardTimeformat(sundayStandard)); //本周终止时间  
            //返回  
            return startStop;
        };
    
    
        //上周
        function getPreviousWeek() {
            //起止日期数组  
            var startStop = new Array();
            //获取当前时间  
            var currentDate = new Date();
            //返回date是一周中的某一天  
            var week = currentDate.getDay();
            //返回date是一个月中的某一天  
            // var month = currentDate.getDate();
            //一天的毫秒数  
            var millisecond = 1000 * 60 * 60 * 24;
            //减去的天数  
            var minusDay = week != 0 ? week - 1 : 6;
    
            //获得当前周的第一天  
            var currentWeekDayOne = new Date(currentDate.getTime() - (millisecond * minusDay));
    
            //上周最后一天即本周开始的前一天  
            var priorWeekLastDay = new Date(currentWeekDayOne.getTime() - millisecond);
    
            //上周的第一天  
            var priorWeekFirstDay = new Date(priorWeekLastDay.getTime() - (millisecond * 6));
    
    
            var month1 = (priorWeekFirstDay.getMonth() + 1) < 10 ? "0" + (priorWeekFirstDay.getMonth() + 1) : (
                priorWeekFirstDay.getMonth() + 1);
    
            var date1 = priorWeekFirstDay.getDate() < 10 ? "0" + priorWeekFirstDay.getDate() : priorWeekFirstDay.getDate();
    
            var priorWeekFirstDay_Format = priorWeekFirstDay.getFullYear() + "-" + month1 + "-" + date1
    
    
            var month2 = (priorWeekLastDay.getMonth() + 1) < 10 ? "0" + (priorWeekLastDay.getMonth() + 1) : (
                priorWeekLastDay.getMonth() + 1);
    
            var date2 = priorWeekLastDay.getDate() < 10 ? "0" + priorWeekLastDay.getDate() : priorWeekLastDay.getDate();
    
            var priorWeekLastDay_Format = priorWeekLastDay.getFullYear() + "-" + month2 + "-" + date2;
    
    
            //添加至数组  
            startStop.push(priorWeekFirstDay_Format);
            startStop.push(priorWeekLastDay_Format);
    
            return startStop;
        };
    
       
    
        // 获取当前月的最第一天
        function getCurrentMonthFirst() {
            //标准时间
            var date = new Date();
            date.setDate(1);
            var y = date.getFullYear();
            var m = (date.getMonth() + 1) < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1);
            var d = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
            var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
            var min = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
            var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
            return y + '-' + m + '-' + d;
        }
    
        // 获取当前月的最后一天
        function getCurrentMonthLast() {
            var date = new Date();
            var currentMonth = date.getMonth();
            var nextMonth = ++currentMonth;
            var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
            var oneDay = 1000 * 60 * 60 * 24;
            var timeStamp=nextMonthFirstDay - oneDay;
            var standard=new Date(timeStamp);
            var y = standard.getFullYear();
            var m = (standard.getMonth() + 1) < 10 ? '0' + (standard.getMonth() + 1) : (standard.getMonth() + 1);
            var d = standard.getDate() < 10 ? '0' + standard.getDate() : standard.getDate();
            var h = standard.getHours() < 10 ? '0' + standard.getHours() : standard.getHours();
            var min = standard.getMinutes() < 10 ? '0' + standard.getMinutes() : standard.getMinutes();
            var s = standard.getSeconds() < 10 ? '0' + standard.getSeconds() : standard.getSeconds();
            return y + '-' + m + '-' + d;
        }
    
    
    
    
    
    
    
        //上月
        function getLastMonth() {
            var nowdays = new Date();
            var year = nowdays.getFullYear();
            var month = nowdays.getMonth();
            if (month == 0) {
                month = 12;
                year = year - 1;
            }
            if (month < 10) {
                month = "0" + month;
            }
            var firstDay = year + "-" + month + "-" + "01"; //上个月的第一天
    
            var myDate = new Date(year, month, 0);
            var lastDay = year + "-" + month + "-" + myDate.getDate(); //上个月的最后一天
    
            return [firstDay, lastDay]
        }
    