// # 业务逻辑部分



// ## 相关处理任务
var fileTypes = {
	'serverList': '开服',
	'payList': '充值记录管理',
	'memberList': '注册用户',
	'admin.complexMemberList': '聚合用户管理-注册用户',
	'admin.complexMemberLoginList': '聚合用户管理-登录用户',
	'admin.complexPayList': '聚合用户管理-充值记录',
	'memberBindList': '绑定信息管理',
	'memberLoginList': '登录用户列表',
	'unionList': '商务账号管理报表',
	'channelList': '推广账号管理报表',
	'gameManagerDown':'游戏管理列表',
	'admin.settleIndex': '结算一级审核',
	'admin.settleCheckList': '结算审核',
	'admin.withdrawIndex': '提现管理',
	'admin.channelPay': '推广员数据',

	'admin.memberCoinIndex': '用户专属平台币',
	'admin.memberCoinDetList': '用户账户明细',
	'admin.channelAccountIndex': '会长账户余额',
	'admin.channelAccountDetList': '推广账户变动明细',
	'admin.memberCoinGetList': '平台币充值记录管理',
	'admin.memberCoinInfoList': '用户平台币明细',
	'retaineGame':'游戏数据统计',
	'retaineGameServer':'游戏区服数据统计',
	'channelRatioCoin':'公会平台币充值导出',
}



// ## 相关处理方法
BusinessActions["reloadBusiness"] = function (data) {
	loadWsBusiness();
}

BusinessActions["authFaild"] = function (data) {
	// wsConnector.send('{"action": "authFaild"}');
}

BusinessActions["ping"] = function (data) {
	wsConnector.send('{"action": "pong"}');
}

BusinessActions["loginResult"] = function (data) {

	if (0 !== data.code) {
		console.log('WS 登录失败, 原因: ' + data.message);
		wsConnector.close();
	} else {
		// 测试

		var template = $('#message-template').html();
		template = template.replace('__NAME__', data.message + '<small class="close-msg">×</small>');
		template = template.replace('__DATE__', '');
		template = template.replace('__CONTENT__', '');
		$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');
		console.log('WS 登录完成');
		// <small class="close-msg">x</small>

	}
}

BusinessActions["receive_broadcast"] = function (data) {
	notice(data, '系统广播消息');

	var template = $('#message-template').html();
	template = template.replace('__NAME__', '系统广播消息：' + '<small class="close-msg">×</small>');
	template = template.replace('__DATE__', '');
	template = template.replace('__CONTENT__', data.message);
	// $('.small-chat-box .content').prepend(template);
	$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');
}

BusinessActions["downloadPaylist"] = function (data) {}

// test: BusinessActions.subpackageFinish({gamename: '卫龙'});
BusinessActions["subpackageFinish"] = function (data) {

	var date = new Date();
	//时间补零
	var messageTime = "";
	if (date.getHours() >= 10) {
		messageTime += date.getHours() + ":";
	} else {
		messageTime += "0" + date.getHours() + ":";
	}
	if (date.getMinutes() >= 10) {
		messageTime += date.getMinutes();
	} else {
		messageTime += "0" + date.getMinutes();
	}
	var message = data.gamename;
	if (data.cid){
		message += '-'+data.cid;
	}

	var template = $('#message-template').html();
	template = template.replace('__NAME__', '游戏打包完成' + '<small class="close-msg">×</small>');
	template = template.replace('__DATE__', messageTime);
	template = template.replace('__CONTENT__', '您的游戏 ' + message + ' 已经打包完成！<br /><a class="btn btn-xs btn-info show-subpackage-list"></a>');

	// $('.small-chat-box .content').prepend(template);
	$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');

	notice('你的游戏 ' + data.gamename + ' 已经打包完成！');
	window.localStorage['push_msg_' + messageTime] = data;


	$("iframe").each(function (index, item) {
		if ($(item).attr("src").indexOf("game/mine") > -1 || $(item).attr("src").indexOf("game/index") > -1 || $(item).attr("src").indexOf("sub_package/index") > -1) {
			$(this)[0].contentWindow.location.reload(true);
		}
	})


	return true;
}

BusinessActions["hasNewGrandMoneyApply"] = function (data) {
	// console.log(data);
	var date = new Date();
	//时间补零
	var messageTime = "";
	if (date.getHours() >= 10) {
		messageTime += date.getHours() + ":";
	} else {
		messageTime += "0" + date.getHours() + ":";
	}
	if (date.getMinutes() >= 10) {
		messageTime += date.getMinutes();
	} else {
		messageTime += "0" + date.getMinutes();
	}


	var template = $('#message-template').html();
	template = template.replace('__NAME__', '有新的发币申请' + '<small class="close-msg">×</small>');
	template = template.replace('__DATE__', messageTime);
	template = template.replace('__CONTENT__', '申请人: ' + data.alipay_nickname + ', 帐号: ' + data.alipay_account + ', 申请金额: ' + data.amount + '<br /><a class="btn btn-xs btn-info show-grand-money-apply-list">查看</a>');


	$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');

	//点击【查看】打开【...币申请列表】页面
	$(".btn.btn-xs.btn-info.show-grand-money-apply-list").on("click", function () {
		var _that = this
		$("cite").each(function (index, item) {
			if (item.innerHTML.indexOf("币申请") > -1) {
				$(item).parent().parent()[0].click();
				$(_that).parent().parent().html("");
			}
		});
	})

	notice('有新的发币申请');
	window.localStorage['push_msg_' + messageTime] = data;

	return true;
}

BusinessActions["hasNewGrandMoneyApplyPassed"] = function (data) {
	var date = new Date();
	//时间补零
	var messageTime = "";
	if (date.getHours() >= 10) {
		messageTime += date.getHours() + ":";
	} else {
		messageTime += "0" + date.getHours() + ":";
	}
	if (date.getMinutes() >= 10) {
		messageTime += date.getMinutes();
	} else {
		messageTime += "0" + date.getMinutes();
	}

	var template = $('#message-template').html();
	template = template.replace('__NAME__', '有新的发币申请已审核通过' + '<small class="close-msg">×</small>');
	template = template.replace('__DATE__', messageTime);
	template = template.replace('__CONTENT__', '申请人: ' + data.alipay_nickname + ', 帐号: ' + data.alipay_account + ', 申请金额: ' + data.amount + '<br /><a class="btn btn-xs btn-info show-grand-money-apply-list">查看</a>');
	// $('.small-chat-box .content').prepend(template);
	$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');

	notice('有新的发币申请已审核通过, 请发币');
	window.localStorage['push_msg_' + messageTime] = data;

	return true;
}

BusinessActions["makeExcelComplete"] = function (data) {
	var date = new Date();
	//时间补零
	var messageTime = "";
	if (date.getHours() >= 10) {
		messageTime += date.getHours() + ":";
	} else {
		messageTime += "0" + date.getHours() + ":";
	}
	if (date.getMinutes() >= 10) {
		messageTime += date.getMinutes();
	} else {
		messageTime += "0" + date.getMinutes();
	}

	var template = $('#message-template').html();
	date = new Date(data.task_time * 1000);
	var taskTime = date.getHours() + ':' + date.getMinutes();

	template = template.replace('__NAME__', '报表生成完成' + '<small class="close-msg">×</small>');
	template = template.replace('__DATE__', messageTime);
	template = template.replace('__CONTENT__', '您于 ' + taskTime + ' 生成的 ' + fileTypes[data.file_type] + ' 报表生成好了。<a href="' + data.file_path + '" target="_blank" class="btn btn-xs btn-info">下载</a>');
	// $('.small-chat-box .content').prepend(template);
	$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');

	notice('报表生成完成，请在右边悬浮按钮里查看。');
	window.localStorage['push_msg_' + messageTime] = data;

	return true;
}






BusinessActions["hasNewGameEnterApply"] = function (data) {
	var date = new Date();
	var messageTime = date.getHours() + ':' + date.getMinutes();
	var template = $('#message-template').html();

	date = new Date(data.apply_time * 1000);
	var gameName = data.game_name;
	var channelName = data.channel_name;
	var applyTime = date.getHours() + ':' + date.getMinutes();

	template = template.replace('__NAME__', '有新的入驻游戏申请，请审核');
	template = template.replace('__DATE__', messageTime);
	template = template.replace('__CONTENT__', '游戏名称： ' + gameName + ' ，申请渠道： ' + channelName + '。 <a class="btn btn-xs btn-info has-new-game-enter-apply">查看</a>');
	$('.small-chat-box .content').prepend(template);

	notice('有新的入驻游戏申请，请审核');
	window.localStorage['push_msg_' + messageTime] = data;

	return true;
}



BusinessActions["hasNewDemandApply"] = function (data) {
	var date = new Date();
	var messageTime = date.getHours() + ':' + date.getMinutes();
	var template = $('#message-template').html();

	date = new Date(data.apply_time * 1000);
	var applyType = data.apply_type;
	var gameName = data.game_name;
	var channelName = data.channel_name;
	var applyTime = date.getHours() + ':' + date.getMinutes();

	if (applyType == 1) {
		var applyTypeName = '新游申请';
		template = template.replace('__NAME__', '有新的游戏需求申请，请审核');

	} else {
		applyType = 2;
		var applyTypeName = '点位申请';
		template = template.replace('__NAME__', '有新的点位需求申请，请审核');
	}
	template = template.replace('__DATE__', messageTime);

	template = template.replace('__CONTENT__', '申请类型： ' + applyTypeName + '，游戏名称： ' + gameName + ' ，申请渠道： ' + channelName + '。 <a class="btn btn-xs btn-info has-new-demand-apply">查看</a>');
	$('.small-chat-box .content').prepend(template);

	if (applyType == 1) {
		notice('有新的游戏需求申请，请审核');
	} else {
		notice('有新的点位需求申请，请审核');
	}

	window.localStorage['push_msg_' + messageTime] = data;

	return true;
}


BusinessActions["hasNewGiftApply"] = function (data) {
	var date = new Date();
	var messageTime = date.getHours() + ':' + date.getMinutes();
	var template = $('#message-template').html();

	date = new Date(data.apply_time * 1000);
	var giftName = data.gift_name;
	var channelName = data.channel_name;
	var applyTime = date.getHours() + ':' + date.getMinutes();

	template = template.replace('__NAME__', '有新的礼包领取申请，请审核');
	template = template.replace('__DATE__', messageTime);
	template = template.replace('__CONTENT__', '礼包名称： ' + giftName + ' ，申请渠道： ' + channelName + '。 <a class="btn btn-xs btn-info has-new-gift-apply">查看</a>');
	$('.small-chat-box .content').prepend(template);

	notice('有新的礼包领取申请，请审核');
	window.localStorage['push_msg_' + messageTime] = data;

	return true;
}




BusinessActions["startTask"] = function (data) {

	var template = $('#message-template').html();

	template = template.replace('__NAME__', '记录时间：' + format(data.task_time * 1000) + '<small class="close-msg">×</small><br/>');
	template = template.replace('__DATE__', '任务内容：' + fileTypes[data.file_type] + '<br>当前状态：任务开始');
		template = template.replace('__CONTENT__', '剩余任务：' + data.remain_task_number);

	$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');

	notice('有新任务开始，请审核');
	window.localStorage['push_msg_' + data.task_time] = data;

	return true;
}



BusinessActions["taskProgress"] = function (data) {

	var template = $('#message-template').html();

	template = template.replace('__NAME__', '记录时间：' + format(data.task_time * 1000) + '<small class="close-msg">×</small><br/>');
	template = template.replace('__DATE__', '任务内容：' + fileTypes[data.file_type] + '<br>当前状态：任务进行中');
	template = template.replace('__CONTENT__', '已完成任务：' + data.records_number);

	$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');

	notice('任务进行中，请审核');
	window.localStorage['push_msg_' + data.task_time] = data;

	return true;
}


BusinessActions["taskError"] = function (data) {

	var template = $('#message-template').html();

	template = template.replace('__NAME__', '记录时间：' + format(data.task_time * 1000) + '<small class="close-msg">×</small><br/>');
	template = template.replace('__DATE__', '任务内容：' + fileTypes[data.file_type] + '<br>当前状态：任务出错');
	template = template.replace('__CONTENT__', '报表出错：' + data.error_type);

	$(".floating_ball_wrap .content").prepend('<section>' + template + '</section>');

	notice('任务出错，请检查');
	window.localStorage['push_msg_' + data.task_time] = data;

	return true;
}







function format(timeStamp) {
	var time = new Date(parseInt(timeStamp));
	var y = time.getFullYear();
	var m = (time.getMonth() + 1) < 10 ? '0' + (time.getMonth() + 1) : (time.getMonth() + 1);
	var d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate();
	var h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours();
	var min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes();
	var s = time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds();
	return y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + s;
}
