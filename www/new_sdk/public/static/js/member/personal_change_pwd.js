   // tab图片切换
   $(".tab1").hover(function () {
    $(".tab1 img").attr("src", "/static/images/icon/userinfo-1.png");
}, function () {
    $(".tab1 img").attr("src", "/static/images/icon/userinfo.png");
});
$(".tab3").hover(function () {
    $(".tab3 img").attr("src", "/static/images/icon/giftbox-1.png");
}, function () {
    $(".tab3 img").attr("src", "/static/images/icon/giftbox.png");
});
$(".tab2").hover(function () {
    $(".tab2 img").attr("src", "/static/images/icon/rechargeinfo-1.png");
}, function () {
    $(".tab2 img").attr("src", "/static/images/icon/rechargeinfo.png");
});
	$(".tab5").hover(function() {
		$(".tab5 img").attr("src", "/static/images/icon/game-wihte.png");
	}, function() {
		$(".tab5 img").attr("src", "/static/images/icon/game-black.png");
	});

// 点击确定

$(".resetpwd").click(function () {
    var oldpwd = $('input[name=oldpwd]').val();
    var newpwd = $('input[name=newpwd]').val();
    var confirmpwd = $('input[name=confirmpwd]').val();
    if (oldpwd == "" || oldpwd.length < 6 || oldpwd.length > 15 || newpwd == "" || newpwd.length < 6 || newpwd.length > 15 || confirmpwd == "" || confirmpwd != newpwd) {
        if (oldpwd == "" || oldpwd.length < 6 || oldpwd.length > 15) {
            $(".oldpwd_error").text('请输入正确的密码，6-15位字符');
            $(".oldpwd_error").show();
            $(".oldpwd input").css("border", "1px solid #FC5351")
        }else {
            $(".oldpwd_error").hide();
        }
        if (newpwd == "" || newpwd.length < 6 || newpwd.length > 15) {
            $(".newpwd_error").text('请输入正确的密码，6-15位字符');
            $(".newpwd_error").show();
            $(".newpwd input").css("border", "1px solid #FC5351")
        }else {
            $(".newpwd_error").hide();
        }
        if (confirmpwd == "" || confirmpwd != newpwd) {
            $(".confirmpwd_error").text('两次输入的密码不一致');
            $(".confirmpwd_error").show();
            $(".confirmpwd input").css("border", "1px solid #FC5351")
        }else {
            $(".confirmpwd_error").hide();
        }
    } else {
        //    判断旧密码是否正确
        $.ajax({
            type: 'POST',
            url: "/user/zhaq/changepwd",
            dataType: 'json',
            data: {oldPwd:oldpwd,password1:newpwd,password2:confirmpwd},
            success: function(res) {
                if(res.code == 1) {
                    layer.msg(res.msg);
                    setTimeout(function () {
                        window.location.href=res.url;
                    },3000);
                }else if (res.code == -1){
                    $(".newpwd_error").text(res.msg);
                    $(".newpwd_error").show();
                    $(".newpwd input").css("border", "1px solid #FC5351")
                }else if (res.code == -2){
                    $(".confirmpwd_error").text(res.msg);
                    $(".confirmpwd_error").show();
                    $(".confirmpwd input").css("border", "1px solid #FC5351")
                }else {
                    $(".oldpwd_error").text(res.msg);
                    $(".oldpwd_error").show();
                    $(".oldpwd input").css("border", "1px solid #FC5351")
                }
            },
            error: function () {
                layer.alert('网络错误，请刷新页面重试');
            }
        });
    }
})


// 输入框失去焦点

$(".oldpwd input").on('blur', function () {
    var oldpwd = $(this).val();
    if (oldpwd == "" || oldpwd.length < 6 || oldpwd.length > 15) {
        $(".oldpwd_error").text('请输入正确的密码，6-15位字符');
        $(".oldpwd_error").show();
        $(this).css("border", "1px solid #FC5351")
    } else {
        $(".oldpwd_error").hide();
        $(this).css("border", "1px solid #eee")
    }
})
$(".newpwd input").on('blur', function () {
    var newpwd = $(this).val();
    if (newpwd == "" || newpwd.length < 6 || newpwd.length > 15) {
        $(".newpwd_error").text('请输入正确的密码，6-15位字符');
        $(".newpwd_error").show();
        $(this).css("border", "1px solid #FC5351")
    } else {
        $(".newpwd_error").hide();
        $(this).css("border", "1px solid #eee")
    }
})
$(".confirmpwd input").on('blur', function () {
    var confirmpwd = $(".newpwd input").val();
    var newpwd = $(this).val();
    if (confirmpwd == "" || confirmpwd != newpwd) {
        $(".confirmpwd_error").text('两次输入的密码不一致');
        $(".confirmpwd_error").show();
        $(this).css("border", "1px solid #FC5351")
    } else {
        $(".confirmpwd_error").hide();
        $(this).css("border", "1px solid #eee")
    }
})
