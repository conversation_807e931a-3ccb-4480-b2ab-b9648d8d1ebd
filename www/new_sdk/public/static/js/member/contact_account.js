

    function fun(){
        var username = $('input[name=username]').val();

        if (username =="")
        {
            $(".username_not_exist").show();
            $(".username_not_exist").text("请输入用户名");
            $("#pasval").css("border","1px solid #FC5350");
        }

        else{
            $("#TencentCaptcha").trigger("click");
        }

        if(username !=""){
            $(".username_not_exist").hide();
            $("#pasval").css("border","1px solid #DCDCDC");

        }

    }


    //拼图验证
    var ticket = '';
    var randstr = '';
    window.callback = function(res){
        if(res.ret === 0){
            var username = $('input[name=username]').val();
            ticket = res.ticket;
            randstr = res.randstr;

            $.ajax({
                type: 'POST',
                url: "/forgotpwd",
                dataType: 'json',
                data: {username:username,ticket:ticket,randstr:randstr},
                success: function(json) {
                    // console.log(json);
                    if(json.code == 1) {
                        window.location.href = json.url;
                    }else if(json.code == 0) {
                        $(".username_not_exist").show();
                        $(".username_not_exist").text(json.msg);
                        $("#pasval").css("border","1px solid #FC5350");
                    }else{
                        json.msg
                    }
                },
                error: function () {
                    alert('网络错误，请刷新页面重试');
                }
            });
        }
    }
