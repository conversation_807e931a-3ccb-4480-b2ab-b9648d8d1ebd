

//发送短信按钮判断
$(".getCodeone").click(function(){

    var phonenumber = $('input[name=phonenumber]').val();
    // 手机号验证
    var preg = /^1[3456789]{1}\d{9}$/;

    if(phonenumber == "" ){
        $(".phonenumber_erro").text('请输入手机号');
        $(".phonenumber_erro").show();
        $("input[name=phonenumber]").css("border","1px solid #FC5351");
    }else if(!preg.test(phonenumber)){
      $(".phonenumber_erro").text('请输入正确的手机号');
      $(".phonenumber_erro").show();
      $("input[name=phonenumber]").css("border","1px solid #FC5351");
      } else{
        $("#phonereg_form #TencentCaptcha").trigger("click");
    }


    if(phonenumber !="" && preg.test(phonenumber)==true){
        $(".phonenumber_erro").hide();
        $("input[name=phonenumber]").css("border","1px solid #DCDCDC");
    }

})



//手机注册按钮判断
$("#phonereg_form .submit").click(function(){

    var phonenumber = $('input[name=phonenumber]').val();
    var passwordone = $('#phonereg_form input[name=passwordone]').val();
    var password = $('#phonereg_form input[name=password]').val();
    var smsCode =$('input[name=smsCode]').val();
    // 手机号验证
    var preg = /^1[3456789]{1}\d{9}$/;

    if(phonenumber == "" || passwordone == ""||password == ""|| !preg.test(phonenumber)||passwordone.length<6||passwordone.length>15 ||passwordone != password || smsCode == ""){
        
        if(phonenumber == "" ){
            $(".phonenumber_erro").text('请输入手机号');
            $(".phonenumber_erro").show();
            $("input[name=phonenumber]").css("border","1px solid #FC5351");
            return;
        }

        if(!preg.test(phonenumber)){
            $(".phonenumber_erro").text('请输入正确的手机号');
            $(".phonenumber_erro").show();
            $("input[name=phonenumber]").css("border","1px solid #FC5351");
            return;
        }
        
        if( passwordone == ""){
            $("#phonereg_form .password_one_erro").text('请输入密码，6-15位字符');
            $("#phonereg_form .password_one_erro").show();
            $("#phonereg_form input[name=passwordone]").css("border","1px solid #FC5351");
         
            return;
        }
        if(passwordone.length<6||passwordone.length>15 ){
            $("#phonereg_form .password_one_erro").text('请输入正确的密码，<br>6-15位字符');
            $("#phonereg_form .password_one_erro").show();
            $("#phonereg_form input[name=passwordone]").css("border","1px solid #FC5351");
         
            return;
        }
        if(password == ""){
            $("#phonereg_form .password_erro").text('请再次输入密码');
            $("#phonereg_form .password_erro").show();
            $("#phonereg_form input[name=password]").css("border","1px solid #FC5351");
            return;
        }
        if(passwordone != password){
            $("#phonereg_form .password_erro").text('两次输入的密码不一致');
            $("#phonereg_form .password_erro").show();
            $("#phonereg_form input[name=password]").css("border","1px solid #FC5351");
            return;
        }
        if(smsCode ==""){
             $(".sms_code_erro").text('短信验证码不能为空');
             $(".sms_code_erro").show();
            $("input[name=smsCode]").css("border","1px solid #FC5351");
            return;
        }

    }else{
        // 注册成功提交表单
        $.ajax({
        type: 'POST',
        url: '/member/register',
        dataType: 'json',
        data: {username:phonenumber,type:'phone',password:password,passwordone:passwordone,smsCode:smsCode},
        success: function(res) {
            // console.log(res)
            if(res.code == -1) {
                $(".phonenumber_erro").text(res.msg);
                $(".phonenumber_erro").show();
                $("input[name=phonenumber]").css("border","1px solid #FC5351");
        
            }else if(res.code == -2) {
                $("#phonereg_form .password_one_erro").text(res.msg);
                $("#phonereg_form .password_one_erro").show();
                $("#phonereg_form input[name=passwordone]").css("border","1px solid #FC5351");
       
            }else if(res.code == -3) {
                $("#phonereg_form .password_erro").text(res.msg);
                $("#phonereg_form .password_erro").show();
                $("#phonereg_form input[name=password]").css("border","1px solid #FC5351");
        
            }else if(res.code == 1) {
                 layer.msg(res.msg);
                   setTimeout(function () {
                   window.location.href=res.url;
                 },3000);

            }else{
                $(".sms_code_erro").text(res.msg);
             $(".sms_code_erro").show();
            $("input[name=smsCode]").css("border","1px solid #FC5351");
                // layer.alert(res.msg);
            }
           
        },
        error: function () {
            alert('网络错误，请刷新页面重试');
        }
    });


    }

    if(phonenumber !="" && preg.test(phonenumber)==true){
        $(".phonenumber_erro").hide();
        $("input[name=phonenumber]").css("border","1px solid #DCDCDC");
    }
    if(passwordone != "" && passwordone.length>=6 && passwordone.length<=15){
        $("#phonereg_form .password_one_erro").hide();
        $("#phonereg_form input[name=passwordone]").css("border","1px solid #DCDCDC");

    }
    if(password!="" && passwordone == password){
        $("#phonereg_form .password_erro").hide();
        $("#phonereg_form input[name=password]").css("border","1px solid #DCDCDC");
    }
    if(smsCode !=""){
         $(".sms_code_erro").hide();
        $("input[name=smsCode]").css("border","1px solid #DCDCDC");
    }

})







    // 用户名注册按钮
    $("#userreg_form .submit").click(function(){

var username = $('#userreg_form input[name=username]').val();
var passwordone = $('#userreg_form input[name=passwordone]').val();
var password = $('#userreg_form input[name=password]').val();
var reg = /^[a-zA-Z0-9]{6,11}$/ ;

if(username == "" || passwordone == ""||password == ""|| !reg.test(username)||passwordone.length<6||passwordone.length>15 ||passwordone != password){

if(username == ""){
    $("#userreg_form .username_erro").text('请输入用户名，6-11位字母或数字');
    $("#userreg_form .username_erro").show();
    $("#userreg_form input[name=username]").css("border","1px solid #FC5351");
}else if(!reg.test(username)){
    $("#userreg_form .username_erro").text('请输入正确的用户名，6-11位字母或数字');
    $("#userreg_form .username_erro").show();
    $("#userreg_form input[name=username]").css("border","1px solid #FC5351");
}

if( passwordone == "" ){
    $("#userreg_form .password_one_erro").text('请输入密码，6-15位字符');
    $("#userreg_form .password_one_erro").show();
    $("#userreg_form input[name=passwordone]").css("border","1px solid #FC5351");
}else if( passwordone.length<6||passwordone.length>15 ){
    $("#userreg_form .password_one_erro").text('请输入正确的密码，6-15位字符');
    $("#userreg_form .password_one_erro").show();
    $("#userreg_form input[name=passwordone]").css("border","1px solid #FC5351");
}

if(password == "" ){
    $("#userreg_form .password_erro").text('请再次输入密码');
    $("#userreg_form .password_erro").show();
    $("#userreg_form input[name=password]").css("border","1px solid #FC5351");
}else if(passwordone != password){
    $("#userreg_form .password_erro").text('两次输入的密码不一致');
    $("#userreg_form .password_erro").show();
    $("#userreg_form input[name=password]").css("border","1px solid #FC5351");
}

}
else{
    $("#TencentCaptcha").trigger("click");

    // $('#userreg_form').submit();
}

if(username !="" && reg.test(username)==true && username.length>=6 && username.length<=12 ){
    $("#userreg_form .username_erro").hide();
    $("#userreg_form input[name=username]").css("border","1px solid #DCDCDC");
}
if(passwordone != "" && passwordone.length>=6 && passwordone.length<=15){
    $("#userreg_form .password_one_erro").hide();
    $("#userreg_form input[name=passwordone]").css("border","1px solid #DCDCDC");

}
if(password!="" && passwordone == password){
    $("#userreg_form .password_erro").hide();
    $("#userreg_form input[name=password]").css("border","1px solid #DCDCDC");
}

})


    //拼图验证回调
    var ticket = '';
    var randstr = '';
    window.callback = function(res){

        if($(".phonereg_right").css("display")=='block'){
            // 手机注册验证码拼图验证
            if(res.ret === 0){
            ticket = res.ticket;
            randstr = res.randstr;
            getCode();
        }  
         }else{
            //  回调用户名注册
            if(res.ret === 0){
            ticket = res.ticket;
            randstr = res.randstr;
            var username = $('#userreg_form input[name=username]').val();
            var passwordone = $('#userreg_form input[name=passwordone]').val();
            var password = $('#userreg_form input[name=password]').val()

            $.ajax({
                type: 'POST',
                url: '/member/register',
                dataType: 'json',
                data: {username:username,type:'username',password:password,passwordone:passwordone,ticket:ticket,randstr:randstr},
                success: function(res) {
                    // console.log(res)
                    if(res.code == -1) {
                        $("#userreg_form .username_erro").text(res.msg);
                        $("#userreg_form .username_erro").show();
                        $("#userreg_form input[name=username]").css("border","1px solid #FC5351");
                        return;

                    }else if(res.code == -2) {
                        $("#userreg_form .password_one_erro").text(res.msg);
                        $("#userreg_form .password_one_erro").show();
                        $("#userreg_form input[name=passwordone]").css("border","1px solid #FC5351");
                        return;

                    }else if(res.code == -3) {
                        $("#userreg_form .password_erro").text(res.msg);
                        $("#userreg_form .password_erro").show();
                        $("#userreg_form input[name=password]").css("border","1px solid #FC5351");
                        return;
                    }else if(res.code == 1) {
                        layer.msg(res.msg);
                        setTimeout(function () {
                            window.location.href=res.url;
                        },3000);
                        return;
                    }else{
                        // layer.alert(res.msg); 
                        $(".sms_code_erro").text(res.msg);
                        $(".sms_code_erro").show();
                        $("input[name=smsCode]").css("border","1px solid #FC5351");
                    }
                    // alert(res.msg);
                    // layer.alert(res.msg);
                },
                error: function () {
                    alert('网络错误，请刷新页面重试');
                }
            });
        }
         }
    
    }



     //获取短信验证码
     function getCode(){
            var runnable = true;
            var error_msg= '';
            var phone = '';
            var verify = '';
            if($(this).hasClass('hasSend')) {
                runnable = false;
                error_msg = '操作过于频繁！';
            }
            if(runnable) {
                phone  = $("#phonenumber").val();
                /*verify = $('#verifycode').val();*/
            }

            /*if(!verify) {
                runnable = false;
                error_msg = '验证码不能为空！';
            }*/

            if(!phone) {
                runnable = false;
                error_msg = '手机号不能为空！';
            }
            if(runnable) {
                $.ajax({
                    type: 'POST',
                    url: "/member/sendcode",
                    data: {phone:phone,ticket:ticket,randstr:randstr,isExistUser:true},
                    success: function(json) {
                        if(json.code==1) {
                            var count = 60;
                            $(".sms_code_erro").hide();
                            $('.getCode').addClass('hasSend');
                            var index = setInterval(function() {
                                if(count >= 0) {
                                    $('.getCode').val(count+' s');
                                    $('.getCode').attr('disabled',true);
                                    $('.getCode').show();
                                    $('.getCodeone').hide();

                                    count --;
                                } else {
                                    $('.getCode').removeClass('hasSend').val('点击获取验证码');
                                    $('.getCode').attr('disabled',false);
                                    $('.getCode').hide();
                                    $('.getCodeone').show();
                                    clearInterval(index);
                                }
                            }, 1000);
                            layer.msg(json.msg);
                        }else  if(json.code== -1){
                            $(".phonenumber_erro").text(json.msg);
                            $(".phonenumber_erro").show();
                            $("input[name=phonenumber]").css("border","1px solid #FC5351");
                        } else {
                            $(".sms_code_erro").text(json.msg);
                            $(".sms_code_erro").show();
                            $("input[name=smsCode]").css("border","1px solid #FC5351");
                        }
                    },
                    dataType: 'json'
                });
            } else {
                alert(error_msg);
            }
        };




// 失去焦点
$("#username").on('blur',function(){
    var username = $(this).val();
    var reg = /^[a-zA-Z0-9]{6,11}$/ ;
    if(username == ""){
        $(".username_erro").text('请输入用户名，6-11位字母或数字');
        $(".username_erro").show();
        $("input[name=username]").css("border","1px solid #FC5351");
    }else if(!reg.test(username)){
        $(".username_erro").text('请输入正确的用户名，6-11位字母或数字');
        $(".username_erro").show();
        $("input[name=username]").css("border","1px solid #FC5351");
    }else{
        $(".username_erro").hide();
        $("input[name=username]").css("border","1px solid #DCDCDC");
    }   
    })

$("#phonenumber").on('blur',function(){
    var phonenumber = $(this).val();
        // 手机号验证
    var preg = /^1[3456789]{1}\d{9}$/;
    if(phonenumber == "" ){
            $(".phonenumber_erro").text('请输入手机号');
            $(".phonenumber_erro").show();
            $("input[name=phonenumber]").css("border","1px solid #FC5351");
     
    }else if(!preg.test(phonenumber)){
           $(".phonenumber_erro").text('请输入正确的手机号');
            $(".phonenumber_erro").show();
            $("input[name=phonenumber]").css("border","1px solid #FC5351");
    }else{
        $(".phonenumber_erro").hide();
        $("input[name=phonenumber]").css("border","1px solid #DCDCDC");
    }
        
    })

    $(".password-one input").on('blur',function(){
    var passwordone = $(this).val();
    if( passwordone == ""){
            $(this).parent(".password-one ").find(".password_one_erro").text('请输入密码，6-15位字符');
            $(this).parent(".password-one ").find(".password_one_erro").show();
            $(this).css("border","1px solid #FC5351");
        }else if(passwordone.length<6||passwordone.length>15 ){
            $(this).parent(".password-one ").find(".password_one_erro").text('请输入正确的密码，6-15位字符');
            $(this).parent(".password-one ").find(".password_one_erro").show();
            $(this).css("border","1px solid #FC5351");
        }  else{
            $(this).parent(".password-one ").find(".password_one_erro").hide();
            $(this).css("border","1px solid #DCDCDC"); 
        }
    })

    $(".password input").on('blur',function(){
    var password = $(this).val();
    var passwordone = $(this).parents(".login_form").find("#passwordone").val();
    if(password == "" ){
            $(this).parent(".password").find(".password_erro").text('请再次输入密码');
            $(this).parent(".password").find(".password_erro").show();
            $(this).css("border","1px solid #FC5351");
        
        }else if(passwordone != password){
            $(this).parent(".password").find(".password_erro").text('两次输入的密码不一致');
            $(this).parent(".password").find(".password_erro").show();
            $(this).css("border","1px solid #FC5351");
        }else{
            $(this).parent(".password").find(".password_erro").hide();
           $(this).css("border","1px solid #DCDCDC");
        }
    })

    $(".w130").on('blur',function(){
    var smsCode = $(this).val();
    if(smsCode ==""){
             $(".sms_code_erro").text('短信验证码不能为空');
             $(".sms_code_erro").show();
            $("input[name=smsCode]").css("border","1px solid #FC5351");
        
        }else{
            $(".sms_code_erro").hide();
        $("input[name=smsCode]").css("border","1px solid #DCDCDC");
        }
    })

    // 点击切换用户名注册
    $(".userReg").click(function(){
        $(".userreg_right").show();
        $(".user_reg").show();
        $(".phonereg_right").hide();
        $(".phone_reg").hide();
    })
    $(".phoneReg").click(function(){
        $(".userreg_right").hide();
        $(".user_reg").hide();
        $(".phonereg_right").show();
        $(".phone_reg").show();
    })