  // tab图片切换
  $(".tab1").hover(function () {
    $(".tab1 img").attr("src", "/static/images/icon/userinfo-1.png");
}, function () {
    $(".tab1 img").attr("src", "/static/images/icon/userinfo.png");
});
$(".tab2").hover(function () {
    $(".tab2 img").attr("src", "/static/images/icon/rechargeinfo-1.png");
}, function () {
    $(".tab2 img").attr("src", "/static/images/icon/rechargeinfo.png");
});
$(".tab4").hover(function () {
    $(".tab4 img").attr("src", "/static/images/icon/zhaq-1.png");
}, function () {
    $(".tab4 img").attr("src", "/static/images/icon/zhaq.png");
});
	$(".tab5").hover(function() {
		$(".tab5 img").attr("src", "/static/images/icon/game-wihte.png");
	}, function() {
		$(".tab5 img").attr("src", "/static/images/icon/game-black.png");
	});
// +—切换
$(".gift_name b").click(function () {

    setTimeout(function () {
        var heigh_right = $(".gift_box").height();
        $(".left_tab").css("height", heigh_right)
    }, 100);

    if ($(this).parents(".gift_records").find(".gift_time").css("display") == 'none') {
        $(this).parents(".gift_records").find(".gift_time").show();
        $(this).html("-")
    } else {
        $(this).parents(".gift_records").find(".gift_time").hide();
        $(this).html("+")
    }
})
setTimeout(function () {
var heigh_right = $(".gift_box").height();
$(".left_tab").css("height", heigh_right)
}, 100);

    $('.record_cont').each(function(){
       var a =$(this).height();
       var b= a+'px'
      $(this).find("div").css("height",a);
      $(this).find("div").css("line-height",b);

    });

setTimeout(function () {
        var heigh_right = $(".gift_box").height();
        $(".left_tab").css("height", heigh_right)
    }, 300);

// 复制按钮
$(".operation a").click(function () {
    var code = $(this).parents(".gift_records").find(".gift_code").text();
    var oInput = document.createElement('input');

    oInput.value = code;
    document.body.appendChild(oInput);
    oInput.select(); // 选择对象
    document.execCommand("Copy"); // 执行浏览器复制命令
    oInput.className = 'oInput';
    oInput.style.display = 'none';
    layer.msg('复制成功');

})

$(".query_form").submit(function () {
    var startTime = $("#strattime").val();
        var start = new Date(startTime.replace("-", "/").replace("-", "/"));
        var endTime = $("#endtime").val();
        var end = new Date(endTime.replace("-", "/").replace("-", "/"));
        if (end < start) {
            layer.msg("开始时间不能大于结束时间");
            return false;
         }

});


layui.use('laydate', function(){
var laydate = layui.laydate;
var $ = layui.$;

//执行一个laydate实例
var start = laydate.render({
elem: '#strattime',
max: 0          
});
var end = laydate.render({
elem: '#endtime' ,
max: 0

})
});

// 按回车键触发查询
$('.gamename').bind('keyup', function () {
// 按回车搜索
var e = event || window.event || arguments.callee.caller.arguments[0];
        if(e && e.keyCode==13){ // enter 键
            document.getElementById("query").click();
        }
});