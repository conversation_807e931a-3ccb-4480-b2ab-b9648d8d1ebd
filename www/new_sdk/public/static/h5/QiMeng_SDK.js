
(function (w) {
    var sdk = w.LING_H5_GAME_SDK || {};
	var initcallback = null
	//登录回调
	var logincallback =null
	//支付回调（成功/失败）
	var paycallback=null
	//角色上传回调=null
	var rolecallback=null
	//登出回调
	var logoutcallback=null
	
	var isInit =false
    sdk.init = function (params,callback) {
		if(!isInit){
			console.log('sdkinit')
			initcallback = callback
			isInit = true;
			config(params);
			sdk.postMsgToParent({
			     operation: "QM_sdkInit",
			     param: params
			  });
		}else{
			console.log("SDK已初始化，无需再次调用")
		}
		
    };
    function config(params) {
        window.addEventListener("message", function (event) {
			// console.log(event.data.operation)
            switch (event.data.operation) {
				case "QM_sdkInit":{
					initcallback(event)
					break
				}
                case "QM_login_success":{
                    console.log("登录成功")
					logincallback(event)
					break
                }
				case "QM_login_fail":{
				    console.log("登录失败")
					logincallback(event)
					break
				}
                case "QM_pay_success": {
                   console.log("支付成功")
				   paycallback(event)
                    break
                } case "QM_pay_fail": {
                   console.log("支付失败")
				   paycallback(event)
                    break
                }
                case "QM_role_success": {
                    console.log("角色上传成功")
					rolecallback(event)
                    break
                }
				case "QM_role_fail": {
                    console.log("角色上传失败")
					rolecallback(event)
                    break
                }
				case "QM_logout_success": {
				    console.log("登出成功")
					logoutcallback(event)
				    break
				}
				case "QM_lougout_fail": {
				    console.log("登出失败")
					logoutcallback(event)
				    break
				}
              
            }
        }, false);
    };
	//登录
    sdk.login = function(info,callback){
    //console.log(info);
	    logincallback = callback;
        sdk.postMsgToParent({
             operation: "QM_login",
             param: info
          });
    };

    //支付
    sdk.pay = function (order,callback) {
		paycallback = callback
        sdk.postMsgToParent({
            operation: "QM_pay",
            param: order
        });
    };
    //角色上传
    sdk.role = function (roleInfo,callback) {
		rolecallback = callback
        sdk.postMsgToParent({
            operation: "QM_role",
            param: roleInfo
        });
    };
    //登出
    sdk.logout = function (info,callback) {
		logoutcallback = callback
        sdk.postMsgToParent({
            operation: "QM_logout",
            param: info
        });
    };

    sdk.postMsgToParent = function (post) {

        window.parent.postMessage(post, '*');
    };


    sdk.getParameter = function(name) {
        var reg = new RegExp("[&?](" + name + ")=([^&?#]*)", "i");
        var r = window.location.search.match(reg);
        return r ? r[2] : null;
    };


    w.QMSDK = sdk;
})(window);