ThinkPHP 5.0
详细开发文档参考 [ThinkPHP5完全开发手册](http://www.kancloud.cn/manual/thinkphp5)
php 正常7.0-7.2 版本都可以

## 相关域名
 - admin.46yxs.com - 后台
 - sdkapi.46yxs.com - 支付回调+
 - cps.46yxs.com - 公会后台
 - cpsapi.46yxs.com - 公会API
 <!-- - mcpsapi.46yxs.com - mcpsapi - (无) -->
 - jhgame.46yxs.com - complex - 渠道SDK对接
 <!-- - m.46yxs.com - mobile - 手机端(无) -->
 - www.46yxs.com - home - 官网


## 相关地址
- 隐私详情页：https://sdkapi.qmgames.cn/privacy.html

## 域名
> 当前域名地址: 7dgame.cn
> 测试域名前加: dev

devsdkdownapp.7dgame.cn
devsdkstatic.7dgame.cn
- 分包服务器请求域名地址：http://sdkpack.qmgames.cn/pack

### 测试
sdkwww - 官网
devsdkadmin - 后台
devsdkapi - 支付回调+
devsdkcps - 公会后台
devsdkcpsapi - 公会API
devsdkjhgame - 渠道SDK对接
devsdkdownapp - 渠道包下载
devsdkdownreport - 导出数据下载地址(go相关服务)
devsdkstatic - 图片地址


server_name sdkwww.7dgames.cn devsdkadmin.7dgames.cn devsdkapi.7dgames.cn devsdkcps.7dgames.cn devsdkcpsapi.7dgames.cn devsdkjhgame.7dgames.cn devsdkdownapp.7dgames.cn devsdkdownreport.7dgames.cn;

### 正式
sdkwww - 官网
sdkadmin - 后台
sdkapi - 支付回调+
sdkcps - 公会后台
sdkcpsapi - 公会API
sdkjhgame - 渠道SDK对接
sdkdownapp - 渠道包下载
sdkdownreport - 导出数据下载地址(go相关服务)
sdkstatic - 图片地址



## 所需环境
- mysql
- redis
- docker
- git

## 项目仓库
- new_sdk - 后台+API+CPS_API
- new_sdk_cps-admin - cps后台
- new_sdk_cps-py - cps打包
- new_sdk_ws - 相关通知服务
- new_sdk_go - excel导出&发放游戏礼包



### 缺少的东西
- 报表生成服务类go 关联的go程序 (app\common\library\MakeReportGo)
- 渠道打包 关联的python程序 (app\common\logic\SubPackage)
- workerman 没有相关配置文件

## 需要的数据以及第三方
- 域名
- 阿里云oss(华为云obs 也可)
- 预警钉钉机器人(可不用)
    - 充值异常
    - 特殊回调异常
- 短信 阿里云
- 支付宝 支付
    - wap 支付
    - 扫码支付
- 微信 支付
    - h5 支付
    - 扫码支付

### 服务器环境
#### 配置: 
- 服务器: 2核4G
- 打包服务器: 2核8G
- 数据库: 2核8G
- redis: 2核4G
- 带宽: 5M

> 开发环境：php+mysql+redis+ws+go+python

- php 7.0-7.2
- redis 5.2.1
- mysql 7


## H5


### code码定义
```
100 = 登录失效
110 = 请求参数有误
120 = 请求处理过程提示
130 = 执行异常
200 = 正常
```