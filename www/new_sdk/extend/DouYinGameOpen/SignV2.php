<?php
namespace DouYinGameOpen;

class SignV2{

    public function GenerateSign($appID, $secretKey, $httpMethod, $rawUrl, $httpBody, $ts, $nonce) {
        $method = strtoupper($httpMethod);
        $path = getURI($rawUrl);
        $sign = getSign(getPayload($method, $path, strval($ts), $nonce, $httpBody), $secretKey);
        $signHeader = buildSignHeader([
            "appid" => $appID,
            "alg" => "HMAC-SHA256",
            "v" => "2.0",
            "nonce" => $nonce,
            "timestamp" => strval($ts),
            "signature" => $sign,
        ]);
        return [$sign, $signHeader];
    }

    public function Verify($signHeader, $rawUrl, $httpMethod, $body, $secretKey) {
        $paramMap = extractSignHeader($signHeader);
        $alg = $paramMap["alg"] ?? null;
        $v = $paramMap["v"] ?? null;
        $nonce = $paramMap["nonce"] ?? null;
        $timestamp = $paramMap["timestamp"] ?? null;
        $signature = $paramMap["signature"] ?? null;
        $appid = $paramMap["appid"] ?? null;


        if ($appid === null || $alg === null || $nonce === null || $timestamp === null || $signature === null || $v === null) {
            return false;
        }

        if ($alg != "HMAC-SHA256") {
            // algorithm not support
            return false;
        }
        if ($v != "2.0") {
            // version not support
            return false;
        }

        $method = strtoupper($httpMethod);
        $path = getURI($rawUrl);
        $sign = getSign(getPayload($method, $path, $timestamp, $nonce, $body), $secretKey);
        return $sign == $signature;
    }

    public function extractSignHeader($signHeader) {
        $result = [];
        $fields = explode(",", $signHeader);
        foreach ($fields as $pair) {
            $kv = explode("=", $pair, 2);
            if (count($kv) != 2) {
                continue; // 跳过无效的键值对
            }
            // 去掉引号
            $key = trim($kv[0]);
            $value = trim($kv[1], "\"");
            $result[$key] = $value;
        }
        return $result;
    }

    public function buildSignHeader($params) {
        $result = [];
        foreach ($params as $k => $v) {
            $result[] = sprintf("%s=\"%s\"", $k, $v);
        }
        return implode(",", $result);
    }

// getURI 返回除域名外的所有路径; e.g. https://some.domain.com/api/trade/v2/query?a=x => /api/trade/v2/query?a=x
    public function getURI($rawUrl) {
        return preg_replace('#^https?://[^/]+#', '', $rawUrl);
    }

    public function getPayload($method, $path, $timestamp, $nonce, $body) {
        return sprintf("%s\n%s\n%s\n%s\n%s\n", $method, $path, $timestamp, $nonce, $body);
    }

    public function getSign($payload, $secretKey) {
        $sign = BASE64(HMAC_SHA256($payload, $secretKey));
        return $sign;
    }

    public function HMAC_SHA256($content, $secret) {
        return hash_hmac('sha256', $content, $secret, true); // true for raw output
    }

    public function BASE64($content) {
        return base64_encode($content);
    }

}