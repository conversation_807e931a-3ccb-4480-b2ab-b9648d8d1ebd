<?php
namespace XiTaiYouPay\util;

/**
 * RSA工具类
 */
class RSAUtil
{
    public $private_key;
    public $public_key;

    /**
     * 利用构造函数，初始化值
     * @param $private_str 私钥串
     * @param $public_str 公钥串
     */
    function __construct($private_str, $public_str)
    {
        /**
         * TODO:以下两行代码，是因为从常量中读取密钥，其为一行数据，且其并无前后标识
         */
        $private_str = "-----BEGIN RSA PRIVATE KEY-----\n" .
            $private_str.
            "\n-----END RSA PRIVATE KEY-----";

        $public_str = "-----BEGIN PUBLIC KEY-----\n" .
            $public_str.
            "\n-----END PUBLIC KEY-----";

        $this->private_key = openssl_get_privatekey($private_str);
        $this->public_key = openssl_get_publickey($public_str);
    }

    /**
     * 创建RSA签名
     * @param $original_str 原数据
     * @return string 签名
     */
    public function createRSASign($original_str)
    {
        openssl_sign($original_str, $sign, $this->private_key);
        openssl_free_key($this->private_key);
        return base64_encode($sign);
    }

    /**
     * RSA验签
     * @param $original_str 原数据
     * @param $sign 签名
     * @return bool 验签结果
     */
    public function verifyRSASign($original_str, $sign)
    {
        return (bool)openssl_verify($original_str, base64_decode($sign), $this->public_key);
    }

    /**
     * 创建RSA2的签名
     * @param $original_str 原数据
     * @return string 签名
     */
    public function createRSA2Sign($original_str)
    {
        openssl_sign($original_str, $sign, $this->private_key, OPENSSL_ALGO_SHA256);
        openssl_free_key($this->private_key);
        return base64_encode($sign);
    }

    /**
     * RSA2验签
     * @param $original_str 原数据
     * @param $sign 签名
     * @return bool 验签结果
     */
    public function verifyRSA2Sign($original_str, $sign)
    {
        return (bool)openssl_verify($original_str, base64_decode($sign), $this->public_key, OPENSSL_ALGO_SHA256);
    }

    /**
     * RSA加密
     * @param $original_str 原数据
     * @return string 加密后的数据
     */
    public function encryptData($original_str)
    {
        openssl_public_encrypt($original_str, $result, $this->public_key, OPENSSL_PKCS1_PADDING);
        return base64_encode($result);
    }

    /**
     * RSA解密
     * @param $encrypted_data 加密的数据
     * @return mixed 加密后的数据
     */
    public function decryptData($encrypted_data)
    {
        openssl_private_decrypt(base64_decode($encrypted_data), $result, $this->private_key, OPENSSL_PKCS1_PADDING);
        return $result;
    }
}