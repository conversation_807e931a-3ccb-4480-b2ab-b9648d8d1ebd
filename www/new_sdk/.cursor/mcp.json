{"mysql": {"host": "localhost", "port": 3306, "database": "new_sdk", "user": "root", "password": "root", "pool": {"max_connections": 10, "min_connections": 2, "idle_timeout": 300, "max_lifetime": 1800}}, "project": {"name": "new_sdk", "type": "php", "framework": "thinkphp", "version": "6.0"}, "editor": {"format_on_save": true, "tab_size": 4, "insert_spaces": true, "trim_trailing_whitespace": true, "insert_final_newline": true}, "php": {"version": "7.4", "extensions": ["pdo", "mysq<PERSON>", "mbstring", "json", "curl"]}, "paths": {"application": "application", "public": "public", "runtime": "runtime", "vendor": "vendor"}}