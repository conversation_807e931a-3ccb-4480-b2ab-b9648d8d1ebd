{extend name="layout/base" /}
{block name="title"}
<title>{eq name='type' value='1'}热门手机游戏礼包_麻花网络礼包中心 {else} 最新手机游戏礼包_麻花网络礼包中心 {/eq}</title>
<meta name="keywords" content="手机游戏礼包,手机游戏礼包领取,手机游戏礼包激活码,手机游戏礼包中心,手机游戏礼包大全"/>
<meta name="description" content="麻花网络礼包发包中心为广大玩家提供最新最全的手机游戏礼包、特权礼包、激活码、新手卡、兑换码等游戏福利，让您畅玩热门手机游戏！找手机游戏礼包就到麻花网络。"/>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>礼包列表</h1>
		<div>
			<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
	<ul class="game_rec">
		<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGiftUrl('hot')}')" class="{eq name='type' value='1'} active {/eq} ">热门礼包</a></li>
		<li><a href="javascript:;"  onclick="window.location.replace('{:getMobileGiftUrl('new')}')" class="{eq name='type' value='0'} active {/eq} ">最新礼包</a></li>
	</ul>
</header>
<!-- header end -->
	{empty name="list"}
	<!-- 无相关礼包 -->
	<div class="no_content" style="margin-top: 70%;">
		<div>
			<img src="/static/images/mobile/icon/no-gift.png">
			<p>暂无相关礼包</p>
		</div>
	</div>
	<!-- 无相关礼包end-->

	{else/}
	<div class="game-list gift-list">
	{volist name="list" id="vo"}
	<div class="game-info">
		<a href="{:getMobileGiftUrl('',$vo.id)}">
			<img lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" />
			<div class="content">
				<h4>{$vo.title}</h4>
				<div class="gift_progress">
					<span>剩余</span>
					<div class="warp_gift_surplus">
						<div class="gift_surplus" style="width:{$vo.percent} ;"></div>
					</div>
					<span>{$vo.percent}</span>
				</div>
				<P>{$vo.content}</P>
			</div>
		</a>
		<div class="receive">
			<a href="{:getMobileGiftUrl('',$vo.id)}">领取</a>
		</div>
	</div>
	{/volist}
	</div>
	{/empty}



<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>

{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script>
	var currentPage = parseInt("{$list->currentPage()}");
	var lastPage = parseInt("{$list->lastPage()}");
	var total = parseInt("{$list->total()}");
	var url = window.location.href;
	var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;

		if (currentPage + 1 > lastPage) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			}
			loading = false;
			return false;
		}
		$(".weui-loadmore").show();
		setTimeout(function() {
			currentPage += 1;
			$.ajax({
				type: "POST",
				timeOut: 10000,
				url: url,
				data: {
					"page": currentPage
				},
				async: false,
				success: function(res) {
					console.log(res);
					var arr = res.data.data;
					arr.forEach(function(val, index) {
						$(".game-list").append(
							"<div class='game-info'>" +
							"<a href='{:getMobileGiftUrl('','"+val.id+"')}'>" +
							"<img lazy-src=" + val.mobileicon + " src=" + defaultImg + " class='smallIcon' />" +
							"<div class='content'><h4>" + val.title + "</h4>" +
							" <div class='gift_progress'>" +
							"<span>剩余</span>" +
							" <div class='warp_gift_surplus'>" +
							" <div class='gift_surplus' style='width: " + val.percent + ";'></div>" +
							"</div><span>" + val.percent + "</span></div><P>" + val.content + "</P></div></a>" +
							"<div class='receive'>" +
							"<a href='{:getMobileGiftUrl('','"+val.id+"')}'>领取</a>" +
							"</div></div>"
						);
					});

				},
				error: function() {
					$.alert("网络错误，请刷新页面重试");
				}
			});
			$.getScript("/static/js/mobile/reload.js");
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});

</script>
{/block}
