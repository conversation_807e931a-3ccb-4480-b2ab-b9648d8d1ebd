{extend name="layout/base" /}
{block name="title"}<title>充值详情_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<style>
	body{
		background: #F9F9F9;
	}
	h2{
		font-size: 0.16rem;
		padding-left: 0.1rem;
		line-height: 0.4rem;
		color: #494949;
	}
	.pay-game {
	    background: #fff;
	    margin: 0 0.1rem 0.1rem;
	    padding: 0.1rem;
	}
	.game-info{
		margin: 0;
	}
	.pay-game p {
	    text-align: right;
	    font-size: 0.15rem;
	    line-height: 0.33rem;
	    margin-top: 0.1rem;
	}
	.pay-game p span {
	    color: #FF9A1C;
	    font-size: 0.18rem;
	}
	.pay-game p span:first-child{
		 font-size: 0.15rem;
	}
	.pay-detail {
	    background: #fff;
	    margin: 0.1rem;
	    padding: 0.12rem;
	    border-radius: 5px;
	}
	.pay-detail li {
	    display: flex;
	    justify-content: space-between;
	    line-height: 0.35rem;
	    font-size: 0.14rem;
	}
	.pay-detail li p:first-child {
    color: #BCBCBC;
}
.define {
    display: block;
    width: 3.55rem;
    height: 0.5rem;
    line-height: 0.5rem;
    text-align: center;
    background: #FFA42D;
    color: #fff;
    font-size: 0.18rem;
    margin: 0.5rem auto 0;
    border-radius: 0.25rem;
}
.game-info .content p{
   text-align: left;
}	
.game-info .content h4{
	    display: -webkit-box;
	    -webkit-box-orient: vertical;
	    -webkit-line-clamp: 2;
	    overflow: hidden;
	    white-space: normal;
	    height: auto;
}
.game-info .content{
	width: 3rem;
}
#detail-title {
	margin-inline-end: 0.4rem;
}
</style>
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="window.history.go(-1);"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1 id="detail-title">充值详情</h1>
		<div>
			<div id="query" ><img src="__STATIC__/images/mobile/icon/query-1.png"  style="width: .2rem;"></div>
		</div>
	</div>
</header>
<!-- header end -->


<div class="top-half">
	<h2>{$info.pay_status}</h2>
	<div class="pay-game">
	<div class="game-info">
			<img lazy-src="{$info.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" />
			<div class="content">
				<h4>{$info.nickname}</h4>
				<p>{$info.productname}</p>
			</div>
	</div>
	<p >充值金额：<span>￥</span><span>{$info.amount}</span></p>
	</div>
	
	<div class="pay-detail">
		<ul>
			<li>
				<p> 订单号码</p>
				<p><span class="orderid">{$info.orderid}</span>
				 <span style="color: #CACACA; margin: 0 0.09rem;">|</span>
				  <span style="color: #FF9A1C;" class="copy">复制</span></p>
			</li>
			<li>
				<p> 支付方式</p>
				<p>{$info.paytype}</p>
			</li>
			{eq name="$info.lb_show" value="1"}
			<li>
				<p> 平台币支付</p>
				<p>￥{:intval($info.real_ptb)}</p>
			</li>
			{/eq}
			{eq name="$info.third_show" value="1"}
			<li>
				<p> 第三方支付</p>
				<p>￥{:intval($info.real_amount)}</p>
			</li>
			{/eq}
			<li>
				<p>下单时间</p>
				<p>{$info.create_time}</p>
			</li>
			{eq name="$info.status" value="1"}
			<li>
				<p>付款时间</p>
				<p>{:date('Y-m-d H:i:s',$info.pay_time)}</p>
			</li>
			{/eq}
		</ul>
	</div>
	
	<a class="define" href="window.history.go(-1);">确定</a>
	
</div>






{/block}

{block name="detail_js"}
<script>
	// 提示
	$('#query img').click(function(){
		$.modal({
		   title: "提示",
		   text:"<P>1.若发现充值不到账，请等待几分钟或退出后重新登录，若还不到账，请尽快联系麻花网络客服。</p><P>2.若为混合支付，并且没有付款时，则平台币会被锁定，半小时后发放回原账号，请注意查收。</p>",
		   buttons: [{
		           text: "确定",
		           className: "pop-button",
		           onClick: function() {}
		       }
		   ]
		});
		
		$(".weui-dialog__bd").css({"color":"#262626","font-size":'0.14rem','text-align':'left'});
		$(".weui-dialog__bd p").css('margin',"0.08rem 0");

	})

	$(".copy").click(function() {
		var payid = $(this).parent("p").find(".orderid").html();
		var oInput = document.createElement('input');
		oInput.readOnly = true;
		oInput.value = payid;
		document.body.appendChild(oInput);
		oInput.select(); // 选择对象
		document.execCommand("Copy"); // 执行浏览器复制命令
		oInput.className = 'oInput';
		oInput.style.display = 'none';
		$.toast("订单号复制成功", "text");
	})
	

</script>


{/block}
