{extend name="layout/base" /}
{block name="title"}<title>个人资料_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/ImgCropping.css">
<link rel="stylesheet" href="__STATIC__/css/cropper.min.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">

<style>
	body{
		background: #f9f9f9;
	}
</style>
{/block}

{block name="content"}
<!-- header start -->
<header style="background: #fff;">
	<a href="{:getMobileEncodeUrl('user')}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>个人资料</h1>
</header>
<ul class="personal-info">
	<li>
		<a href="javascript:;">
			<p>账号</p>
			<p>
			<span>{$info.username}</span>
			</p>
		</a>
	</li>
	<li>
		<a href="javascript:;" style="height: 0.7rem;" class="replaceImg">
			<p>头像</p>
			<p>
			<img src="{empty name='$info.avatar'}/static/images/mobile/icon/user-head.png{else /}{$Think.STATIC_DOMAIN}{$info.avatar}{/empty}"  id="finalImg"    style="width: 0.51rem; height: 0.51rem;margin-right: 0.08rem;border-radius: 50%;" />
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li>
		<a href="{:getMobileUserCenterUrl('nickname')}">
			<p>昵称</p>
			<p>
			<span>{$info.nickname}</span>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li>
		<a href="{:getMobileUserCenterUrl(1.1)}">
			<p>QQ</p>
			<p>
			<span>{$info.qq}</span>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li>
		<a  id='sex'>
			<p>性别</p>
			<p>
			<span>
				{switch name="$info.sex" }
					{case value="1"}女{/case}
					{case value="2"}男{/case}
					{case value="3"}保密{/case}
					{default /}
				{/switch}
			</span>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li >
		<a href="javascript:;" id="date">
			<p>生日</p>
			<p>
			<span>{$info.birthday}</span>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li>
		<a href="{:getMobileUserCenterUrl(1.2)}">
			<p>邮政编码</p>
			<p>
			<span>{$info.zipcode}</span>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li>
		<a href="{:getMobileUserCenterUrl(1.3)}">
			<p>联系地址</p>
			<p>
				<span>{$info.address}</span>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
</ul>

 <a class="exit" >退出</a>
 
 <div class="poup-sex">
	 <div class="sex-list">
		 <div class="title">性别</div>
		 <a class="select">男</a>
		 <a class="select">女</a>
		 <a class="select">保密</a>
		 <a class="cancel">取消</a>
	 </div>
 </div>



<!--图片裁剪框 start-->
<div style="display: none" class="tailoring-container">
    <div class="black-cloth" onclick="closeTailor(this)"></div>
    <div class="tailoring-content">
            <div class="tailoring-content-one">
                <label title="上传图片" for="chooseImg" class="l-btn choose-btn">
                    <input type="file" accept="image/jpg,image/jpeg,image/png" name="file" id="chooseImg" class="hidden" onchange="selectImg(this)">
                    选择图片
                </label>
                <div class="close-tailoring"  onclick="closeTailor(this)">×</div>
            </div>
            <div class="tailoring-content-two">
                <div class="tailoring-box-parcel">
                    <img id="tailoringImg">
                </div>
                <div class="preview-box-parcel">
                    <p>图片预览：</p>
                    <div class="square previewImg"></div>
                    <div class="circular previewImg"></div>
                </div>
            </div>
            <div class="tailoring-content-three">
                <button class="l-btn cropper-reset-btn">复位</button>
                <button class="l-btn cropper-rotate-btn">旋转</button>
                <button class="l-btn cropper-scaleX-btn">换向</button>
                <button class="l-btn sureCut" id="sureCut">确定</button>
            </div>
        </div>
</div>
<!--图片裁剪框 end-->




<footer class="footer" style="background: #F9F9F9 !important;">
    <div class="copyright">
        <p><a href="http://beian.miit.gov.cn">福建麻花网络科技有限公司版权所有</a></p>
        <p><a href="http://beian.miit.gov.cn">闽ICP备18008231号-2</a></p>
    </div>
</footer>
{/block}

{block name="detail_js"}
<script src="__STATIC__/js/cropper.min.js"></script>
<script src="__STATIC__/js/mobile/datePicker.js"></script>
<script>
	
	
	var calendar = new datePicker();
	  var myDate = new Date;
	  var year=myDate.getFullYear();
	  var month=myDate.getMonth()+1; 
	  var date=myDate.getDate();
	calendar.init({
	    'trigger': '#date', /*按钮选择器，用于触发弹出插件*/
	    'type': 'date',/*模式：date日期；datetime日期时间；time时间；ym年月；*/
	    'minDate':'1900-1-1',/*最小日期*/
	    'maxDate':year+'-'+month+'-'+date,/*最大日期*/
	    'onSubmit':function(theSelectData){
            var theSelectData = calendar.value;
            var datevalue = $("#date span").html();
            if(theSelectData != datevalue) {
                // ajax提交日期
                $.ajax({
                    type: 'POST',
                    url: "/member/editInfo",
                    dataType: 'json',
                    data: {name:'birthday',value:theSelectData},
                    success: function(res) {
                        console.log(res)
                        if (res.code){
                            $("#date span").html(theSelectData);
                            $.toast("保存成功", "text");
                        }else {
                            $.toast(res.msg, "text");
                        }
                    },
                    error: function () {
                        $.toast( "网络错误，请刷新页面重试", "text");
                    }
                });
            }
	    },
	    'onClose':function(theSelectData){
	    }
	});
	

	$("#sex").click(function(){
		$(".poup-sex").show()
	})	
	
	$(".sex-list .cancel").click(function(){
		$(".poup-sex").hide()
	})
	
	$(".poup-sex").click(function(e) {
		var target = $(e.target);
		if (target.closest(".sex-list").length != 0) return;
		$(".poup-sex").hide()
	})
	$(".sex-list .select").click(function(){
        var sexvalue = $(this).html();
        var sexval = $("#sex span").html();
	    var sexcode = '';
	    if (sexvalue == '男'){
            sexcode = 2
		}else if (sexvalue == '女'){
            sexcode = 1
        } else {
            sexcode = 3
		}

        if (sexval != sexvalue) {
            // ajax提交性别
            $.ajax({
                type: 'POST',
                url: "/member/editInfo",
                dataType: 'json',
                data: {name:'sex',value:sexcode},
                success: function(res) {
                    console.log(res)
                    if (res.code){
                        $("#sex span").html(sexvalue);
                        $.toast("保存成功", "text");

                    }else {
                        $.toast(res.msg, "text");
                    }
                },
                error: function () {
                    $.toast( "网络错误，请刷新页面重试", "text");
                }
            });
        }
        $(".poup-sex").hide()
	})

	$(".exit").click(function () {
        $.ajax({
            type: 'POST',
            url: '/member/logout',
            data: {},
            dataType: 'json',
            success: function (json) {
                $.toast('退出成功', "text");
                setInterval(function() {
                    window.location.href = json.data;
                }, 3000);
            },
            error: function () {
                $.toast('网络错误，请刷新页面重试', "text");
            }
        });
    })
	

	
	  //弹出框水平垂直居中
	  var win_height = $(window).height();
	  var win_width = $(window).width();
	  if (win_width <= 768){
	      $(".tailoring-content").css({
	          "top": (win_height - $(".tailoring-content").outerHeight())/2,
	          "left": 0
	      });
	  }else{
	      $(".tailoring-content").css({
	          "top": (win_height - $(".tailoring-content").outerHeight())/2,
	          "left": (win_width - $(".tailoring-content").outerWidth())/2
	      });
	  }
			  
	(window.onresize = function () {
	    var win_height = $(window).height();
	    var win_width = $(window).width();
	    if (win_width <= 768){
	        $(".tailoring-content").css({
	            "top": (win_height - $(".tailoring-content").outerHeight())/2,
	            "left": 0
	        });
	    }else{
	        $(".tailoring-content").css({
	            "top": (win_height - $(".tailoring-content").outerHeight())/2,
	            "left": (win_width - $(".tailoring-content").outerWidth())/2
	        });
	    }
	})();
	//弹出图片裁剪框
	$(".replaceImg").click(function(){
	    $(".tailoring-container").css("display","block");
	});
	//图像上传
	function selectImg(file) {
		
	     var fileSize = file.files[0].size;
	     var size = fileSize / 1024; 
	     var name = file.files[0].name;
	     var fileName = name.substring(name.lastIndexOf(".")+1).toLowerCase();
	 	
	    if(size > 3000 || (fileName !="jpg" && fileName !="png")){
					$.modal({
					   title: "提示",
					   text: "请上传不超过3M的.jpg或.png的图片文件。",
					   buttons: [{
					           text: "确定",
					           className: "pop-button",
					           onClick: function() {}
					       }
					   ]
					});
	      return
	    }
	    if (!file.files || !file.files[0]){
	        return;
	    }
	    var reader = new FileReader();
	    reader.onload = function (evt) {
	        var replaceSrc = evt.target.result;
	        //更换cropper的图片
	        $('#tailoringImg').cropper('replace', replaceSrc,false);//默认false，适应高度，不失真
	    }
	    reader.readAsDataURL(file.files[0]);
	}
	//cropper图片裁剪
	$('#tailoringImg').cropper({
	    aspectRatio: 1/1,//默认比例
	    preview: '.previewImg',//预览视图
	    guides: false,  //裁剪框的虚线(九宫格)
	    autoCropArea: 0.5,  //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
	    movable: false, //是否允许移动图片
	    dragCrop: true,  //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
	    movable: true,  //是否允许移动剪裁框
	    resizable: true,  //是否允许改变裁剪框的大小
	    zoomable: false,  //是否允许缩放图片大小
	    mouseWheelZoom: false,  //是否允许通过鼠标滚轮来缩放图片
	    touchDragZoom: true,  //是否允许通过触摸移动来缩放图片
	    rotatable: true,  //是否允许旋转图片
	    crop: function(e) {
	        // 输出结果数据裁剪图像。
	    }
	});
	//旋转
	$(".cropper-rotate-btn").on("click",function () {
	    $('#tailoringImg').cropper("rotate", 45);
	});
	//复位
	$(".cropper-reset-btn").on("click",function () {
	    $('#tailoringImg').cropper("reset");
	});
	//换向
	var flagX = true;
	$(".cropper-scaleX-btn").on("click",function () {
	    if(flagX){
	        $('#tailoringImg').cropper("scaleX", -1);
	        flagX = false;
	    }else{
	        $('#tailoringImg').cropper("scaleX", 1);
	        flagX = true;
	    }
	    flagX != flagX;
	});
	
	//裁剪后的处理
	$("#sureCut").on("click",function () {
	    if ($("#tailoringImg").attr("src") == null ){
	        return false;
	    }else{
	        var cas = $('#tailoringImg').cropper('getCroppedCanvas');//获取被裁剪后的canvas
	        var base64url = cas.toDataURL('image/png'); //转换为base64地址形式
	        $("#finalImg").prop("src",base64url);//显示为图片的形式
	        //关闭裁剪框
	        closeTailor();
			
			var form_data = new FormData();
			
			form_data.append("file",base64url);
			
			
			$.ajax({
			    type: 'POST',
			    url: "/member/uploadUserImg",
			    dataType: 'json',
			    data: form_data,
			    processData: false,  // 注意：让jQuery不要处理数据
			    contentType: false,  // 注意：让jQuery不要设置contentType
			    success: function(res) {
			         //console.log(res)
					 $.modal({
						title: "提示",
						text: res.msg,
						buttons: [{
								text: "确定",
								className: "pop-button",
								onClick: function() {}
							}
						]
					 });
			
			    },
			    error: function () {
			        $.alert('网络错误，请刷新页面重试');
			    }
			});
			
        
			
	    }
	});
	//关闭裁剪框
	function closeTailor() {
	    $(".tailoring-container").toggle();
	}
	

	
	
	
</script>
{/block}

