{extend name="layout/base" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<style>
.header .back{
	padding-right: 0.02rem !important;
}	
.no_content{
	flex-direction: column;
}
</style>
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>我的游戏</h1>
		<div>
			<div id="query" ><img src="__STATIC__/images/mobile/icon/query-1.png"  style="width: .2rem;"></div>
		</div>
	</div>
</header>
<!-- header end -->

<!-- 开测 -->

<div class=" mygame top-half">


	{notempty name="list"}
	<div class="title" style="border-bottom: 0.1rem solid #F9F9F9;margin: 0;">
		<p>共<span>{$list->total()}</span>款游戏</p>
	</div>
	<div class="game-list">
		{volist name="list" id="vo"}
		<div class="game-info">
			<a {eq name="$vo.gstatus" value="正常"} href="{$vo.gameUrl}" {/eq}>
				<img lazy-src="{$vo.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon">
				<div class="content">
					<h4 class="title" id="my-title">{$vo.nickname}</h4>
					<p>{$vo.type_name}<span>|</span>{$vo.subject_name}<span>
					<P>{$vo.gstatus}</P>
				</div>
			</a>
			{eq name="$vo.gstatus" value="正常"}
			<div class="download">
				<a href="javascript:;">下载</a>
				<p class="android_down_url">{$vo.download}</p>
				<p class="ios_down_url"></p>
			</div>
			{/eq}
		</div>
		{/volist}
	</div>

	{if condition="$list->total() > 10"}
	<a href="javascript:;" class="moregame">查看更多</a>
	<div class="weui-loadmore">
		<i class="weui-loading"></i>
		<span class="weui-loadmore__tips">数据加载中，请稍后</span>
	</div>
	{/if}
	{else /}
	<div class="no_content">
		<img src="__STATIC__/images/icon/game.png">
		<p>暂无玩过的游戏</p>
		<a href="{:getMobileTypeGame()}" style="color: #51a7ff;font-size: 12px;">点击选取游戏</a>
	</div>
	{/notempty}
	

</div>


<!-- 猜你喜欢 -->

<div class="new-game">
	<div class="title">
		<h4><span></span>猜你喜欢</h4>
	</div>
	<div class="new-game-list">
		{volist name="guesList" id="vo"}
		<div class="new-game-info">
			<a href="{$vo.gameUrl}">
				<img lazy-src="{$vo.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon">
				<h4>{$vo.nickname}</h4>
				<p>{$vo.size}</p>
				<p>{$vo.type_name}<span>|</span>{$vo.subject_name}</p>
			</a>
			<div class="download">
				<a href="javascript:;">下载</a>
				<p class="android_down_url">{$vo.download}</p>
				<p class="ios_down_url"></p>
			</div>
		</div>
		{/volist}
	</div>
</div>




{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	// 提示
	$('#query img').click(function(){
		$.modal({
		   title: "提示",
		   text:"<P><b>正常：</b>正常在麻花网络官网运营的游戏；</p><P><b>非运营状态：</b>该游戏还未停运，但因某些原因，不在麻花网络官网运营；</p><p><b>已下架：</b>该游戏即将或已经在麻花网络平台停运。</p>",
		   buttons: [{
		           text: "确定",
		           className: "pop-button",
		           onClick: function() {}
		       }
		   ]
		});
		
		$(".weui-dialog__bd").css({"color":"#262626","font-size":'0.14rem','text-align':'left'});
		$(".weui-dialog__bd p").css('margin',"0.08rem 0");

	})


    var currentPage = parseInt("{$list->currentPage()}");
    var lastPage = parseInt("{$list->lastPage()}");
    var url = '/member/ajaxMyGame';
	// 点击更多
    $(".moregame").click(function () {
        $(".moregame").hide();
        $(".weui-loadmore").show();

        if (currentPage + 1 > lastPage) {
            $(".moregame").hide();
            $(" .weui-loadmore").hide();
            return false;
        }
        $(" .weui-loadmore").show();

        currentPage += 1;
        $.ajax({
            type: "POST",
            timeOut: 10000,
            url: url,
            data: {
                "page": currentPage,
                "serverType": 1
            },
            async: false,
            success: function (res) {

                if (!res.code) {
                    layer.msg(res.msg);
                    return false;
				}

				var data = res.data.list.data;
                var html = '';

                for (var i=0; i < data.length; i++){
					var vo = data[i];
                    html += '<div class="game-info">';

                    if(vo.gstatus == '正常'){
                        html += '<a href="'+vo.gameUrl+'">';
                    }else {
                        html += '<a >';
					}

                    html +='<img lazy-src="'+vo.icon+'" src="__STATIC__/images/icon/150-150.png" class="smallIcon">'
                        +'<div class="content">'
                        +'<h4 class="title" id="my-title">'+vo.nickname+'</h4>'
                        +'<p>'+vo.type_name+'<span>|</span>'+vo.subject_name+'<span>'
                        +'<P>'+vo.gstatus+'</P>'
                        +'</div>'
                        +'</a>';

					if(vo.gstatus == '正常'){
                        html += '<div class="download">'
                            +'<a href="javascript:;">下载</a>'
                            +'<p class="android_down_url">'+vo.download+'</p>'
                            +'<p class="ios_down_url"></p>'
                            +'</div>';
					}

                    html += '</div>'
				}
                $(".game-list").append(html);
            }
        });
        $.getScript("/static/js/mobile/reload.js");
        $(".weui-loadmore").hide();

        if (currentPage >= lastPage) {
            $(".moregame").hide();
        } else {
            $(".moregame").show();
        }

        $(".weui-loadmore").hide();
    })
</script>
{/block}
