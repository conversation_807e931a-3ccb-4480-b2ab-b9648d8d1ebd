{extend name="layout/base" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}
{block name="content"}
		<!-- header start -->
		<header class="top">
			<div class="header">
				<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
				<div>
					<a href="javascript:;" onclick="window.location.replace('{:getMobileTypeGame(0)}')"><h2 {if condition="$showType eq 'type'"} class="active type"{/if} class="type">类型</h2></a>
					<a href="javascript:;" onclick="window.location.replace('{:getMobileSubjectGame(0)}')"><h2 {if condition="$showType eq 'subject'"} class="active"{/if}>题材</h2></a>
				</div>
				<div>
					<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
					<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
				</div>
			</div>
		</header>
		<!-- header end -->

		{if condition="$showType eq 'type'"}
		<ul class="game-category-title">
            <li {if(input('type')=='0' || input('type')=='')} class="active"{/if}>
				<a href="javascript:;" onclick="window.location.replace('{:getMobileTypeGame(0)}')">全部</a>
				</div>
            {volist name="gameTypeList" id="gtvo"}
            <li {if(input('type')==$gtvo.id)} class="active"{/if}>
				<a href="javascript:;" onclick="window.location.replace('{:getMobileTypeGame($gtvo['id'])}')">{$gtvo.name}</a>
				</div>
            {/volist}
		</ul>
		{else/}
		<ul class="game-category-title">
            <li {if(input('subject')=='0' || input('subject')=='')} class="active"{/if}>
				<a href="javascript:;" onclick="window.location.replace('{:getMobileSubjectGame(0)}')">全部</a>
				</div>
            {volist name="gameSubjectList" id="gsvo"}
            <li {if(input('subject')==$gsvo.id)} class="active"{/if}>
				<a href="javascript:;" onclick="window.location.replace('{:getMobileSubjectGame($gsvo['id'])}')">{$gsvo.name}</a>
				</div>
            {/volist}
		</ul>
		{/if}
<div>
	<div class="warp-gamelist">
		{notempty name="$list"}
		<div class="game-list" >
			<p>共<span>{$totalGameCnt}</span>款游戏</p>
			{volist name="list" id="vo"}
			<div class="game-info">
				<a href="{$vo.url}">
					<img lazy-src="{$vo.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" />
					<div class="content">
						<h4>{$vo.nickname}</h4>
						<p>{$vo.typename}<span>|</span>{$vo.subjectname}<span>|</span>{$vo.size}</p>
						{if($vo['publicity'])}<P>{$vo.publicity}</P>{/if}
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url">{$vo.download}</p>
					<p class="ios_down_url">{$vo.ios_download}</p>
				</div>
			</div>
			{/volist}
		
		</div>	
		
		<div class="weui-loadmore">
			<i class="weui-loading"></i>
			<span class="weui-loadmore__tips">数据加载中，请稍等</span>
		</div>
		<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>
		
		{include file="layout/footer" /}
		
		{else/}
		<!-- 无相关游戏 -->
	 	<div class="no_content">
			<div>
				<img src="__STATIC__/images/mobile/icon/no-gift.png">
				<p>暂无相关游戏</p>
			</div>
		</div>
		<!-- 无相关游戏end-->
		{/notempty}
		
		</div>	
	
	</div>

{/block}

{block name="detail_js"}
<script>	
var gamelist = $(".game-list").height();
 var h = $("footer").height();
if (aHigh > (gamelist + head+ h)) {
	$(".footer").addClass("fixed");	
	$(".footer").css("width","3.02rem")
	}else{
	$(".footer").addClass("no-fixed");	
	}

	// 加载更多
	var currentPage = parseInt("{$list->currentPage()}");
	var lastPage = parseInt("{$list->lastPage()}");
	var total = parseInt("{$list->total()}");
	var url = $(".active").attr('href');
	var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(".warp-gamelist").infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;

		if (currentPage + 1 > lastPage) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			}
			loading = false;
			return false;
		}
		$(".weui-loadmore").show();
		setTimeout(function() {
			currentPage += 1;
			$.ajax({
				type: "POST",
				timeOut: 10000,
				url: url,
				data: {
					"page": currentPage
				},
				async: false,
				success: function(res) {
					console.log(res);
					var arr = res.data.data;
					arr.forEach(function(val, index) {
						var html='';
						html += "<div class='game-info'>"+
							"<a href='"+val.url+"'>"+
							"<img lazy-src='"+val.icon+"' src='__STATIC__/images/icon/150-150.png' class='smallIcon' />"+
							"<div class='content'><h4>"+val.nickname+"</h4><p>"+val.typename+"<span>|</span>"+val.subjectname+"<span>|</span>"+val.size+"</p>";
						if(val.publicity){
							html += "<P>"+val.publicity+"</P>";
						}
						html += "</div></a>"+
							"<div class='download'>"+
							"<a href='javascript:;'>下载</a>"+
							"<p class='android_down_url'>"+val.download+"</p>"+
							"<p class='ios_down_url'>"+val.ios_download+"</p></div></div>";
						$(".game-list").append(html);
					});
				},
				error: function() {
					$.alert("网络错误，请刷新页面重试");
				}
			});
			$.getScript("/static/js/mobile/reload.js");
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});
	
	// 返回顶部
	
	$(function () {
	    //检测屏幕高度  
	    var height = $(".warp-gamelist").height();
	    height = 3 / 4 * height;
	    //scroll() 方法为滚动事件  
	    $(".warp-gamelist").scroll(function () {
		    
			console.log(11111)
	        if ($('.warp-gamelist').scrollTop() > height) {
	            $(".return-top").fadeIn(500);
	        } else {
	            $(".return-top").fadeOut(500);
	        }
	
	    });
	});
	
	$(".return-top").click(function () {
	    $(".warp-gamelist").animate({ scrollTop: '0px' }, 1000);
	});
</script>

{/block}
		

