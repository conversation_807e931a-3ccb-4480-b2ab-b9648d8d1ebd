{extend name="layout/base" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<style>
	.weui-loadmore{
		background: #F9F9F9;
	}
	.weui-loadmore {
		display: none;
		width: 100%;
		margin: 0;
		padding: 1.5em 0 0;
	}
</style>
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>资讯详情</h1>
		<div>
			<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end --> 
<div class="news-details top-half">
	
	<h1>{$detail.title}</h1>
	<div>
		<p>来源：麻花网络整理</p>
		<p>发布时间：{$detail.createdate} {$detail.createtime}</p>
	</div>
	
	{$detail.content}
	
</div>

{notempty name="relNewsList"}
<div class="rela-news">
	<div class="title">
		<h4><span></span>相关资讯</h4>
	</div>

	<ul>
		{volist name="relNewsList" id="vo"}
		<li>
			<a href="{$vo.url}">
			<p> {$vo.title}</p>
			<span>{$vo.create_date}</span>
			</a>
		</li>
		{/volist}
	</ul>	
	{/notempty}
</div>



<!-- 相关游戏 -->
{notempty name="relGameInfo"}
<div class="relate-game" style=" display: flex;left: 0;">
	<a href="{:getMobileGameUrl($relGameInfo['id'])}">
		<img src="{$relGameInfo.icon}" />
		<div class="content">
			<h4>{$relGameInfo.nickname}</h4>
			<p>{$relGameInfo.typename}<span>|</span>{$relGameInfo.subjectname}<span>|</span>{$relGameInfo.size}</p>
		</div>
	</a>
	<div class="download">
		<a href="javascript:;">下载</a>
		<p class="android_down_url">{$relGameInfo.download}</p>
		<p class="ios_down_url">{$relGameInfo.ios_download}</p>
	</div>
	<img src="__STATIC__/images/mobile/icon/close-white.png" class="close" style="margin-top: -0.25rem;" />
</div>
{/notempty}

{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	$(".close").click(function(){
		$(".relate-game").fadeOut(500);
	})
</script>
{/block}
