{extend name="layout/base" /}

{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>资讯</h1>
		<div>
			<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
	<ul class="game_rec">
        {volist name="newsCatgList" id="cat"}
			<li><a  href="javascript:;" onclick="window.location.replace('{:getMobileNewsIndexUrl($cat['id'])}')" {if($type==$cat.id)} class="active" {else/} {/if}>{$cat.name}</a></li>
        {/volist}
	</ul>
</header>
<!-- header end -->

<!-- 游戏资讯 -->
<div class="info-new" style="margin-top: 1rem;">
	<p style="line-height: 0.5rem;">相关资讯<span>{$totalNewsCnt}</span>条</p>
	{volist name="list" id="vo"}
	<div class="new-info">
		<a href="{$vo.url}"> <img lazy-src="{$vo.image}" src="__STATIC__/images/icon/990-625.png" class="new-img"></a>
		<div>
			<a href="{$vo.url}">
			<h4>{$vo.title}</h4>
			<p>{$vo.createtime}</p>
			</a>
		</div>
	</div>
	{/volist}

</div>
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>

{empty name="list"}
 	<div class="no_content">
		<div>
			<img src="/static/images/mobile/icon/information.png">
			<p>暂无相关资讯</p>
		</div>
	</div> 
{/empty}


{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	var currentPage = parseInt("{$list->currentPage()}");
	var lastPage = parseInt("{$list->lastPage()}");
	var total = parseInt("{$list->total()}");
	var url = window.location.href;
	var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;

		if (currentPage + 1 > lastPage) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			}
			loading = false;
			return false;
		}
		$(".weui-loadmore").show();
		setTimeout(function() {
			currentPage += 1;
			$.ajax({
				type: "POST",
				timeOut: 10000,
				url: url,
				data: {
					"page": currentPage
				},
				async: false,
				success: function(res) {
					console.log(res);
					var arr = res.data.data;
					arr.forEach(function(val, index) {
						$(".info-new").append(
							'<div class="new-info">'+
								'<a href="'+val.url+'"> <img lazy-src="'+val.image+'" src="/static/images/icon/990-625.png" class="new-img"></a>'+
								'<div>'+
								'<a href="'+val.url+'">'+
									'<h4>'+val.title+'</h4>'+
									'<p>'+val.createtime +'</p></a>'+
								'</div>'+
							'</div>'

						);
					});

				},
				error: function() {
					$.alert("网络错误，请刷新页面重试");
				}
			});
			$.getScript("/static/js/mobile/reload.js");
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});

</script>
{/block}
