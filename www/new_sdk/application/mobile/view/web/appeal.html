{extend name="layout/base" /}
{block name="title"}<title>账号申诉_麻花网络客服中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>账号申诉</h1>
</header>
<form class="form appeal">
	<p> 账号申诉是指通过提交个人资料及账号使用信息来证明您是账号主人的方式。</p>
	<p> 申诉成功后，您可到“<a href="">申诉查询</a>”中，输入申诉编号后，修改密码。申诉查询申诉结果一般会在一个工作日内，发送到您的联系邮箱或手机。</p>
	<div class="warp-input">
		<p>账号申诉：</p>
		<input type="text" placeholder="请输入申诉账号" id="account" name="account" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
	</div>
	
	<div class="warp-input appeal-way">
		<p>申诉接收方式：<span>手机</span></p>
		<img src="__STATIC__/images/mobile/icon/right-gray.png" class="right">
	</div>
	
	<div class="warp-input phonenum">
		<p>手机号：</p>
		<input type="number" placeholder="请输入手机号" id="phonenum"  name="phonenum" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
	</div>
	
	<div class="warp-input email">
		<p>邮箱：</p>
		<input type="text" placeholder="请输入邮箱" id="emailnum"  name="emailnum" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
	</div>
	
	<div class="warp-input appeal-reason">
		<p>申诉原因：<span>请选择申诉原因</span></p>
		<img src="__STATIC__/images/mobile/icon/right-gray.png" class="right">
	</div>
	<p class="agree">继续即代表同意<a href="">《麻花网络服务使用协议》</a></p>
	<input type="button" value="下一步" class="submit clickable" />
</form>

 <div class="poup-sex poup_1">
 	 <div class="sex-list">
 		 <div class="title">申诉接收方式</div>
 		 <a class="select">手机</a>
 		 <a class="select">邮箱</a>
 		 <a class="cancel">取消</a>
 	 </div>
 </div>
 
<div class="poup-sex poup_2">
 	 <div class="sex-list">
 		 <div class="title">申诉原因</div>
 		 <a class="select">忘记密码</a>
 		 <a class="select">账号被盗</a>
 		 <a class="cancel ">取消</a>
 	 </div>
 </div>
 
{/block}

{include file="layout/footer" /}
{/block}
{block name="detail_js"}
<script>
	$(" #phonenum,#account").keyup(function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	})
	
	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})
	
	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("#phonenum,#account").on("postpaste", function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
			
	}).pasteEvents();
	
	$(".poup-sex").click(function(e) {
		var target = $(e.target);
		if (target.closest(".sex-list").length != 0) return;
		$(".poup-sex").hide()
	})
	
	$(".appeal-way").click(function(){
		$(".poup_1").show()
	})	
	$(".appeal-reason").click(function(){
		$(".poup_2").show()
	})	
	
	$(".cancel").click(function(){
		$(".poup-sex").hide()
	})
	
	$(".poup_1 .select").click(function(){
		var appealway = $(this).html();
		$('.appeal-way span').html(appealway);
		if(appealway == "手机"){
		$(".phonenum").show();
		$(".email").hide();
		 }
		 if(appealway == "邮箱"){
			$(".phonenum").hide();
			$(".email").show(); 
		 }
		 $(".poup-sex").hide()
	})
	
	$(".poup_2 .select").click(function(){
		var reason = $(this).html();
		$('.appeal-reason span').html(reason);
		$('.appeal-reason span').css('color','#262626')
		 $(".poup-sex").hide()
	})
	
	if($(".phonenum").css("display")=="block"){
	$(".phonenum").show();
	$(".email").hide();
	}else{
		$(".phonenum").hide();
		$(".email").show(); 
	}
	
	
	// 点击下一步
	$(".submit").click(function(){
		var account = $("#account").val();
		var reason = $('.appeal-reason span').html();
		var preg = /^1[3456789]{1}\d{9}$/;
		var reg = /^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i;
	    // 手机号
		if($(".phonenum").css("display")=="block"){
		  var phonenum = $("#phonenum").val();
		  if(account ==""){
			  $.toast("请输入申诉账号", "text");
		  }else if(phonenum ==""){
			 $.toast("请输入手机号", "text"); 
		  }else if(!preg.test(phonenum)){
			$.toast("请输入正确的手机号", "text");   
		  }else if(reason =="请选择申诉原因"){
			$.toast("请选择申诉原因", "text");  
		  }else{
			  // 跳转手机号验证
		  }
		}
		
		 // 邮箱
		 if($(".email").css("display")=="block"){
		   var emailnum = $("#emailnum").val();
		   if(account ==""){
		 	  $.toast("请输入申诉账号", "text");
		   }else if(emailnum ==""){
		 	 $.toast("请输入邮箱", "text"); 
		   }else if(!reg.test(emailnum)){
		 	$.toast("请输入正确的邮箱", "text");   
		   }else if(reason =="请选择申诉原因"){
		 	$.toast("请选择申诉原因", "text");  
		   }else{
		 	  // 跳转手机号验证
		   }
		 }
	
	})
</script>
{/block}
