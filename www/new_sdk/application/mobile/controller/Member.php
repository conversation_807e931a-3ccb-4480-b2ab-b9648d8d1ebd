<?php
/**
 * 用户 控制器
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/29
 * Time: 14:11
 */
namespace app\mobile\controller;

use app\api\controller\Api;
use think\Config;
use app\common\library\FileUpload;

class Member extends Mobile
{
    private $_passwordSalt = '';      // 密码加密的盐
    private $_encryptMethod = '';       // 密码加密方法
    private $_passwordEncrypt = null;    // 加密密码
    private $_password        = null;    // 密码明文
    private $_username        = null;    // 用户名
    private $_mobile          = '';    // 手机号
    private $_nameLengthMin   = 6;       // 用户名最小长度
    private $_nameLengthMax   = 11;      // 用户名最大长度
    private $_pswdLengthMin   = 6;       // 密码最小长度
    private $_pswdLengthMax   = 15;      // 密码最大长度
    private $_registerMsgCode   = 'SMS_192577923';   //'【麻花网络游戏】您正在进行注册，验证码：[%s]，有效期10分钟，请勿向任何人提供您收到的验证码。';      // 手机号注册短信验证码格式
    private $_findPwdMsgCode   =  'SMS_192542814';   //'【麻花网络游戏】您正在找回密码，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 忘记密码-手机验证身份
    private $_bindEmailVLMobile   = 'SMS_192572807';   //'【麻花网络游戏】您正在换绑邮箱，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定邮箱-手机验证身份
    private $_bindEmail   = 'SMS_192577915';  //'【麻花网络游戏】您正在绑定邮箱，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定邮箱
    private $_bindMobile   = 'SMS_192532751';  //'【麻花网络游戏】您正在绑定手机，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定手机
    private $_bindMobileVLMobile   = 'SMS_192532752';  //'【麻花网络游戏】您正在换绑手机，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定手机-手机验证身份

    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 登录
     */
    public function login() {

        if ($this->request->isAjax()){
            $username = $this->request->param('username' , '' );
            $password = $this->request->param('password' , '' );
            $Ticket   = $this->request->param('ticket' , '' );
            $Randstr  = $this->request->param('randstr' , '' );
            $username = strtolower($username);

            if ( empty($username)) $this->jsonResult([],-1,'请输入用户名');
            if ( empty($password) ) $this->jsonResult([],-2,'请输入密码');


            $slider = $this->sliderVelidate($Ticket,$Randstr);
            if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

            $this->_username = $username;
            $this->_password = $password;

            $member = model('Members')->field(["id","username","password","email","fromflag","reg_time",'flag','nickname','avatar'])->where(["username"=>$this->_username])->find();

            if ( empty($member) ) $this->jsonResult([],-2,'用户名或密码错误');
            if ( !empty($member['flag']) ) $this->jsonResult([],-1,'该用户已被冻结，请联系客服');


            if ( $member['password'] != $this->getPassword() ) $this->jsonResult([],-2,'用户名或密码错误');

            unset($member['password']);
            session('front_userid',$member['id']);
            session('front_account',$member['username']);
            $userinfo = array('front_userid'=>$member['id'], 'front_account'=>$username);
            $userinfo = json_encode($userinfo);
            setcookie('t', auth_code($userinfo, 'ENCODE'), time()+86400, '/', SECOND_DOMAIN);

            if (empty($member['nickname'])) {
                $newNickname = model('Members')->creatNickname($member['id']);
                /*$member['nickname'] = $newNickname;*/
            }
            /*session('front_info',$member);*/

            $preUrl = $this->getPrePageUrl();
            $this->success('登录成功！',$preUrl);
        }

        if (session('front_userid')){
            $this->redirect($this->defaultPage);
            exit;
        }
        $this->setPreUrl();
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch();
    }

    /**
     * 退出登录
     */
    public function logout(){
        setcookie('t', '', time()-1, '/', SECOND_DOMAIN);
        session(null);
        session_destroy();
        $this->jsonResult($this->defaultPage,1,'退出登录成功！');
    }

    // 获取加密密码
    public function getPassword() {
        $password = (string)$this->_passwordEncrypt;

        if ( empty($password) ) {
            switch ( strtoupper($this->_encryptMethod) ) {
                case 'MD5':
                    $password = $this->_md5Code($this->_password, $this->_passwordSalt);
                    break;

                default:
                    $password = $this->_authCode($this->_password, $this->_passwordSalt);
                    break;
            }

            $this->_passwordEncrypt = $password;
        }

        return $password;
    }

    private function _md5Code($password,$salt='') {
        return md5($password . $salt);
    }

    private function _authCode($password,$salt='') {
        return auth_code($password, 'ENCODE', $salt);
    }

    public function userReg(){
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        $this->setPreUrl();
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch('user_reg');
    }

    /**
     * 注册
     */
    public function register(){
        if ($this->request->isPost()){
            $type = $this->request->param('type', '');
            $username = $this->request->param('username','');
            $password = $this->request->param('password','');
            $password_1 = $this->request->param('passwordone','');
            $sms_code = $this->request->param('smsCode','');

            if ( empty($username)) {
                $this->jsonResult([],-1,($type == 'phone') ? '请输入正确的手机号' : '请输入正确的用户名，'.$this->_nameLengthMin.'-'.$this->_nameLengthMax.'位字母或数字');
            }

            if ( empty($password_1) ) {
                $this->jsonResult([],-2,'请输入正确的密码，'.$this->_pswdLengthMin.'-'.$this->_pswdLengthMax.'位字符');
            }
            if ( empty($password) ) {
                $this->jsonResult([],-3,'请再次输入密码');
            }

            if ($password_1 != $password){
                $this->jsonResult([],-3,'两次输入的密码不一致');
            }

            if ( ! $this->checkPassword($password,false) ) {
                $this->jsonResult([],-2,"请输入正确的密码，{$this->_pswdLengthMin} - {$this->_pswdLengthMax}位字符");
            }

            $this->_username = $username;
            if($type == 'phone') {
                $result = (new \app\common\library\Sms)->checkCode($username,$sms_code);
                if(!$result['status']) {
                    $this->error($result['msg']);
                }
                $this->_mobile = $username;
            } elseif($type == 'username') {
                $resUser = $this->checkUsername($username,false);
                if (!$resUser['code']) $this->jsonResult([],-1,$resUser['msg']);
            } else {
                $this->error('非法请求！');
            }

            if ( $this->isExistUser($this->_username) ) {
                $this->jsonResult([],-1,($type == 'phone') ? '该手机号已经被注册' : '该用户名已经被注册' );
            }

            if($type == 'username') {
                $Ticket   = $this->request->param('ticket' , '' );
                $Randstr  = $this->request->param('randstr' , '' );
                $resUser = $this->sliderVelidate($Ticket,$Randstr);
                if (!$resUser['code']) $this->jsonResult([],0,$resUser['msg'] );
            }


            $ip       = GetIP(0);
            $device   = getDevice();
            $agent    = 'default';

            /*! empty($passwordSalt) && $this->setPasswordSalt($passwordSalt);
            ! empty($encryptMethod) && $this->setEncryptMethod($encryptMethod);*/

            $this->_passwordEncrypt = $this->getPassword();

            $last_user_id = model('Members')->insertGetId(["username"=>$this->_username,"password"=>$this->_passwordEncrypt,"mobile"=>$this->_mobile,"agent"=>$agent, "device"=>$device,"ip"=>$ip,"reg_time"=>$_SERVER['REQUEST_TIME'], 'channel_id'=>MEMBER_DEFAULT_CHANNEL_ID]);


            if ( $last_user_id <= '0' ) {
                $this->error('注册失败');
                return false;
            }

            $this->insertChangeLog($last_user_id, $password,null,$this->_mobile);

            session('front_userid',$last_user_id);
            session('front_account',$this->_username);
            // 加密用户信息，用于跨二级域名登录
            $userinfo = array('front_account'=>$username,'front_userid'=>$last_user_id);
            $userinfo = json_encode($userinfo);
            setcookie('t', auth_code($userinfo, 'ENCODE'), time()+86400, '/', SECOND_DOMAIN);

            $newNickname = model('Members')->creatNickname($last_user_id);
            /*$member = [
                'id'       => $last_user_id ,
                "username" => $this->_username ,
                'nickname' => $newNickname ,
                "avatar"   => '' ,
                "reg_time" => $_SERVER['REQUEST_TIME']
            ];
            session('front_info',$member);*/


            $this->success('注册成功！',$this->getPrePageUrl());

        }
        $this->error('非法请求！');
    }

    /**
     * 发送手机验证码
     */
    public function sendCode($isExistUser = false) {

        $phone = $this->request->param('phone', '');

        if (!$this->checkMobile($phone)) {
            $this->error('请输入正确的手机号');
        }

        if ($isExistUser){
            $res = $this->isExistUser($phone);
            if ($res) $this->jsonResult([],-1,'该手机号已被注册');
        }


        $Ticket   = $this->request->param('ticket' , '' );
        $Randstr  = $this->request->param('randstr' , '' );


        $slider = $this->sliderVelidate($Ticket,$Randstr);
        if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

        $result = (new \app\common\library\Sms)->sendCode($phone,$this->_registerMsgCode);

        if ($result['status']) $this->success('验证码发送成功');

        $this->error($result['msg']);
    }

    // 校验用户名是否符合规则
    public function checkUsername($username,$isExit = true) {
        if($this->checkMobile($username)) {
            if ($isExit) $this->error('手机号请选择手机号注册方式');
            return ['code'=>0,'msg'=>'手机号请选择手机号注册方式'];
        } elseif ( strlen($username) < $this->_nameLengthMin || strlen($username) > $this->_nameLengthMax ||  preg_match('/[^0-9A-Za-z_]/', $username)) {
            if ($isExit) $this->error("请输入正确的用户名，{$this->_nameLengthMin} - {$this->_nameLengthMax}位字母或数字");
            return ['code'=>0,'msg'=>"请输入正确的用户名，{$this->_nameLengthMin} - {$this->_nameLengthMax}位字母或数字"];
        }else {
            $this->_username = $username;
            return ['code'=>1,'msg'=>''];
        }
    }

    /**
     * 检查是否为手机号
     */
    public function checkMobile($phone){
        $preg = '/^1[0123456789]{1}\d{9}$/';
        if (0 == preg_match($preg, $phone)) {
            return false;
        }
        return true;
    }

    /**
     * 判断用户是否存在
     * @param string $username 用户名
     * @return boolean
     */
    public function isExistUser($username) {
        $username = filterAndTrimInput($username);
        $result   = model('Members')->field('id')->where(["username"=>$username])->find();
        if ( isset($result['id']) && $result['id'] > 0 ) {
            return true;
        }
        return false;
    }

    public function insertChangeLog($userid,$password=null,$email=null,$mobile=null) {
        $record = ['userid' => $userid, 'ip' => GetIP(1), 'create_time' => NOW_TIMESTAMP];

        !empty($password) && $record['password'] = auth_code($password, 'ENCODE');
        !empty($email) && $record['email'] = $email;
        !empty($mobile) && $record['mobile'] = $mobile;

        return model('MemberHistory')->insertGetId($record);
    }

    /**
     * 校验密码是否符合规则
     * @param $password
     * @return bool
     */
    public function checkPassword($password,$isReturn = true) {
        if ( strlen($password) > $this->_pswdLengthMax || strlen($password) < $this->_pswdLengthMin ) {
            if ($isReturn) $this->error("请输入正确的密码，{$this->_pswdLengthMin} - {$this->_pswdLengthMax}位字符");
            return false;
        } else {
            $this->_password = strval($password);
            return true;
        }
    }

    /**
     * 忘记密码-- 确认账号
     */
    public function contactAccount(){
        if ($this->request->isAjax()){
            $username = input('post.username' , '' );
            if (empty($username)) $this->error('用户名不能为空');

            $Ticket   = $this->request->param('ticket' , '' );
            $Randstr  = $this->request->param('randstr' , '' );
            $slider = $this->sliderVelidate($Ticket,$Randstr);
            if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

            $info     = model('Members')->field('id,email,mobile,username,flag')->where(["username" => $username])->find();
            if (empty($info)) $this->error('用户名不存在');
            if ( !empty($info['flag']) ) $this->error('该用户已被冻结，请联系客服');

            if ( !empty($info['email'])) $info['email'] = mailObfuscation($info['email']);
            if ( !empty($info['mobile'])) $info['mobile'] = stringObfuscation($info['mobile'] , 3);
            session('check_uid',$info);
            $this->success('',getMobileForgetPwdUrl(2));
        }
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch('contact_account');
    }

    /**
     * 忘记密码--提供验证方式
     */
    public function contactWay() {

        $info = session('check_uid');
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }

        if (!$info) $this->error('非法请求');

        $this->assign('info',$info);
        return $this->fetch('contact_way');
    }

    /**
     * 忘记密码--提供验证方式--邮箱或者手机号验证
     */
    public function contact() {
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        $typeArr = ['phone' , 'email'];
        $type    = $this->request->param('type' , '' );
        $info = session('check_uid');

        if (!$info || !in_array($type,$typeArr)) $this->redirect(getMobileForgetPwdUrl(2));

        $this->assign('info',$info);
        $this->assign('type',$type);
        return $this->fetch();
    }

    /**
     * 忘记密码--发送验证码
     */
    public function resetPwdSendCode(){
        $type    = input('post.type' , '' );
        $id      = session('check_uid')['id'];
        $typeArr = ['phone' , 'email'];

        if (!in_array($type,$typeArr) || empty($id)) $this->error('非法请求');
        $info     = model('Members')->field('id,username,email,mobile')->where(["id" => $id , 'flag' => 0])->find();

        if ($type == $typeArr[0]){
            $result = (new \app\common\library\Sms)->sendCode($info['mobile'],$this->_findPwdMsgCode);

        }else{
            $result = (new \app\common\library\Mail)->sendCodeMailByType($info['username'], $info['email']);
        }

        if(isset($result) && $result['status']){
            $this->jsonResult([],1,'发送成功');
        }else{
            $this->jsonResult([],0,$result['msg']);
        }
    }

    /**
     * 忘记密码--验证验证码
     */
    public function resetPwdCheckCode() {
        $id      = session('check_uid')['id'];
        $code    = input('post.code' , 0 , 'intval');
        $type    = input('post.type' , '' );
        $typeArr = ['phone' , 'email'];

        if ( !in_array($type , $typeArr) || empty($id)) $this->jsonResult([],0,'非法请求');
        if (empty($code)) $this->jsonResult([],0,'请输入验证码');
        $info = model('Members')->field('id,username,email,mobile')->where(["id" => $id , 'flag' => 0])->find();

        if ($type == $typeArr[0]) {
            $result = (new \app\common\library\Sms)->checkCode($info['mobile'] , $code);

        } else {
            $result = (new \app\common\library\Mail)->checkCode($info['username'] , $info['email'] , $code);
        }

        if (isset($result) && $result['status']) {
            session('hasCheck',true);
            $this->success('验证成功',getMobileForgetPwdUrl(3));
        }
        $errMsg = $result['msg'];
        $this->jsonResult([],0,$errMsg);
    }

    /**
     * 忘记密码--重置密码
     */
    public function forgetPwdToReset(){
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        if ($this->request->isAjax()){
            $id        = session('check_uid')['id'];
            $password1 = input('post.password1' , '' );
            $password2 = input('post.password2' , '' );

            if (empty($id)) $this->jsonResult([],0,'非法请求');
            if (empty($password1)) $this->jsonResult([],-1,'请输入新密码，'.$this->_pswdLengthMin.'-'.$this->_pswdLengthMax.'位字符');
            if (empty($password2)) $this->jsonResult([],-2,'请再次输入密码');
            //if (!session('hasCheck')) $this->error('请先通过身份验证');
            if ($password1 != $password2) $this->jsonResult([],-2,'两次输入的密码不一致');

            $res = $this->resetPassword($id,$password1);
            if ($res == 3) $this->jsonResult([],-1,'请输入正确的密码，'.$this->_pswdLengthMin.'-'.$this->_pswdLengthMax.'位字符');
            if ($res == 1 || $res == 2) {
                session('hasCheck',null);
                session('check_uid',null);

                $this->success('重置成功',getMobileEncodeUrl('login'));
            }
            if ($res == 0) $this->jsonResult([],-2,'新密码不能与旧密码相同');
        }

        return $this->fetch('forget_pwd_to_reset');
    }

    /**
     * 重置密码
     *$flag: 账号是否解冻 1解冻  0不解冻
     * return：0新密码不能与旧密码相同，1密码重置成功，2密码重置失败，3密码格式不正确
     */
    public function resetPassword($id,$password1,$flag = 0){

        $res1 = $this->checkPassword($password1,false);
        if (!$res1) return 3;

        $info = model('Members')->field('id,username,email,mobile,password')->where(["id" => $id])->find();

        $this->_passwordEncrypt = $this->getPassword();
        if ($this->_passwordEncrypt == $info['password']) return 0;
        $data = ['password'  => $this->_passwordEncrypt];
        if ($flag == 1) $data['flag'] = 0;

        $res = model('Members')->save($data,['id' => $id]);

        if ($res){
            session(null);
            session_destroy();
            setcookie('t', '', time()-1, '/', SECOND_DOMAIN);

            $record = [
                'userid' => $id ,
                'ip' => GetIP(1) ,
                'password' => $this->_passwordEncrypt ,
                'create_time' => NOW_TIMESTAMP,
            ];
            model('MemberHistory')->insertGetId($record);
            return 1;
        }
        return 2;
    }

    /**
     * 个人中心
     */
    public function personalCenter(){
        $this->_isLogin();
        $userName = model('Members')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->field('nickname,avatar')->where(['id'=>session('front_userid')])->find();
        $this->assign('user_name',$userName);
        return $this->fetch('personal_center');
    }

    /**
     * 滑块验证
     * @param $Ticket
     * @param $Randstr
     * @return array
     */
    public function sliderVelidate($Ticket,$Randstr){
        // todo 开发环境跳过滑块验证
        if (APP_STATUS == 'dev') return ['code'=>1,'msg'=>''];

        if ( empty($Ticket) || empty($Randstr) ) return ['code'=>0,'msg'=>'非法请求'];

        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $AppSecretKey = Config::get('SLIDER_VERIFICATION.AppSecretKey');
        $url          = Config::get('SLIDER_VERIFICATION.VERIFYURL');
        $UserIP       = GetIP(1);

        $params = array(
            "aid"          => "$appid" ,
            "AppSecretKey" => $AppSecretKey ,
            "Ticket"       => $Ticket ,
            "Randstr"      => $Randstr ,
            "UserIP"       => $UserIP
        );
        $paramstring = http_build_query($params);
        $content = txcurl($url,$paramstring);
        $result = json_decode($content,true);
        if($result){
            if($result['response'] == 1){
                return ['code'=>1,'msg'=>''];
            }else{
                return ['code'=>0,'msg'=>'系统繁忙，请稍后再试！'];
            }
        }else{
            return ['code'=>0,'msg'=>'系统繁忙，请稍后再试！'];
        }
    }

    /**
     * 个人中心--个人资料
     */
    public function personalInfo(){
        $this->_isLogin();
        $defaultBrith = '0000-00-00';
        $info = $this->getUserInfo();
        if ($info['birthday'] == $defaultBrith) $info['birthday'] = null;
        $this->assign('info',$info);
        return $this->fetch('personal_info');
    }

    /**
     * 个人中心--个人资料--编辑
     */
    public function editInfo(){
        if (!session('?front_userid')){
            $this->error('请先登录！');
        }
        $files = $data = ['sex' , 'qq' , 'address' , 'zipcode' , 'birthday' ];

        $type = input('post.name' , '' );
        $value = input('post.value' , '' );

        if (empty($type) || !in_array($type,$files)) $this->error('参数错误！');


        if($type == $files[1] && !empty($value) && 0 == preg_match('/^\d*$/', $value)) {
            $this->jsonResult('',0,'请输入正确的QQ号');
        }
        if($type == $files[3] && !empty($value) && 0 == preg_match('/^(\d{6})?$/', $value)) {
            $this->jsonResult('',0,'请输入正确的邮政编码');
        }
        if($type == $files[2] && !empty($value) && iconv_strlen($value,"UTF-8") >40) {
            $this->jsonResult('',0,'联系地址不能超过40个字');
        }

        $data = [$type=>$value];

        model('MembersTwo')->updateInfo($data, session('front_userid'));
        $this->success('保存成功',getMobileUserCenterUrl(1));
    }

    /**
     * 获取用户资料
     */
    public function getUserInfo(){
        $id = session('front_userid');
        $info = model('Members')->alias('m')
            ->join('cy_memberstwo t','m.id=t.userid','left')
            ->field('m.id,m.username,t.sex,t.qq,t.birthday,t.zipcode,t.address,t.id as memberstwo_id,m.nickname,m.avatar')
            ->where(["m.id" => $id , 'm.flag' => 0])
            ->find();

        return $info;
    }
    /**
     * 个人中心--个人资料--QQ
     */
    public function editqq(){
        $info = $this->getUserInfo();
        $this->assign('qq',$info['qq']);
        return $this->fetch('personal_qq');
    }

    /**
     * 个人中心--个人资料--联系地址
     */
    public function editaddress(){
        $info = $this->getUserInfo();
        $this->assign('address',$info['address']);
        return $this->fetch('personal_addr');
    }

    /**
     * 个人中心--个人资料--邮政编码
     */
    public function editzipcode(){
        $info = $this->getUserInfo();
        $this->assign('zipcode',$info['zipcode']);
        return $this->fetch('personal_zipcode');
    }

    /**
     * 前置方法(参数获取)
     */
    protected function _beforeGetParams()
    {
        $start    = $this->request->param('start' , '');
        $end      = $this->request->param('end' , '');
        $orderid  = $this->request->param('orderid' , '' , 'trim');
        $gamename = $this->request->param('gamename' , '' , 'trim');
        $status = $this->request->param('status' , '');
        $condition = [];

        if ( !in_array($status , ['' , 0 , 1 , 2])) $status = '';
        $condition = ['p.userid' => session('front_userid')];

        if ($status != '') {
            $condition['p.status'] = $status;
        }

        if (!empty($orderid)) {
            $condition['p.orderid'] = $orderid;
        }

        if (!empty($gamename)) {
              $condition['g.nickname'] = ['LIKE', '%' . $gamename . '%'];
        }

        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $condition['p.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $condition['p.create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $condition['p.create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {

        }

        return ['where'=>$condition,'start'=>$start,'end'=>$end,'status'=>$status];
    }

    /**
     * 个人中心--充值记录
     */
    public function personalPay(){
        $this->_isLogin();

        $GetParams = $this->_beforeGetParams();
        $condition = $GetParams['where'];

        $PayModel = model('Common/Pay');

        $list = $PayModel->alias('p')->field('p.id,p.orderid,p.amount,p.create_time,g.nickname,p.status,p.productname,info.mobileicon')
            ->join('cy_game g','p.gameid=g.id','left')
            ->join('cy_gameinfo info', 'g.id = info.game_id')
            ->where($condition)
            ->order('p.create_time desc')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function ($item,$index){
                $item['amount'] = intval($item['amount']);
                $item['icon'] = getGameIcon($item['mobileicon']);

                $item['pay_status'] = '';
                switch ($item['status']){
                    case 0: $item['pay_status'] = '未支付';
                        break;
                    case 1: $item['pay_status'] = '支付成功';
                        break;
                    case 2: $item['pay_status'] = '支付失败';
                        break;
                }

                return $item;
            });
        if ($this->request->isAjax()) $this->jsonResult($list,1);
        $this->assign('status',$GetParams['status']);
        $this->assign('start',$GetParams['start']);
        $this->assign('end',$GetParams['end']);
        $this->assign('list',$list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    /**
     * 个人中心--存号箱 页面（未过期）
     */
    public function giftbox(){
        $keyword = input('keyword');

        $list = $this->memberGift('now',$keyword);
        if ($this->request->isAjax()) $this->jsonResult($list,1,'now');
        $this->assign('type','now');
        $this->assign('list',$list);
        $this->assign('keyword',$keyword);
        $this->assign('page', $list->render());
        return $this->fetch('member_gift');
    }

    /**
     * 个人中心--存号箱 页面（过期）
     */
    public function giftboxgp(){
        $keyword = input('keyword');
        $list = $this->memberGift('overtime',$keyword);
        if ($this->request->isAjax()) $this->jsonResult($list,1,'overtime');
        $this->assign('type','overtime');
        $this->assign('list',$list);
        $this->assign('page', $list->render());
        $this->assign('keyword',$keyword);
        return $this->fetch('member_gift');
    }

    /**
     * 个人中心--存号箱（数据）
     */
    public function memberGift($dateType = '',$keyword=''){
        $this->_isLogin();

        $condition = ['a.username'=>session('front_account')];
        if ($dateType == 'overtime'){
            $condition['l.endtime'] = ['elt',NOW_TIMESTAMP];
        }elseif ($dateType == 'now'){
            $condition['l.endtime'] = ['gt',NOW_TIMESTAMP];
        }

        if (!empty($keyword)){
            $condition['l.title'] = ['like','%'.$keyword.'%'];
        }

        $list = model('Libaolog')->alias('a')
            ->join('cy_libaoinfo l','l.id=a.libaoid')
            ->join('cy_game g','g.id=l.gameid','left')
            ->join('cy_gameinfo info', 'g.id=info.game_id', 'left')
            ->field('a.id,l.title,a.username,a.create_time,g.name,a.code,l.content,l.starttime,l.endtime,info.mobileicon')
            ->where($condition)
            ->order('a.create_time desc')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function ($item,$index){
                $item['endtime'] = date('Y-m-d H:i:s',$item['endtime']);
                $item['mobileicon'] = getGameIcon($item['mobileicon']);
                return $item;
            });
        return $list;
    }

    /**
     * 个人中心--账号安全
     */
    public function personalAccount() {
        $this->_isLogin();
        $id   = session('front_userid');
        $info = model('Members')->alias('m')
            ->join('cy_memberstwo t' , 'm.id=t.userid' , 'left')
            ->field('m.id,m.email,m.mobile,t.idcard,t.realname')
            ->where(["m.id" => $id , 'm.flag' => 0])
            ->find();

        $info['isVerified'] = 1;
        if (empty($info['realname']) || empty($info['idcard']) || ((- 1 == $info['realname']) && (- 1 == $info['idcard']))) {
            $info['isVerified'] = 0;
        }

        if ( !empty($info['email'])) $info['email'] = mailObfuscation($info['email']);
        if ( !empty($info['mobile'])) $info['mobile'] = mobileObfuscation($info['mobile']);

        $this->assign('info',$info);
        return $this->fetch();
    }

    /**
     * 个人中心--修改密码
     */
    public function personalChangePwd(){
        $this->_isLogin();
        if($this->request->isPost() ){
            $oldPwd    = input('post.oldPwd' , '' );
            $password1 = input('post.password1' , '' );
            $password2 = input('post.password2' , '' );

            if (empty($oldPwd)) $this->jsonResult([],0,'请输入旧密码');
            if (empty($password1)) $this->jsonResult([],-1,'请输入密码，6-15位字符');
            if (empty($password2)) $this->jsonResult([],-2,'请再次输入密码');

            if ($oldPwd == $password1) $this->jsonResult([],-1,'新密码不能与旧密码相同');
            if ($password1 != $password2) $this->jsonResult([],-2,'两次输入的密码不一致');
            $this->checkPassword($password1);

            $id = session('front_userid');
            $info = model('Members')->field('id,username,email,mobile,password')->where(["id" => $id, 'flag' => 0])->find();

            $this->_password = $oldPwd;
            $this->_passwordEncrypt = $this->getPassword();

            if ($this->_passwordEncrypt != $info['password']) $this->jsonResult([],0,'旧密码输入错误');

            $this->_password = $password1;$this->_passwordEncrypt = '';
            $this->_passwordEncrypt = $this->getPassword();

            $res = model('Members')->save(['password'  => $this->_passwordEncrypt],['id' => $id]);
            model('MemberHistory')->insertGetId(['password'  => $this->_passwordEncrypt,'userid'=> $id,'ip' => GetIP(1) ,'create_time' => NOW_TIMESTAMP]);

            if ($res) $this->success('修改成功',getMobileUserCenterUrl('4'));

            $this->jsonResult([],0,'修改失败');
        }
        return $this->fetch();
    }


    /**
     * 个人中心--实名认证--页面
     */
    public function personalIdentityPage(){
        $this->_isLogin();

        $id   = session('front_userid');
        $info = model('Members')->alias('m')
            ->join('cy_memberstwo t' , 'm.id=t.userid' , 'left')
            ->field('m.id,m.email,m.mobile,t.idcard,t.realname')
            ->where(["m.id" => $id , 'm.flag' => 0])
            ->find();

        if (empty($info['realname']) || empty($info['idcard']) || ((- 1 == $info['realname']) && (- 1 == $info['idcard']))) {
            // 未实名认证
            return $this->fetch('identity');
        }

        if ( !empty($info['realname'])) $info['realname'] = usernameObfuscation($info['realname']);
        if ( !empty($info['idcard'])) $info['idcard'] = idcardObfuscation($info['idcard']);

        $this->assign('info',$info);
        return $this->fetch('identity_info');
    }

    /**
     * 个人中心--实名认证
     */
    public function personalIdentity(){
        $this->_isLogin();

        $data = [
            'realname' => input('post.realname', ''),
            'idcard'   => input('post.idcard', ''),
        ];
        $result = $this->validate($data, [
            ['realname', 'require|length:2,10|chs', '请输入姓名|姓名只能是2-10位的汉字|姓名只能是2-10位的汉字'],
            ['idcard', 'require|identityNumber', '请输入身份证号码|身份证号码格式不正确'],
        ]);
        if (true !== $result) $this->jsonResult($result,0,$result);

        // 通过身份证验证年龄
        if (!isMeetAgeByIDCard($data['idcard'],9,120)) $this->jsonResult([], 0, '您未满足年龄要求（9-120岁），无法通过认证');

        $res = model('MembersTwo')->updateInfo($data, session('front_userid'));

        if ($res) $this->success('实名认证成功',getMobileUserCenterUrl('4'));
        $this->jsonResult('', 0, '认证失败');
    }

    /**
     * 个人中心--绑定手机或邮箱--1
     */
    public function toBindOne(){
        $this->_isLogin();
        $type = input('type', '');
        if (!in_array($type,['mobile','email'])) {
            $this->error('请求错误');
        }

        $id   = session('front_userid');
        $info = model('Members')->alias('m')
            ->field('m.id,m.email,m.mobile')
            ->where(["m.id" => $id , 'm.flag' => 0])
            ->find();

        // 请求绑定手机，就判断是否绑定邮箱；请求绑定邮箱，就判断是否绑定手机
        $runable = 'no'; // 是否去先验证另外一个联系方式

        if ($type == 'mobile' && (!empty($info['email']) || !empty($info['mobile'])) ) {
            $runable = 'email';
        }
        if ($type == 'email' && (!empty($info['email']) || !empty($info['mobile']))) {
            $runable = 'mobile';
        }

        if ($runable == 'no'){
            $appid        = Config::get('SLIDER_VERIFICATION.APPID');
            $this->assign('appid',$appid);
            $this->assign('type',$type);
            return $this->fetch('bind_page');
        }

        session('velidate_info',['type'=>$type,'info_data'=>$info,'velidate_type'=>'']);
        $this->assign('type',$type);
        $this->assign('info',$info);
        return $this->fetch('bind_other');
    }

    /**
     * 个人中心--绑定手机或邮箱--2  验证其他方式的页面
     */
    public function toVelidatePage(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->redirect(getUserCenterUrl(1));
        $type = input('type', '');
        if (!in_array($type,['mobile','email'])) {
            $this->redirect(getUserCenterUrl(1,url('member/personalInfo')));
        }

        $velidate_info['velidate_type'] = $type;
        session('velidate_info',$velidate_info);
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        $this->assign('info',$velidate_info);
        return $this->fetch('velidate_page');
    }

    /**
     * 个人中心--绑定手机或邮箱--3  发送验证码
     */
    public function toVelidateSendCode(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->error('系统繁忙！');

        /*$Ticket   = $this->request->param('ticket' , '' );
        $Randstr  = $this->request->param('randstr' , '' );
        $slider = $this->sliderVelidate($Ticket,$Randstr);
        if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);*/

        $temple = ''; // 短信或邮件模板
        if ($velidate_info['velidate_type'] == 'mobile'){

            if ($velidate_info['type'] == 'mobile'){
                $temple = $this->_bindMobileVLMobile;
            }else{
                if (empty($velidate_info['info_data']['email'])){
                    $temple = $this->_bindEmail;
                }else{
                    $temple = $this->_bindEmailVLMobile;
                }
            }
            $result = (new \app\common\library\Sms)->sendCode($velidate_info['info_data']['mobile'],$temple);

        }else{
            if ($velidate_info['type'] == 'email'){
                $temple = 'bindEmailVLEmail';
            }else{
                if (empty($velidate_info['info_data']['mobile'])){
                    $temple = 'bindMobil';
                }else{
                    $temple = 'bindMobilVLEmail';
                }
            }
            $result = (new \app\common\library\Mail)->sendCodeMailByType(session('front_account'), $velidate_info['info_data']['email'],$temple);
        }

        if(isset($result) && $result['status']){
            $this->jsonResult([],1,'发送成功');
        }else{
            $this->jsonResult([],0,$result['msg']);
        }
    }

    /**
     * 个人中心--绑定手机或邮箱--4 验证 手机或邮箱验证码
     */
    public function velidateCode(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->redirect(getUserCenterUrl(1));
        $code    = input('post.code' , 0 , 'intval');

        if (empty($code)) $this->jsonResult([],0,'请输入验证码');

        if ($velidate_info['velidate_type'] == 'mobile') {
            $result = (new \app\common\library\Sms)->checkCode($velidate_info['info_data']['mobile'] , $code);

        } else {
            $result = (new \app\common\library\Mail)->checkCode(session('front_account') , $velidate_info['info_data']['email'] , $code);
        }

        if (isset($result) && $result['status']) {
            $this->success('验证成功',getMobileAccountUrl(2.2) );
        }

        $this->jsonResult([],0,$result['msg']);
    }

    /**
     * 个人中心--绑定手机或邮箱--5  直接绑定的页面
     */
    public function bindPage(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->redirect(getMobileUserCenterUrl(1));
        $this->assign('type',$velidate_info['type']);
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch();
    }

    /**
     * 个人中心--绑定手机或邮箱--6  发送验证码
     */
    public function bindSendCode(){

        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->error('系统繁忙');
        $this->directBindSendCode($velidate_info['type']);
    }

    /**
     * 个人中心--绑定手机或邮箱--直接绑定页面--6  发送验证码
     */
    public function directBindSendCode($type=''){

        $contact = $this->request->param('contact');
        if (empty($type)) $this->error('系统繁忙');
        if (empty($contact)) {
            if ($type == 'mobile') $this->error('请输入正确的手机号');
            $this->error('请输入正确的邮箱');
        }

        $Ticket   = $this->request->param('ticket' , '' );
        $Randstr  = $this->request->param('randstr' , '' );
        $slider = $this->sliderVelidate($Ticket,$Randstr);
        if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

        $velidate_info = session('velidate_info');
        if ($type == 'mobile' && $contact == $velidate_info['info_data']['mobile']){
            $this->error('新旧手机号不能相同');
        }
        if ($type == 'email' && $contact == $velidate_info['info_data']['email']){
            $this->error('新旧邮箱不能相同');
        }

        if ($type == 'mobile'){
            $result = (new \app\common\library\Sms)->sendCode($contact,$this->_bindMobile);

        }else{
            $result = (new \app\common\library\Mail)->sendCodeMailByType(session('front_account'), $contact,'bindEmail');
        }

        if(isset($result) && $result['status']){
            $this->jsonResult([],1,'发送成功');
        }
        $this->jsonResult([],0,$result['msg']);
    }

    /**
     * 个人中心--绑定手机或邮箱--7  进行绑定操作(直接进行绑定)
     */
    public function directBindContact($type = ''){
        $contact = $this->request->param('contact');
        $code    = $this->request->param('code' , 0 , 'intval');
        if (!in_array($type,['mobile','email'])) $this->error('系统繁忙');
        if (empty($contact)) {
            if ($type == 'mobile') $this->error('请输入正确的手机号');
            $this->error('请输入正确的邮箱');
        }
        if (empty($code)) $this->jsonResult([],0,'请输入验证码');


        if ($type == 'mobile') {
            $result = (new \app\common\library\Sms)->checkCode($contact , $code);

        } else {
            $result = (new \app\common\library\Mail)->checkCode(session('front_account') , $contact , $code);
        }

        if (isset($result) && $result['status']) {

            $userid = session('front_userid');
            $res = model('Members')->save([$type  => $contact],['id' => $userid]);
            model('MemberHistory')->insertGetId([$type  => $contact,'userid'=> $userid,'ip' => GetIP(1),'create_time' => NOW_TIMESTAMP]);

            session('velidate_info',null);
            $this->success('绑定成功',getMobileUserCenterUrl(4));
        }
        $this->jsonResult([],0,$result['msg'] );
    }

    /**
     * 编辑昵称
     */
    public function editnickname(){
        if ($this->request->isPost()){
            $data['nickname'] = input('post.nickname');

            if (empty($data)) $this->error('请输入昵称');

            $res = validataNickname($data['nickname']);
            if (!$res['code']) $this->error($res['msg']);

            model('members')->save($data,['id'=>session('front_userid')]);
            /*$this->_updateUserInfo($data);*/
            $this->success('保存成功',getMobileUserCenterUrl(1));
        }

        $info = $this->getUserInfo();
        $this->assign('nickname',$info['nickname']);
        return $this->fetch('personal_nickname');
    }

    /**
     * 用户头像上传
     * 由于用户头像经过裁剪，前端传过来的是base64位的数据流，故要先保存在服务器，然后调接口上传到华为云，最后删除服务器上的图片。
     * @return bool
     */
    public function uploadUserImg(){
        (new \app\home\controller\Member)->uploadUserImg();
    }

    /**
     * 我要反馈
     */
    public function feedback(){
        if ($this->request->isPost()){
            (new \app\home\controller\Member)->ajaxFeedback('mobile');
        }

        $kefu = model('Kefu')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->field('qq')->find();
        $this->assign('kefu',$kefu['qq']);
        return $this->fetch();
    }


    /**
     * 我的游戏
     */
    public function mygame(){

        $obj = (new \app\home\controller\Member)->mygame(1);

        $this->assign('list' , $obj['list']);
        $this->assign('page' , $obj['page']);
        $this->assign('guesList' , $obj['guesList']);
        return $this->fetch();
    }

    /**
     * 我的游戏分页
     */
    public function ajaxMyGame(){
        (new \app\home\controller\Member)->ajaxMyGame();
    }

    /**
     * 个人中心--充值记录
     */
    public function personalPayDetail(){
        $this->_isLogin();
        $id = input('id',0,'intval');

        $condition = ['p.userid' => session('front_userid'),'p.id'=>$id];

        $PayModel = model('Common/Pay');
        $list = $PayModel->alias('p')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->field('p.id,p.orderid,p.amount,p.create_time,g.nickname,p.status,p.productname,info.mobileicon,p.paytype,p.real_ptb,p.real_amount,p.pay_time')
            ->join('cy_game g','p.gameid=g.id','left')
            ->join('cy_gameinfo info', 'g.id = info.game_id')
            ->where($condition)
            ->find();
        $list['pay_status'] = '';
        switch ($list['status']){
            case 0: $list['pay_status'] = '未支付';
                break;
            case 1: $list['pay_status'] = '支付成功';
                break;
            case 2: $list['pay_status'] = '支付失败';
                break;
        }
        $list['amount'] = intval($list['amount']);
        $list['icon'] = getGameIcon($list['mobileicon']);


        // 统一显示为“微信支付”。
        $wxzfArr = ['wxpay','swiftwxpay','wxpay-h5','ipaywxpay','ios-wxpay'];
        // 统一显示为“微信混合支付”。
        $wxhhzfArr = ['mix-swiftwxpay','mix-ipaywxpay','mix-wxpay-h5'];
        // 统一显示为“支付宝支付”。
        $zfbzfArr = ['zfb','ios-zfb'];
        // 平台币支付显示规则
        $lbArr = array_merge(['ptb','mix-zfb'],$wxhhzfArr);
        // 第三方支付显示规则
        $thirdArr = array_merge(['mix-zfb'],$wxhhzfArr,$wxzfArr,$zfbzfArr);

        $list['lb_show'] = 0;
        $list['third_show'] = 0;
        if (in_array($list['paytype'],$lbArr)) $list['lb_show'] = 1;
        if (in_array($list['paytype'],$thirdArr)) $list['third_show'] = 1;
        if (in_array($list['paytype'],$wxzfArr)){
            $list['paytype'] = '微信支付';
        }elseif (in_array($list['paytype'],$wxhhzfArr)){
            $list['paytype'] = '微信混合支付';
        }elseif (in_array($list['paytype'],$zfbzfArr)){
            $list['paytype'] = '支付宝';
        }else{
            $list['paytype'] = config('paytype.'.$list['paytype']);
        }

        $this->assign('info',$list);
        return $this->fetch('personal_pay_detail');
    }
}
