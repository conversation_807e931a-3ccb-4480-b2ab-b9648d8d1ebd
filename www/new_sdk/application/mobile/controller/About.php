<?php
/**
 * 关于我们等页面 控制器
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/29
 * Time: 14:11
 */
namespace app\mobile\controller;

use think\Request;

class About extends Mobile
{

    
    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
        // 获取底部关于我们列表
        $aboutList = model('Aboutus')->getAboutList();
        $this->assign('aboutList',$aboutList);
    }

    public function _empty()
    {
        $action = Request::instance()->action();
        
        $info = $this->getAboutData($action);

        $this->assign('info',$info);

        return $this->fetch('about/index');
        
    }

    /**
     * 关于 的页面加载
     */
    private function getAboutData($action) {
       
        $info = model('Aboutus')->where('name',$action)->find();
        
        if (empty($info)) $this->error('暂无数据');

        return $info;
    }

    /**
     * 服务协议
     */
    public function agreement(){
        return $this->fetch();
    }

}
