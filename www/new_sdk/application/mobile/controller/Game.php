<?php
/**
 * 前端游戏控制器
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/29
 * Time: 14:11
 */
namespace app\mobile\controller;

use app\common\model\GameHome as GameModel;
use app\common\model\News as NewsModel;
use think\Db;
use think\Exception;
use think\Config;
class Game extends Mobile
{
    // 开测类型  1-预告 2-开服 3-删档内测 4-不删档内测 5-公测
    private $_serstatus = [
        '1' =>  '预告',
        '2' =>  '开服',
        '3' =>  '删档内测',
        '4' =>  '不删档内测',
        '5' =>  '公测',
    ];
    /**
     * 找游戏
     */
    public function index() {
        $showType = 'type';

        $gameModel = new GameModel;
        
        $where = [];
		
        if(input('type') != ''){
			if(intval(input('type'))){
				$where['game.type'] = intval(input('type'));
			}
			$showType = 'type';
        }
        else if(input('subject') != ''){
			if(intval(input('subject'))){
				$where['game.subject'] = intval(input('subject'));
			}
			$showType = 'subject';
        }
		else{
			if(intval(input('type'))){
				$where['game.type'] = intval(input('type'));
			}
			$showType = 'type';
		}

		$list = $gameModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->alias('game')
				->join('cy_gameinfo info', 'game.id = info.game_id','left')
				->join('cy_gametype t', 'game.type = t.id','left')
				->join('cy_gamesubject s', 'game.subject = s.id','left')
				->join('cy_sdkgamelist sdk', 'game.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
				->field('game.id,game.nickname,game.power,game.publicity,info.androidurl,info.platform,game.pinyin,info.mobileicon,game.type,game.subject,sdk.filename,t.name as typename,s.name as subjectname')
				->where(['game.cooperation_status' => ['in','1,2'],'game.is_show' => 1])
				->where($where)
				->order('game.create_time desc')
				->paginate(10, false, array('query' => input('get.')))
				->each(function ($item, $key){
					$item['icon'] = "http://static.46yx.com/".$item['mobileicon'];
					$item['typename'] = getTypename($item['typename']);
					$item['subjectname'] = getSubjectname($item['subjectname']);
					$item['size'] = getSize($item);
					if($item['platform']==1){
						$item['download'] = '';
						$item['ios_download'] = getIosDownload($item);
					}
					else{
						$item['download'] = getDownload($item);
						$item['ios_download'] = '';
					}
					$item['url'] = getMobileGameUrl($item['id']);
					return $item;
			});
		if ($this->request->isAjax()) $this->jsonResult($list,1);

		$totalGameCnt = $gameModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->alias('game')
				->join('cy_gameinfo info', 'game.id = info.game_id','left')
				->join('cy_sdkgamelist sdk', 'game.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
				->field('game.id,game.nickname,game.power,info.androidurl,game.pinyin,info.mobileicon,game.type,game.subject,sdk.filename')
				->where(['game.cooperation_status' => ['in','1,2'],'game.is_show' => 1])
				->where($where)->count();

        //获取游戏分类列表
        $gameTypeList = Db::connect(config('database_slave'))->table('cy_gametype')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->select();
        //游戏题材表
        $gameSubjectList = Db::connect(config('database_slave'))->table('cy_gamesubject')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->select();
       
		//游戏分类一维数组
        $gameType = arrayColumnByObject($gameTypeList,'name','id');
        //游戏题材一维数组
        $gameSubject = arrayColumnByObject($gameSubjectList,'name','id');

        $this->assign('gameTypeList',$gameTypeList);
        $this->assign('gameSubjectList',$gameSubjectList);
     //   $this->assign('gameType',$gameType);
     //   $this->assign('gameSubject',$gameSubject);
        $this->assign('list',$list);
        $this->assign('totalGameCnt',$totalGameCnt);

		$title = '手机游戏_麻花网络';
		if(intval(input('type')) || intval(input('subject'))){
			if(intval(input('type'))){
				$title = $gameType[intval(input('type'))].$title;
			}
			else if(intval(input('subject'))){
				$title = $gameSubject[intval(input('subject'))].$title;
			}
		}
        $seo['title'] = $title;
        $seo['keywords'] = '找手机游戏,手机游戏大全,手机游戏排行榜,好玩的手机游戏';
        $seo['description'] = '麻花网络找游戏频道免费为玩家提供最新最好玩的手机游戏下载,同时也提供最热门的手机游戏排行榜和各种手机游戏分类筛选服务。';
        $this->assign('seo',$seo);
		
        $this->assign('showType',$showType);
        return $this->fetch();
    }

    public function detail() {
		$gameModel = new GameModel;
        $gameid = intval(input('id'));
		$type = trim(input('type'));

		if(in_array($type,['index','news','gift'])){
			if($type=='index'){
				return $this->info();
			}
			else if($type=='news'){
				return $this->news();
			}
			else{
				return $this->gift();
			}
		}
		else{
			return $this->info();
		}
    }

    public function info() {
		$gameModel = new GameModel;
        $gameid = intval(input('id'));
        if(!$gameid) {
		//	throw new Exception("游戏不存在");
            $this->_abort404();
        }

        if ($this->request->isAjax()) {
            $serverType = input('serverType', 0, 'intval');
			$kcServers = $this->getGameServer($gameid,$serverType);
            $kcServers = $kcServers['list'];
			$this->jsonResult($kcServers,1);
        }

		$this->gameDetail('info');
		$kcServers = $this->getGameServer($gameid,1);
	//	var_dump($kcServers);
		$kcServerCnt = $kcServers['list']->total();
	//	echo $kcServerCnt."---kcServerCnt----<br>";

		$kfServers = $this->getGameServer($gameid,0);
	//	var_dump($kfServers);
		$kfServerCnt = $kfServers['list']->total();
	//	echo $kfServerCnt."----kfServerCnt-----<Br>";

    	$this->assign('kcServers',$kcServers['list']);
        $this->assign('kcServerCnt',$kcServerCnt);
    	$this->assign('kfServers',$kfServers['list']);
        $this->assign('kfServerCnt',$kfServerCnt);

        return $this->fetch('info');
    }

    private function news() {
		$gameModel = new GameModel;
        $gameid = intval(input('id'));
        if(!$gameid) {
            $this->_abort404();
        }
		$newsModel = new NewsModel;

		$list = $newsModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->alias('a')
				->join('cy_content b', 'a.id = b.id','left')
				->field('a.id,a.title,a.image,a.jlink,a.create_time,a.total,a.zhiding,a.type,a.gameid,b.content')
				->where(['a.gameid'=>$gameid,'a.isdelete' => 0,'a.type'=>['in','1,2,3,4,5']])
				->order('a.create_time DESC')
				->paginate(10, false, array('query' => input('get.')))
				->each(function ($item, $key){
					$item['image'] = getNewsImage($item['image']);
					$item['url'] = getMobileNewsUrl($item['id'],$item['jlink']);
					$item['createdate'] = date('Y-m-d',$item['create_time']);
					$item['createtime'] = date('Y-m-d H:i:s',$item['create_time']);
					$item['cutcontent'] = getCutcontent($item['content']);
					return $item;
				});
	//	var_dump($list);
		if ($this->request->isAjax()) $this->jsonResult($list,1);

		$this->gameDetail('news');
		$totalNewsCnt = $newsModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->alias('a')
				->join('cy_content b', 'a.id = b.id','left')
				->field('a.id,a.title,a.image,a.jlink,a.create_time,a.total,a.zhiding,a.type,a.gameid,b.content')
				->where(['a.gameid'=>$gameid,'a.isdelete' => 0,'a.type'=>['in','1,2,3,4,5']])
				->count();
	//	var_dump($totalNewsCnt);
	//	echo $totalNewsCnt."----totalNewsCnt------<br>";
		$this->assign('list',$list);
		$this->assign('totalNewsCnt',$totalNewsCnt);

	//	$this->assign('page', $list->render());
        return $this->fetch('info-news');
    }
    private function gift() {
		$gameModel = new GameModel;
        $gameid = intval(input('id'));
        if(!$gameid) {
		//	throw new Exception("游戏不存在");
            $this->_abort404();
        }
        $where = [
            'a.gameid'    =>  $gameid,
            'a.isdelete'  =>  0,
            'a.endtime'  =>  ['gt',NOW_TIMESTAMP],
            'b.is_show' => 1,
            'b.cooperation_status'  => ['in',[0,1,2]]
        ];
        $list = Db::connect(config('database_slave'))->table('cy_libaoinfo')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->alias('a')
            ->join('cy_game b','a.gameid = b.id')
            ->join('cy_gameinfo info', 'b.id=info.game_id', 'left')
            ->field('a.id,a.gameid,a.title,a.content,a.total,a.used,a.starttime,a.endtime,info.mobileicon')
            ->where($where)
            ->where('a.total > a.used')
            ->order('a.create_time DESC')
			->paginate(10, false, array('query' => input('get.')))
            ->each(function ($item, $key){
                $used = $item['used'];
                $total    = $item['total'];
                $item['percent'] = '100%';
                if ( $used >= $total ) {
                    $item['percent'] = '0%';
                } else if ( $used && $total ) {
                    $item['percent'] =  (bcsub( 1, bcdiv($used, $total, 2), 2 ) * 100) . '%';
                }
                $item['mobileicon'] = "http://static.46yx.com/".$item['mobileicon'];
                $item['starttime'] = date('Y-m-d H:i',$item['starttime']);
                $item['endtime'] = date('Y-m-d H:i',$item['endtime']);
                return $item;
            });
		if ($this->request->isAjax()) $this->jsonResult($list,1);

		$this->gameDetail('gift');
        $totalGiftCnt = Db::connect(config('database_slave'))->table('cy_libaoinfo')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->alias('a')
            ->join('cy_game b','a.gameid = b.id')
            ->join('cy_gameinfo info', 'b.id=info.game_id', 'left')
            ->where($where)
            ->where('a.total > a.used')
            ->count();

		$this->assign('list',$list);
		$this->assign('totalGiftCnt',$totalGiftCnt);
	//	var_dump($list);
	//	echo $totalGiftCnt."----totalGiftCnt-----<br>";

	//	$this->assign('page', $list->render());
        return $this->fetch('info-gift');
    }

    private function gameDetail($p_type='info') {
		$gameModel = new GameModel;
        $gameid = intval(input('id'));
        if(!$gameid) {
		//	throw new Exception("游戏不存在");
            $this->_abort404();
        }
        $detail = $gameModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
                ->alias('game')
                ->join('cy_gameinfo info', 'game.id = info.game_id','left')
				->join('cy_gametype t', 'game.type = t.id','left')
				->join('cy_gamesubject s', 'game.subject = s.id','left')
				->join('cy_sdkgamelist sdk', 'game.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
                ->field('game.id,game.nickname,game.power,info.androidurl,info.platform,game.pinyin,info.mobileicon,game.type,game.subject,game.publicity,info.developer,info.bigimage,t.name as typename,s.name as subjectname,info.description,sdk.filename')
                ->where(['game.id'=>$gameid,'game.cooperation_status' => ['in','0,1,2'],'game.is_show' => 1])
                ->find();
        if ( empty($detail)) {
		//	throw new Exception("游戏不存在或无效");
            $this->_abort404();
        }
		else{
			$detail['icon'] = "http://static.46yx.com/".$detail['mobileicon'];
			$detail['size'] = getSize($detail);
			$detail['version'] = getVersion($detail);
			$detail['updatetime'] = getUpdateTime($detail);
			if($detail['platform']==1){
				$detail['download'] = '';
				$detail['ios_download'] = getIosDownload($detail);
			}
			else{
				$detail['download'] = getDownload($detail);
				$detail['ios_download'] = '';
			}
			$detail['screenShot'] = getScreenShot($detail['bigimage']);
			$detail['typename'] = getTypename($detail['typename']);
			$detail['subjectname'] = getSubjectname($detail['subjectname']);
			$detail['developer'] = getDeveloper($detail['developer']);
		}
	//	var_dump($detail);

		if($p_type=='info'){
			//猜你喜欢游戏列表(读取和当前详情页的游戏相同类型 或者 题材的游戏,不重复显示同一款游戏而且不包括当前详情页的这款游戏)
			$relGameList = array();
			$whereSql = '';
			if(intval($detail['type']) || intval($detail['subject'])){
				if(intval($detail['type']) && intval($detail['subject'])){
					$whereSql = '(game.type='.intval($detail['type']).') or (game.subject='.intval($detail['subject']).')';
				}
				else if(intval($detail['type'])){
					$whereSql = '(game.type='.intval($detail['type']).')';
				}
				else if(intval($detail['subject'])){
					$whereSql = '(game.subject='.intval($detail['subject']).')';
				}
			}
			if($whereSql){
				$relGameList = $gameModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
						->alias('game')
						->join('cy_gameinfo info', 'game.id = info.game_id')
						->join('cy_gametype t', 'game.type = t.id','left')
						->join('cy_gamesubject s', 'game.subject = s.id','left')
						->join('cy_sdkgamelist sdk', 'game.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
						->field('game.id,game.nickname,game.power,game.publicity,info.androidurl,info.platform,game.pinyin,info.mobileicon,game.type,game.subject,sdk.filename,t.name as typename,s.name as subjectname')
						->where(['game.cooperation_status' => ['in','1,2'],'game.is_show' => 1,'game.id' => ['neq',$gameid]])
						->where($whereSql)
						->orderRaw('rand()')
						->limit(8)
						->select();
				foreach ( $relGameList as &$game ) {
					$game['icon'] = "http://static.46yx.com/".$game['mobileicon'];
					$game['size'] = getSize($game);
					$game['typename'] = getTypename($game['typename']);
					$game['subjectname'] = getSubjectname($game['subjectname']);
					$game['url'] = getGameUrl($game['id'],2);
					if($game['platform']==1){
						$game['download'] = '';
						$game['ios_download'] = getIosDownload($game);
					}
					else{
						$game['download'] = getDownload($game);
						$game['ios_download'] = '';
					}
				}
			}
			$title = $detail['nickname'].'下载_'.$detail['nickname'].'开服开测'.'_麻花网络';
			$this->assign('relGameList',$relGameList);
		}
		else if($p_type=='news'){
			$title = $detail['nickname'].'资讯攻略_麻花网络';
		}
		else if($p_type=='gift'){
			$title = $detail['nickname'].'礼包_麻花网络';
		}
		else{
			$title = $detail['nickname'].'下载_'.$detail['nickname'].'开服开测'.'_麻花网络';
		}
		
        $seo['title'] = $title;
        $seo['keywords'] = $detail['nickname'].','.$detail['nickname'].'下载,'.$detail['nickname'].'安卓版,'.$detail['nickname'].'攻略,'.$detail['nickname'].'资讯,'.$detail['nickname'].'礼包,'.$detail['nickname'].'开服,'.$detail['nickname'].'开测';
        $seo['description'] = '麻花网络'.$detail['nickname'].'手游专区，为您免费提供'.$detail['nickname'].'安卓版游戏下载，最实用的'.$detail['nickname'].'手游攻略资讯，还有'.$detail['nickname'].'礼包激活码等你拿，欢迎来麻花网络下载'.$detail['nickname'].'。';
        $this->assign('seo',$seo);

        $this->assign('detail',$detail);
    }

    /**
     * 开服、开测--列表
     */
    private function getGameServer($game_id,$type) {
        /*$type = $this->request->param('t',1,'intval');*/
        if (!in_array($type,[0,1])) $type = 0;
		$game_id = intval($game_id);

        $where = [
			'gameid'=>intval($game_id),
            'type'=>$type,
            'isdelete'=>0,
        ];
        $nowtime = NOW_TIMESTAMP;

		$list = Db::connect(config('database_slave'))->table('cy_serverinfo')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->field('id,gameid,sername,sertime,serstatus,type,sercertype')
				->where($where)
				->where("( ( sertime > {$nowtime} and sercertype = 1 ) or sercertype = 2 )")
				->order('sercertype asc,sertime asc')
				->paginate(5, false)
				->each(function ($item, $key){
						$td = date("Y-m-d");
						$tm = date("Y-m-d",strtotime("+1 day"));
						$item['subscribe'] = 1;    // 预约游戏
						$item['serverName'] = '';    
						if ($item['sercertype'] == 1){
							if(date("Y-m-d",$item['sertime']) == $td){
								$item['serverDate'] = '今日';
								$item['serverTime'] = date('H:i',$item['sertime']);
							}
							else if(date("Y-m-d",$item['sertime']) == $tm){
								$item['serverDate'] = '明日';
								$item['serverTime'] = date('H:i',$item['sertime']);
							}
							else{
								$item['serverDate'] = date('m',$item['sertime']).'-'.date('d',$item['sertime']);
								$item['serverTime'] = date('H:i',$item['sertime']);
							}
							if( $item['sertime'] <= NOW_TIMESTAMP + 60*60){
								$item['subscribe'] = 0;
							}
						}
						else{
							$item['serverDate'] = '';
							$item['serverTime'] = '长期有效';
							$item['subscribe'] = 0;
						}
						if($item['type'] == 1){
							$item['serverName'] = $this->_serstatus[$item['serstatus']];
						}
						else{
							$item['serverName'] = $item['sername'];
						}
						return $item;
			});

		$TempDate = '';
		foreach ( $list as &$item ) {
			if($item['serverDate'] == $TempDate){
				$item['serverDate'] = '';
			}
			else{
				$TempDate = $item['serverDate'];
			}
		}
		return ['list'=>$list];
    }

	public function gameList(){
		$condition = array();
        $condition['g.cooperation_status']  = ['in','1,2'];
        $condition['g.is_show']             = 1;
       
		$type = trim(input('type'));
		if(in_array($type,['hot','new','recommend'])){
			if($type=='hot'){
				$condition['g.lanmu'] = 2;
			}
			else if($type=='new'){
				$condition['g.lanmu'] = 3;
			}
			else{
				$condition['g.lanmu'] = 4;
			}
		}
		else{
			$condition['g.lanmu'] = 2;
		}
        $gameList = Db::connect(config('database_slave'))
				->table('cy_game g')
				->join('cy_gameinfo info', 'g.id = info.game_id')
				->join('cy_gametype t', 'g.type = t.id','left')
				->join('cy_gamesubject s', 'g.subject = s.id','left')
				->join('cy_sdkgamelist sdk', 'g.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
				->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->field('g.id,g.nickname,g.power,info.androidurl,info.platform,g.pinyin,info.mobileicon,g.type,g.subject,g.publicity,t.name as typename,s.name as subjectname,sdk.filename')
                ->where($condition)
                ->order('g.xulie DESC,g.create_time desc')
                ->paginate(10, false, array('query' => input('get.')))
				->each(function ($item, $key){
					$item['mobileicon'] = "http://static.46yx.com/".$item['mobileicon'];
					$item['url'] = getMobileGameUrl($item['id']);
					if($item['platform']==1){
						$item['download'] = '';
						$item['ios_download'] = getIosDownload($item);
					}
					else{
						$item['download'] = getDownload($item);
						$item['ios_download'] = '';
					}
					$item['size'] = getSize($item);
					$item['typename'] = getTypename($item['typename']);
					$item['subjectname'] = getSubjectname($item['subjectname']);
					return $item;
				});
	//	var_dump($gameList);
		if ($this->request->isAjax()) $this->jsonResult($gameList,1);

		$type = trim(input('type'));
		if($type=='new'){
			$seo['title'] = '最新游戏_最新手机游戏排行_麻花网络';
			$seo['keywords'] = '最新手机游戏,最新手机游戏排行榜,最新手机游戏下载';
			$seo['description'] = '麻花网络热门手游推荐，为玩家提供当前最新的手机游戏排行，还有最新手机游戏免费下载。';
		}
		else if($type=='recommend'){
			$seo['title'] = '本周推荐_本周手机游戏排行_麻花网络';
			$seo['keywords'] = '本周热门手游,本周推荐手游,本周好玩的手机游戏下载';
			$seo['description'] = '麻花网络本周手游推荐，为玩家提供本周推荐的手机游戏排行，还有本周推荐的手机游戏免费下载。';
		}
		else{
			$seo['title'] = '热门游戏_热门手机游戏排行_麻花网络';
			$seo['keywords'] = '热门手机游戏,热门手机游戏排行榜,热门手机游戏下载';
			$seo['description'] = '麻花网络热门手游推荐，为玩家提供当前最热门的手机游戏排行，还有最热门手机游戏免费下载。';
		}
        $this->assign('seo',$seo);

        $this->assign('game_list', $gameList);
        return $this->fetch();
	}

    // 搜索游戏关键字
    public function ajaxGameList()
    {
        $keyword = input('keyword');

        $list = (new GameModel())->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->alias('game')
                ->join('cy_gameinfo info', 'game.id = info.game_id')
                ->join('cy_sdkgamelist sdk', 'game.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
                ->where(['game.cooperation_status' => ['in','1,2'],'game.is_show' => 1])
                ->where('game.nickname|game.pinyin','like','%'. $keyword .'%')
                ->field('LENGTH(game.nickname) as tLen,LOCATE("'.$keyword.'",game.nickname) as tIndex,LOCATE("'.$keyword.'",game.pinyin) as cIndex,game.id,game.nickname')
                ->order('tIndex,cIndex,tLen,game.create_time desc')
                ->limit(10)
                ->select();

        foreach ($list as $key => &$value) {
        	$value['url'] = getMobileGameUrl($value['id']);
        	$value['nickname'] = preg_replace("/($keyword)/i","<b style=\"color:#51a7ff\">\\1</b>",$value['nickname']);
        }

        return $list;    

    }
}
