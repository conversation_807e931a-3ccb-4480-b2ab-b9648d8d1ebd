<?php
/**
 * 前端调试
 */
namespace app\mobile\controller;

use think\Db;
use think\Config;

class Web extends Mobile
{
    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    public function index()
    {
        $p = $this->request->param('p');
        return $this->fetch();
    }
	
    public function contact_account()
    {
        $p = $this->request->param('p');
        return $this->fetch();
    }
	
	public function agreement()
	   {
	       $p = $this->request->param('p');
	       return $this->fetch();
	   }
	   
    public function contact_way()
	   {
	       $p = $this->request->param('p');
	       return $this->fetch();
	   }
	   
	public function contact()
	   {
	       $p = $this->request->param('p');
	       return $this->fetch();
	   }
	   
	   public function forget_pwd_to_reset()
	      {
	          $p = $this->request->param('p');
	          return $this->fetch();
	      }
		  
		public function service()
		   {
		       $p = $this->request->param('p');
		       return $this->fetch();
		   }
		   
   public function personal_center()
      {
          $p = $this->request->param('p');
          return $this->fetch();
      }
	  
	  public function   personal_info()
	     {
	         $p = $this->request->param('p');
	         return $this->fetch();
	     }
		 
	  public function  qqnum()
	     {
	         $p = $this->request->param('p');
	         return $this->fetch();
	     }
		 
		 public function  postcode()
		    {
		        $p = $this->request->param('p');
		        return $this->fetch();
		    }
			
		public function  address()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function  personal_pay()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function  member_gift()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
			public function  personal_account()
				{
				    $p = $this->request->param('p');
				    return $this->fetch();
				}
		
		public function  personal_change_pwd()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
	
	    public function  bind_page()
	    	{
	    	    $p = $this->request->param('p');
	    	    return $this->fetch();
	    	}
		
		public function  identity()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  identity_info()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function  csc()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  question()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  appeal()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  check()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  appeal_account()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  appeal_history()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  appeal_ending()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  appeal_result()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function  appeal_result_page()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  wechat_appeal()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  a404()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  a500()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  about_us()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function  subscription()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function  recommend_games()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function game_category()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function game_info()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function info_gift()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function info_news()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function news()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}	
		
		public function news_details()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function search()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function search_result()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function search_result_new()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function personal_nickname()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function mygame()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
		public function feedback()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
		
		public function pay_detail()
			{
			    $p = $this->request->param('p');
			    return $this->fetch();
			}
			
			
					
}
