
<!DOCTYPE html>
<html lang="zh-cn">

<head>
	<meta charset="UTF-8">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
	<title></title>
	<meta itemprop="name" content=""/>
	<meta name="description" itemprop="description" content=""/>
<!--	<script src="__OBS_URL__/h/swiper-3.3.1.min.js"></script>-->
	<script type="text/javascript" src="__STATIC__/js/jquery_v3.3.1.js"></script>
	<link rel="stylesheet" type="text/css" href="__OBS_URL__/h/swiper-3.3.1.min.css">
	<link rel="stylesheet" type="text/css" href="__OBS_URL__/h/style_lsm.css">

<!--		<script src="__OBS_URL__/h/zepto.min.js"></script>-->
<!--		<script src="__OBS_URL__/h/swiper-3.3.1.min.js"></script>-->


	<!--	<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>-->
	<!--	<script type="text/javascript" src="__STATIC__/js/jquery_v3.3.1.js"></script>-->
	<!--	<script type="text/javascript" src="__STATIC__/js/api/scroll_through.js"></script>-->
	<!--	<script type="text/javascript" src="__STATIC__/lib/OwlCarousel2-2.3.4/owl.carousel.min.js"></script>-->
	<!--	<script type="text/javascript" src="__STATIC__/js/swiper-bundle.min.js"></script>-->
	<style>

	</style>
	<script>!function(vWidth){
		function Adp(){
			var w=document.documentElement.clientWidth;
			if(w>vWidth){w=vWidth}document.documentElement.style.fontSize=w/vWidth*100+"px"
		}
		var timer=null;window.addEventListener("onorientationchange" in window?"orientationchange":"resize",function(){clearTimeout(timer);timer=setTimeout(Adp,300)},false);
		window.addEventListener("pageshow",function(window){window.persisted&&(clearTimeout(timer),timer=setTimeout(Adp,300))},false);
		document.addEventListener("DOMContentLoaded", function(){clearTimeout(timer);timer=setTimeout(Adp,300)}, false)}(750);
	</script>
</head>

<body>
<!--<a class="btn-dl2 btn-bxfr"  href="javascript:void(0)"><img src="__OBS_URL__/h/3214e2354r.png" alt=""></a>-->
<div class="bg1" id="bg1">
	<a class="btn-bxfr" href="javascript:void(0)" >
		<img class="commentimg" src="__OBS_URL__/h/hsdj.png" alt="">
	</a>
</div>
<!-- 蒙板 -->
<div class="mask">
	<div class="m-cont">
		<img class="jiantou" src="__OBS_URL__/h/jiantou.png">
		<p class="p">
			<span class="num-icon">1</span>
			点击右上角的
			<img class="icon" src="__OBS_URL__/h/icon1.png">
			按钮
		</p>
		<p>
			<span class="num-icon">2</span>
			选择在浏览器
			<img class="icon" src="__OBS_URL__/h/icon2.png">
			中打开即可下载
		</p>
	</div>
</div>
<style>

	.btn-dl2 img{
		width: 100%;
	}


	.mask{
		background: rgba(0,0,0,0.85);
		position: absolute;
		top: 0;
		color: #fff;
		font-size: 22px;
		z-index: 9999;
		display: none;
		overflow-y: hidden;
	}
	.mask .m-cont{
		padding: 30% 5% 30%;
	}
	.mask .num-icon{
		display: inline-block;
		width: 28px;
		height: 28px;
		background: #ffae46;
		border-radius: 14px;
		font-size: 22px;
		font-weight: 700;
		text-align: center;
		line-height: 28px;
	}
	.p{
		padding-bottom: 5%;
	}
	.mask .icon{
		display: inline-block;
		vertical-align: middle;
		width: 14%;
	}
	.jiantou{
		width: 15%;
		position: absolute;
		top: 10px;
		right: 25px;
	}
</style>
<!-- 蒙板结束 -->



<script type="text/javascript">
	//微信打开需要到浏览器下载
	var ua = window.navigator.userAgent.toLowerCase();
	if(ua.match(/MicroMessenger/i) == 'micromessenger'){
		$(".mask").css("display", "block");
	}




	//安卓游戏下载地址
	var android_down_url = 'http://api-game.meizu.com/games/public/download/fixed/link?source=1&packageName=com.hnzh.dldlhsdj.mz&channelNo=801003&sign=2c17cab76e09edc78d245c8db07ee66f&auth=f23714fc4ebc224de08325986c79d99f';

	//IOS游戏下载地址
	var ios_down_url = 'undefined';

	$(".btn-bxfr").on("click", function () {


		if (which_device() == "Android") { //如果设备是安卓则直接下载
			if (android_down_url.length !== 0 && android_down_url !== "undefined") {

				$(this).attr("href", android_down_url);
				var newUrl=android_down_url.replace('http','https')
				window.open(newUrl,'_self');
				// window.location.href=android_down_url;
			} else {
				alert("暂无安卓版本")
			}
		} else if (which_device() == "iOS") { //如果设备是iOS

			if (ios_down_url.length !== 0 && ios_down_url !== "undefined") {
				$(this).attr("href", 'itms-services://?action=download-manifest&url=' + ios_down_url);

			} else {
				alert("暂无IOS版本")
			}

		} else {
			//除了这两种设备外的，如PC端，还是直接下载安卓的
			if (android_down_url.length !== 0 && android_down_url !== "undefined") {
				// console.log(android_down_url);
				$(this).attr("href", android_down_url);
				var newUrl=android_down_url.replace('http','https')
				window.open(newUrl,'_self');
			} else {
				alert("暂无安卓版本")
			}
		}
		if (navigator.userAgent.match(/(iPhone|iPod|iPad|Android|ios)/i)) {

		} else {
		}
	})
	//判断是安卓还是iOS
	function which_device() {
		var ua = navigator.userAgent.toLowerCase();
		if (/android|adr/gi.test(ua)) {
			// 安卓
			return 'Android';
		} else if (/\(i[^;]+;( U;)? CPU.+Mac OS X/gi.test(ua)) {
			//苹果
			return 'iOS';
		} else if (/(Mac OS X)/i.test(ua)) {
			//苹果
			return 'iOS';
		} else if (/iPad/gi.test(ua)) {
			//ipad
			return 'iOS';
		}
	}
</script>
</body>

</html>
