<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1,minimal-ui">
<meta http-equiv="X-UA-Compatible" content="IE=Edge">
<title>充值</title>

</head>

<body>

<style>
    *{font-family: 'Droid Sans Fallback', '苹方', 'Microsoft Yahei', '微软雅黑', Sim<PERSON><PERSON>,"黑体";}
html{    background: #E2E2E2; height:100%;}
body {letter-spacing: 0.05em; padding-top:1px; background:#fff; min-height:100%; font-family: -apple-system, BlinkMacSystemFont, "PingFang SC","Helvetica Neue",STHeiti,"Microsoft Yahei",Tahoma,Simsun,sans-serif;  position:relative;  max-width:650px;min-width:320px; margin:0 auto; font-family: arial,"微软雅黑",<PERSON><PERSON><PERSON><PERSON>,"Microsoft Yahei",tahoma,'Hiragino Sans GB',sans-serif;}
a{ text-decoration:none;}
.pay_suc{}
.pay_suc{ text-align:center; width:80%; padding:40px 0px; margin:0 auto;}
.pay_suc img{ width:30%; max-width:150px;}
.pay_suc span{ font-size:18px; color:#333; width:90%; display:block; margin: 20px auto; line-height:25px;}
.pay_suc h3{ font-size:20px; color:#333; font-weight:bold; height:50px;     margin-top:30px; border-bottom:1px dashed #e7e7e7; color:#0bc8a6;}
.pay_suc p{ color: #999;
    height: 30px;
    line-height: 30px;
    text-align: center;
    width: 80%;
	max-width:210px!important;
    font-size: 0.8em;
    display: block;
    margin: 0 auto;}

.pay_suc a{width: 80%;
    max-width:300px;
    font-size: 20px;
    max-width: 300px;
    border: 1px solid #959595;
    color: #959595; 
    border-radius: 15px;
    height: 45px;
    line-height: 45px;
    display: block;
    margin: 0 auto; margin-top:40px;}
	.pay_suc .success{
		    background: #83E48D;
		    border: 1px solid #83E48D;
		    color: #fff ;
			background: #83E48D;
			border: 1px solid #83E48D;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
	}
.pay_suc a:hover{ background:#999; color:#fff;}

</style>
<div class="news_area" style="padding:0px;">
<input type="hidden" name="orderid" class="orderid" value="{:input('orderid')}">
<!--未绑定-->
<div class="pay_suc">
{if condition="$showResult eq 1"}
	{if condition="$paystatus eq 1"}
		<img src="__STATIC__/images/my_suc.png">
		<h3>支付成功</h3>
	<script src="__STATIC__/js/jquery-v1.11.3.min.js"></script>
		<script type="text/javascript">
			var orderid=$('.orderid').val();
			var u = navigator.userAgent;
			var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
			if(isAndroid){
				window.mengchuang.getPayResult('succeed');
			}else{
			//	  window.location.href = "SDKTest://1&back";

					
				  window.location.href = "www.46yx.com://showWapPayResult_btnType_?1&back";
			}
			setTimeout(function () {
				window.location.href = "http://www.46yx.com";
			},2000)
	</script>


	{else/}




	<img src="__STATIC__/images/my_fail.png">
	<h3>支付失败{$paystatus}</h3>

	<script src="__STATIC__/js/jquery-v1.11.3.min.js"></script>
	<script type="text/javascript">
	var orderid=$('.orderid').val();
	var u = navigator.userAgent;
	var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
	if(isAndroid){
		window.mengchuang.getPayResult('fail');
	}else{

		//	  window.location.href = "SDKTest://0&back";
		 window. location.href = "www.46yx.com://showWapPayResult_btnType_?0&back";
	}
	setTimeout(function () {
		window.location.href = "http://www.46yx.com";
	},2000)

	</script>

	{/if}

{else/}
	<a class="success" href="<?php echo 'http://'.$_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];?>&act=success">已完成支付<img src="__STATIC__/images/success.png" style="width: 45px;"></a>  
	&nbsp;&nbsp;&nbsp;
	<a href="<?php echo 'http://'.$_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];?>&act=success">取消支付</a>  
{/if}
<!-- <p>商品名称: 赤月传说</p> -->
<!-- <p>商品金额: 8887845</p> -->
<!-- <a href="mengchuang://showWapPayResult_btnType_?1&back">返回</a> -->
</div>
<!--未绑定结束-->

</div>

</body>
</html>

