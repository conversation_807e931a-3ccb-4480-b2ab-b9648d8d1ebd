{extend name="v1/common/layout" /}
{block name="header"}
<title>换绑邮箱</title>
<style>
    body {
        background: #ffffff;
    }

    .mui-input-group .mui-input-row {
        background: #F4F4F4 !important;
        margin-top: .4375rem;
        height: 2.5rem;
        position: relative;
    }

    /* 打叉图标 */

    .J_clear {
        position: absolute;
        top: .9375rem;
        width: .65rem;
        height: .65rem;
        left: 11.55rem;
    }


    .mui-input-group {
        padding: .8125rem;
    }


    input[type=text] {
        line-height: 1.3125rem;
        width: 100%;
        height: 2.5rem;
        /* margin-bottom: .9375rem; */
        padding: .625rem .9375rem;
        font-size: 0.8rem;
    }

    ::-webkit-input-placeholder {
        /* WebKit, Blink, Edge */
        font-size: 0.8rem;
    }

    :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        font-size: 0.8rem;
    }

    ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        font-size: 0.8rem;
    }

    :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        font-size: 0.8rem;
    }

    .btnWrap {
        text-align:center;
    	margin:0.625rem auto !important;
    	width: 100%;
    }

    .setMargin{
       	margin:0.625rem auto !important;
        text-align:center;
        width: 100%;
     }

	.setFixed{
		width: 100%;
	 	position: fixed;
	 	text-align:center;
        bottom: 1.25rem;
        left:0;
    }

    /* 确定按钮 */

    .mui-btn {
        color: white;
        width: 90%;
        background-color: #FF7E00;
        border: 0;
        padding: .625rem;
        font-size: .875rem;
    }

    .btn {
        color: #FF7E00;
        border: 1px solid #FF7E00;
        position: absolute;
        top: .29rem;
        right: .3625rem;
        background: #f4f4f4;
    }
    #img_code,
    #sms_code {
        width: 60%;
    }
    .yzm_img_wrap {
        width: 35%;
        height: 45px;
        float: right;

    }

    .yzm_img_wrap img {
        width: 100%;
        height:100%;


    }

    button {
        font-size: .875rem;
        font-weight: 400;
        line-height: 1.42;
        padding: .345rem .65rem;
        border-radius: .1875rem;
        border-top-left-radius: .1875rem;
        border-top-right-radius: .1875rem;
        border-bottom-right-radius: .1875rem;
        border-bottom-left-radius: .1875rem;
    }

    .size {
        padding: .3rem .24rem;
    }

    .mui-input-row .mui-input-clear~.mui-icon-clear,
    .mui-input-row .mui-input-password~.mui-icon-eye,
    .mui-input-row .mui-input-speech~.mui-icon-speech {
        font-size: 1.25rem;
        top: .625rem;
        right: 0;
        width: 2.375rem;
        height: 2.375rem;

    }

    p#showPhoneNum {
        text-align: center;
        margin: 10px 0;
    }

    .tA_center {
        text-align: center;
    }

</style>
{/block}

{block name="content"}
<body>
<div class="mui-content">
    <form class="mui-input-group">
        <p id="showPhoneNum">绑定邮箱:
            <span style="color:red">{$email}</span>
        </p>

        <div class="mui-input-row">
            <input type="text" placeholder="请输入图形验证" id="img_code" class="mui-input-clear">
            <div class="yzm_img_wrap">
                <img src="{:captcha_src('','api')}" alt="" id='captcha_src' onclick="javascript:this.src='{:captcha_src(\'\',\'api\')}?'+Math.random();">
            </div>
        </div>

        <div class="mui-input-row">
            <input type="text" placeholder="请输入验证码" id="sms_code" class="mui-input-clear">
            <button type="button" class="btn size" id="sendCodeAsk_btn">获取验证码</button>
        </div>

        <div class="tA_center">
            <p>点击图形验证码，刷新验证码</p>
            <p>请点击发送验证码按钮</p>
            <p>将收到的验证码填写到输入框中</p>
        </div>

        <div class="btnWrap">
            <button type="button" class="mui-btn" id="submit_btn">确定</button>
        </div>
    </form>

</div>
</body>

{/block}


{block name="footer"}
<script type="text/javascript">

    mui.init();

    $(document).ready(function () {
         if(document.body.clientHeight<400){
	      $("#submit_btn").parent().addClass("setMargin");
	  }else{

	      $("#submit_btn").parent().addClass("setFixed");
         }
      })
    $(function () {

        $(".mui-input-row").eq(0).children("input").focus(function () {
            $(".mui-input-row").eq(0).children("span").css({
                right: "6.2rem"
            })
        });

        $(".mui-input-row").eq(1).children("input").focus(function () {
            $(".mui-input-row").eq(1).children("span").css({
                right: "6.2rem"
            })
        });

        var email = "{$email}";
        //刷新验证码
        var captchaImg = $('#captcha_src'), captchaSrc = captchaImg.attr('src');
        // 发送验证码
        $("#sendCodeAsk_btn").bind("tap", function () {
            var img_code = $.trim($("#img_code").val());
            if (0 == img_code.length) {
                mui.toast("请输入图形验证码");
                return false;
            }
            $.ajax({
                type: "POST",
                url: "{:url('sendEmailCode')}",
                timeOut: 5000,
                dataType: "json",
                data: {"email": email, "img_code": img_code},
                success: function (res) {
                    if (res.code) {
                        countDown();
                    } else {
                        mui.toast(res.msg);
                        captchaImg.attr('src', captchaSrc + '?_t=' + Math.random());
                    }
                },
                error: function () {
                    mui.toast("网络错误，请重新打开该页面");
                }
            });
        });

        //绑定手机
        $("#submit_btn").bind("tap", function () {
            //var email = $.trim($("#email").val());
            var sms_code = $.trim($("#sms_code").val());
            if (0 == email.length) {
                mui.toast("请填写邮箱");
                return false;
            }
            if (0 == sms_code.length) {
                mui.toast("请填写邮箱验证码");
                return false;
            }
            $.ajax({
                type: "post",
                url: "{:url('velidateCode')}",
                async: false,
                data: {"email": email, "sms_code": sms_code},
                dataType: "json",
                timeOut: 5000,
                success: function (res) {
                    if (!res.code) {
                        mui.toast(res.msg);
                    }
                    if (res.code) {
						window.location.href = res.data.url;
                    }
                },
                error: function () {
                    mui.toast("网络错误，请重新打开页面");
                }
            });
        });
    });

    function countDown() {
        $("#sendCodeAsk_btn").attr("disabled", "disabled");
        var i = 60;
        count_down = setInterval(function () {
            $('#sendCodeAsk_btn').html("剩余" + i + "秒");
            i--;
            if (i < 0) {
                clearInterval(count_down);
                $("#sendCodeAsk_btn").removeAttr("disabled");
                $("#sendCodeAsk_btn").html("发送验证码");
            }
        }, 1000);
    }
</script>
{/block}