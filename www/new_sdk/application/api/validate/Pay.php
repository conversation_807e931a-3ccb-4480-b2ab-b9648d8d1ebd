<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 小夏 < <EMAIL>>
// +----------------------------------------------------------------------
namespace app\api\validate;

use app\common\library\ValidateExtend;

class Pay extends ValidateExtend
{
    protected $rule = [
        'gameid'        => 'require|positiveInteger',
        'userid'        => 'require|integer',
        'appid'         => 'require|integer',
        'serverid'      => 'require|max:50',
        'servername'    => 'require|max:255',
//        'amount'        => 'require|integer|gt:0',
//         'amount'	=> 'require|float|gt:0',
        'amount'	=> 'require|float|gt:0',
        'roleid'        => 'require',
        'attach'        => 'require',
        'mc_id'         => 'integer',
        'channel_id'    => 'require|integer',
        'productname'   => 'require',
        'paytype'       => 'require',
        'imeil'         => 'max:40',
        'coupon_member_id' => 'integer'
    ];
    protected $message = [
        'gameid.require'        => '游戏id不能为空',
        'gameid.positiveInteger'=> '游戏id必须为正整型',
        'userid.require'        => '用户ID不能为空',
        'userid.integer'        => '用户ID必须为整型',
        'appid.require'         => 'APPID不能为空',
        'appid.integer'         => 'APPID必须为整型',
        'serverid.require'      => '游戏服务器id不能为空',
        'serverid.max'          => '游戏服务器id不能超过50个字符',
        'servername.require'    => '游戏区服名称不能为空',
        'servername.max'        => '游戏区服名称不能超过255个字符',
        'amount.require'        => '充值金额不能为空',
       // 'amount.integer'        => '充值金额必须为正整型',
        'amount.float'             => '充值金额有误',
        'roleid.require'        => '角色id不能为空',
        'attach.require'        => '游戏合作方的订单参数不能为空',
        'mc_id.integer'         => '平台币劵ID必须为正整型',
        'channel_id.require'    => '渠道ID不能为空',
        'channel_id.integer'    => '渠道ID必须为整型',
        'productname.require'   => '消费的商品名称不能为空',
        'paytype.require'       => '支付类型不能为空',
        'imeil.max'             => '手机imei码不能超过40个字符',
        'coupon_member_id.integer' => '代金券id必须是整数'
    ];

    protected $scene = [
        'index'=> ['gameid', 'userid', 'appid', 'serverid','amount','roleid','attach','channel_id','productname','imeil','mc_id'],
        'add'  => ['gameid', 'userid', 'appid', 'serverid','servername','amount','roleid','attach','channel_id','productname','paytype','imeil','mc_id'],
    ];
}
