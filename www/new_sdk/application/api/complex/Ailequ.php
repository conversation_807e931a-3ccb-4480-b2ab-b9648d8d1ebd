<?php

namespace app\api\complex;
class Ailequ implements ComplexInterface
{
    private $channel = 'ailequ';
    private $check_url = "http://sdk.user.i.267zf.com/game/verify/login";


    public function checkLogin($data, $polyChannelGame = [])
    {

        if (!$paramArr = json_decode($polyChannelGame['param'], true)) {
            return false;
        }

        $param = [
            'appId' => $paramArr['appid'],
            'token' => $data['channelToken'],
        ];
        $param['sign'] = md5($param['appId'] . $paramArr['serverkey'] . $param['token']);
        $result = curl($this->check_url, $param);
        // log_message($this->check_url . '?' . http_build_query($param) . '  ' . $result, 'log', LOG_PATH . 'complexLoginlog/' . $this->channel . '/');
        $res = json_decode($result, true);
        if ($res['status'] == 1) {
            return true;
        } else {
            return false;
        }
    }

    public function paySign($data, $polyChannelGame = [])
    {
        if (!$paramArr = json_decode($polyChannelGame['param'], true)) {
            return false;
        }
        $param = [
            'appId' => $data['appId'],
            'userId' => $data['userId'],
            'orderNum' => $data['orderNum'],
            'money' => $data['money'],
            'serverId' => $data['serverId'],
            'roleId' => $data['roleId'],
            'roleName' => $data['roleName'],
            'extInfo' => $data['extInfo'],
            'status' => $data['status'],
        ];

        $sign = md5($param['appId'] . $param['userId'] . $param['orderNum'] . $param['money'] . $param['serverId'] . $param['roleId'] . $param['roleName'] . $param['extInfo'] . $param['status'] . $paramArr['serverkey']);
        if ($data['sign'] == $sign && $param['status'] == 1) {
            return true;
        }

        return false;
    }

    public function getData($data)
    {
        return [
            'orderid' => $data['extInfo'],//我方聚合订单
            'amount' => $data['money'],  // 充值金额
            'sub_orderid' => $data['orderNum'],//渠道订单
        ];
    }

    public function getFail($msg = '')
    {
        echo 'FAILURE';
        exit();
    }

    public function getSuccess($msg = '')
    {
        echo 'SUCCESS';
        exit();
    }
}
