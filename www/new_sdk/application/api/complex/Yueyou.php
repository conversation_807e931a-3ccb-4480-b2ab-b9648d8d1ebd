<?php

namespace app\api\complex;

use ComplexThree\JuLe\joloRsa;

/**
 * 悦游
 */
class <PERSON><PERSON><PERSON> implements ComplexInterface
{
    private $channel = 'yueyou';

    /**
     * ## 登陆 - 校验
     *
     * @param $data
     * @param $polyChannelGame
     *
     * @return false
     */
    public function getSpecialParam($data, $polyChannelGame = [])
    {
        // channel_token = 客户端的 access_token
        if (empty($data['channel_token'])) {
            return false;
        }
        if (empty($polyChannelGame['param'])) {
            return false;
        }
        if (!$channelConfig = json_decode($polyChannelGame['param'], true)) {
            return false;
        }

        $param = [
            'appId' => $channelConfig['app_id'],
            'timestamp' => time(),
            'token' => $data['channel_token'],
        ];
        $param['sign'] = $this->sign($param, $channelConfig['server_key']);
        $res = get_http_response('http://rhsdk.yueeyou.com/api/cp/checkToken', $param);
        $res = json_decode($res, true);
        if ($res['code'] == 1) {
            return $res['userId'];
        } else {
            return false;
        }
    }

    // ## 登陆 - 登陆
    public function checkLogin($data, $polyChannelGame = [])
    {
        return true;
    }

    // ## 下单 - 客户端需要的参数(客户端自己做了，这块暂时不用)
    public function getOrder($data)
    {
        return ['code' => 200, 'msg' => 'success', 'param' => []];
    }

    // ## 回调 - 效验
    public function paySign($data, $polyChannelGame = [])
    {
        /**
         * 回调实例：http://cp.host.xxxx?amount=1.00&appId=1&channelId=1&cpOrderId=666666666666&ext=&orderId=123456789ooL&result=1&timestamp=1494221088&userId=12345&sign=f2b9cefc310a46e35c072f1d6da011ce
         */
        if (empty($polyChannelGame['param']) || !isset($data['amount']) || !isset($data['orderId']) || !isset($data['cpOrderId'])) {
            return false;
        }
        if (!$channelConfig = json_decode($polyChannelGame['param'], true)) {
            return false;
        }

        $param = [
            'result' => $data['result'], // 支付结果，固定值。“1”代表成功，“0”代表失败
            'amount' => $data['amount'], // 支付金额，单位：元，两位小数。
            'channelId' => $data['channelId'],
            'appId' => $data['appId'],
            'orderId' => $data['orderId'],
            'cpOrderId' => $data['cpOrderId'],
            'userId' => $data['userId'],
            'timestamp' => $data['timestamp'],
            'ext' => $data['ext'],
        ];
        $sign = $data['sign'];
        $hadleSign = $this->sign($param, $channelConfig['server_key']);
        if ($sign == $hadleSign) {
            return true;
        }
        return false;
    }

    // ## 回调 - 下单参数获取
    public function getData($data)
    {
        if(!isset($data['cpOrderId']) || !isset($data['amount']) || !isset($data['orderId'])){
            return false;
        }
        return [
            'orderid' => $data['cpOrderId'],         // 我方聚合订单
            'amount' => $data['amount'],             // 充值金额(单位：元)
            'sub_orderid' => $data['orderId'], // 渠道订单
        ];
    }

    // ## 回调 - 成功返回
    public function getSuccess($msg = '')
    {
        echo 'OK';
        exit();
    }

    // ## 回调 - 失败返回
    public function getFail($msg = '')
    {
        echo 'fail';
        exit();
    }


    // 签名
    private function sign($param, $serverKey)
    {
        ksort($param);
        $sign = '';
        foreach ($param as $key => $value) {
            if($sign){
                $sign .= '&'.$key . '=' . $value;
            }else{
                $sign = $key . '=' . $value;
            }
        }
        return md5($sign . $serverKey);
    }

}
