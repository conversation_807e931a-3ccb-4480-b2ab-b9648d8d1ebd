<?php

namespace app\api\complex;

use ComplexThree\JuLe\joloRsa;

/**
 * 司墨
 */
class Simo implements ComplexInterface
{
    private $channel = 'simo';

    /**
     * ## 登陆 - 校验
     *
     * @param $data
     * @param $polyChannelGame
     *
     * @return false
     */
    public function getSpecialParam($data, $polyChannelGame = [])
    {
        // 参数：user_id = uid, extparam = auth
        // {"appid":335,"sign":"01512cbbea69bc822ee0b6c9f4094faa","imeil":"ffffffff-bab6-5798-ffff-ffffef05ac4a","gameid":348,"channel_mark":"simo","device":"2","ext":"1","user_id":"2234424","extparam":"MjU5YzFiYjEwZmY0ZTQ2MUogZZmQj9yt719FCz3YG8doFOS_0vd9klL43PVEMa_Ml0pP_DGkSMMyFLtRLcm37995qqTHicusTujVGJgsrQeNq2-Ol7ZFjen1YtQBVthNiB8Evuu2dmo4DAHlEUitjYUOwvGGCiyGXGXZTFdKmyQxgp4DqferIFZ5Usfh-cwjzJnsW4U5FpAe9YildjUWXAWiXQMK8nYDKSU_dVC2FB2Ngnry_puqrHwBXmjBesENPj9AasvMH1ToX20qfkAr5akMvZX0KyBUOdln3I-EdIlTnAvIxO8tIth_fZceg6CPdPB5LjEJGfhY96g0DRz8DA","qmKey":"b743ca00f72bf6a55458e7451442b0e3"}
        if (empty($data['user_id']) || empty($data['extparam'])) {
            return false;
        }
        if (empty($polyChannelGame['param'])) {
            return false;
        }
        if (!$channelConfig = json_decode($polyChannelGame['param'], true)) {
            return false;
        }

        $param = [
            'uid' => $data['user_id'],
            'auth' => $data['extparam'],
            'tm' => time(),
        ];
        $param['sign'] = $this->signData($param, $channelConfig['app_key']);
        $res = get_http_response('https://api.63yx.cn/user/loginVerify', $param);
        $res = json_decode($res, true);
        if ($res['code'] === 0) {
            return $res['data']['user_id'];
        } else {
            return false;
        }
    }

    // ## 登陆 - 登陆
    public function checkLogin($data, $polyChannelGame = [])
    {
        return ['user_id' => $data['username']];
    }

    // ## 下单 - 客户端需要的参数(客户端自己做了，这块暂时不用)
    public function getOrder($data)
    {
        return ['code' => 200, 'msg' => 'success', 'param' => []];
    }

    // ## 回调 - 效验
    public function paySign($data, $polyChannelGame = [])
    {
        $param = [
            'tm' => $data['tm'],                                                                                                   // 请求时间戳
            'm_order_no' => $data['m_order_no'],                                                                                   // 007的平台订单号
            'cp_order_no' => $data['cp_order_no'],                                                                                 // 对接007的上游的游戏订单号
            'out_trade_no' => $data['out_trade_no'],                                                                               // 平台支付订单号（其他第三方的）
            'pay_status' => $data['pay_status'],                                                                                   // 支付结果 固定值1 支付成功 0 支付失败
            'pay_amount' => $data['pay_amount'],                                                                                   // 订单金额（元）
            'uid' => $data['uid'],                                                                                                 // 用户Id唯一标识
            'server_id' => $data['server_id'],                                                                                     // 区服ID
            'server_name' => $data['server_name'],                                                                                 // 区服名称
            'role_id' => $data['role_id'],                                                                                         // 角色ID
            'role_name' => $data['role_name'],                                                                                     // 角色名称
            'product_id' => $data['product_id'],                                                                                   // 道具ID
            'product_name' => $data['product_name'],                                                                               // 道具名称
        ];
        $getValues = array_values($param);
        if (empty(array_diff(['m_order_no', 'cp_order_no', 'out_trade_no', 'pay_status', 'pay_amount', 'uid'], $getValues))) {
            return false;
        }

        if (!$channelConfig = json_decode($polyChannelGame['param'], true)) {
            return false;
        }

        $sign = $data['sign'];
        $hadleSign = $this->notifySign($param, $channelConfig['pay_key']);
        if ($sign == $hadleSign) {
            return true;
        }
        return false;
    }

    // ## 回调 - 下单参数获取
    public function getData($data)
    {
        if (!isset($data['cp_order_no']) || !isset($data['pay_amount']) || !isset($data['m_order_no'])) {
            return false;
        }
        return [
            'orderid' => $data['cp_order_no'],           // 我方聚合订单
            'amount' => $data['pay_amount'],             // 充值金额(单位：元)
            'sub_orderid' => $data['m_order_no'],           // 渠道订单
        ];
    }

    // ## 回调 - 成功返回
    public function getSuccess($msg = '')
    {
        echo 'success';
        exit();
    }

    // ## 回调 - 失败返回
    public function getFail($msg = '')
    {
        log_message('getFail: '.$msg, 'error', LOG_PATH.'complexPaylog/simo/');
        echo 'fail';
        exit();
    }


    // 登陆签名
    private function signData($param, $app_key)
    {
        ksort($param);
        foreach ($param as $k => $v) {
            $tmp[] = $k . '=' . urlencode($v);
        }
        $str = implode('&', $tmp) . $app_key;
        return md5($str);
    }

    // 支付回调签名
    private function notifySign($params, $key)
    {
        $data = array(
            "tm" => $params['tm'],
            "m_order_no" => $params['m_order_no'],
            'cp_order_no' => $params['cp_order_no'],
            'out_trade_no' => $params['out_trade_no'],
            "pay_status" => $params['pay_status'],
            'pay_amount' => $params['pay_amount'],
            'uid' => $params['uid'],
            'server_id' => $params['server_id'],
            'server_name' => $params['server_name'],
            'role_id' => $params['role_id'],
            "role_name" => $params['role_name'],
            "product_id" => $params['product_id'],
            "product_name" => $params['product_name'],
        );
        ksort($data);
        $dataSign = http_build_query($data);
        return md5($dataSign . $key);
    }

}
