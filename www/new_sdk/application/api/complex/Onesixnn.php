<?php

namespace app\api\complex;
class Onesixnn implements ComplexInterface
{
    private $channel = 'onesixnn';
    private $check_url = "https://unapi.mzy88.cn/hu/cp/user/check";


    public function checkLogin($data, $polyChannelGame = [])
    {

        if (!$paramArr = json_decode($polyChannelGame['param'], true)) {
            return false;
        }

        $param = [
            'app_id' => $paramArr['appid'],
            'mem_id' => $data['username'],
            'user_token' => $data['channelToken'],
        ];
        $param['sign'] = md5('app_id=' . $param['app_id'] . '&mem_id=' . $param['mem_id'] . '&user_token=' . $param['user_token'] . '&app_key=' . $paramArr['appkey']);
        $result = curl($paramArr['url'], $param);
        // log_message($paramArr['url'] . '?' . http_build_query($param) . '  ' . $result, 'log', LOG_PATH . 'complexLoginlog/' . $this->channel . '/');
        $res = json_decode($result, true);
        if ($res['status'] == 1) {
            return true;
        } else {
            return false;
        }
    }

    public function paySign($data, $polyChannelGame = [])
    {
        if (!$paramArr = json_decode($polyChannelGame['param'], true)) {
            return false;
        }
        $param = [
            'app_id' => urlencode($data['app_id']),
            'ch_id' => isset($data['ch_id']) ? urlencode($data['ch_id']) : '',
            'ch_order_id' => isset($data['ch_order_id']) ? urlencode($data['ch_order_id']) : '',
            'order_id' => urlencode($data['order_id']),
            'cp_order_id' => urlencode($data['cp_order_id']),
            'mem_id' => urlencode($data['mem_id']),
            'order_status' => urlencode($data['order_status']),
            'pay_time' => urlencode($data['pay_time']),
            'finish_time' => isset($data['finish_time']) ? urlencode($data['finish_time']) : '',
            'product_id' => urlencode($data['product_id']),
            'product_name' => urlencode($data['product_name']),
            'product_price' => urlencode($data['product_price']),
            'server_id' => isset($data['server_id']) ? urlencode($data['server_id']) : '',
            'role_id' => isset($data['role_id']) ? urlencode($data['role_id']) : '',
            'ext'=> isset($data['ext']) ? urlencode($data['ext']) : ''
        ];

        if($param['ch_id']){
            $sign = md5('app_id='.$param['app_id'].'&ch_id='.$param['ch_id'].'&ch_order_id='.$param['ch_order_id'].'&cp_order_id='.$param['cp_order_id'].'&ext='.$param['ext'].'&finish_time='.$param['finish_time'].'&mem_id='.$param['mem_id'].'&order_id='.$param['order_id'].'&order_status='.$param['order_status'].'&pay_time='.$param['pay_time'].'&product_id='.$param['product_id'].'&product_name='.$param['product_name'].'&product_price='.$param['product_price'].'&role_id='.$param['role_id'].'&server_id='.$param['server_id'].'&app_key='.$paramArr['appkey']);
        }else{
            $sign =  md5('app_id='.$param['app_id'].'&cp_order_id='.$param['cp_order_id'].'&mem_id='.$param['mem_id'].'&order_id='.$param['order_id'].'&order_status='.$param['order_status'].'&pay_time='.$param['pay_time'].'&product_id='.$param['product_id'].'&product_name='.$param['product_name'].'&product_price='.$param['product_price'].'&app_key='.$paramArr['appkey']);
        }

        if ($data['sign'] == $sign  && $data['order_status'] == 2) {
            return true;
        }

        return false;
    }

    public function getData($data)
    {
        return [
            'orderid' => $data['cp_order_id'],//我方聚合订单
            'amount' => $data['product_price'],  // 充值金额
            'sub_orderid' => $data['order_id'],//渠道订单
        ];
    }

    public function getFail($msg = '')
    {
        echo 'FAILURE';
        exit();
    }

    public function getSuccess($msg = '')
    {
        echo 'SUCCESS';
        exit();
    }
}
