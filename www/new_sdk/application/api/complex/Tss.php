<?php
/**
 * 376 渠道
 */

namespace app\api\complex;
class Tss
{
    private $channel = 'tss';
    private $check_url = "https://zs-api.go.cc/api/check_user_token";
    private $appid = 51000;
    private $paykey = '3P7duy9vj2bDQK14WGX0ZIMFoCqRYg6i';

    /**
     * 二次登录验证
     */
    public function checkLogin($data,$polyChannelGame = [])
    {
        if($data['gameid'] == 175){
            return true;
        }
        $param = [
            'user_id' => $data['username'],
            'app_id' => $this->appid,
            'user_token'=>$data['channelToken'],
        ];

        $result = getCurl($this->check_url,$param);
        log_message($this->check_url. '?' . http_build_query($param).'  '.$result,'log',LOG_PATH . 'complexLoginlog/'.$this->channel.'/');
        $result = json_decode($result,true);
        if($result['result'] == 1){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 验签
     */
    public function paySign($data,$polyChannelGame = []){

        if (empty($data)) {
            return false;
        }

        $params['app_id'] = $this->appid;
        $params['order_id'] = $data['order_id']; //下级渠道的订单号
        $params['cp_order_id'] = $data['cp_order_id']; //这个才是自己聚合层(本级)的订单编号
        $params['user_id'] = $data['user_id'];
        $params['role_id'] = $data['role_id'];
        $params['server_code'] = $data['server_code'];
        $params['price'] = priceFormat($data['price'],1);  //金额
        $params['pay_time'] = $data['pay_time'];
        $params['goods_code'] = $data['goods_code'];
        $params['is_sandbox'] = $data['is_sandbox'];
        $params['extend_data'] = $data['extend_data'];
        $sign = $data['sign'];
//var_dump($params['app_id'].$params['order_id'].$params['cp_order_id'].$params['user_id'].$params['role_id'].$params['server_code'].$params['price'].$params['pay_time'].$params['goods_code'].$params['is_sandbox'].$this->paykey);
        $thisSign = md5($params['app_id'].$params['order_id'].$params['cp_order_id'].$params['user_id'].$params['role_id'].$params['server_code'].$params['price'].$params['pay_time'].$params['goods_code'].$params['is_sandbox'].$this->paykey);
//        var_dump($thisSign);die();
        if ($sign == $thisSign ) {
            return true;
        }
        return false;
    }

    /**
     * 系统使用参数名转换
     */
    public function getData($data){

        return [
            'orderid'=>$data['cp_order_id'],//我方聚合订单
            'amount'=>$data['price'],  // 充值金额
            'sub_orderid'=>$data['order_id'],//渠道订单
        ];
    }

    public function getFail($msg = ''){
        echo json_encode(['result'=>0,'desc'=>$msg]);
        exit();
    }

    public function getSuccess($msg = ''){
        echo json_encode(['result'=>1,'desc'=>$msg]);
        exit();
    }
}
