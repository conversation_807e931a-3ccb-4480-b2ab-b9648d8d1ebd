<?php

namespace app\api\complex;

class Haipai implements ComplexInterface
{
    private $channel = 'haofan';
    private $check_url = "http://api.tyyc88.com/?ct=sdk&ac=user_login";


    public function checkLogin($data, $polyChannelGame = [])
    {
        $param = [
            'token' => $data['channelToken'],
        ];
        $result = curl($this->check_url, $param);
        // log_message($this->check_url . '?' . http_build_query($param) . '  ' . $result, 'log', LOG_PATH . 'complexLoginlog/' . $this->channel . '/');
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            return true;
        } else {
            return false;
        }
    }

    public function paySign($data, $polyChannelGame = [])
    {
        if (!$paramArr = json_decode($polyChannelGame['param'], true)) {
            return false;
        }
        $param = [
            'cporderid' => $data['cporderid'],
            'orderid' => $data['orderid'],
            'appid' => $data['appid'],
            'uid' => $data['uid'],
            'time' => $data['time'],
            'extinfo' => $data['extinfo'],
            'amount' => $data['amount'],
            'serverid' => $data['serverid'],
            'charid' => $data['charid'],
        ];
        ksort($param);
        $signstr = http_build_query($param);
        $sign = md5($signstr . $paramArr['paykey']);
        if ($data['sign'] == $sign) {
            return true;
        }

        return false;
    }

    public function getData($data)
    {
        return [
            'orderid' => $data['cporderid'], //我方聚合订单
            'amount' => $data['amount'],  // 充值金额
            'sub_orderid' => $data['orderid'], //渠道订单
        ];
    }

    public function getFail($msg = '')
    {
        echo 'error';
        exit();
    }

    public function getSuccess($msg = '')
    {
        echo 'success';
        exit();
    }

    /**
     * 获取用户id
     * @param $data
     * @return bool
     */
    public function getSpecialParam($data, $polyChannelGame = [])
    {
        $param = [
            'token' => $data['channelToken'],
        ];
        $result = curl($this->check_url, $param);
        // log_message($this->check_url . '?' . http_build_query($param) . '  ' . $result, 'log', LOG_PATH . 'complexLoginlog/' . $this->channel . '/');
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            return $result['data']['uid'];
        } else {
            return false;
        }
    }
}
