<?php
/**
 * api登录控制器
 */

namespace app\api\controller\v1;

use app\api\controller\Api;
use app\common\library\IpLimit;
use app\common\model\Members;
use app\common\model\Logininfo;
use app\common\model\Logoutinfo;
use app\common\model\Channel;
use app\common\model\Subaccount;
use app\common\library\Sms;
use app\common\service\FkStrategiesService;
use app\service\TiktokService;
use think\Db;
use think\Cache;
use app\common\logic\Member as MemberLogic;
use app\common\logic\Coin;
use app\common\model\Setting;
use think\Env;
use think\Exception;
use think\Request;

class Login extends Api
{
    protected $membersModel;
    protected $noNeedLogin = ['index', 'loginsms', 'loginqq', 'loginweibo', 'third'];

    public function _initialize()
    {
        parent::_initialize();
        $this->membersModel = new Members();
    }

    /**
     * 普通用户登录处理
     */
    public function index()
    {
        // if (temporary()) {
        //     $this->jsonResult('', 0, '禁止登录');
        // }
        $username = $this->input('username');             //用户名
        $password = $this->input('password');             //密码
        $imeil = trim($this->input('imeil'));            //手机imeil码
        $gameid = $this->input('gameid');               //游戏ID
        $device = $this->input('device');                //设备
        $channelId = $this->input('channel_id');           //当前登录的渠道ID

        //海外游戏ip限制
        if (in_array($gameid, config('haiwai_game_ids'))) {
            $res = (new IpLimit())->validateIp(request()->ip(), $username);
            if ($res != 1) {
                $this->jsonResult('', -110, '国内用户禁止登录');
            }
        }

        if (empty($username)) {
            $this->jsonResult('', -100, '用户名不能为空');
        } elseif (empty($password)) {
            $this->jsonResult('', -100, '密码不能为空');
        }
        //游戏ID不能为空
        if (empty($gameid)) {
            $this->jsonResult('', -100, '游戏ID不能为空');
        }
        if (empty($device)) {
            $this->jsonResult('', -100, '没有注册设备来源');
        }
        if (empty($imeil)) {
            $this->jsonResult('', -100, '设备imeil异常');
        }
        if (empty($channelId)) {
            $this->jsonResult('', -100, '渠道不能为空');
        }

        //判断渠道
        $channelInfo = (new Channel)->where(['status' => 1, 'id' => $channelId])->find();
        if (!$channelInfo) {
            $this->jsonResult('', -120, '当前渠道不存在或已禁用！');
        } else if ($channelInfo['level'] <> 3) {
            $this->jsonResult('', -120, '非推广员渠道,不能进行推广,请联系客服');
        }

        if (!$game = model('Common/Game')->where(['id' => $gameid])->find()) {
            $this->jsonResult('', -120, '游戏信息不存在');
        }
        if ($game['cooperation_status'] == 3) {
            $this->jsonResult('', -120, '游戏已下架不存在');
        }
        if ($game['is_login'] == 1) {
            // 游戏登录白名单
            if(Db::name('cy_game_user_white')->where(['game_id' => $gameid, 'username' => $username])->count() == 0){
                $this->jsonResult('', -120, '当前游戏禁止登录！');
            }
        }

        // 渠道禁止判断
        if (isFrozenOption($channelId, $gameid, 'member_login')) {
            $this->jsonResult('', -120, '您所在渠道已经被禁止登录此游戏!! ');
        }
        // 封禁仙剑安卓包的主播包登陆
        if (!in_array($username, ['zbgzzdaj', 'zbcobpyl'])) {
            if ($channelId == 100 && $gameid == 266) {
                $this->jsonResult('', -120, '您所在渠道已经被禁止登录此游戏!! ');
            }
        }
        
        //加密后密码
        $password = auth_code($password, "ENCODE", Env::get($this->authKey));
        //用户信息
        $userinfo = $this->membersModel->where(['username' => $username, 'password' => $password])->find();
        if (empty($userinfo)) {
            $this->jsonResult('', -120, '账号不存在或者密码不正确');
        }

        // 账号是否被冻结
        if (1 == $userinfo['flag']) {
            $this->jsonResult('', -120, '您的账号已被冻结，请联系客服QQ：' . CUSTOMER_SERVICE_QQ);
        }

        // 子账号是否被冻结
        $subaccountInfo = model('Subaccount')->where(['member_id' => $userinfo['id'], 'game_id' => $gameid])->find();
        if (1 == $subaccountInfo['flag']) {
            $this->jsonResult('', -120, '您的子账号已被冻结，请联系客服QQ：' . CUSTOMER_SERVICE_QQ);
        }
        //elseif($userinfo['channel_id'] != $channelId){
        //  $this->jsonResult("",0,"登录渠道号和注册渠道不一致,无法登录");
        // }
        // $channelId = $userinfo['channel_id'];

        // ## 风控策略
        $fkData = [
            // 'event_type' => 'login', // username
            'username' => $username, // username
            'ip' => request()->ip(), // ip
            'imei' => $imeil, // imei
            'mobile' => $userinfo['mobile'], // 注册手机号
        ];
        $result = (new FkStrategiesService())->processEvent($fkData); // 登录
        if ($result['status']) {
            $this->jsonResult('', -120, '您的账号已被冻结，1请联系客服QQ：' . CUSTOMER_SERVICE_QQ);
        }

        //用户登录公共部分处理
        $this->loginProcess($userinfo, $gameid, $imeil, $device, $channelId);

        exit;
    }

    /**
     * 手机验证码登录
     */
    /*
    public function loginSms()
    {
        $username	= $this->input('username');             //用户名
        $code       = $this->input('sms_code');             //短信验证码
        $imeil	    = $this->input('imeil');				  //手机imeil码
        $gameid     = $this->input('gameid');               //游戏ID
        $device		= $this->input('device');	              //设备

        if (empty($username)) {
            $this->jsonResult('',0,'用户名不能为空');
        }
        //游戏ID不能为空
        if (empty($gameid)) {
            $this->jsonResult('',0,'游戏ID不能为空');
        }
        if (empty($device)) {
            $this->jsonResult('',0,'没有注册设备来源');
        }
        if (empty($imeil)) {
            $this->jsonResult('',0,'设备imeil异常');
        }

        if($code==''){

            $this->jsonResult('',0,'短信验证码不能为空');
        }

        $sms = new Sms;

        $codeResult = $sms->checkCode($username, $code);

        if(!$codeResult['status']) {

            $this->jsonResult('', 0, $codeResult['msg']);
        }

        //用户信息
        $userinfo = $this->membersModel->where(['username'=>$username])->find();

        if(empty($userinfo))
        {
            $this->jsonResult('',0,'账号不存在');
        }
        // 判断一下当前账号是否被冻结
        elseif(1 == $userinfo['flag']) {
            $this->jsonResult('',0,'您的账号已被冻结，请联系客服QQ：'.CUSTOMER_SERVICE_QQ);
        }

        //用户登录公共部分处理
        $this->loginProcess($userinfo, $gameid, $imeil, $device);

        exit;
    }*/

    /**
     * QQ第三方登录
     */
    /*
    public function loginQq()
    {
        $access_token   = $this->input('access_token');         //Access Token
        $imeil          = $this->input('imeil');				  //手机imeil码
        $gameid         = $this->input('gameid');               //游戏ID
        $device         = $this->input('device');	              //设备

        if (empty($access_token)) {
            $this->jsonResult('',0,'access_token码不能空。');
        }
        //游戏ID不能为空
        if (empty($gameid)) {
            $this->jsonResult('',0,'游戏ID不能为空');
        }
        if (empty($device)) {
            $this->jsonResult('',0,'没有注册设备来源');
        }

        $url="https://graph.qq.com/oauth2.0/me?access_token={$access_token}&unionid=1";

        $unioid_content = getCurlContent($url);

        $subStr = substr($unioid_content, strpos($unioid_content, "{"));
        $subStr = strstr($subStr, "}", true) . "}";
        $unioidInfo = json_decode($subStr, true);

        if(!isset($unioidInfo['openid']) || !isset($unioidInfo['unionid'])) {

            $this->jsonResult('',0,'获取QQ的用户信息失败,请检查Access Token是否有效');
        }
        else{
            $request_url="https://graph.qq.com/user/get_user_info?oauth_consumer_key=".QQ_APPID."&access_token={$access_token}&openid={$unioidInfo['openid']}";

            $request_content = http_get($request_url);
            $qqInfo = json_decode($request_content, true);

            if(empty($qqInfo) || $qqInfo['ret']!=0)
            {
                $this->jsonResult('',0,'获取QQ的用户信息失败');
            }
            else{
                $qqInfo['unionid'] 	= $unioidInfo['unionid'];
                $qqInfo['qq_openid']= $unioidInfo['openid'];

                $qqModel = new MembersQq;

                $membersQqInfo = $qqModel->where(['unionid'=>$qqInfo['unionid']])->find();

                //注册
                if(empty($membersQqInfo))
                {

                }
                //登录
                else{
                    //用户信息
                    $userinfo = $this->membersModel->where(['id'=>$membersQqInfo['userid']])->find();

                    //用户登录公共部分处理
                    $this->loginProcess($userinfo, $gameid, $imeil, $device);
                }
           }
       }


    }*/

    /**
     *  判断是否节假日
     */
    public function getHoliday($date)
    {
        try {
            $url = "https://tool.bitefu.net/jiari/?d=$date&back=json";
            $result = file_get_contents($url);
            $result = json_decode($result, true);
            if (isset($result[$date]) && $result[$date] == 2) {
                return true;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }


    }

    /**
     * 用户登录公共部分处理
     *
     * @param $userinfo    : array 用户信息数组
     * @param $gameid      : int 游戏ID
     * @param $imeil       : string 手机imeil码
     * @param $device      : int 注册设备来源
     * @param $channelId   : int 当前登录的渠道ID
     *
     */
    private function loginProcess($userinfo, $gameid, $imeil, $device, $channelId)
    {
        //判断游戏是否冻结IP或者IMEI
        $LoginIp = request()->ip();
        $BannedInfo = model('GameBanned')->where(['game_id' => array('in', [0, $gameid]), 'banned_type' => 'ip', 'banned_device' => $LoginIp])->find();
        if (!empty($BannedInfo)) {
            log_message(json_encode([$userinfo, $gameid, $imeil, $device, $channelId, $LoginIp]), 'log', LOG_PATH . 'login/');
            $template = '【封禁提示】IP禁止登录：账号：' . $userinfo['username'] . ' ip:' . $LoginIp;
            curlDD($template, Env::get('dingtalk.notice_weelfar_url'), true);
            $this->jsonResult('', 0, '您的IP已被禁止登录此游戏! ');
        }
        if ($imeil) {
            $BannedInfo = model('GameBanned')->where(['game_id' => array('in', [0, $gameid]), 'banned_type' => 'imeil', 'banned_device' => $imeil])->find();
            if (!empty($BannedInfo)) {
                log_message(json_encode([$userinfo, $gameid, $imeil, $device, $channelId]), 'log', LOG_PATH . 'login/');
                $template = '【封禁提示】设备登录：账号：' .$userinfo['username'].' imeil:'.$imeil;
                curlDD($template, Env::get('dingtalk.notice_weelfar_url'), true);
                $this->jsonResult('', 0, '该设备已被禁止登录此游戏! ');
            }
        }

        // 渠道禁止判断
        if (isFrozenOption($channelId, $gameid, 'member_login')) {
            $this->jsonResult('', 0, '您所在渠道已经被禁止登录此游戏! ');
        }

        //游戏防沉迷限制
        $restrictInfo = model('GameRestrict')->where(['game_id' => $gameid, 'restrict_type' => 'preventhook'])->find();
        if ($restrictInfo) {
            $isPreventHook = intval($restrictInfo['restrict_status']);
        } else {
            $settingModel = new Setting;
            $isPreventHook = intval($settingModel::getSetting('CHILD_LIMIT'));
        }

        $subaccountModel = model('Subaccount');
        $offline_time = null;
        $gameAccountInfo = $subaccountModel->where(['game_id' => $gameid, 'member_id' => $userinfo['id']])->find();

        if ($isPreventHook == 2) {
            // vendor('authSdk.Authentication', '.class.php');
            // $auth = new \Authentication();

            // 已实名认证用户做防沉迷判断
            $membersTwoInfo = model('MembersTwo')->where(['userid' => $userinfo['id']])->find();

            $gameInfo = model('Game')->where(['id' => $gameid])->find();
            if ($gameInfo['game_auth_type'] == 1) {  //中宣部实名认证
                // // 判断其他子账户是否已实名，是的话，使用其他账户的实名信息。
                // if(!$gameAccountInfo || !$gameAccountInfo['idcard_num']){
                //     $shimingInfo = $subaccountModel->where(['member_id' => $userinfo['id'], 'auth_type' => 1, 'auth_status' => 3])->find();
                //     if($shimingInfo){
                //         $zxResult = $auth->check(trim($gameInfo['game_bizid']), $gameAccountInfo['member_id'] . '-' . $gameAccountInfo['id'], $shimingInfo['real_name'], $shimingInfo['idcard_num']);
                //         if($zxResult['errcode'] == 0 && isset($zxResult['data']['result']['pi'])){
                //             if(!$gameAccountInfo){
                //                 //生成子账号记录
                //                 $gameAccountInfo = $subaccountModel->create([
                //                     'auth_type' => 1,
                //                     'real_name' => $shimingInfo['real_name'],
                //                     'idcard_num' => $shimingInfo['idcard_num'],
                //                     'auth_status' => 3,
                //                     'auth_pi' => $zxResult['data']['result']['pi'],
                //                     'member_id' => $userinfo['id'],
                //                     'game_id' => $gameid,
                //                     'createtime' => NOW_TIMESTAMP,
                //                 ]);
                //             }else{
                //                 $gameAccountInfo->auth_type = 1;
                //                 $gameAccountInfo->real_name = $shimingInfo['real_name'];
                //                 $gameAccountInfo->idcard_num = $shimingInfo['idcard_num'];
                //                 $gameAccountInfo->auth_status = 3;
                //                 $gameAccountInfo->auth_pi = $zxResult['data']['result']['pi'];
                //                 $gameAccountInfo->last_auth_time = time();
                //                 $gameAccountInfo->save();
                //             }
                //
                //             // 插入玩家历史记录
                //             Db::table('cy_member_history')->insert([
                //                 'userid' => $userinfo['id'],
                //                 'password' => $shimingInfo['real_name'] . ' ' . $shimingInfo['idcard_num'] . ' ' . $shimingInfo['sub_username'],
                //                 'mobile' => '',
                //                 'ip' => request()->ip(),
                //                 'create_time' => time(),
                //             ]);
                //         }
                //     }
                // }
                if ($gameAccountInfo) {
                    if ($gameAccountInfo['auth_status'] == 3) {
                        $membersTwoInfo['realname'] = trim($gameAccountInfo['real_name']);
                        $membersTwoInfo['idcard'] = trim($gameAccountInfo['idcard_num']);
                    } else if ($gameAccountInfo['auth_status'] == 1) {
                        $membersTwoInfo['realname'] = '';
                        $membersTwoInfo['idcard'] = '';
                    } else {
                        $membersTwoInfo['realname'] = '';
                        $membersTwoInfo['idcard'] = '';
                    }
                } else {
                    $membersTwoInfo['realname'] = '';
                    $membersTwoInfo['idcard'] = '';
                }
            }

            if ($membersTwoInfo && !empty($membersTwoInfo['realname']) && !empty($membersTwoInfo['idcard']) && $membersTwoInfo['realname'] <> -1 && $membersTwoInfo['idcard'] <> -1) {
                if (!isMeetAgeByIDCard($membersTwoInfo['idcard'], 18, 120)) {
                    // 节假日
                    $holiday = $this->getHoliday(date("Ymd"));
                    if ($holiday) {
                        if (!(date("H:i") > "20:00" && date("H:i") < "21:00")) {
                            $this->jsonResult('', 0, '未成年用户，仅可在周五、周六、周日和法定节假日每日20时至21时进行游戏，其他时间均无法进行游戏 ');
                        } else {
                            $offline_time = strtotime(date("Y-m-d") . " 21:00");
                        }
                    } else {
                        $date = date("w");
                        if (in_array($date, [0, 5, 6])) {
                            if (!(date("H:i") > "20:00" && date("H:i") < "21:00")) {
                                $this->jsonResult('', 0, '未成年用户，仅可在周五、周六、周日和法定节假日每日20时至21时进行游戏，其他时间均无法进行游戏 ');
                            } else {
                                $offline_time = strtotime(date("Y-m-d") . " 21:00");
                            }
                        } else {
                            $this->jsonResult('', 0, '未成年用户，仅可在周五、周六、周日和法定节假日每日20时至21时进行游戏，其他时间均无法进行游戏 ');
                        }
                    }

                }
            }
        }

        $logininfoModel = new Logininfo();
        $member_channel_game_rel = model('MemberChannelGame');
        $nowTime = NOW_TIMESTAMP;

        // 启动事务
        Db::startTrans();
        try {
            // 用户&游戏 绑定数据
            $info = $member_channel_game_rel->where(['member_id' => $userinfo['id'], 'game_id' => $gameid])->find();
            
            /**
             * 所属渠道:
             * 1. 根据注册的渠道，注册渠道没有则根据客户端传递的;
             */
            $bandChannelId = $channelId; // 默认客户端传递的渠道
            if(!empty($gameAccountInfo['channel_id'])){
                $bandChannelId = $gameAccountInfo['channel_id'];
            }

            $userId = $userinfo['id'];
            $gameId = $gameid;
            // 首次登录-游戏渠道注册
            if (empty($info)) {
                $info = $member_channel_game_rel->create([
                    'member_id' => $userId,
                    'channel_id' => $bandChannelId,
                    'game_id' => $gameId,
                    'mcgr_createtime' => NOW_TIMESTAMP,
                    'update_time' => NOW_TIMESTAMP,
                    'mcgr_imeil' => $imeil,
                    'mcgr_ip' => request()->ip()
                ]);
                // 插入玩家游戏渠道绑定记录
                $channelName = model('Channel')->where(['id' => $bandChannelId])->value('name');
                $gameName = model('Common/Game')->where(['id' => $gameId])->value('name');
                Db::table('cy_member_history')->insert([
                    'userid' => $userId,
                    'channel' => $gameName . "," . $channelName,
                    'ip' => request()->ip(),
                    'create_time' => time()
                ]);

                /**
                 * 注册子账户以及所属渠道绑定
                 * 1. 查询对应 安卓/IOS 另一端游戏绑定的渠道号；
                 */
                $gameType = model('Common/Game')->where(['id' => $gameid])->value('type');
                if ($gameType == 1) {
                    $getbanGameChannelId = model('common/GameBand')->alias('gb')->join('cy_member_channel_game_rel mcg', 'mcg.game_id = gb.android_game_id AND mcg.member_id=' . $userinfo['id'], 'left')->where(['gb.ios_game_id' => $gameid])->value('mcg.channel_id');
                } else if ($gameType == 2) {
                    $getbanGameChannelId = model('common/GameBand')->alias('gb')->join('cy_member_channel_game_rel mcg', 'mcg.game_id = gb.ios_game_id AND mcg.member_id=' . $userinfo['id'], 'left')->where(['gb.android_game_id' => $gameid])->value('mcg.channel_id');
                }
                if (!empty($getbanGameChannelId)) {
                    $bandChannelId = $getbanGameChannelId;
                }
                
                // 用于首充发放 (用于首次登录)
                //   (new Coin())->autoSend($gameId, $bandChannelId, $userId);
            } else {
                // 更新游戏最后登录时间
                $member_channel_game_rel->where(['member_id' => $userId, 'game_id' => $gameId])->update(['update_time' => time()]);
            }

            //为了兼容旧SDK保存memkey,新SDK没有用到这个值，旧SDK关闭后可以删除该代码
            // if (!trim($userinfo['nickname'])) {
            //     $userNickName = random(3) . $userinfo['id'];
            //     $this->membersModel->where(['id' => $userinfo['id']])->update(['memkey' => md5($userinfo['username'] . $userinfo['password'] . rand(0, 1000)), 'login_time' => $nowTime, 'nickname' => $userNickName]);
            // } else {
            //     $this->membersModel->where(['id' => $userinfo['id']])->update(['memkey' => md5($userinfo['username'] . $userinfo['password'] . rand(0, 1000)), 'login_time' => $nowTime]);
            // }

            /**
             * 子账户信息
             * 1. 有则根据查询；无则创建子账户；
             * 2. 关联游戏使用主账户的ID+正式/测试的标识；
             */
            $sub_username = '';
            if(!empty($gameAccountInfo['sub_username'])){
                $sub_username = $gameAccountInfo['sub_username'];
                $subaccountModel->save(['update_time' => $nowTime ], ['member_id' => $userinfo['id'], 'game_id' => $gameid ]);
            }else {
                // 指定的几款游戏 不需要注册，账号从cy_members 取
                // $settingModel = new Setting();
                // $accountArr = explode(',', $settingModel->getSetting('ANDROID_IOS_ACCOUNT'));
                //
                // if (in_array($gameid, $accountArr)) {
                //     $sub_username = $userinfo['username'];
                // } elseif ($gameid > 220) {
                //     $sub_username = $userinfo['id'];
                // } else {
                //     //旧SDK的玩家,绑定关系表有记录时
                //     if (!empty($info)) {
                //         $sub_username = $userinfo['username'];
                //     } //旧SDK的channel_id=0或者默认渠道的玩家
                //     elseif ($logininfoModel->where(['userid' => $userinfo['id'], 'gameid' => $gameid])->find()) {
                //         $sub_username = $userinfo['username'];
                //     } else {
                //         $sub_username = strtolower(random(20));
                //         //保证子账号名唯一性
                //         while ($subaccountModel->field('id')->where(['sub_username' => $sub_username])->find()) {
                //             $sub_username = strtolower(random(20));
                //         }
                //     }
                // }
                // $sub_username .= '_'.$gameid.Env::get('sdk_mark_type');
                $sub_username = $userinfo['id'].'_'.APP_STATUS;

                //生成子账号记录
                if (isset($gameAccountInfo['id']) && isset($gameAccountInfo['idcard_num'])) {
                    $subaccountModel->save([
                        'update_time' => $nowTime,
                        'sub_username' => $sub_username,
                        'channel_id' => $bandChannelId,
                    ], [
                        'member_id' => $userinfo['id'],
                        'game_id' => $gameid,
                    ]);
                } else {
                    $gameAccountInfo = $subaccountModel->create([
                        'member_id' => $userinfo['id'],
                        'game_id' => $gameid,
                        'channel_id' => $bandChannelId,
                        'sub_username' => $sub_username,
                        'create_time' => $nowTime,
                    ]);
                }
            }

            $logData['sub_id'] = $gameAccountInfo['id'];
            $logData['userid'] = $userinfo['id'];
            $logData['gameid'] = $gameid;
            $logData['imeil'] = $imeil;
            $logData['device'] = $device;
            $logData['reg_time'] = $userinfo['reg_time'];
            $logData['fromflag'] = $userinfo['fromflag'];
            $logData['channel_id'] = $channelId;
            $logData['login_time'] = $nowTime;
            $logData['ip'] = request()->ip();

            //登录日志
            $logininfoModel->limitInsert($logData);

            $platform = ($device == 3 ? 1 : 0);
            //收集设备信息
            $this->saveDevices($gameid, $imeil, $platform);

            $this->membersModel->save(['update_time' => $nowTime, 'login_time' => $nowTime], ['id' => $userinfo['id']]);

            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            $this->jsonResult('', 0, '登录失败：' . $e->getMessage());
        }

        // 查询出帐号在用户中心的openid，下面使用openid交互
        // $mgMemberInfo = Db::connect('db_config_resource_center')->table('mg_member')->field('mem_openid')->where(['mem_login_name'=>$userinfo['username'],'pl_id'=>2])->find();

        // $signStr = "username=".$mgMemberInfo['mem_openid']."&appkey=".$this->appInfo['appkey']."&logintime=".$nowTime;
        // $returnData['sign']         = md5($signStr);
        // $returnData['memkey']       = md5($mgMemberInfo['mem_openid'].$userinfo['password'].rand(0,1000));        //登录生成用户key
        // $returnData['login_time']   = $nowTime;
        // $returnData['openid']       = $mgMemberInfo['mem_openid'];

        //token的随机码
        $token_random = random(8);
        $token = auth_code($userinfo['id'] . '|' . $userinfo['username'] . '|' . $gameid . '|' . $sub_username . '|' . $token_random . '|sdk', "ENCODE", Env::get($this->authKey));

        // $imeil 做为唯一登录标识
        Cache::store('redis')->set($token, $imeil, 60 * 60 * 24 * 15);
        // redis中玩家登录安全控制的随机码
        Cache::store('redis')->set('token|sdk|' . $userinfo['id'] . '|' . $gameid, $token_random, 60 * 60 * 24 * 15);
        //用户推广信息保存
        setChannelId($userinfo['id'], $bandChannelId);

        $returnData['token'] = $token;
        $returnData['username'] = $userinfo['username'];        //主账号名
        $returnData['sub_username'] = $sub_username;                //子账号名
        $returnData['offline_time'] = $offline_time;
        file_put_contents(RUNTIME_PATH . '/register.txt', var_export($returnData, true) . PHP_EOL, FILE_APPEND);

        if ($isPreventHook == 2) {
            // 已实名认证用户做防沉迷判断
            $membersTwoInfo = model('MembersTwo')->where(['userid' => $userinfo['id']])->find();
            $gameInfo = model('Game')->where(['id' => $gameid])->find();
            if ($gameInfo['game_auth_type'] == 1) {  //中宣部实名认证
                $gameAccountInfo = model('Subaccount')->where(['game_id' => $gameid, 'member_id' => $userinfo['id']])->find();
                if ($gameAccountInfo) {
                    if ($gameAccountInfo['auth_status'] == 3) {
                        $membersTwoInfo['realname'] = trim($gameAccountInfo['real_name']);
                        $membersTwoInfo['idcard'] = trim($gameAccountInfo['idcard_num']);
                    } else if ($gameAccountInfo['auth_status'] == 1) {
                        $membersTwoInfo['realname'] = '';
                        $membersTwoInfo['idcard'] = '';
                    } else {
                        $membersTwoInfo['realname'] = '';
                        $membersTwoInfo['idcard'] = '';
                    }
                } else {
                    $membersTwoInfo['realname'] = '';
                    $membersTwoInfo['idcard'] = '';
                }
                vendor('authSdk.Authentication', '.class.php');
                $auth = new \Authentication();
                if (!$membersTwoInfo['realname'] || !$membersTwoInfo['idcard']) {  //未实名
                    $result = $auth->loginout(trim($gameInfo['game_bizid']), $token, 1, 2, $imeil);
                    if ($result['errcode'] == 0) {
                    }
                } else {  //已实名
                    $result = $auth->loginout(trim($gameInfo['game_bizid']), $token, 1, 0, $imeil, $gameAccountInfo['auth_pi']);
                    if ($result['errcode'] == 0) {
                    }
                }
            }
        }
        //
        // try {
        //     //2022-10-13 抖音推送
        //     $extend = [
        //         'Oaid' => '',
        //         'Idfa' => '',
        //         'Androidid' => '',
        //         'Imei' => '',
        //         'PayAmount' => 0,
        //         'Ip' => request()->ip()
        //     ];
        //     $tiktokService = new TiktokService();
        //     $tiktokService->push('tiktok.retention_days', $userinfo['id'], $gameid, $bandChannelId, $extend);
        // } catch (Exception $e) {
        //     $template = '抖音登录推送异常 userid' . $userinfo['id'] . $e->getMessage();
        //     $ddurl = Env::get('warning_url');
        //     $result = curlDD($template, $ddurl, true);
        // }

        $this->jsonResultUnescape($returnData, 1, '登录成功');
    }

    /**
     * 退出登录
     *
     */
    public function logout()
    {
        $logoutModel = new Logoutinfo();

        $username = $this->input('username');             //用户名
        $imeil = $this->input('imeil');                //手机imeil码
        $gameid = $this->input('gameid');               //游戏ID
        $device = $this->input('device');                //设备

        if (empty($username)) {
            $this->jsonResult('', 0, '用户名不能为空');
        }
        //游戏ID不能为空
        if (empty($gameid)) {
            $this->jsonResult('', 0, '游戏ID不能为空');
        }
        if (empty($device)) {
            $this->jsonResult('', 0, '没有注册设备来源');
        }

        $data = [
            'username' => $username,
            'gameid' => $gameid,
            'imeil' => $imeil,
            'device' => $device,
            'logout_time' => time()
        ];

        $logoutModel->insert($data);

        //用户信息
        $userinfo = $this->membersModel->field('id,reg_time,fromflag,flag,nickname')->where(['id' => $this->input('userid')])->find();

        if (empty($userinfo)) {
            $this->jsonResult('', 0, '账号不存在');
        }

        session(null);

        Cache::store('redis')->rm($this->input('token'));

        Cache::store('redis')->rm('token|sdk|' . $this->input['userid'] . '|' . $gameid);

        //游戏防沉迷限制
        $restrictInfo = model('GameRestrict')->where(['game_id' => $gameid, 'restrict_type' => 'preventhook'])->find();
        if ($restrictInfo) {
            $isPreventHook = intval($restrictInfo['restrict_status']);
        } else {
            $settingModel = new Setting;
            $isPreventHook = intval($settingModel::getSetting('CHILD_LIMIT'));
        }
        if ($isPreventHook == 2) {
            // 已实名认证用户做防沉迷判断
            $membersTwoInfo = model('MembersTwo')->where(['userid' => $userinfo['id']])->find();
            $gameInfo = model('Game')->where(['id' => $gameid])->find();
            if ($gameInfo['game_auth_type'] == 1) {  //中宣部实名认证
                $gameAccountInfo = model('Subaccount')->where(['game_id' => $gameid, 'member_id' => $userinfo['id']])->find();
                if ($gameAccountInfo) {
                    if ($gameAccountInfo['auth_status'] == 3) {
                        $membersTwoInfo['realname'] = trim($gameAccountInfo['real_name']);
                        $membersTwoInfo['idcard'] = trim($gameAccountInfo['idcard_num']);
                    } else if ($gameAccountInfo['auth_status'] == 1) {
                        $membersTwoInfo['realname'] = '';
                        $membersTwoInfo['idcard'] = '';
                    } else {
                        $membersTwoInfo['realname'] = '';
                        $membersTwoInfo['idcard'] = '';
                    }
                } else {
                    $membersTwoInfo['realname'] = '';
                    $membersTwoInfo['idcard'] = '';
                }
                vendor('authSdk.Authentication', '.class.php');
                $auth = new \Authentication();
                if (!$membersTwoInfo['realname'] || !$membersTwoInfo['idcard']) {  //未实名
                    $result = $auth->loginout(trim($gameInfo['game_bizid']), $this->input('token'), 0, 2, $imeil);
                    //	var_dump($result);
                    if ($result['errcode'] == 0) {
                    }
                } else {  //已实名
                    $result = $auth->loginout(trim($gameInfo['game_bizid']), $this->input('token'), 0, 0, $imeil, $gameAccountInfo['auth_pi']);
                    //	var_dump($result);
                    if ($result['errcode'] == 0) {
                    }
                }
            }
        }

        $this->jsonResult('', 1, '退出成功');

    }

    /**
     * 校验是否登录
     */
    public function checkToken()
    {
        if (temporary()) {
            $this->jsonResult('', 0, '禁止登录');
        }
        $channel_id = $this->input('channel_id');           //渠道ID
        $gameid = $this->input('gameid');               //游戏ID
        $device = $this->input('device');                //设备  (v3.0.2 新增参数)
        $imeil = $this->input('imeil');
        //海外游戏ip限制
        if (in_array($gameid, config('haiwai_game_ids'))) {
            $res = (new IpLimit())->validateIp(request()->ip(), $this->input['username']);
            if ($res != 1) {
                $this->jsonResult('', 0, '国内用户禁止登录');
            }
        }
        if (empty($channel_id)) {
            $this->jsonResult('', 0, '渠道ID不能为空');
        }
        if (!$game = model('Common/Game')->where(['id' => $gameid])->find()) {
            $this->jsonResult('', 0, '游戏信息不存在');
        }

        if ($game['cooperation_status'] == 3) {
            $this->jsonResult('', 0, '游戏已下架不存在');
        }
        if ($game['is_login'] == 1) {
            // 游戏登录白名单
            if(Db::name('cy_game_user_white')->where(['game_id' => $gameid, 'username' => $this->input['username']])->count() == 0){
                $this->jsonResult('', 0, '当前游戏禁止登录！');
            }
        }

        //用户信息
        $userinfo = $this->membersModel->field('id,reg_time,fromflag,flag,nickname,channel_id')->where(['id' => $this->input('userid')])->find();
        // 封禁仙剑安卓包的主播包登陆
        if(!in_array($this->input['username'], ['zbgzzdaj', 'zbcobpyl'])){
            if($channel_id == 100 && $gameid == 266){
                $this->jsonResult('', 0, '您所在渠道已经被禁止登录此游戏!! ');
            }
        }

        if (empty($userinfo)) {
            $this->jsonResult('', 0, '账号不存在');
        } // 判断一下当前账号是否被冻结
        elseif (1 == $userinfo['flag']) {

            Cache::store('redis')->rm($this->input('token'));

            $this->jsonResult('', 0, '您的账号已被冻结，请联系客服QQ：' . CUSTOMER_SERVICE_QQ);
        } // 渠道禁止判断
        elseif (isFrozenOption($channel_id, $gameid, 'member_login')) {

            Cache::store('redis')->rm($this->input('token'));

            $this->jsonResult('', 0, '您所在渠道已经被禁止登录此游戏! ');
        }

        //判断游戏是否冻结IP或者IMEI
        $LoginIp = request()->ip();
        $BannedInfo = model('GameBanned')->where(['game_id' => array('in', [0, $gameid]), 'banned_type' => 'ip', 'banned_device' => $LoginIp])->find();
        if (!empty($BannedInfo)) {
            log_message(json_encode([$userinfo, $gameid, $imeil, $device, $LoginIp]), 'log', LOG_PATH . 'login/');
            $template = '【封禁提示】IP禁止登录：账号：' .$userinfo['username'].' ip:'.$LoginIp;
            curlDD($template, Env::get('notice_weelfar_url'), true);
            $this->jsonResult('', 0, '您的IP已被禁止登录此游戏! ');
        }
        if ($imeil) {
            $BannedInfo = model('GameBanned')->where(['game_id' => array('in', [0, $gameid]), 'banned_type' => 'imeil', 'banned_device' => $imeil])->find();
            if (!empty($BannedInfo)) {
                log_message(json_encode([$userinfo, $gameid, $imeil, $device]), 'log', LOG_PATH . 'login/');
                $template = '【封禁提示】设备登录：账号：' .$userinfo['username'].' imeil:'.$imeil;
                curlDD($template, Env::get('notice_weelfar_url'), true);
                $this->jsonResult('', 0, '该设备已被禁止登录此游戏! ');
            }
        }

        //游戏防沉迷限制
        $restrictInfo = model('GameRestrict')->where(['game_id' => $gameid, 'restrict_type' => 'preventhook'])->find();
        if ($restrictInfo) {
            $isPreventHook = intval($restrictInfo['restrict_status']);
        } else {
            $settingModel = new Setting;
            $isPreventHook = intval($settingModel::getSetting('CHILD_LIMIT'));
        }
        $offline_time = null;
        if ($isPreventHook == 2) {
            // 已实名认证用户做防沉迷判断
            $membersTwoInfo = model('MembersTwo')->where(['userid' => $userinfo['id']])->find();
            if ($membersTwoInfo && !empty($membersTwoInfo['realname']) && !empty($membersTwoInfo['idcard']) && $membersTwoInfo['realname'] <> -1 && $membersTwoInfo['idcard'] <> -1) {
                if (!isMeetAgeByIDCard($membersTwoInfo['idcard'], 18, 120)) {
                    //if (!(date('H:i') > '08:00' && date('H:i') < '22:00')) {
                    //  $this->jsonResult('', 0, '您未满18周岁，22:00~8:00无法登录 ');
                    // }
                    // 节假日
                    $holiday = $this->getHoliday(date("Ymd"));
                    if ($holiday) {
                        if (!(date("H:i") > "20:00" && date("H:i") < "21:00")) {
                            $this->jsonResult('', 0, '未成年用户，仅可在周五、周六、周日和法定节假日每日20时至21时进行游戏，其他时间均无法进行游戏 ');
                        } else {
                            $offline_time = strtotime(date("Y-m-d") . " 21:00");
                        }
                    } else {
                        $date = date("w");
                        if (in_array($date, [0, 5, 6])) {
                            if (!(date("H:i") > "20:00" && date("H:i") < "21:00")) {
                                $this->jsonResult('', 0, '未成年用户，仅可在周五、周六、周日和法定节假日每日20时至21时进行游戏，其他时间均无法进行游戏 ');
                            } else {
                                $offline_time = strtotime(date("Y-m-d") . " 21:00");
                            }
                        } else {
                            $this->jsonResult('', 0, '未成年用户，仅可在周五、周六、周日和法定节假日每日20时至21时进行游戏，其他时间均无法进行游戏 ');
                        }
                    }
                }
            }
        }

        $logininfoModel = new Logininfo();

        if (empty($device)) {

            $platform = model('Common/GameInfo')->where(['game_id' => $gameid])->value('platform');

            if ($platform == 1)
                $device = 3;      //IOS
            elseif ($platform == 0)
                $device = 2;      //安卓
            else
                $device = 1;      //PC
        }

        $gameAccountInfo = (new Subaccount())->where(['game_id' => $gameid, 'member_id' => $userinfo['id']])->find();
        $logData['sub_id'] = $gameAccountInfo['id'];
        $logData['userid'] = $userinfo['id'];
        $logData['gameid'] = $gameid;
        $logData['imeil'] = $imeil;
        $logData['device'] = $device;
        $logData['reg_time'] = $userinfo['reg_time'];
        $logData['fromflag'] = $userinfo['fromflag'];
        $logData['channel_id'] = $userinfo['channel_id'];
        $logData['login_time'] = NOW_TIMESTAMP;
        $logData['ip'] = request()->ip();

        //登录日志
        $logininfoModel->limitInsert($logData);

        //用户推广信息保存
        setChannelId($userinfo['id'], $userinfo['channel_id']);

        //更新最近登录时间
        if (!trim($userinfo['nickname'])) {
            $userNickName = random(3) . $userinfo['id'];
            $this->membersModel->where(['id' => $userinfo['id']])->update(['login_time' => NOW_TIMESTAMP, 'nickname' => $userNickName]);
        } else {
            $this->membersModel->where(['id' => $userinfo['id']])->update(['login_time' => NOW_TIMESTAMP]);
        }

        $this->jsonResult(['is_login' => 1, "offline_time" => $offline_time], 1, '有效token');
    }

    /**
     * 普通用户登录处理
     */
    public function third()
    {
        // if (temporary()) {
        //     $this->jsonResult('', 0, '禁止登录');
        // }
        $username = $this->input('username');             //用户名
        $imeil = trim($this->input('imeil'));            //手机imeil码
        $gameid = $this->input('gameid');               //游戏ID
        $device = $this->input('device');                //设备
        $channelId = $this->input('channel_id');           //当前登录的渠道ID
        $type_id = $this->input('type_id');           // 当前登录的渠道ID：1=微信, 2=qq, 3=抖音
        $oaid = $this->input('oaid', '');
        $dy_channel_id = $this->input('dy_channel_id', 0);

        // log_message(json_encode(input()), 'log', LOG_PATH . 'third/');
        //海外游戏ip限制
        if (in_array($gameid, config('haiwai_game_ids'))) {
            $res = (new IpLimit())->validateIp(request()->ip(), $username);
            if ($res != 1) {
                $this->jsonResult('', 0, '国内用户禁止登录');
            }
        }


        if (empty($username)) {
            $this->jsonResult('', 0, '用户名不能为空');
        }
        //游戏ID不能为空
        if (empty($gameid)) {
            $this->jsonResult('', 0, '游戏ID不能为空');
        }
        if (empty($device)) {
            $this->jsonResult('', 0, '没有注册设备来源');
        }
        if (empty($imeil)) {
            $this->jsonResult('', 0, '设备imeil异常');
        }

        if (empty($channelId)) {
            $this->jsonResult('', 0, '渠道不能为空');
        }

        //判断渠道
        $channelInfo = (new Channel)->where(['status' => 1, 'id' => $channelId])->find();
        if (!$channelInfo) {
            $this->jsonResult('', 0, '渠道信息不存在');
        } else if ($channelInfo['level'] <> 3) {
            $this->jsonResult('', 0, '非推广员渠道,不能进行推广,请联系客服');
        }

        if (!$game = model('Common/Game')->where(['id' => $gameid])->find()) {
            $this->jsonResult('', 0, '游戏信息不存在');
        }
        if ($game['cooperation_status'] == 3) {
            $this->jsonResult('', 0, '游戏已下架不存在');
        }


        // 渠道禁止判断
        if (isFrozenOption($channelId, $gameid, 'member_login')) {
            $this->jsonResult('', 0, '您所在渠道已经被禁止登录此游戏!! ');
        }

        $fromflag = 1;
        if (!in_array($type_id, [1, 2, 3])) {
            $this->jsonResult('', 0, '请求类型有误!');
        }
        if ($type_id == 1) { // 微信
            $userinfo = $this->membersModel->where(['wx_uid' => $username])->find();
        } else if ($type_id == 2) { // qq
            $userinfo = $this->membersModel->where(['wx_uid' => $username])->find();
        } else if ($type_id == 3) { // 抖音
            $userinfo = $this->membersModel->where(['dy_uid' => $username])->find();
            $fromflag = 3;
        }


        if ($userinfo) {
            if (1 == $userinfo['flag']) {
                $this->jsonResult('', 0, '您的账号已被冻结，请联系客服QQ：' . CUSTOMER_SERVICE_QQ);
            }
            $channelId = $userinfo['channel_id'];

            //用户登录公共部分处理
            $this->loginProcess($userinfo, $gameid, $imeil, $device, $channelId);
        } else {

            $username_third = strtolower(random(11));
            while ($this->membersModel->field('id')->where(['username' => $username_third])->find()) {
                $username_third = strtolower(random(11));
            }

            $data = [
                'username' => $username_third,
                'password' => auth_code('a12345fewdcv2', "ENCODE", Env::get($this->aesKey)),
                'device' => $this->input('device'),
                'gameid' => $gameid,
                'imeil' => $imeil,
                'channel_id' => $this->input('channel_id'),
                'deviceinfo' => $this->input('deviceinfo'),
                'mobile' => '',
                'is_local' => 0,
                'reg_time' => time(),
                'login_time' => time(),
                'ip' => request()->ip(),
                'nickname' => random(8),
                'fromflag' => $fromflag
            ];
            if ($type_id == 1) { // 微信
                $data['wx_uid'] = $username;
            } else if ($type_id == 2) { // qq
                $data['qq_uid'] = $username;
            } else if ($type_id == 3) { // 微信
                $data['dy_uid'] = $username;
            }
            // 启动事务
            Db::startTrans();

            try {
                $member_id = $this->membersModel->insertGetId($data);

                if ($member_id) {
                    // 插入玩家历史记录
                    Db::table('cy_member_history')->insert([
                        'userid' => $member_id,
                        'password' => $data['password'],
                        'mobile' => $data['mobile'],
                        'ip' => request()->ip(),
                        'create_time' => time()
                    ]);

                    $realname = '';
                    $idcard = '';

                    //插入用户扩展记录
                    Db::table('cy_memberstwo')->insert([
                        'userid' => $member_id,
                        'username' => $data['username'],
                        'realname' => $realname,
                        'idcard' => $idcard,
                        'create_time' => time()
                    ]);

                    //绑定抖音channel 和 平台内的channel
                    // $dy_channel_info = Db::table("mw_dy_channel_rel")
                    //     ->where("channel_id", $data['channel_id'])
                    //     ->where("dy_channel_id", $dy_channel_id)->find();
                    // if (empty($dy_channel_info)) {
                    //     $dy_channel_rel = [
                    //         'channel_id' => $data['channel_id'],
                    //         'dy_channel_id' => $dy_channel_id,
                    //         'create_time' => NOW_TIMESTAMP,
                    //         'update_time' => NOW_TIMESTAMP,
                    //     ];
                    //     Db::table("mw_dy_channel_rel")->insert($dy_channel_rel);
                    // }
                    // 提交事务
                    Db::commit();
                    // 登录处理
                    $this->loginProcess(array_merge($data, ['id' => $member_id, 'gameid' => $gameid, 'oaid' => $oaid]), $gameid, $imeil, $device, $channelId);
                } else {
                    throw new Exception("注册失败!");
                }

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();

                $this->jsonResult('', 0, '注册失败!' . $e->getMessage());
            }
        }
        exit;
    }
}
