<?php
/**
 * 发送手机短信
 */
namespace app\api\controller\v1;

use app\api\controller\Api;
use app\common\model\Members;
use app\common\model\MailLog;
use app\common\library\Sms;

class SendSms extends Api
{
    public function _initialize()
    {
        //不是通过ajax请求的需要进行数据验签
        if(!$this->request->isAjax())
        {
            parent::_initialize();
        }
    }

    /**
     * 用户注册时，发送验证码
     */
    public function index()
    {
        $membersModel   = new Members;
        $mailLogModel   = new MailLog;
        $sms            = new Sms;

        $mobile = $this->input('mobile');
        $action = $this->input('action');     //操作类型
        if (isset($this->input['captcha'], $this->input['random']) && $this->input['captcha'] && $this->input['random']) {
            $captcha = new \app\common\library\Captcha();
            if (!$captcha->check($this->input['captcha'], $this->input['random'])) {
                $this->jsonResult('', 0, '图形验证码错误', 'json');
            }
        }

        switch($action) {
            case 'login': // 手机验证码登录
                if ( empty($mobile) ) {
                    $this->jsonResult('',0,'手机号码不能为空');
                } elseif (empty($membersModel->where(['username'=>$mobile])->find()) ) {
                    $this->jsonResult('',0,'您的手机号尚未注册，请先去注册');
                }
                break;
            case 'register': // 注册
                if(isVirtualMobile($mobile)){
                    $this->jsonResult('', 0, '禁止使用虚拟手机号！');
                }
                if ( empty($mobile) ) {
                    $this->jsonResult('' ,0, '手机号码不能为空');
                } elseif ( ! empty($membersModel->where(['username'=>$mobile])->find()) ) {
                    $this->jsonResult('', 0, '您的手机号已经注册，请直接登录');
                }
                break;
            case 'password_find': // 找回密码
                $member_id = session('to_reset');
                if($member_id)
                {
                    $memberInfo = $membersModel->where(['id'=>$member_id])->find();
                    if ( empty($memberInfo) || empty($memberInfo['mobile']) ) {
                        $this->jsonResult('',0,'用户不存在或被冻结');
                    } else{
                        $mobile = $memberInfo['mobile'];
                    }
                } else{
                    $this->jsonResult('',0,'用户不存在或被冻结');
                }   
                break;
            default:
                $this->jsonResult('',0,'非法操作');
                break;
        }

        $result = $sms->sendCode($mobile);
        if ( $result['status'] ) {
            $mailLogModel->insert(['content'=>$sms->_validate_content, 'target'=>$mobile, 'create_time'=>time(), 'client_ip'=>request()->ip(), 'type'=>'sms']);
            $this->jsonResult('', 1, '发送成功，请查收');
        } else {
            $this->jsonResult('', 0, $result['msg']);
        }
    }
}
