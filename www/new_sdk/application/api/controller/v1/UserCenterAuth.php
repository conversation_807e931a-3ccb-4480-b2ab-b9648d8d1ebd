<?php
/**
 * Copyrght (C) 2018 Baidu, Inc. All Rights Reserved.
 */

/**
 * 用户中心通用认证
 */

namespace app\api\controller\v1;

use app\api\controller\Api;
use think\Cache;
use think\Env;
use think\Session;

class UserCenterAuth extends Api
{
    protected $memberModel;
    protected $uid;
    protected $game_id;
    protected $username;
    protected $userid;


    public function _initialize()
    {

        $token = $this->request->get('token');
        if (empty($token) || !Cache::store('default')->get($token)) {
            $this->error('请先登录!');
        }
        list($userid, $username, $gameid,$sub_username,$token_random) = explode('|', auth_code($token, "DECODE", Env::get('auth_key')));
        
        $redisTokenRandom = Cache::store('default')->get('token|sdk|'.$userid.'|'.$gameid);
        
        //redis中玩家登录安全控制的随机码不存在时
        if ( !$redisTokenRandom ) {
            
            Cache::store('default')->rm($token);
            
            $this->error('token随机码不存在,请重新登录');
        }
        //两个token随机码不同时
        elseif($redisTokenRandom!=$token_random){
            
            Cache::store('default')->rm($token);
            
            $this->error('token随机码错误,请重新登录');
        }

        $this->userid   = $userid;
        $this->username = $username;
        $this->game_id  = $gameid;
        Session::set('username', $this->username);
        Session::set('game_id', $this->game_id);
        Session::set('userid', $this->userid);
        Session::set('sub_username', $sub_username);
    }


    public function index()
    {
        $this->redirect(url('v1.user_center/index'));
    }

    public function identity()
    {
        $this->redirect(url('v1.identity/index'));
    }

//    public function isVerified()
//    {
//        $membersTwoModel = new MembersTwo();
//        $membersTwoInfo  = $membersTwoModel->where(['userid' => $this->userid])->find();
//        if (empty($membersTwoInfo) || empty($membersTwoInfo['realname']) || empty($membersTwoInfo['idcard'])) {
//            $isVerified = 0;
//        } else if ((-1 == $membersTwoInfo['realname']) && (-1 == $membersTwoInfo['idcard'])) {
//            $isVerified = 0;
//        } else {
//            $isVerified = 1;
//        }
//        if ($isVerified) {
//            $this->result("", 1, '已经名验证成功', 'json');
//        } else {
//            $this->result("", 0, '未实名验证', 'json');
//        }
//    }

}