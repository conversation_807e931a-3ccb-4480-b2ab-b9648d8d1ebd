<?php
/**
 * 用户CP服务端登录验证(SDK和聚合全都使用同一个接口)
 */
namespace app\api\controller\v1;

use think\Cache;
use app\api\controller\Api;
use think\Config;
use think\Env;

class Helper extends Api
{
    public function _initialize()
    {
        self::initDevice();
    }

    /**
     * 校验Token 是否有效
     */
    public function checkToken()
    {
        if (request()->isPost()) {
            // $token = input('post.token');
            $token = $_POST['token'];
            $username = input('post.username');
            
            // 空格转换为+
            $token = str_replace(" ", "+", $token);
            // 检查字符串中是否包含反斜线
            if (strpos($token, '\\') !== false) {
                // 如果包含反斜线，则使用 stripslashes
                $token = stripslashes($token);
            }
            log_message($username . "====" . $token, 'log', LOG_PATH . 'Helper/checkToken/');

            $result = $this->validate(
                [
                    'token'     => $token,
                    'username'  => $username
                ],
                [
                    ['token', 'require', 'token不能为空'],
                    ['username', 'require', 'username不能为空']
                ]);

            if (true !== $result) {
                $this->result('', 0, $result, 'json');
            }

            $r = Cache::store('redis')->get($token);
            if ( !$r ) {
                log_message('验证不通过的参数'.$username."====".$token,'log',LOG_PATH . 'Helper/checkToken/');
                $this->result('',0,'无效token','json');
            }

            if(!auth_code($token, "DECODE", Env::get($this->authKey))){
                $this->jsonResult('', -135, '登陆异常, 请重新登录！', 'json');
            }
            //聚合渠道时,$sub_username对应的值是mg_username
            list($userid2, $username2, $gameid2,$sub_username,$token_random,$type) = explode('|', auth_code($token,"DECODE", Env::get($this->authKey)));
            $redisTokenRandom = Cache::store('redis')->get('token|'.$type.'|'.$userid2.'|'.$gameid2);
            //redis中玩家登录安全控制的随机码不存在时
            if ( !$redisTokenRandom ) {

                Cache::store('redis')->rm($token);

                $this->result('',0,'token随机码不存在','json');
            }
            //两个token随机码不同时
            elseif($redisTokenRandom!=$token_random){

                Cache::store('redis')->rm($token);

                $this->result('',0,'token随机码错误','json');
            }


            if (strtolower($sub_username) != strtolower($username)) {
                $this->result('',0,'username错误','json');
            }

            $this->result('',1,'有效token','json');
        }
        else{
            $this->result('',0,'请认真查看文档，使用正确的请求方式','json');
        }
    }

}
