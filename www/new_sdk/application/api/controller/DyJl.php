<?php
/**
 *巨量手动授权获取access_token回调
 ***/
namespace app\api\controller;

use think\Controller;
use think\Env;
use think\Request;
use think\Cache;

class DyJl extends Controller
{
    private $appid = "";
    private $secret = "";
    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->appid = Env::get("juliang.app_id");
        $this->secret = Env::get("juliang.secret");
    }

    //分包
    public function createPackage(){
        $url = "https://ad.oceanengine.com/open_api/2/tools/app_management/extend_package/create_v2/";
        $params = [];
        $redis = Cache::store('default');
        $token = $redis->get('juliang_access_token');
        return $token;
        $params['account_id'] = ****************;
        $params['account_type'] = 'AD';
        $params['mode'] = 'Manual';
        $params['package_id'] = '0d46029b934b70c75a060ba858789668b18ab568';
        $params['channel_count'] = 1;
        $params['channel_list'][] = array('channel_id'=>'123456','remark'=>'测试接口');
        $result = get_http_response($url,json_encode($params),'post', array('Content-Type:application/json','Access-Token:'.$token));
        $result = json_decode($result,true);
        if (isset($result['code']) && $result['code'] == 0){
            return "分包成功";
        }else{
            return "分包失败";
        }
    }
    //查询应用信息 用APP_ID获取package_id 用于分包ID
    public function getAppInfo(){
        $url = " https://api.oceanengine.com/open_api/2/tools/app_management/android_app/list/";
        $params = [];
        $redis = Cache::store('default');
        $token = $redis->get('juliang_access_token');
        $params['account_id'] = ****************;
        $params['account_type'] = 'AD';
        $params['filtering'] = ['search_key'=>""];
        $result = get_http_response($url,json_encode($params),'get', array('Content-Type:application/json','Access-Token:'.$token));
        $result = json_decode($result,true);
        if (isset($result['code']) && $result['code'] == 0){

            return $result['data']['list'];
        }else{
            return "分包失败";
        }
        return $token;
    }

    //开放平台配置
    public function callback(Request $request)
    {
        $params = [];
        $params['auth_code'] = $_GET['auth_code'];
        $params['app_id'] = $this->appid;
        $params['secret'] = $this->secret;
        $url = "https://api.oceanengine.com/open_api/oauth2/access_token/";
        $redis = Cache::store('default');
        $result = get_http_response($url,$params,'post', array('Content-Type' => 'application/json'));
        $result = json_decode($result,true);
        if (isset($result['code']) && $result['code'] == 0){
            $redis->set('juliang_access_token', $result['data']['access_token'], 86000);
            $redis->set('juliang_refresh_token', $result['data']['refresh_token'], 600000);
            return "授权成功";
        }else{
            return "授权失败";
        }

    }

    //刷新token
    public function refreshToken(){
        $params = [];
        $redis = Cache::store('default');
        $params['refresh_token'] = $redis->get('juliang_refresh_token');
        $params['app_id'] = $this->appid;
        $params['secret'] = $this->secret;
        $url = "https://ad.oceanengine.com/open_api/oauth2/refresh_token/";

        $result = get_http_response($url,$params,'post', array('Content-Type' => 'application/json'));
        $result = json_decode($result,true);
        if (isset($result['code']) && $result['code'] == 0){
            $redis->set('juliang_access_token', $result['data']['access_token'], 86000);
            $redis->set('juliang_refresh_token', $result['data']['refresh_token'], 600000);
            return "刷新成功";
        }else{
            return "刷新失败";
        }
    }

}
