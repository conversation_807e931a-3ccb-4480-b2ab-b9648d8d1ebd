<?php
namespace app\api\controller\v2;

use app\api\controller\Api;
use app\common\library\DyGameOpen as DyGameOpenLib;
use app\common\library\Sms;
use think\Db;
use think\Request;

// 抖音游戏开放平台
class DyGameOpen
{
    public function __construct()
    {
        $input = input();
        $url = request()->url();
        log_message('DyGameOpen.Input: '. $url.' : '.json_encode($input), 'log', LOG_PATH . 'DyGameOpen/');
    }

    public function getCommon(){
        return json(['err_no' => 0, 'err_msg' => ""]);
        // return $this->success();
    }

    private function success($data = "", $code = 0, $msg = ""){
        $result = ['err_no' => $code, 'err_msg' => $msg];
        if($data){
            $result['data'] = $data;
        }
        log_message('DyGameOpen.result.success: '. json_encode($result), 'log', LOG_PATH . 'DyGameOpen/');
        return json($result);
    }
    private function fail($code, $msg){

        $result = ['err_no' => $code, 'err_msg' => $msg];
        log_message('DyGameOpen.result.fail: '. json_encode($result), 'log', LOG_PATH . 'DyGameOpen/');
        return json($result);
    }



    //
    private function getGameRole($string, $type=1){
        // $type=1; $string=open_id; 授权登录，获取游戏的账户下所有角色
        // $type=1; $string=phone_num; 手机验证码登录，获取游戏的账户下最新的角色


        $game_id = '10'; // TODO: 待完善

        $roleList = Db::table('cy_members')->alias('cm')
            ->join('nw_member_game_server nmgs', 'cm.id = nmgs.member_id', 'left')
            ->where(['cm.dy_uid' => $string, 'nmgs.game_id' => $game_id])->field('nmgs.mgs_id, nmgs.rolename, nmgs.game_id, cm.mobile')->select();
        // dump($roleList);
        if(!$roleList){
            return $this->fail(4014540, '用户游戏账号不存在！');
        }

        // $this->getHashMobile($open_id); // 新注册用户 获取 加密手机号

        $newList = [];
        foreach($roleList as $v){
            $newList[] = [
                'game_user_id' => $v['mgs_id'],
                'game_user_name' => $v['rolename'],
                'mask_account_number' => mobileObfuscation($v['mobile']),
            ];
        }

        $data = [
            'game_users' => $newList
        ];
    }

    /**
     * 3.4.1 抖音授权登录
     *
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getAuthLogin(){
        $app_id = input('app_id', '');
        $auth_code = input('auth_code', '');

        if(empty($app_id) || empty($auth_code)){
            return $this->fail(4001015, '请求参数有误！');
        }

        $dy = new DyGameOpenLib($app_id, 'app_id');
        $dy_config = $dy->dy_config;
        $dyResult = $dy->getAccessToken($auth_code);

        if($dyResult['code'] != 100){
            return $this->fail(-100, $dyResult['msg']);
        }

        $memberInfo = Db::table('cy_members')->where(['dy_uid' => $dyResult['data']['open_id'], 'gameid' => $dy_config['game_id']])->field('id, username')->find();
        if(!$memberInfo){
            return $this->fail(4014540, '用户游戏账号不存在！');
        }
        $data = [
            'game_users' => [
                [
                    'game_user_id' => $memberInfo['username'],
                    // 'game_user_name' => $memberInfo['rolename'],
                    // 'mask_account_number' => mobileObfuscation($memberInfo['mobile']),
                ]
            ]
        ];

        // $this->getHashMobile($open_id); // 新注册用户 获取 加密手机号

        return $this->success($data);
    }

    /**
     * 3.4.2 抖音授权登录根据 code 获取加密手机号
     *  - 用于抖音新注册用户。
     *  - 目前没有什么用处，暂不获取，不然还需要进行存储。
     *
     * @param $open_id
     *
     * @return \think\response\Json|void
     */
    private function getHashMobile($open_id, $app_id){
        $dy = new DyGameOpenLib($app_id, 'app_id');
        $result = $dy->getHashMobile($open_id);
        if($result['code'] != 100){
            return $this->fail(-100, $result['msg']);
        }

        $hash_mobile = $result['data'];
        // TODO: 存储加密手机号
    }

    /**
     * 3.4.3 输入手机号发送验证码
     *
     * @return \think\response\Json
     */
    public function getMobileCode(){
        $app_id = input('app_id', '');
        $phone_num = input('phone_num', '');

        if(empty($app_id) || empty($phone_num)){
            return $this->fail(4014456, '请求参数有误！');
        }

        if(preg_match('/^1[3-9]\d{9}$/', $phone_num) !== 1){
            return $this->fail(4014456, '手机号格式有误！');
        }

        $sms = new Sms;
        $sms_result = $sms->sendCode($phone_num);
        if(!$sms_result['status']){
            return $this->fail(4014458, $sms_result['msg']);
        }

        Db::table('cy_mail_log')->insert([
            'content' => $sms->_validate_content,
            'target' => $phone_num,
            'create_time' => time(),
            'client_ip' => request()->ip(),
            'type' => 'sms'
        ]);

        return $this->success();
    }

    /**
     * 3.4.4 输入手机号和验证码进行校验(登录|获取绑定过的角色信息)
     *
     * @return \think\response\Json
     */
    public function getMobileCheck(){
        $app_id = input('app_id', '');
        $phone_num = input('phone_num', '');
        $captcha = input('captcha', '');

        if(empty($app_id) || empty($phone_num) || empty($captcha)){
            return $this->fail(4014457, '请求参数有误！');
        }

        $sms_result = (new Sms)->checkCode($phone_num, $captcha);
        if(!$sms_result['status']){
            return $this->fail(4014457, $sms_result['msg']);
        }

        $dy = new DyGameOpenLib($app_id, 'app_id');
        $dy_config = $dy->dy_config;

        $memberInfo = Db::table('cy_members')->where(['mobile' => $phone_num, 'gameid' => $dy_config['game_id']])->field('id,username')->order('id desc')->find();
        if(!$memberInfo){
            return $this->fail(4014540, '用户游戏账号不存在！');
        }

        $info = [
            'game_user_id' => $memberInfo['username'],
            // 'game_user_name' => $roleInfo['rolename'],
            // 'mask_account_number' => mobileObfuscation($phone_num),
        ];

        return $this->success($info);
    }

    /**
     * 3.4.8 获取角色列表
     *
     *  - 请求范例：{"app_id":721109,"users":null,"game_user_ids":["c6zk0ix18lb"], "open_id":"_0003zW-CmybIK-fFQr7LgYR8DLJXsQgatI0"}
     *
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getRoleList(){

        $app_id = input('app_id', '');
        $game_user_ids = input('game_user_ids/a', []); // 游戏账号 ID 列表
        $open_id = input('open_id', '');
        if(empty($app_id) || empty($open_id)){
            return $this->fail(-100, '请求参数有误！');
        }

        $dy = new DyGameOpenLib($app_id, 'app_id');
        $dy_config = $dy->dy_config;

        $roleInfo = Db::table('cy_members')->alias('cm')
            ->join('nw_member_game_server nmgs', 'cm.id=nmgs.member_id', 'left')
            ->where(['cm.dy_uid' => $open_id, 'nmgs.game_id' => $dy_config['game_id']])
            ->field('cm.username, nmgs.roleid, nmgs.rolename, nmgs.rolelevel, nmgs.serverid, nmgs.servername')
            ->select();

        $head_url = Db::table('cy_gameinfo')->where(['game_id' => $dy_config['game_id']])->value('mobileicon');
        $newList = [];
        foreach($roleInfo as $v){
            $newList[] = [
                "game_user_id" => $v['username'],
                "role_id" => $v['roleid'],
                "role_name" => $v['rolename'],
                "level" => $v['rolelevel'],
                "region_id" => $v['serverid'],
                "region_name" => $v['servername'],
                "avatar_url" => STATIC_DOMAIN.$head_url,

            ];
        }

        return $this->success([
            'roles' => $newList
        ]);
    }

    /**
     * 3.4.9 抖音游戏绑定角色/解绑角色/绑定游戏账号通知
     *  - 用户游戏绑定抖音：{"app_id":721109,"game_user_id":"c6zk0ix18lb","open_id":"_0003zW-CmybIK-fFQr7LgYR8DLJXsQgatI0","role_id":"5","allied_id":"","bind":1,"tm":1748050777,"bind_type":0}
     *
     * @return void
     */
    public function handleEventNotice()
    {
        // $input = Request::instance()->only(['app_id', 'game_user_id', 'role_id', 'open_id', 'allied_id', 'bind', 'bind_type', 'tm', 'changes']);
        return $this->success();
    }

}