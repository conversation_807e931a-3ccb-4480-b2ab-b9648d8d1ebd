<?php

namespace app\api\controller\v2;

use app\api\controller\Api;


class Config extends Api
{
    // 第三方登录参数配置
    public function getConfig()
    {
        // 目前配置项有问题，改为写到代码里
        // $info = model('LoginConfig')->field('game_id,wx_type,wx_apppid,wx_secret,qq_type,qq_apppid,qq_secret')->cache('config:getconfig:' . $this->appInfo['gameid'], 180)
        //     ->whereRaw(sprintf('FIND_IN_SET(%s,game_id)', $this->appInfo['gameid']))->find();
        // if (!$info) {
        //     $info = [
        //         'game_id' => '',
        //         'wx_type' => 0,
        //         'wx_apppid' => '',
        //         'wx_secret' => '',
        //         'qq_type' => 0,
        //         'qq_apppid' => '',
        //         'qq_secret' => '',
        //         'username_type' => 0, // 一键注册状态 = 0=关闭、1=开启
        //     ];
        // }

        $info = [
            'id' => 1,
            'game_id' => $this->input('gameid'),
            'username_type' => 1,
            'wx_type' => 0,
            'qq_type' => 0,
        ];
        if(($this->input('gameid') == 344 && $this->input('version') >= 'v3.5.0') || ($this->input('gameid') == 345 && $this->input('version') >= 'v3.4.9')){
            $info['username_type'] = 0;
        }
        $this->jsonResult($info, 1, '获取成功');
    }

}