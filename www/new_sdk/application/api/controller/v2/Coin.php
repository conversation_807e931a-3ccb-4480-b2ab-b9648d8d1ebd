<?php

namespace app\api\controller\v2;

use app\api\controller\Api;
use app\common\model\Members;
use app\common\model\PayType;
use app\common\model\Payrule;
use app\common\model\Game;
use app\service\MemberCoinService;
use think\Cache;
use think\Config;
use think\Model;

class Coin extends Api
{
    /**
     * 支付方式
     */
    public function payType()
    {
        // return $this->jsonResult('', 0, '平台币充值维护中');
        $checkResult = $this->validate($this->input,
            [
                ['version', 'require', '版本号不能为空'],
            ]
        );
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }
        $game_id = $this->input('gameid');                   //游戏ID
        $version = $this->input('version');                  //sdk版本号   [v3.0.2 参数]
        
        if($this->device == 4){
            $payTypeList = [
                ["paytype" => "wx_h5", "payname" => "微信支付", "pay_scene" => "wx_h5", "sort" => 1],
                ["paytype" => "ali_h5", "payname" => "支付宝", "pay_scene" => "ali_h5", "sort" => 2],
            ];
            $this->jsonResult($payTypeList, 1, '成功!');
        }
        if ($version > config('pay_handle_version')) {
            $payTypeList = [
                ["paytype" => "ybzf_wxmp_h5", "payname" => "微信支付", "pay_scene" => "wx_h5", "sort" => 1],
                ["paytype" => "zfb", "payname" => "支付宝(快捷)", "pay_scene" => "ali_app", "sort" => 2],
                ["paytype" => "old-zfb-wap", "payname" => "支付宝", "pay_scene" => "ali_h5", "sort" => 3],
            ];
            $this->jsonResult($payTypeList, 1, '成功!!');
        }

        $payTypeModel = new PayType;
        $sdkModel = model('common/AndroidSdk');

        // TODO：萌战无双游戏微信支付申请-01.02
        if ($game_id == Config::get('gb_game')) {
            $this->jsonResult([["paytype" => "ybzf_wxmp_h5", "payname" => "微信"]], 1, '成功');
        }

        $denyPayType = ['ptb', 'coinpay'];
        $android_sdk_id = $sdkModel->where(['version' => $version])->value('id');
        if (empty($android_sdk_id)) {
            $this->jsonResult('', 0, 'sdk版本信息不存在');
        }

        $arrPayType = $payTypeModel->field('paytype,payname')->where(['status' => 1, 'paytype' => ['not in', $denyPayType], 'used_sdk_id' => ['like', '%,' . $android_sdk_id . ',%']])->order('sort asc')->select();
        $this->jsonResult($arrPayType, 1, '成功!!!');
    }



    public function pay()
    {
        // return $this->jsonResult('', 0, '平台币充值维护中');

        $data['paytype'] = $this->input['paytype'];             //支付类型
        $data['amount'] = $this->input['amount'];                 //支付金额
        $data['accountType'] = $this->input['accountType']; // 账号类型:1=当前账号, 2=其他账号
        $data['username'] = $this->input['username'];
        $data['ip'] = request()->ip();
        $data['version'] = $this->input['version'];

        $checkResult = $this->validate($data,
            [
                ['paytype', 'require', '支付类型不能为空'],
                ['amount', 'require|integer|egt:0', '支付金额不能为空|支付金额必须为整型|支付金额大于等于0的整型'],
                ['accountType', 'require|in:1,2', '账号类型不能为空|账号类型错误'],
                ['username', 'requireIf:accountType,2', '账号必填'],
                ['version', 'require', '版本号必填'],
            ]);

        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }
       // $data['type'] = $this->input['type'];                   //类型 1充值平台币 2 充值游戏

        // TODO：萌战无双游戏微信支付申请-01.02
        if ($this->input['gameid'] == Config::get('gb_game')) {
            $this->jsonResult('', 0, '接入中...');
        }

        // 封禁IOS老版本的支付方式
        if(version_compare($this->input('version'), 'v3.2.6') < 1){
            if(in_array($this->input('paytype'), ['ybzf_wxmp_h5', 'zfb']) && in_array($this->input('gameid'), ['20', '245', '248', '258', '259', '261', '263'])){
                $this->jsonResult('', 0, "请下载最新游戏包，或选择【支付宝（旧）】进行支付。");
            }
        }

        if ($data['accountType'] == 2) {
            if (!$user = (new Members())->getUseridByName($data)) {
                $this->jsonResult('', 0, '账号不存在');
            }
            $data['member_id'] = $user['id'];
        } else {
            $data['member_id'] = $this->input['userid'];
            // $data['username'] = $this->input['username'];
            if (!$user = (new Members())->getUseridById($data)) {
                $this->jsonResult('', 0, '账号不存在');
            }
        }
        $data['place_user_id'] = $this->input['userid'];
        $data['gameid'] = $this->input['gameid']; // 微信H5支付时需要用到
        $data['device'] = $this->device;

        $result = (new MemberCoinService())->payCoin($data);

        if ($result['code'] == 1) {
            $this->jsonResult($result['data'], 1, (isset($result['data']['pay_status']) && $result['data']['pay_status'] == 1) ? '付款成功' : '订单生成成功', true);
        } else {
            $this->jsonResult('', 0, $result['msg']);
        }

    }
}
