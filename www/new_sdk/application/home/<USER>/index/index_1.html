{extend name="layout/base" /}{block name="detail_css"}
<style>

 
    .left-nav {
        padding-top: 0;
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        z-index: 999;
        width: 221px;
        overflow-x: hidden;
    }

    .layui-nav-tree {
        width: 100%;
        padding: 0;
    }

    .layui-tab-content .layui-nav .sub-menu a {
        color: rgba(255, 255, 255, .7);
    }


    /* 切换tab时的效果1 */

    .layui-tab-title .layui-this {
        color: #000;
    }

    .page-content {
        position: absolute;
        top: 46px;
        right: 0px;
        bottom: 42px;
        left: 221px;
        z-index: 1;
        overflow: hidden;
    }


    .layui-tab-title li:hover {
        background: #ddd;
    }

    .sub-menu dd:hover {
        background: #009688;
    }

    .footer {
        color: black;
        text-align: center;
        background: #ffffff;
    }



    /*悬浮球*/

    .floating_ball_wrap {
        position: fixed;
        right: 10px;
        top: 180px;
        opacity: 0.7;
        z-index: 999999999;
    }

    .floating_ball_wrap #floating_ball {
        display: inline-block;
        width: 50px;
        height: 50px;
        background: green;
        border-radius: 50%;
        text-align: center;
        line-height: 50px;
        text-decoration: none;
        color: white;
    }

    .floating_ball_wrap #content_wrap {
        width: 250px;
        height: 250px;
        overflow: auto;
        text-align: center;
        background-color: #ddd;
        position: fixed;
        right: 80px;
        top: 160px;
    }

    .floating_ball_wrap .title {
        background: #2f4050;
        color: white;
        margin: 0;
        padding: 10px;
        display: flex;
        justify-content: space-between;
    }

    .floating_ball_wrap .title span {
        font-size: 14px;
    }

    .floating_ball_wrap .title span:nth-of-type(2) {
        font-size: 12px;
    }

    .floating_ball_wrap .content section {
        margin: 10px;
        background: #f3f3f4;
        word-wrap: break-word;
        word-break: normal;
    }

    .red_dot {
        display: inline-block;
        width: 5px;
        height: 5px;
        background: red;
        border-radius: 50%;
        position: absolute;
        top: 10px;
        right: 10px;
    }

    .show-grand-money-apply-list{
       background-color:#009688;
       color:white;
    }

    /* 隐藏小箭头 */
    .layui-nav-tree .layui-nav-more {
        top: 20px;
        right: 10px;
        display: none !important;
    }

    .layui-nav .layui-nav-mored {
        top: 14px;
    }

    .iconfont.nav_right{
        position:absolute;
        top: 0px;
        right: 10px;
    }

    .layui-tab {
        margin: 0;
        text-align: left!important;
    }

    .sub-menu{
        display: none;
    }

    .active a {
        background: #009688;
        color: white;
    }


   .layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this>a, .layui-nav-tree .layui-this>a:hover {
        background-color: #393D49;
        color: #fff;
    }
  
  .close-msg{
    float:right;
    margin-right:5px;
    
  }

  .close-msg:hover{
      cursor: pointer;
  }

  .show-grand-money-apply-list:hover{
    cursor: pointer;
  }
</style>
{/block} {block name="content"}
<!-- 顶部开始 -->
<div class="container">
    <div class="logo">
        <a href="/">祈盟文化</a>
    </div>

    <div class="left_open">
        <i title="展开左侧栏" class="iconfont">&#xe699;</i>
    </div>

    <ul class="layui-nav right" lay-filter="">
        <li class="layui-nav-item to-index">
            <a href="{:url('SdkCache/clearRedisCache')}">清除缓存</a>
        </li>

        <li class="layui-nav-item">
            <a href="javascript:;">{php}echo session('USERNAME');{/php}</a>
            <dl class="layui-nav-child">
                <!-- 二级菜单 -->
                <dd>
                    <a href="{:url('user/modifyPassword')}">修改密码</a>
                </dd>
                <dd>
                    <a href="{:url('login/logout')}">退出</a>
                </dd>
            </dl>
        </li>

        <li class="layui-nav-item to-index">
            <a href="/">首页</a>
        </li>
    </ul>

</div>


<div class="layui-tab layui-tab-brief">

    <ul class="layui-tab-title" id="tabChoose">
        <li class="layui-this">SDK管理后台</li>
        <li>运营平台及官网后台</li>
        <li>系统管理</li>
    </ul>

    <div class="layui-tab-content">

        <div class="layui-tab-item layui-show" id="tab1-container">
            <div class="left-nav layui-bg-black" style="top:87px;">
                <div id="side-nav">
                    <dl class="layui-nav layui-nav-tree layui-inline" id="nav2">

                    </dl>
                </div>
            </div>
        </div>
    </div>


    <div class="page-content" style="top:87px;">
        <div class="layui-tab tab" lay-filter="xbs_tab" lay-allowclose="false">

            <ul class="layui-tab-title">
                <li>我的桌面</li>
            </ul>

            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <iframe src='' frameborder="0" scrolling="yes" class="x-iframe"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

{/block} {block name="detail_js"}
<script type="text/javascript" src="__STATIC__/js/link_to_wsocket.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">
    var WEB_SOCKET_URL = "{$Think.config.WEBSOCKET_WS_ADDR}";
    var WEB_SOCKET_TOKEN = "{:session_id()}";
    var isNewMesg = 0;
    var ERR_CONNECTION_TIMED_OUT_CODE = 1006;
    var BusinessActions = {};

    function loadWsBusiness() {

        //通过 AJAX 请求来获得并运行一个 JavaScript 文件
        $.getScript(" __STATIC__/js/wsbusiness.js?v={$Think.STATIC_VERSION}");
    }


    $(function () {

        loadWsBusiness();


        $(".sub-menu dd a").addClass("refreshThis");
        $(".sub-menu dd").on("click", function () {

            var url = $(this).children('a').attr('_href');
            var index = $('.sub-menu dd').index($(this));

            if ($(this).children('a').hasClass("refreshThis")) {
                $(this).children('a').removeClass("refreshThis");

                for (var i = 0; i < $('.x-iframe').length; i++) {
                    if ($('.x-iframe').eq(i).attr('tab-id') == index + 1) {
                        $('.x-iframe').eq(i)[0].contentWindow.location = url;
                    }
                };
                setTimeout(function () {
                    $(".sub-menu dd a").addClass("refreshThis");
                }, 300)
            } else {
                layer.msg("慢点！");
            }
        });

        // 悬浮球

        var olink = document.getElementById("floating_ball");
        var odiv = document.getElementById("content_wrap");
        var p_content = document.getElementsByClassName("content")[0];

        olink.onclick = function () {
            Show_Hidden(odiv);
            return false;
        }

    });



    var $dd = $("#nav2>dd");
    for (var i = 0; i < $dd.length; i++) {
        $dd[i].style.display = "none"
    }

    $dd.each(function (index, item) {
        if ($(item).hasClass("tab1")) {
            item.style.display = "block"
        }
    })


    $("#tabChoose").on("click", "li", function () {
        var i = $(this).index() + 1;
        for (var j = 0; j < $dd.length; j++) {
            $dd[j].style.display = "none";
        }
        $dd.each(function (index, item) {
            if ($(item).hasClass("tab" + i)) {
                item.style.display = "block";
            }
        })
    });


    function Show_Hidden(obj) {
        if (obj.style.display == "block") {
            obj.style.display = 'none';
        } else {
            obj.style.display = 'block';
        }
    }

    function toggleBtn(el, classA, classB, type) {
        $(el).click(function () {

            var isRead = document.getElementById("isRead");
            if (isRead.className == "red_dot") {
                $(isRead).removeClass("red_dot");
                isNewMesg == 0;
            }

            var that = $(this);
            if (type == "class") {
                if (that.children().hasClass(classA)) {
                    that.children().removeClass(classA).addClass(classB);
                } else {
                    that.children().removeClass(classB).addClass(classA);
                }
            }
        })
    }

    toggleBtn('#floating_ball', 'fa fa-close', 'fa fa-wechat', 'class');

    //推送点击打叉关闭
    $(".floating_ball_wrap #content_wrap .content").on("click","small.close-msg",function () { 
        $(this).parent().parent().parent().remove();
    })

</script>
{/block}