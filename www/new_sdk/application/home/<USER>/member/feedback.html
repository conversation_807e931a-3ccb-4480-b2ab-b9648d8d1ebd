{extend name="layout/content" /}
{block name="title"}
<title>意见反馈_祈盟文化</title>
{/block}
{block name="header"}
<style>
	.warp_public_nav{
		  border-bottom: 1px solid #EEEEEE;
	  }
	  .feedback_cont{
		  width: 1010px;
		  margin: auto;
		  
		  }
	  .text1{
		  color: #494949;
		  font-size: 16px;
		  margin: 20px 0;
	  }
	  .text2{
		  width: 740px;
		  font-size: 14px;
		  color: #B2B2B2;
		  line-height: 28px;
	  }
	   .text2 span{
		  color: #51a7ff; 
	   }
	   .feedback_form{
		   line-height: 28px;
	   }
	   .feedback_form>div {
	       overflow: hidden;
	   }
	   .feedback_form>div>span {
	       display: block;
	       width: 84px;
	       font-size: 14px;
	       color: #494949;
	       float: left;
		   margin-right: 20px;
	   }
	   .feedback_form span b {
	       color: #FC5351;
	   }
	  .erro_type,.erro_cont,.erro_qq{
	       font-size: 12px;
	       color: #FC5351;
	       margin-left: 105px;
		   display: none;
	   }
	   .type {
	       height: 66px;
		   margin-top: 50px;
	   }
	   .feedback_form textarea {
	       width: 884px;
	       height: 136px;
	       padding: 10px;
	       border: 1px solid #EEEEEE;
	       border-radius: 5px;
		   margin-top: 6px;
	   }
	   .erro_cont{
		   float: left;
	   }
	   .tip_cont {
	       float: right;
	   }
	   .tip_cont span{
		   color: #51a7ff;
	   }
	   .type input {
	       margin-left: 70px;
		       width: 20px;
		       height: 20px;
		       appearance: none;
		       -webkit-appearance: none;
		       position: relative;
		       top: -2px;
	   }
	   .radio_type:after {
	       content: '';
	       width: 13px;
	       height:13px;
	       text-align: center;
	       background: #eeeeee;
	       border-radius: 50%;
	       display: block;
	       position: absolute;
	       top: 11px;
	       left: 1px;
	   }
	   .radio_type:before {
	       content: '';
	       width: 13px;
	       height: 13px;
	       border: 1px solid #D2D2D2;
	       display: inline-block;
	       border-radius: 50%;
	       vertical-align: middle;
	   }
	   .radio_type:checked:before {
	       content: '';
	       width: 13px;
	       height: 13px;
	       border: 1px solid #51a7ff;
	       display: inline-block;
	       border-radius: 50%;
	       vertical-align: middle;
	   }
	   .radio_type:checked:after {
	       content: '';
	       width: 13px;
	       height: 13px;
	       text-align: center;
	       background: #51a7ff;
	       border-radius: 50%;
	       display: block;
	       position: absolute;
	      top: 11px;
	        left: 1px;
	   }
	   .type label {
	       line-height: 20px;
	       display: inline-block;
	       margin-left: 5px;
	       margin-right: 15px;
	       color: #777;
	   }
	   #radio1{
		   margin-left: 0px;
	   }
	   .upload_img {
	       margin-top: 30px;
	   }
	   .tip_uoload {
	       font-size: 12px;
	       color: #B2B2B2;
	       line-height: 53px;
	       margin-left: 102px;
	   }
	   .contact input {
	       width: 890px;
	       border: 1px solid #EEEEEE;
	       border-radius: 5px;
	       padding-left: 10px;
	   }
	   .contact {
	       height: 65px;
	   }
	   .feedback_cont  #btnSubmit {
	       width: 300px;
	       height: 44px;
	       color: #fff;
	       font-size: 17px;
	       background: #51a7ff;
	       border: none;
	       border-radius: 5px;
	       margin: auto;
		   margin: 30px 350px;
	   }
	   
	   #upBox{
	   	width: 900px;
	   	position: relative;
		margin-top: 8px;
		float: left;
	   }
	  #inputBox {
	      width: 150px;
	      height: 150px;
	      border: 1px solid #EEEEEE;
	      color: #B2B2B2;
	      position: relative;
	      text-align: center;
	      line-height: 150px;
	      overflow: hidden;
	      font-size: 50px;
	      bottom: 0px;
	      float: left;
		  cursor: pointer;
		  border-radius: 5px;
	  }
	   #inputBox input{
	   	width: 114%;
	   	height: 150px;
	   	opacity: 0;
	   	cursor: pointer;
	   	position: absolute;
	   	top: 0;
	   	left: -14%;
	   }
	
	   .imgContainer {
	       display: inline-block;
	       width: 150px;
	       height: 150px;
	       margin-right: 1%;
	       position: relative;
	       box-sizing: border-box;
	       float: left;
	   }
	   .imgContainer img{
	   	width: 100%;
	   	height: 150px;
	   	cursor: pointer;
	   }
	   .imgContainer p {
	       position: absolute;
	       top: -8px;
	       right: -6px;
	       width: 25px;
	       height: 25px;
	       background: #fff;
	       text-align: center;
	       line-height: 25px;
	       color: #494949;
	       font-size: 21px;
	       font-weight: bold;
	       cursor: pointer;
	       border-radius: 50%;
	       border: 1px solid #D2D2D2;
	   }
	   .imgContainer:hover p{
	   	display: block;
	   }
.footer ul {
    background: #494949 !important;
    color: #fff;
}
.footer ul a{
	color: #fff;
}
  </style>
{/block}
{block name="nav_title"}
<h1>意见反馈</h1>
{/block}
{block name="content"}

<div class="feedback_cont">
	<p class="text1">欢迎反馈问题，您的意见与建议就是我们的动力！</p>
	<p class="text2">我们会认真查阅您的反馈内容，并持续改进，在这里您可以提出任何在使用过程中遇到的问题，或发表自己的想法和建议如出现无法充值、充值不到账、游戏无法更新、无法注册登录等，可以先联系祈盟文化客服<span>QQ：{$kefu}</span></p>
	<form class="feedback_form" id="feedback_form"  enctype="multipart/form-data">
		<div class="type">
			<span class="">反馈类型 <b>*</b> ：</span>
			<input type="radio" name="type" value="1" id="radio1" class="radio_type" />Bug反馈
			<label for="radio1"></label>
			<input type="radio" name="type" value="2" id="radio2" class="radio_type" />游戏相关问题
			<label for="radio2"></label>
			<input type="radio" name="type" value="3" id="radio3" class="radio_type" />优化建议
			<label for="radio3"></label>
			<input type="radio" name="type" value="4" id="radio4" class="radio_type" />其他
			<label for="radio4"></label>
			<p class="erro_type">请选择反馈类型</p>
		</div>

		<div class="content">
			<span>反馈内容 <b>*</b> ：</span>
			<textarea id="content" name="content" maxlength="500" placeholder="请具体描述您的意见，我们将不断改进"></textarea>
			<div>
				<p class="erro_cont">请至少输入5个字</p>
				<p class="tip_cont">最少5个字，最多还可以输入<span id="contentwordage">500</span>个字</p>
			</div>
		</div>
		<div class="upload_img">
			<span>上传图片 ：</span>
			<!-- <div id="cupload"></div> -->
			<div id="upBox">
				<div id="imgBox">

				</div>
				<div id="inputBox"><input type="file" title="请选择图片" id="file" name="file1"  accept="image/png,image/jpg,image/JPEG" />+</div>
			</div>

			<p class="tip_uoload">选填，仅可上传jpg、png、的图片，最多可上传3张，每张不超过3M.</p>
		</div>
		<div class="contact">
			<span>联系QQ:</span>
			<input placeholder="选填，方便客服人员与您取得联系" class="qq" name="qq" />
			<p class="erro_qq">请输入正确的QQ号</p>
		</div>
		<input style="cursor:pointer" type="button" id="btnSubmit" value="提交" />
	</form>
</div>



{/block}

{block name="detail_js"}
<script type="text/javascript">
	imgUpload({
		inputId: 'file', //input框id
		imgBox: 'imgBox', //图片容器id
		buttonId: 'btnSubmit', //提交按钮id
		upUrl: '/member/ajaxFeedback', //提交地址
		data: 'file1', //参数名
		num: "3" //上传个数
	})
	
		var limitNum = 500;
		$('#content').keyup(function() {
			var remain = $(this).val().length;
		   var result = limitNum - remain;
			$('#contentwordage').html(result);
		});


var imgSrc = []; //图片路径
var imgFile = []; //文件流
var imgName = []; //图片名字
//选择图片
function imgUpload(obj) {
	var oInput = '#' + obj.inputId;
	var imgBox = '#' + obj.imgBox;
	var btn = '#' + obj.buttonId;
	$(oInput).on("change", function() {
		var fileImg = $(oInput)[0];
		var fileList = fileImg.files;
		var fileSize = fileImg.files[0].size;
		var size = fileSize / 1024; 
		var name = fileImg.files[0].name;
		var fileName = name.substring(name.lastIndexOf(".")+1).toLowerCase();
		/*console.log(fileSize)
		console.log(fileName)*/
		 if(size > 3000 || (fileName !="jpg" && fileName !="png")){  
			   layer.alert('请上传不超过3M的.jpg或.png的图片文件。')
		       return
		     }
		for(var i = 0; i < fileList.length; i++) {
			var imgSrcI = getObjectURL(fileList[i]);
			imgName.push(fileList[i].name);
			imgSrc.push(imgSrcI);
			imgFile.push(fileList[i]);
		}
		addNewContent(imgBox);
		if( $(".imgContainer").length > 2){
			$("#inputBox").hide();
			}
	})
	$(btn).on('click', function() {
		var type = $('input:radio[name="type"]:checked').val();
		var content =$("#content").val().length;
		var qq = $(".qq").val();
		var  regexp = /^[1-9]\d{4,9}$/;
		
		
		 
		if (type == null || content<5 ||(qq != "" && regexp.test(qq) == false)) {
			if (type == null ){
				$(".erro_type").show()
			}
			if(content<5){
				$(".erro_cont").show()
			}
			if(qq != "" && regexp.test(qq) == false){
				 $(".erro_qq").show()
			}
		}else{
			
			
		//用formDate对象上传
		var fd = new FormData($('form')[0]);
		for(var i=0;i<imgFile.length;i++){
			fd.append(obj.data+"[]",imgFile[i]);
		}
		submitPicture(obj.upUrl, fd);
			
		}
		
	if (type != null ){
		$(".erro_type").hide()
	}
	if(content>=5){
		$(".erro_cont").hide()
	}
	if(qq == "" || regexp.test(qq)){
		$(".erro_qq").hide()
	}
	
	
	})
}
//图片展示
function addNewContent(obj) {
	$(imgBox).html("");
	for(var a = 0; a < imgSrc.length; a++) {
		var oldBox = $(obj).html();
		$(obj).html(oldBox + '<div class="imgContainer"><img title=' + imgName[a] + ' alt=' + imgName[a] + ' src=' + imgSrc[a] + ' onclick="imgDisplay(this)"><p onclick="removeImg(this,' + a + ')" class="imgDelete">×</p></div>');
	}
	
	
}
//删除
function removeImg(obj, index) {
	imgSrc.splice(index, 1);
	imgFile.splice(index, 1);
	imgName.splice(index, 1);
	var boxId = "#" + $(obj).parent('.imgContainer').parent().attr("id");
	addNewContent(boxId);
	if( $(".imgContainer").length < 3){
		$("#inputBox").show();
		}
	$("#file").val('');
}
//限制图片个数
function limitNum(num){
	if(!num){
		return true;
	}else if(imgFile.length>num){
		return false;
	}else{
		return true;
	}
}

//上传(将文件流数组传到后台)
function submitPicture(url,data) {
    for (var p of data) {
	  	console.log(p);
	}
	// if(url&&data){
		$.ajax({
			type: "post",
			url: url,
			async: true,
			data: data,
			processData: false,
			contentType: false,
			success: function(dat) {
				console.log(dat)
				if (dat.code==1){
					layer.confirm('提交成功，感谢您的反馈。', {
					  btn: ['确定','取消'] //按钮
					}, function(){
					  window.location.reload();
					}, function(){
					  window.location.reload();
					});
				   
				}else if (!dat.code && dat.data == 'type'){
                    $(".erro_type").show()
				}else if (!dat.code && dat.data == 'qq'){
                    $(".erro_qq").show()
				}else if (!dat.code && dat.data == 'content'){
                    $(".erro_cont").text(dat.msg);
                    $(".erro_cont").show()
				}else{
					layer.confirm(dat.msg, {
					  btn: ['确定','取消'] //按钮
					});
				}
			},
			error: function () {
				$.alert("网络错误，请刷新页面重试");
			}
		});
	// }else{
	//   alert('请打开控制台查看传递参数！');
	// }
}
//图片灯箱
function imgDisplay(obj) {
	var src = $(obj).attr("src");
	var imgHtml = '<div onclick="closePicture1(this)" style="width: 100%;height: 100vh;overflow: auto;background: rgba(0,0,0,0.5);text-align: center;position: fixed;top: 0;left: 0;z-index: 1000;display: grid;align-items: center;justify-content: center;"><img onclick="event.stopPropagation();" src=' + src + ' /><p style="font-size: 50px;position: fixed;top: 30px;right: 30px;color: white;cursor: pointer;" onclick="closePicture(this)">×</p></div>'
	$('body').append(imgHtml); 
}
//关闭
function closePicture(obj) {
	$(obj).parent("div").remove();
}
function closePicture1(obj) {
	$(obj).remove();
}

//图片预览路径
function getObjectURL(file) {
	var url = null;
	if(window.createObjectURL != undefined) { // basic
		url = window.createObjectURL(file);
	} else if(window.URL != undefined) { // mozilla(firefox)
		url = window.URL.createObjectURL(file);
	} else if(window.webkitURL != undefined) { // webkit or chrome
		url = window.webkitURL.createObjectURL(file);
	}
	return url;
}
</script>
{/block}
