{extend name="layout/content" /}
{block name="title"}<title>实名认证_账号安全_祈盟文化个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/userinfo.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}


{block name="content"}
<div class="warp_userinfo">
    <div class="container">
        <!-- 左侧菜单栏 -->
        <div class="left_tab">
            <a href="{:getUserCenterUrl('1')}" class="tab1 ">
                <img src="__STATIC__/images/icon/userinfo.png">
                个人资料
            </a>
			<a href="{:getUserCenterUrl('5')}" class="tab5">
				<img src="__STATIC__/images/icon/game-black.png">
				我的游戏
			</a>
            <a href="{:getUserCenterUrl('2')}" class="tab2 ">
                <img src="__STATIC__/images/icon/rechargeinfo.png">
                充值记录
            </a>
            <a href="{:getUserCenterUrl('3')}" class="tab3 ">
                <img src="__STATIC__/images/icon/giftbox.png">
                存箱号
            </a>
            <a href="{:getUserCenterUrl('4')}" class="tab4 active">
                <img src="__STATIC__/images/icon/zhaq-1.png">
                账号安全
            </a>

        </div>
        <!-- 右侧内容 -->
        <div class="content_right">

            <div class="realname">
                <!-- 未实名 -->
                <P>根据《网络游戏管理暂行办法》，祈盟文化游戏用户需要使用有效身份证进行实名认证。实名后无法修改，请务必填写真实信息，我们将全力保证您的账户资料安全。</P>
                <form class="real_name">
                    <div>
                        <span>姓名：</span>
                        <input type="text" placeholder="请输入姓名" name="fullname" class="fullname">
                        <p class="name_error">请输入姓名</p>
                    </div>
                    <div>
                        <span>身份证号：</span>
                        <input type="text" placeholder="请输入身份证号" name="idnumber" class="idnumber">
                        <p class="idnumber_error">请输入身份证号</p>
                    </div>
                    <input type="button" value="确定" class="define">
                </form>
            </div>


        </div>
    </div>

</div>

{/block}

{block name="detail_js"}
<script src="__STATIC__/js/member/identity.js"></script>
{/block}