{extend name="layout/content" /}
{block name="title"}<title>忘记密码_祈盟文化</title>{/block}
{block name="header"}
{/block}

{block name="nav_title"}<h1>忘记密码</h1> {/block}

{block name="content"}

<!-- 忘记密码内容 -->
<div class="warp_forgetpd">
    <div class="container">
        <div class="forgetpd">

             

            
		<div class="rebinding-box">
                <div class="box-title">
                    <h2 class="mtb5">忘记密码</h2>
                </div>

                <div class="box-timeline">
                    <ul class="text-center" >
                        <li class="step1 pass">
                            <div class="box-outside" id="outside">
                              <div class="box-num">
                                  1
                              </div>
                           </div>
                            确认账号
                        </li>
                        <li class="step2 pass">
                            <div class="box-outside" id="outside">
                                <div class="box-num">
                                  2
                                </div>
                            </div>
                            验证身份
                        </li>
                        <li class=" step3 pass">
                            <div class="box-outside" id="outside">
                                <div class="box-num" >
                                 3
                               </div>
                            </div>
                            重置密码
                        </li>
                     <!-- 重置不成功 li 加一个class类   fail -->
                        <li class="step4 current">
                                <div class="box-outside" id="outside">
                                    <div class="box-num" >
                                     4
                                   </div>
                                </div>
                               重置成功
                            </li>
                        <div class="clear">
                            
                        </div>
                    </ul>
                    
                    
                </div>

               <!-- 第四步 -->
               <div class="fourbox-form" id="fourform">
               {switch name="res" }
                   {case value="1" }
                   <!-- 重置成功 -->
                   <div class="reset_success">
                       <img src="__STATIC__/images/icon/correct.png">
                       <p>密码重置成功！</p>
                       <p class="count_down"><span id="countdown">0</span>秒后自动返回登录页</p>
                       <a href="{:getEncodeUrl('login')}">前往登录页</a>
                   </div>
                   {/case}
                   {case value="2"}
                   <!-- 重置不成功 -->
                   <div class="reset_success" >
                        <img src="__STATIC__/images/icon/error.png">
                        <p>密码重置失败,请稍后再试</p>
                        <p class="count_down"><span id="countdown">0</span>秒后自动返回登录页</p>
                        <a href="{:getEncodeUrl('login')}">前往登录页</a>
                    </div>
                   {/case}
                   {default /}
               {/switch}
            </div>   
        </div> 
        </div>

    </div>
</div>

<!-- 忘记密码内容end -->
{/block}

{block name="detail_js"}
<script type="text/javascript">

// 跳转页面
//设定倒数秒数 
var count = 5;
	function countDown(){
		document.getElementById("countdown").innerHTML= count;
		count -= 1;
		if(count==0){
			location.href="{:getEncodeUrl('login')}";
		}
		setTimeout("countDown()",1000);
	}

countDown();
</script>
{/block}