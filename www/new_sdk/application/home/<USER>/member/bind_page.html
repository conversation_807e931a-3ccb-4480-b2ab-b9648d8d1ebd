{extend name="layout/content" /}
{block name="title"}<title>{eq name="$type" value="mobile"}绑定手机{else /}绑定邮箱{/eq}_账号安全_祈盟文化个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/userinfo.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}


{block name="content"}
<div class="warp_userinfo">
    <div class="container">
        <!-- 左侧菜单栏 -->
        <div class="left_tab">
            <a href="{:getUserCenterUrl('1')}" class="tab1 ">
                <img src="__STATIC__/images/icon/userinfo.png">
                个人资料
            </a>
            <a href="{:getUserCenterUrl('5')}" class="tab5">
                <img src="__STATIC__/images/icon/game-black.png">
                我的游戏
            </a>
            <a href="{:getUserCenterUrl('2')}" class="tab2">
                <img src="__STATIC__/images/icon/rechargeinfo.png">
                充值记录
            </a>
            <a href="{:getUserCenterUrl('3')}" class="tab3">
                <img src="__STATIC__/images/icon/giftbox.png">
                存箱号
            </a>
            <a href="{:getUserCenterUrl('4')}" class="tab4 active">
                <img src="__STATIC__/images/icon/zhaq-1.png">
                账号安全
            </a>

        </div>
        <!-- 右侧内容 -->
        <div class="content_right">
            <div class="bindcode">
                <p>请输入要绑定的{eq name="$type" value="mobile"}手机{else /}邮箱{/eq}，点击“发送验证码”，获取验证码进行验证。</p>
                <div class="warp_input">
                    <div class="input-img">
                        {eq name="$type" value="mobile"}<img src="__STATIC__/images/icon/phone-1.png">
                        {else /}<img src="__STATIC__/images/icon/giftbox-1.png">{/eq}
                    </div>
                    <input type="text" name="bindphone" class="bind_input {eq name="$type" value="mobile"}bindphone{else /}bindmail{/eq}" placeholder="请输入{eq name=" $type"
                    value="mobile"}手机号{else /}邮箱{/eq}">
                    <p class="phone_error">请输入正确的{eq name="$type" value="mobile"}手机号{else /}邮箱{/eq}</p>
                </div>
                <div class="bind_code warp_input">
                    <div class="input-img">
                        <img src="__STATIC__/images/icon/message-1.png">
                    </div>
                    <input type="text" name="getcode" class="getcode" placeholder="请输入验证码">
                    <input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback"
                           style="display: none;"/>
                    <button class="sendcode" onclick="sendcode()">发送验证码</button>

                    <p class="code_error">请输入验证码</p>

                </div>
                <input type="button" value="确认" class="confirm">
            </div>


        </div>
    </div>

</div>

{/block}

{block name="detail_js"}
<script src="__STATIC__/js/member/bind_page.js"></script>
{/block}