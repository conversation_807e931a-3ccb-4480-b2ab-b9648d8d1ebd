{extend name="layout/content" /}
{block name="title"}<title>用户登录_祈盟文化</title>{/block}
{block name="header"}
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}



{block name="content"}
<!-- 登录内容 -->
<div class="warp_login">
    <div class="container">
        <div class="login">
            
            <!-- 用户登录表单 -->
            <div class="login_right">

            <form action="" class="login_form" id="login_form">
                <h2>用户登录<span>USER LOGIN</span></h2>

            <div class="username">
                <img src="__STATIC__/images/icon/user.png">
                 <input type="text" name="username" placeholder="请输入用户名" id="username" required="required" >
                 <p class="username_erro">请输入用户名</p>
            </div>

            <div class="password">
                <img src="__STATIC__/images/icon/password.png">
                <input type="password" name="password" placeholder="请输入密码" id="password" required="required" autocomplete="new-password">
                <p class="password_erro">请输入密码</p>
            </div>

           <div class="remember">
                <input id="but1" type="checkbox" name="checkbox">
                <label for="but1"></label>
                <span>记住账号</span>
            </div>

            <input type="button" value="登录"  class="submit" id="dl"/>
            <input type="button" id="TencentCaptcha"  data-appid="{$appid}" data-cbfn="callback"   style="display: none;"/>
           
            </form>

            <div class="login_bottom">
               <a href="{:getForgetPwdUrl(1)}" >忘记密码？</a>
               <a href="{:getEncodeUrl('register')}" >立即注册></a>
            </div>
        </div>

        <div class="verBox">
                <div id="imgVer" style="display:inline-block;"></div>
                </div>
        </div>
    </div>

</div>
{/block}


{block name="detail_js"}
<script src="__STATIC__/js/member/login.js"></script>
{/block}