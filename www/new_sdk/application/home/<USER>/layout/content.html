<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    {block name="title"}
    {notempty name="$seo.title"}<title>{$seo.title}</title> {/notempty}
    {notempty name="$seo.keywords"}
    <meta name="keywords" content="{$seo.keywords}" /> {/notempty}
    {notempty name="$seo.description"}
    <meta name="description" content="{$seo.description}" /> {/notempty}
    {/block}
    <link rel="stylesheet" href="__STATIC__/css/home/<USER>">
    <link rel="stylesheet" href="__STATIC__/css/common.css">
    <link rel="stylesheet" href="__STATIC__/lib/layer/layer.css">
    <link rel="stylesheet" href="__STATIC__/lib/layui/css/layui.css">
    {block name="header"} {/block}
</head>

<body>
    <div class="warp_public_top">
        <div class="public_top">
            <div class="public_top_left">
                <div title="祈盟文化">
                    <div><img src="__STATIC__/images/home/<USER>" alt=""></div>
                    <a href="{:getIndex()}">首页</a>
                </div>
                <div title="祈盟文化客服中心">
                    <div><img src="__STATIC__/images/home/<USER>" alt=""></div>
                    <a href="{:getServiceUrl('1')}" target="_blank">客服中心</a>
                </div>
            </div>

            {empty name="Think.session.front_userid"}
            <!-- 未登录 -->
            <div class="public_top_right">
                <a class="public_top_right_text" href="{:getEncodeUrl('login')}">登录</a>
                <span>|</span>
                <a class="public_top_right_text" href="{:getEncodeUrl('register')}">注册</a>
            </div>
            {else /}
            <!-- 登录状态下 -->
            <div class="public_top_right user_popup">
                <a href="{:getUserCenterUrl('1')}" target="_blank" class="user_name">{$userinfo.nickname}</a>
                <div class="land_popup">
                    <div class="user_center">
                        <a href="{:getUserCenterUrl('1')}" target="_blank"><img
                                src="{empty name='$userinfo.avatar'}/static/images/icon/user-head.png{else /}{$Think.STATIC_DOMAIN}{$userinfo.avatar}{/empty}"></a>
                        <a href="{:getUserCenterUrl('4')}" target="_blank">账号安全</a>
                        <a class="exit" onclick="toExit()">退出</a>
                    </div>
                    <div class="user_btn">
                        <span><a href="{:getUserCenterUrl('5')}" target="_blank">我的游戏</a></span>
                        <span><a href="{:getUserCenterUrl('2')}" target="_blank">充值记录</a></span>
                        <span><a href="{:getUserCenterUrl('3')}" target="_blank">存号箱</a></span>
                    </div>
                </div>
            </div>
            {/empty}



        </div>
    </div>
    <!-- 导航栏 -->
    <div class="warp_public_nav">
        <div class="public_nav">
            <div class="public_nav_img">
                <a href="{:getIndex()}" class="logo" title="祈盟文化">祈盟文化</a>
            </div>
            {block name="nav_title"} {/block}
        </div>
    </div>

    {block name="content"}{/block}
    <!-- 右侧控件 start -->

    <!-- <div class="warp_ctlist">
        <ul class="ctlist">
            <li>
                <div class="warp_weixin">
                    <div class="weixin ico">
                        <img src="__STATIC__/images/home/<USER>">
                        <span>公众号</span>
                    </div>
                    <div class="weixin ico" style="color: #fff;">
                        <img src="__STATIC__/images/home/<USER>">
                        <span>公众号</span>
                    </div>
                </div>
                <div class="code"><img src="__STATIC__/images/code.jpg">
                    <div class="warp_right warp_code">
                        <div class="border-right-empty">
                            <span></span>
                        </div>
                    </div>

                </div>
            </li>
            <li>
                <div class="warp_qq">
                    <div class="qq ico">
                        <img src="__STATIC__/images/home/<USER>">
                        <span>客服QQ</span>
                    </div>
                    <div class="qq ico" style="color: #fff;">
                        <img src="__STATIC__/images/home/<USER>">
                        <span>客服QQ</span>
                    </div>
                </div>
                <div class="customer_service">
                    <ul>
                        <li class="online">
                            <div>
                                <span>在线客服</span>
                                <span>工作时间：{$kf_time}</span>
                            </div>
                        </li>
                        {volist name="kefuList" id="vo"}
                        <li class="qq_service qq_1">
                            <div>
                                <div class="qq_img qq_img_1"><img src="__STATIC__/images/home/<USER>"></div>
                                <a data-QQ="{$vo.qq}">
                                    <div class="qq_txt" title="{$vo.qq}">
                                        <span>{$vo.nickname}</span>
                                        <span>QQ:{$vo.qq}</span>
                                    </div>
                                </a>
                            </div>
                        </li>
                        {/volist}
                    </ul>
                    <div class="warp_right warp_qq_icon">
                        <div class="border-right-empty">
                            <span></span>
                        </div>
                    </div>
                </div>
            </li>
            <li>
                <div class="warp_feedback">
                    <div class="feedback ico">
                        <img src="__STATIC__/images/icon/feedback-black.png">
                        <span>我要反馈</span>
                    </div>
                    <div class="feedback ico">
                        <a href="{:getEncodeUrl('feedback')}">
                            <img src="__STATIC__/images/icon/feedback-white.png">
                            <span style="color: #fff;">我要反馈</span>
                        </a>
                    </div>
                </div>
            </li>
            <li class="hide_top">
                <div class="warp_totop">
                    <div class="totop ico">
                        <img src="__STATIC__/images/home/<USER>">
                    </div>
                    <div class="totop ico">
                        <img src="__STATIC__/images/home/<USER>">
                    </div>
                </div>
            </li>
        </ul>
    </div> -->

    <!-- 右侧控件 end -->
    <!-- 网站统计控件 statr -->
    <div style="display: none">
        <script type="text/javascript">
            var cnzz_protocol = (("https:" == document.location.protocol) ? "https://" : "http://");
            document.write();
        </script>
    </div>
    <!-- / 网站统计控件 end -->

    {include file='../application/home/<USER>/layout/footer.html' /}

    <script src="__STATIC__/js/jquery v1.11.3.min.js"></script>
    <script src="__STATIC__/js/common.js"></script>
    <script src="__STATIC__/lib/layer/layer.js"></script>
    <script src="__STATIC__/lib/layui/layui.js"></script>
    <script src="__STATIC__/lib/layui/lay/modules/form.js"></script>
    {block name="detail_js"}{/block}
</body>

</html>
