{volist name="list" id="vo" length="9"}
<div class="hot_rank_{$i} ranklist">
	<div class="small">
		<div>
			<span class="hot_{$i}">{$i}</span>
			<div><span>{$vo.nickname}</span></div>
		</div>
		<div>
			<span>{if(isset($gameType[$vo.type]))}{$gameType[$vo.type]}{else}未知{/if}</span><span>|</span><span>{if(isset($gameSubjct[$vo.subject]))}{$gameSubjct[$vo.subject]}{else}未知{/if}</span>
			<span id="download">下载</span>
		</div>
	</div>
	<div class="big">
		<div class="hot_{$i}">{$i}</div>
		<div class="big_main">
			<div>
			    <a href="{:getGameUrl($vo['id'])}" title="{$vo.nickname}" target="_blank"><img lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" ></a>	
			</div>
			<div>
				<p><a href="{:getGameUrl($vo['id'])}" title="{$vo.nickname}" target="_blank">{$vo.nickname}</a></p>
				<p class="game_style">
					<span >{if(isset($gameType[$vo.type]))}{$gameType[$vo.type]}{else}未知{/if}</span><span>|</span><span>{if(isset($gameSubjct[$vo.subject]))}{$gameSubjct[$vo.subject]}{else}未知{/if}</span>
				</p>
				<p>
					<span>热度：</span><span>{$vo.power}</span>
				</p>
			</div>
		</div>
		
		{if($vo.download)}
		<div class="download downloadlink"> 
			<button>下载</button>
			<span id="text-1" style="display: none;">{$vo.download}</span>
			<span id="text-2" style="display: none;">{$vo.nickname}</span>
			<span id="download-page" style="display: none;">http://wl.weilongwl.com/download/{$vo.id}.html</span>
		</div>
		{else}
		<div class="no_download downloadlink"> 
			<button>下载</button>
		</div>
		{/if}
	</div>
</div>
{/volist}




<script>
	// url生成二维码

         $(".download").click(function () {
				$(".warp_download_popup").show(); 
				var p_text = $(this).find("#text-1").text(); 
				var p_title = $(this).find("#text-2").text(); 
				var p_page = $(this).find("#download-page").text(); 
				qrcode.makeCode(p_page);
				 $('.az_hover a').attr('href',p_text);
				 $('.download_popup p').html(p_title);
        });


        $(".no_download").click(function () {
			$(".warp_no_download_popup").show(); 
    });

	$(".downloadlink").each(function(){
    if($(this).is('.no_download')){
     //修改父元素
    $(this).parents(".ranklist").find("#download").addClass("nodownload")
    }
    })
   
// 
    $(".small").hover(function(){
		$(this).parent(".ranklist").find(".big .big_main>div:first-child img").attr('src',$(this).parent(".ranklist").find(".big .big_main>div:first-child img").attr('lazy-src'));
	})

  //延迟加载图片
  $(function(){
        var visible;
        $('img').each(function(){
            visible = $(this).offset().top - $(window).scrollTop();
            if((visible > 0) && (visible < $(window).height())) {
                visible = true;
            }else {
                visible = false;
            }
            if(visible) {
                $(this).attr('src', $(this).attr('lazy-src'));
            }
        });
        $(window).scroll(function(){
            $('img').each(function(){
                // if('undefined' == typeof $(this).attr('src')) {
                    visible = $(this).offset().top - $(window).scrollTop();
                    if((visible > 0) && (visible < $(window).height())) {
                        visible = true;
                    }else {
                        visible = false;
                    }
                    if(visible) {
                        $(this).attr('src', $(this).attr('lazy-src'));
                    }
                // }
            });
        });
    });
</script>
