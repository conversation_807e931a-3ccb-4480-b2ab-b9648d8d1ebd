{extend name="layout/base" /}
{block name="header"}

		<link rel="stylesheet" href="__STATIC__/css/game-info.css">
		<link rel="stylesheet" href="__STATIC__/css/home/<USER>">
		{/block}

{block name="content"}
		
		<!-- 页面导航 -->
		<div class="curLoc">
			<div class="container">
				<span class="txt">当前位置：</span><a href="/"  title="祈盟文化游戏">首页</a> &gt; <a href="{:getGameIndex()}" title="手游中心">找游戏</a> &gt;<span>
					{if($detail['nickname'])} {$detail.nickname}	{else/}  游戏详情页 {/if} </span>
			</div>
		</div>
		
		<div class="game_info">
			<div class="container">
				<!-- 左侧 -->
				<div class="game_info_left">
					<!-- 头部信息部分 -->
					<div>
						<img lazy-src="{$detail.icon}" src="__STATIC__/images/icon/150-150.png" alt="{$detail.nickname}" class="smallIcon" title="{$detail.nickname}">
						<div style="float: left;">
							<h2 title="{$detail.nickname}">{$detail.nickname}</h2>
							<span id="code_url" style="display: none;">http://www.weilongwl.com/download/{$detail.id}.html</span>
							<div class="info ">
								<ul class="info1">
									<li><span>发布时间： </span><span>{$detail.updatetime}</span></li>
									<li><span>游戏版本： </span><span>{$detail.version}</span></li>
									<li><span>游戏大小： </span><span>{$detail.size}</span></li>
								</ul>
								<ul class="info2">
									<li><span>游戏类型： </span><span>{$detail.typename}</span></li>
									<li><span>游戏题材： </span><span>{$detail.subjectname}</span></li>
									<li>
										<div>开发厂商： </div>
										<div>{$detail.developer}</div>
									</li>
								</ul>
							</div>
						</div>

						{if($detail['download'])}
                           <!-- 有下载链接时 -->
						<div class="az_code_right">
                            <a href="{$detail.download}">
							<div class="az_ed_download" >
								<img src="__STATIC__/images/home/<USER>">
								<span>安卓版本下载</span>
                            </div>
                        </a>
							<div class="small-code">
								<img src="__STATIC__/images/home/<USER>">
							</div>

							<div class="big_az_code">
									<div id="qrcode_title"></div>
								<div class="scan_code">
									<img src="__STATIC__/images/icon/scan.png">
									<span>扫码立即下载</span>
								</div>
								<div class="border-left-empty">
									<span></span>
								</div>
							</div>
						</div>
						{else/}
						<!-- 没有下载链接时 -->
						<div class="az_no_link">
							<div class="az_no_download">
								<img src="__STATIC__/images/home/<USER>">
								<span>安卓版本下载</span>
							</div>

							<div class="small-code">
								<img src="__STATIC__/images/home/<USER>">
							</div>
						</div>
						{/if}

					</div>


					<!-- tab切换 -->
					<div class="game_info_tab">
						<div class="tab_title">
                            	<a href="{:getGameUrl($detail['id'])}" title="{$detail.nickname}游戏介绍"><p class="title">游戏介绍</p></a>
								<a href="{:getGameNewsUrl($detail['id'])}" title="{$detail.nickname}游戏资讯"><p class="title">游戏资讯</p></a>
								<a href="{:getGameGiftUrl($detail['id'])}" title="{$detail.nickname}游戏礼包"><p class="active">游戏礼包</p></a>
								<a href="{:getGameServerUrl($detail['id'])}" title="{$detail.nickname}开服信息"><p class="title">开服信息</p></a>
						</div>
						<div class="tab_content">
						
							<!-- 游戏礼包 -->
							<div class="game_gift">
							{notempty name="list"}
								{volist name="list" id="vo"}
								 <div class="tab_gift_content">
									 <a href="{:getGiftUrl($vo['id'])}" title="{$vo.title}" target="_blank"><img lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon"></a>
									 <div class="gift_text">
										<h4><a href="{:getGiftUrl($vo['id'])}" title="{$vo.title}" target="_blank">{$vo.title}</a></h4>
										<p class="gift_content">礼包内容：{$vo.content}</p>
										<p>有效时间：{$vo.starttime} 至 {$vo.endtime}</p>
										<div class="gift_progress">
											<p>礼包剩余：</p>
												<div class="warp_gift_surplus">
													<div class="gift_surplus" style="width:{$vo.percent} ;"></div>
												</div>
												<p>{$vo.percent}</p>
											</div>
									 </div>
									<a class="have_gift" href="{:getGiftUrl($vo['id'])}" target="_blank">领取</a>
								 </div>
								 {/volist}            
								<!-- 分页 -->
							 	<div class="page ">
									{$page}
									<div class="rows">共 <b>{$list->total()}</b>条 / <b>{$list->toArray()['last_page']}</b>页 记录 </div>
								</div>
								 <!-- 分页end -->

							{else/}
                               <!-- 无游戏礼包 -->
								<div class="no_content">
									<img src="__STATIC__/images/icon/no-gift.png">
									<p style="padding-left: 60px;">该游戏礼包正在加紧补货中...</p>
                                </div>
                               <!-- 无游戏礼包end -->
							{/notempty}
							</div>
						</div>
					</div>
                </div>
			
				<div class="game_info_right">
	              <!-- 手游排行 -->
				  <div class="rank">
						<h4 title="手游排行">
							<span>手游排行</span>
						</h4>
						<div class="rank_list" id="J_rank_list">
							
							
						</div>
					</div>


            <!-- 两张广告图片 -->
       
           
		
           <!-- 两张广告图片end -->

                     <!-- 最新上线 -->
                     <div class="new_outline">
                         <h4 title="最新上线"><span>最新上线</span></h4>
                         <div class="warp_cont">
						 {volist name="newGameList" id="vo"}
                         <div class="new_outline_img"> 
                             <a href="{$vo.url}" title="{$vo.nickname}" target="_blank"><img lazy-src="{$vo.icon}" src="__STATIC__/images/icon/150-150.png"  class="smallIcon"></a>
                             <p><a href="{$vo.url}" title="{$vo.nickname}" target="_blank">{$vo.nickname}</a></p>
                         </div>
						 {/volist}
                        </div>
                     </div>
                    </div>

				</div>
			</div>
		</div>

     
    <!-- 下载弹窗 start -->
    <div class="warp_download_popup">
            <div class="download_popup" >
                <div class="download_title"><span>下载</span></div>
             <div class="game_title"> <p>传奇帝国之荣耀骑士传奇，传奇帝国之荣耀骑士传奇传奇帝国之荣耀骑士传奇...</p></div>
                <div class="az_download">
                    <div class="az_hover">
                <a href="" >
                    <img src="__STATIC__/images/home/<USER>">
                    <span>安卓版本下载</span>
                </a>
                    </div>
                    <div class="az_code"><img src="__STATIC__/images/home/<USER>"></div>
                </div>
                <div class="big_az_code">

                   <div id="qrcode"></div>
                   <div class="scan_code">
                   <img src="__STATIC__/images/icon/scan.png">
                   <span>扫码立即下载</span>
                   </div>
                   <div class="border-left-empty">
                    <span></span>
                    </div>
               </div>
               <div class="close">
                </div>
            </div>
        </div>
        <div class="warp_no_download_popup">
            <div class="no_download_popup" >
            <div class="tip"><span>提示</span><div class="close">
            </div></div>
            <div class="tip_txt">
                <p>该游戏暂无下载链接，稍后再试</p>
                <div class="tip_btn">
                <div class="determine"><span>确定</span></div>
                <div class="cancel"><span>取消</span></div>
              </div>
            </div>
            </div>
        </div>
        
            <!-- 下载弹窗 end -->

				{/block}

		{block name="detail_js"}
		<script src="__STATIC__/js/common.js"></script>
		<script src="__STATIC__/js/qrcode.min.js" ></script>
		<script src="__STATIC__/js/game/public.js" ></script>          
         {/block}