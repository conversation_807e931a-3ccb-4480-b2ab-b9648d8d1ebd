<?php


use think\Env;

if(!function_exists('GetIP')) {
    // 取得当前IP
    function GetIP($type=0){
        if(!empty($_SERVER["HTTP_CLIENT_IP"])) {
            $cip = $_SERVER["HTTP_CLIENT_IP"];
        } else if(!empty($_SERVER["HTTP_X_FORWARDED_FOR"])) {
            $cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        } else if(!empty($_SERVER["REMOTE_ADDR"])) {
            $cip = $_SERVER["REMOTE_ADDR"];
        } else {
            $cip = "";
        }

        preg_match("/[\d\.]{7,15}/", $cip, $cips);
        $cip = $cips[0] ? $cips[0] : 'unknown';
        unset($cips);
        if ($type==1) $cip = myip2long($cip);
        return $cip;
    }
}

if(!function_exists('myip2long')) {
    //转换IP
    function myip2long($ip){
//		$ip_arr = split('\.',$ip);
        $ip_arr = explode('.', $ip);
        $iplong = (16777216 * intval($ip_arr[0])) + (65536 * intval($ip_arr[1])) + (256 * intval($ip_arr[2])) + intval($ip_arr[3]);
        $iplong = sprintf("%u",$iplong);
        return $iplong;
    }
}

/**
 * 根据user_agent判断使用的设备类型
 * @return int
 */
function getDevice(){
    //判断终端是手机(iphone,ipad,android)还是电脑访问网站
    $user_agent = strtolower($_SERVER['HTTP_USER_AGENT']);
    if(strpos($user_agent, 'windows nt')) { // 使用pc
        return 1;
    }
    if(strpos($user_agent, 'iphone')) {
        return 3;
    }
    if(strpos($user_agent, 'ipad')) {
        return 3;
    }
    if(strpos($user_agent, 'android')) {
        return 2;
    }
}

/**
 * 账号密码加解密
 */
if(!function_exists('auth_code')) {
    function auth_code($string, $operation = 'DECODE', $key = '', $expiry = 0) {
        $ckey_length = 0;

        $key = md5($key ? $key : Env::get('auth_key') );
        $keya = md5(substr($key, 0, 16));
        $keyb = md5(substr($key, 16, 16));
        $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length): substr(md5(microtime()), -$ckey_length)) : '';

        $cryptkey = $keya.md5($keya.$keyc);
        $key_length = strlen($cryptkey);

        $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0).substr(md5($string.$keyb), 0, 16).$string;
        $string_length = strlen($string);

        $result = '';
        $box = range(0, 255);
        // $box = 100;

        $rndkey = array();
        for($i = 0; $i <= 255; $i++) {
            $rndkey[$i] = ord($cryptkey[$i % $key_length]);
        }

        for($j = $i = 0; $i < 256; $i++) {
            $j = ($j + $box[$i] + $rndkey[$i]) % 256;
            $tmp = $box[$i];
            $box[$i] = $box[$j];
            $box[$j] = $tmp;
        }

        for($a = $j = $i = 0; $i < $string_length; $i++) {
            $a = ($a + 1) % 256;
            $j = ($j + $box[$a]) % 256;
            $tmp = $box[$a];
            $box[$a] = $box[$j];
            $box[$j] = $tmp;
            $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
        }

        if($operation == 'DECODE') {
            if((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26).$keyb), 0, 16)) {
                return substr($result, 26);
            } else {
                return '';
            }
        } else {
            return $keyc.str_replace('=', '', base64_encode($result));
        }
    }
}

/**
 * 获取游戏icon， 没有图片时，给个默认icon
 * @param $url
 * @return string
 */
function getGameIcon($url,$use_small_icon=1){
    if ( empty($url) ) {
        return  '/static/images/aoyou_game.png';
    }

	$smallIconExt = '?x-image-process=style/small';

    if ( strpos($url, 'platform/') === 0 || strpos($url, 'image/') === 0 ) {
		if(substr(STATIC_DOMAIN,-1)=='/' && substr($url,0,1)=='/'){
			return $use_small_icon?STATIC_DOMAIN.'/'.$url.$smallIconExt:STATIC_DOMAIN.'/'.$url;
		}
		else if(substr(STATIC_DOMAIN,-1)<>'/' && substr($url,0,1)<>'/'){
			 return $use_small_icon?substr(STATIC_DOMAIN,0,strlen(STATIC_DOMAIN)-1).$url.$smallIconExt:substr(STATIC_DOMAIN,0,strlen(STATIC_DOMAIN)-1).$url;
		}
		else{
			return $use_small_icon?STATIC_DOMAIN.$url.$smallIconExt:STATIC_DOMAIN.$url;
		}
        return $use_small_icon?STATIC_DOMAIN.'/'.$url.$smallIconExt:STATIC_DOMAIN.'/'.$url;
    }

	if(substr(STATIC_DOMAIN,-1)=='/'){
		return $use_small_icon?STATIC_DOMAIN.'image/'.$url.$smallIconExt:STATIC_DOMAIN.'image/'.$url;
	}
	else{
		return $use_small_icon?STATIC_DOMAIN.'/image/'.$url.$smallIconExt:STATIC_DOMAIN.'/image/'.$url;
	}
}
function getGameUrl($id,$type=1) {
	if(intval($id)){
	    if ($type == 1) return WEBSITE_DOMAIN.'/game'.$id.'.html';
	    if ($type == 2) return MOBILE_SITE_DOMAIN.'/game'.$id.'.html';
	//	return url('game/info',array('id'=>$id));
	}
	else{
		return '';
	}
}
function getGameNewsUrl($id) {
	if(intval($id)){
		return WEBSITE_DOMAIN.'/game'.$id.'/news/';
	//	return url('game/news',array('id'=>$id));
	}
	else{
		return '';
	}
}
function getGameGiftUrl($id) {
	if(intval($id)){
		return WEBSITE_DOMAIN.'/game'.$id.'/gift/';
	//	return url('game/gift',array('id'=>$id));
	}
	else{
		return '';
	}
}
function getGameServerUrl($id) {
	if(intval($id)){
		return WEBSITE_DOMAIN.'/game'.$id.'/kfkc/';
	//	return url('game/server',array('id'=>$id));
	}
	else{
		return '';
	}
}
function getGameIndex() {
	return WEBSITE_DOMAIN.'/game/';
//	return url('game/index','',false);
}


// 开服开测
function getServerUrl($type) {
    // 开服
    if ( $type == 'kf') {
        return WEBSITE_DOMAIN.'/kf/';
    }
    // 开测
    if ( $type == 'kc') {
        return WEBSITE_DOMAIN.'/kc/';
    }

    return '';
}

// 首页
function getIndex(){
    return WEBSITE_DOMAIN;
}

// 其他地址
function getOtherUrl($action){
    return WEBSITE_DOMAIN.'/'.$action.'.html';
}

function getNewsImage($image) {
    if(!$image) {
        return '/static/images/icon/990-625.png';
    } else {
        if ( strpos($image, 'platform/') === 0 || strpos($image, 'image/') === 0) {
			if(substr(STATIC_DOMAIN,-1)=='/' && substr($image,0,1)=='/'){
				return STATIC_DOMAIN.'/'.$image;
			}
			else if(substr(STATIC_DOMAIN,-1)<>'/' && substr($image,0,1)<>'/'){
				 return substr(STATIC_DOMAIN,0,strlen(STATIC_DOMAIN)-1).$image;
			}
			else{
				return STATIC_DOMAIN.$image;
			}
        }

		if(substr(STATIC_DOMAIN,-1)=='/'){
			return STATIC_DOMAIN.'image/'.$image;
		}
		else{
			return STATIC_DOMAIN.'/image/'.$image;
		}
    }
}
function getNewsIndexUrl($type='') {
	if($type==1){
		return WEBSITE_DOMAIN.'/news/xyzx/';
	//	return url('news/index',array('type'=>$type));
    }
	else if($type==2){
		return WEBSITE_DOMAIN.'/news/lyzx/';
	//	return url('news/index',array('type'=>$type));
    }
	else if($type==3){
		return WEBSITE_DOMAIN.'/news/xypc/';
	//	return url('news/index',array('type'=>$type));
    }
	else if($type==4){
	    return WEBSITE_DOMAIN.'/news/xygl/';
	//	return url('news/index',array('type'=>$type));
    }
	else if($type==5){
		return WEBSITE_DOMAIN.'/news/jcsp/';
	//	return url('news/index',array('type'=>$type));
    }
	else {
		return WEBSITE_DOMAIN.'/news/';
	//	return url('news/index');
    }
}
function getNewsUrl($id,$redirecturl='') {
    if ( empty($redirecturl) ) {
	//	return url('news/detail',array('id'=>$id));
        return WEBSITE_DOMAIN.'/news/'.$id.'.html';
    } else {
        return $redirecturl;
    }
}
function getCutcontent($content) {
    $cutcontent = mb_substr(str_replace("　　", '', strip_tags($content)),0,80,'UTF-8') ."...";
    return $cutcontent;
}
/**
 * 获取游戏大小
 * @return string
 */
function getSize($game) {
    return '未知';
    // 苍穹变、五行天手游  游戏大小做特殊处理
    if($game['id'] == 231){
        return "293.01M";
    }elseif ($game['id'] == 784){
        return "302.63M";
    }
	if(!isset($game['platform'])){
		$game['platform'] = 0;
	}
	if($game['pinyin']){
		$size = model('Game')->getApkInfo( $game['androidurl'], $game['pinyin'], 'size', $game['platform']);
	}
	else{
		$size = '';
	}
    return $size ? $size : '未知';
}
/**
 * 获取游戏版本
 * @return string
 */
function getVersion($game) {
    // 苍穹变、五行天手游  游戏版本做特殊处理
    if($game['id'] == 231){
        return "5.0.0";
    }elseif ($game['id'] == 784){
        return "1.9.1";
    }
	if(!isset($game['platform'])){
		$game['platform'] = 0;
	}
	$version = model('Game')->getApkInfo( $game['androidurl'], $game['pinyin'], 'version', $game['platform']);
	return $version ? $version : '未知';
}

/**
 * 获取游戏更新时间
 * @return string
 */
function getUpdateTime($game) {
    // 苍穹变、五行天手游  发布时间做特殊处理
    if($game['id'] == 231){
        return "2016-09-01";
    }elseif ($game['id'] == 784){
        return "2017-03-25";
    }
	if(!isset($game['platform'])){
		$game['platform'] = 0;
	}
	$updtime = model('Game')->getApkInfo( $game['androidurl'], $game['pinyin'], 'updtime', $game['platform']);
	return $updtime ? $updtime : '未知';
}

/**
 * 获取游戏下载地址
 * @return string
 */
function getDownload($game) {
    // 游戏的下载链接做特殊处理
	$gameInfo = model('Game')->field('id,name,nickname,game_kind,is_default,download_url')->where(['id'=>$game['id']])->find();
	if($gameInfo['download_url']){
		return $gameInfo['download_url'];
	}
    if ($game['filename']) {
        return APK_DOWN_DOMAIN.'/sygame/'.$game['pinyin'].'/'.$game['filename'];
    } else {
        return '';
    }
}

/**
 * 获取游戏截图
 * @return array
 */
function getScreenShot($image) {
    if ( empty($image) ) {
        return array();
    }
    $screenShot = explode(';', $image);
    foreach ($screenShot as $key => $value) {
        $screenShot[$key] = getGameIcon($value,0);
    }
    return $screenShot;
}

/**
 * 获取游戏类型
 * @return string
 */
function getTypename($p_type_name='') {
    return $p_type_name ? $p_type_name : '未知';
}

/**
 * 获取游戏题材
 * @return string
 */
function getSubjectname($p_subject_name='') {
    return $p_subject_name ? $p_subject_name : '未知';
}

/**
 * 获取开发厂商
 * @return string
 */
function getDeveloper($p_developer='') {
    return $p_developer ? $p_developer : '未知';
}


function __getUrlContent($url, $post_fields=array()) {
    $curl_handler = curl_init();
    curl_setopt($curl_handler, CURLOPT_URL, $url);
    curl_setopt($curl_handler, CURLOPT_HEADER, 0);
    curl_setopt($curl_handler, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl_handler, CURLOPT_TIMEOUT, 60);
    if ( ! empty($post_fields) ) {
        curl_setopt($curl_handler, CURLOPT_POST, 1);
        curl_setopt($curl_handler, CURLOPT_POSTFIELDS, json_encode($post_fields));
    }

    $response = curl_exec($curl_handler);
    curl_close($curl_handler);

    return $response;
}

/**
 * 腾讯滑块验证
 * @param      $url
 * @param bool $params
 * @param int  $ispost
 * @return bool|mixed
 */
function txcurl($url,$params=false,$ispost=0){
    $httpInfo = array();
    $ch = curl_init();

    curl_setopt( $ch, CURLOPT_HTTP_VERSION , CURL_HTTP_VERSION_1_1 );
    curl_setopt( $ch, CURLOPT_USERAGENT , 'JuheData' );
    curl_setopt( $ch, CURLOPT_CONNECTTIMEOUT , 60 );
    curl_setopt( $ch, CURLOPT_TIMEOUT , 60);
    curl_setopt( $ch, CURLOPT_RETURNTRANSFER , true );
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    if( $ispost )
    {
        curl_setopt( $ch , CURLOPT_POST , true );
        curl_setopt( $ch , CURLOPT_POSTFIELDS , $params );
        curl_setopt( $ch , CURLOPT_URL , $url );
    }
    else
    {
        if($params){
            curl_setopt( $ch , CURLOPT_URL , $url.'?'.$params );
        }else{
            curl_setopt( $ch , CURLOPT_URL , $url);
        }
    }
    $response = curl_exec( $ch );
    if ($response === FALSE) {
        //echo "cURL Error: " . curl_error($ch);
        return false;
    }
    $httpCode = curl_getinfo( $ch , CURLINFO_HTTP_CODE );
    $httpInfo = array_merge( $httpInfo , curl_getinfo( $ch ) );
    curl_close( $ch );
    return $response;
}

/**
 * 根据身份证判断,是否满足年龄条件
 * @param type $IDCard 身份证
 * @param type $minAge 最小年龄
 * @param type $minAge 最大年龄
 */

function isMeetAgeByIDCard($IDCard, $minAge,$maxAge) {

    if (strlen($IDCard) <= 15) {
        $IDCard = convertIDCard15to18($IDCard);
    }

    $year = date('Y') - substr($IDCard, 6, 4);
    $monthDay = date('md') - substr($IDCard, 10, 4);

    // 验证最小年龄
    if ($year < $minAge || $year == $minAge && $monthDay < 0) {
        return false;
    }
    // 验证最大年龄
    if ($year > $maxAge || $year == $maxAge && $monthDay > 0) {
        return false;
    }
    return true;
}

// 将15位身份证升级到18位
function convertIDCard15to18($IDCard) {
    if (strlen($IDCard) != 15) {
        return false;
    } else {
        // 如果身份证顺序码是996 997 998 999，这些是为百岁以上老人的特殊编码
        if (array_search(substr($IDCard, 12, 3), array('996', '997', '998', '999')) !== false) {
            $IDCard = substr($IDCard, 0, 6) . '18' . substr($IDCard, 6, 9);
        } else {
            $IDCard = substr($IDCard, 0, 6) . '19' . substr($IDCard, 6, 9);
        }
    }
    $IDCard = $IDCard . calcIDCardCode($IDCard);
    return $IDCard;
}

//计算身份证的最后一位验证码,根据国家标准GB 11643-1999
function calcIDCardCode($IDCardBody) {
    if (strlen($IDCardBody) != 17) {
        return false;
    }

    //加权因子
    $factor = array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
    //校验码对应值
    $code = array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
    $checksum = 0;

    for ($i = 0; $i < strlen($IDCardBody); $i++) {
        $checksum += substr($IDCardBody, $i, 1) * $factor[$i];
    }

    return $code[$checksum % 11];
}

/**
 * 获取礼包发号模块url
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getGiftUrl($type,$id='',$redirecturl='') {
    if ( empty($redirecturl) ) {
        if (empty($id)){
            return WEBSITE_DOMAIN.'/gift/'.$type.'/';
        }
        return WEBSITE_DOMAIN.'/gift/'.$id.'.html';
    } else {
        return $redirecturl;
    }
}
/**
 * 设置 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getEncodeUrl($name,$suffix=1,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($suffix == 1){
            return WEBSITE_DOMAIN.'/'.$name.'/';
        }elseif ($suffix == 2){
            return WEBSITE_DOMAIN.'/'.$name.'.html';
        }

    } else {
        return $redirecturl;
    }
}
/**
 * 设置 【忘记密码】 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getForgetPwdUrl($type,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($type == 1){
            return WEBSITE_DOMAIN.'/forgotpwd.html';
        }elseif ($type == 2){
            return WEBSITE_DOMAIN.'/forgotpwd/check.html';
        }elseif ($type == 3){
            return WEBSITE_DOMAIN.'/forgotpwd/resetpwd.html';
        }elseif ($type == 4.1){
            /*return WEBSITE_DOMAIN.'/gift/'.$id.'.html';*/
            return WEBSITE_DOMAIN.'/forgotpwd/result1.html';
        }elseif ($type == 4.2){
            return WEBSITE_DOMAIN.'/forgotpwd/result2.html';
        }elseif ($type == 2.1){
            return WEBSITE_DOMAIN.'/forgotpwd/check/mobile.html';
        }elseif ($type == 2.2){
            return WEBSITE_DOMAIN.'/forgotpwd/check/email.html';
        }
    } else {
        return $redirecturl;
    }
}
/**
 * 设置 【个人中心】 模块 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getUserCenterUrl($type,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($type == 1){
            return WEBSITE_DOMAIN.'/user/userinfo/';
        }elseif ($type == 2){
            return WEBSITE_DOMAIN.'/user/rechargeinfo/';
        }elseif ($type == 3){
            return WEBSITE_DOMAIN.'/user/giftbox/';
        }elseif ($type == 4){
            return WEBSITE_DOMAIN.'/user/zhaq/';
        }elseif ($type == 5){
            return WEBSITE_DOMAIN.'/user/mygame/';
        }
    } else {
        return $redirecturl;
    }
}
/**
 * 设置 【个人中心--账号安全】 模块 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getAccountUrl($type,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($type == 1){
            return WEBSITE_DOMAIN.'/user/zhaq/changepwd/';
        }elseif ($type == 2){
            return WEBSITE_DOMAIN.'/user/zhaq/bind/selc/mobile/';
        }elseif ($type == 2.1){
            return WEBSITE_DOMAIN.'/user/zhaq/bind/validate/mobile/';
        }elseif ($type == 2.2){
            return WEBSITE_DOMAIN.'/user/zhaq/bindpage/';
        }elseif ($type == 3){
            return WEBSITE_DOMAIN.'/user/zhaq/bind/selc/email/';
        }elseif ($type == 3.1){
            return WEBSITE_DOMAIN.'/user/zhaq/bind/validate/email/';
        }elseif ($type == 3.2){
            return WEBSITE_DOMAIN.'/user/zhaq/bindpage/';
        }elseif ($type == 4){
            return WEBSITE_DOMAIN.'/user/zhaq/realname/';
        }
    } else {
        return $redirecturl;
    }
}
/**
 * 设置 【客服中心】 模块 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getServiceUrl($type,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($type == 1){
            return WEBSITE_DOMAIN.'/kefu/cjwt/';
        }elseif ($type == 2){
            return WEBSITE_DOMAIN.'/kefu/zhss/';
        }elseif ($type == 3){
            return WEBSITE_DOMAIN.'/kefu/sscx/';
        }elseif ($type == 4){
            return WEBSITE_DOMAIN.'/kefu/wxss/';
        }
    } else {
        return $redirecturl;
    }
}

/**
 * 判断字符串是否包含敏感词
 * @param $word
 * @return bool
 */
function isSensitiveWord($word){
    $list = model('SensitiveWord')->column('word');
    $res = false;
    foreach ($list as $v){
        if(strpos($word,$v) !== false){
            $res = true;
            break;
        }
    }
    return $res;
}

/**
 * 用户昵称验证
 * @param $nickname
 * @return array
 */
function validataNickname($nickname){
    $code = ['code'=>0,'msg'=>''];
    if (((strlen($nickname) + mb_strlen($nickname,'UTF8')) / 2) > 14) {
        $code['msg'] = '昵称不能超过14个字符';
        return $code;
    }

    $res = isSensitiveWord($nickname);
    if ($res) {
        $code['msg'] = '昵称内含有敏感词，请重新输入';
        return $code;
    }

    $num = model('members')->where(['nickname'=>$nickname,'id'=>['neq',session('front_userid')]])->count();
    if ($num) {
        $code['msg'] = '该昵称已存在，请重新输入';
        return $code;
    }

    return ['code'=>1,'msg'=>''];
}

function jsonResult($data, $code = 0, $msg = '',$isExit=true)
{
    $result = [
        'code' => $code,
        'msg'  => $msg,
        'data' => $data,
    ];

    return json_encode($result);
}
