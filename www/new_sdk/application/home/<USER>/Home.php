<?php

/**
 * 官网的公共控制器类
 *
 */

namespace app\home\controller;

use app\common\controller\Base;
use think\Db;
use think\Config;

class Home extends Base
{
    // 不验证权限的action
    protected $noCheckAuth = [];

    protected $current_node;

    // 登录返回上一页，需跳过的页面url
    /*protected $noPreviousPage = [WEBSITE_DOMAIN.'/login/',WEBSITE_DOMAIN.'/register/',WEBSITE_DOMAIN.'/forgotpwd.html',WEBSITE_DOMAIN.'/forgotpwd/check.html',WEBSITE_DOMAIN.'/forgotpwd/resetpwd.html'];*/
    protected $noPreviousPage = [];

    // 登录返回上一页，默认url
    protected $defaultPage = WEBSITE_DOMAIN;
    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        $this->noPreviousPage = [WEBSITE_DOMAIN.'/login/',WEBSITE_DOMAIN.'/register/',WEBSITE_DOMAIN.'/forgotpwd.html',WEBSITE_DOMAIN.'/forgotpwd/check.html',WEBSITE_DOMAIN.'/forgotpwd/resetpwd.html',WEBSITE_DOMAIN.'/csc/resetpwdresult.html',WEBSITE_DOMAIN.'/csc/resetpwdresult/suc/1.html'];
        $seo['title'] = '祈盟文化_简单生活，快乐游戏';
        $seo['keywords'] = '好玩的手机游戏,手机游戏下载,热门手游,最新手游,手游排行榜,安卓游戏,祈盟文化游戏,祈盟文化';
        $seo['description'] = '祈盟文化手游平台为玩家提供免费手机游戏下载，最新、热门手机游戏排行以及海量免费的手游礼包。祈盟文化还提供最新鲜好玩的手机游戏资讯、最精彩的手游攻略、手游评测、手游视频等内容!';
        // 获取在线客服时间
        $kf_time = model('Setting')->getSetting('CUSTOMER_SERVICE_TIME');
        // 获取底部关于我们列表
        $aboutList = model('Aboutus')->getAboutList();
		// 客服QQ列表
        $kefuList = model('Kefu')->getKefuList();
        // 底部悬浮框
        $footPopupList = Db::table('cy_ad')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->field('id,title,url,image')->where(['type'=>13])->order('sort asc,create_time asc')->find();
        if ( !empty($footPopupList)) {
            $footPopupList['url'] = preg_match("/^http(s)?:\\/\\/.+/",$footPopupList['url']) ? $footPopupList['url'] : 'http://' . $footPopupList['url'];
        }
        // 祈盟文化app下载链接
        $link = model('Setting')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->where('name','APP_LINK')->value('value');
        // 热搜词
        $trendingSearch = Db::name('cy_keywords')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->where('type',1)->order('create_time desc')->value('title');

        // 用户昵称
        $userinfo = '';
        if (session('front_userid')){
            $userinfo = model('members')->field('nickname,avatar,username,id,amount')->where('id',session('front_userid'))->find();
        }


        $this->assign('seo',$seo);
        $this->assign('kf_time',$kf_time);
        $this->assign('aboutList',$aboutList);
        $this->assign('kefuList',$kefuList);
        $this->assign('footPopupList',$footPopupList);
        $this->assign('link',$link);
        $this->assign('trendingSearch',$trendingSearch);
        $this->assign('userinfo',$userinfo);

    }

    /**
     * 判断用户是否登录，未登录跳转登录页面
     */
    protected function _isLogin(){
        if (!session('?front_userid')){
            $this->redirect('Home/member/login');
            exit;
        }
    }

    /**
     * 返回 json
     * @access protected
     * @param mixed $data 要返回的数据
     * @param int $code 返回的 code
     * @param mixed $msg 提示信息
     * @return void
     * @throws HttpResponseException
     */
    public function jsonResult($data, $code = 0, $msg = '')
    {
        $this->result($data, $code, $msg, 'json');
    }


    /**
     * 获取上一页的URL （有过滤）
     * @param bool $toUrl  url是否转完整地址
     * @return string
     */
    public function getPrePageUrl(){
        $pre_page = session('HTTP_REFERER_URL');
        if (!$pre_page || session('has404')){
            session('has404',null);
            return  $this->defaultPage;
        }

        return $pre_page;
    }

    protected function setPreUrl(){
        if (session('front_userid')) return false;
        $url = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : null;
        if (!empty($url) && !in_array($url,$this->noPreviousPage)) {
            session('HTTP_REFERER_URL' , $url);
            return true;
        }
        return false;
    }

    /**
     * 验证手机号是否正确
     *
     * <AUTHOR>
     * @param number $mobile
     */
    function isMobile($mobile) {
        if (!is_numeric($mobile)) {
            return false;
        }
        return preg_match('#^1[\d]{10}$#', $mobile) ? true : false;
    }


    /**
     * 正则表达式验证email格式
     *
     * @param string $str    所要验证的邮箱地址
     * @return boolean
     */
    function isEmail($str) {
        if (!$str) {
            return false;
        }
        return preg_match('/^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i', $str) ? true : false;
    }

    /**
     * 页面404输出
     */
    public function _abort404(){
        session('has404',true);
        abort(404);
    }

    /**
     * 更新用户信息缓存
     * @param $arr  ['字段名'=> 值]
     */
    /*public function _updateUserInfo($arr = []){
        if (empty($arr)) return false;
        $info = session('front_info');
        foreach ($arr as $filed=>$value){
            $info[$filed] = $value;
        }
        session('front_info',$info);
        return true;
    }*/
}
