<?php
/**
 * 用户 控制器
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/29
 * Time: 14:11
 */
namespace app\home\controller;

use app\api\controller\Api;
use think\Config;
use think\Db;
use app\common\library\FileUpload;
use app\common\library\HuaweiObs;

class Member extends Home
{
    private $_passwordSalt = '';      // 密码加密的盐
    private $_encryptMethod = '';       // 密码加密方法
    private $_passwordEncrypt = null;    // 加密密码
    private $_password        = null;    // 密码明文
    private $_username        = null;    // 用户名
    private $_mobile          = '';    // 手机号
    private $_nameLengthMin   = 6;       // 用户名最小长度
    private $_nameLengthMax   = 11;      // 用户名最大长度
    private $_pswdLengthMin   = 6;       // 密码最小长度
    private $_pswdLengthMax   = 15;      // 密码最大长度
    private $_registerMsgCode   = 'SMS_462250586';   //'【祈盟文化游戏】您正在进行注册，验证码：[%s]，有效期10分钟，请勿向任何人提供您收到的验证码。';      // 手机号注册短信验证码格式
    private $_findPwdMsgCode   = 'SMS_462250586';   //'【祈盟文化游戏】您正在找回密码，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 忘记密码-手机验证身份
    private $_bindEmailVLMobile   = 'SMS_462250586';   //'【祈盟文化游戏】您正在换绑邮箱，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定邮箱-手机验证身份
    private $_bindEmail   = 'SMS_462250586';  //'【祈盟文化游戏】您正在绑定邮箱，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定邮箱
    private $_bindMobile   = 'SMS_462250586';  //'【祈盟文化游戏】您正在绑定手机，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定手机
    private $_bindMobileVLMobile   = 'SMS_462250586';  //'【祈盟文化游戏】您正在换绑手机，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定手机-手机验证身份
    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 登录
     */
    public function login() {

        if ($this->request->isAjax()){
            $username = $this->request->param('username' , '' );
            $password = $this->request->param('password' , '' );
            $Ticket   = $this->request->param('ticket' , '' );
            $Randstr  = $this->request->param('randstr' , '' );
            $username = strtolower($username);

            if ( empty($username)) $this->jsonResult([],-1,'请输入用户名');
            if ( empty($password) ) $this->jsonResult([],-2,'请输入密码');


            $slider = $this->sliderVelidate($Ticket,$Randstr);
            if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

            $this->_username = $username;
            $this->_password = $password;

            $member = model('Members')->field(["id","username","password","email","fromflag","reg_time",'flag','nickname','avatar'])->where(["username"=>$this->_username])->find();

            if ( empty($member) ) $this->jsonResult([],-2,'用户名或密码错误');
            if ( !empty($member['flag']) ) $this->jsonResult([],-1,'该用户已被冻结，请联系客服');


            if ( $member['password'] != $this->getPassword() ) $this->jsonResult([],-2,'用户名或密码错误');

            unset($member['password']);
            session('front_userid',$member['id']);
            session('front_account',$member['username']);
            $userinfo = array('front_userid'=>$member['id'], 'front_account'=>$username);
            $userinfo = json_encode($userinfo);
            setcookie('t', auth_code($userinfo, 'ENCODE'), time()+86400, '/', SECOND_DOMAIN);

            if (empty($member['nickname'])) {
                $newNickname = model('Members')->creatNickname($member['id']);
                /*$member['nickname'] = $newNickname;*/
            }
            /*session('front_info',$member);*/

            $preUrl = $this->getPrePageUrl();
            $this->success('登录成功！',$preUrl);
        }

        if (session('front_userid')){
            $this->redirect($this->defaultPage);
            exit;
        }
        $this->setPreUrl();
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch();
    }

    /**
     * 退出登录
     */
    public function logout(){
        setcookie('t', '', time()-1, '/', SECOND_DOMAIN);
        session(null);
        session_destroy();
        $this->jsonResult([],1,'退出登录成功！');
    }

    // 获取加密密码
    public function getPassword() {
        $password = (string)$this->_passwordEncrypt;

        if ( empty($password) ) {
            switch ( strtoupper($this->_encryptMethod) ) {
                case 'MD5':
                    $password = $this->_md5Code($this->_password, $this->_passwordSalt);
                    break;

                default:
                    $password = $this->_authCode($this->_password, $this->_passwordSalt);
                    break;
            }

            $this->_passwordEncrypt = $password;
        }

        return $password;
    }

    private function _md5Code($password,$salt='') {
        return md5($password . $salt);
    }

    private function _authCode($password,$salt='') {
        return auth_code($password, 'ENCODE', $salt);
    }

    public function userReg(){
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        $this->setPreUrl();
        return $this->fetch('user_reg');
    }
    public function phoneReg(){
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        $this->setPreUrl();
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch('phone_reg');
    }

    /**
     * 注册
     */
    public function register(){
        if ($this->request->isPost()){
            $type = $this->request->param('type', '');
            $username = $this->request->param('username','');
            $password = $this->request->param('password','');
            $password_1 = $this->request->param('passwordone','');
            $sms_code = $this->request->param('smsCode','');

            if ( empty($username)) {
                $this->jsonResult([],-1,($type == 'phone') ? '请输入正确的手机号' : '请输入正确的用户名，'.$this->_nameLengthMin.'-'.$this->_nameLengthMax.'位字母或数字');
            }

            if ( empty($password_1) ) {
                $this->jsonResult([],-2,'请输入正确的密码，'.$this->_pswdLengthMin.'-'.$this->_pswdLengthMax.'位字符');
            }
            if ( empty($password) ) {
                $this->jsonResult([],-3,'请再次输入密码');
            }

            if ($password_1 != $password){
                $this->jsonResult([],-3,'两次输入的密码不一致');
            }

            if ( ! $this->checkPassword($password,false) ) {
                $this->jsonResult([],-2,"请输入正确的密码，{$this->_pswdLengthMin} - {$this->_pswdLengthMax}位字符");
            }

            $this->_username = $username;
            if($type == 'phone') {
                $result = (new \app\common\library\Sms)->checkCode($username,$sms_code);
                if(!$result['status']) {
                    $this->error($result['msg']);
                }
                $this->_mobile = $username;
            } elseif($type == 'username') {
                $resUser = $this->checkUsername($username,false);
                if (!$resUser['code']) $this->jsonResult([],-1,$resUser['msg']);
            } else {
                $this->error('非法请求！');
            }

            if ( $this->isExistUser($this->_username) ) {
                $this->jsonResult([],-1,($type == 'phone') ? '该手机号已经被注册' : '该用户名已经被注册' );
            }

            if($type == 'username') {
                $Ticket   = $this->request->param('ticket' , '' );
                $Randstr  = $this->request->param('randstr' , '' );
                $resUser = $this->sliderVelidate($Ticket,$Randstr);
                if (!$resUser['code']) $this->jsonResult([],0,$resUser['msg'] );
            }


            $ip       = GetIP(0);
            $device   = getDevice();
            $agent    = 'default';

            /*! empty($passwordSalt) && $this->setPasswordSalt($passwordSalt);
            ! empty($encryptMethod) && $this->setEncryptMethod($encryptMethod);*/

            $this->_passwordEncrypt = $this->getPassword();

            $last_user_id = model('Members')->insertGetId(["username"=>$this->_username,"password"=>$this->_passwordEncrypt,"mobile"=>$this->_mobile,"agent"=>$agent, "device"=>$device,"ip"=>$ip,"reg_time"=>$_SERVER['REQUEST_TIME'], 'channel_id'=>MEMBER_DEFAULT_CHANNEL_ID]);


            if ( $last_user_id <= '0' ) {
                $this->error('注册失败');
                return false;
            }

            $this->insertChangeLog($last_user_id, $password,null,$this->_mobile);

            session('front_userid',$last_user_id);
            session('front_account',$this->_username);
            // 加密用户信息，用于跨二级域名登录
            $userinfo = array('front_account'=>$username,'front_userid'=>$last_user_id);
            $userinfo = json_encode($userinfo);
            setcookie('t', auth_code($userinfo, 'ENCODE'), time()+86400, '/', SECOND_DOMAIN);

            $newNickname = model('Members')->creatNickname($last_user_id);
            /*$member = [
                'id'       => $last_user_id ,
                "username" => $this->_username ,
                'nickname' => $newNickname ,
                "avatar"   => '' ,
                "reg_time" => $_SERVER['REQUEST_TIME']
            ];
            session('front_info',$member);*/


            $this->success('注册成功！',$this->getPrePageUrl());

        }
        $this->error('非法请求！');
    }

    /**
     * 发送手机验证码
     */
    public function sendCode($isExistUser = false) {

        $phone = $this->request->param('phone', '');

        if (!$this->checkMobile($phone)) {
            $this->error('请输入正确的手机号');
        }

        if ($isExistUser){
            $res = $this->isExistUser($phone);
            if ($res) $this->jsonResult([],-1,'该手机号已被注册');
        }


        $Ticket   = $this->request->param('ticket' , '' );
        $Randstr  = $this->request->param('randstr' , '' );


        $slider = $this->sliderVelidate($Ticket,$Randstr);
        if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

        $result = (new \app\common\library\Sms)->sendCode($phone,$this->_registerMsgCode);

        if ($result['status']) $this->success('验证码发送成功');

        $this->error($result['msg']);
    }

    // 校验用户名是否符合规则
    public function checkUsername($username,$isExit = true) {
        if($this->checkMobile($username)) {
            if ($isExit) $this->error('手机号请选择手机号注册方式');
            return ['code'=>0,'msg'=>'手机号请选择手机号注册方式'];
        } elseif ( strlen($username) < $this->_nameLengthMin || strlen($username) > $this->_nameLengthMax ||  preg_match('/[^0-9A-Za-z_]/', $username)) {
            if ($isExit) $this->error("请输入正确的用户名，{$this->_nameLengthMin} - {$this->_nameLengthMax}位字母或数字");
            return ['code'=>0,'msg'=>"请输入正确的用户名，{$this->_nameLengthMin} - {$this->_nameLengthMax}位字母或数字"];
        }else {
            $this->_username = $username;
            return ['code'=>1,'msg'=>''];
        }
    }

    /**
     * 检查是否为手机号
     */
    public function checkMobile($phone){
        $preg = '/^1[0123456789]{1}\d{9}$/';
        if (0 == preg_match($preg, $phone)) {
            return false;
        }
        return true;
    }

    /**
     * 判断用户是否存在
     * @param string $username 用户名
     * @return boolean
     */
    public function isExistUser($username) {
        $username = filterAndTrimInput($username);
        $result   = model('Members')->field('id')->where(["username"=>$username])->find();
        if ( isset($result['id']) && $result['id'] > 0 ) {
            return true;
        }
        return false;
    }

    public function insertChangeLog($userid,$password=null,$email=null,$mobile=null) {
        $record = ['userid' => $userid, 'ip' => GetIP(1), 'create_time' => NOW_TIMESTAMP];

        !empty($password) && $record['password'] = auth_code($password, 'ENCODE');
        !empty($email) && $record['email'] = $email;
        !empty($mobile) && $record['mobile'] = $mobile;

        return model('MemberHistory')->insertGetId($record);
    }

    /**
     * 校验密码是否符合规则
     * @param $password
     * @return bool
     */
    public function checkPassword($password,$isReturn = true) {
        if ( strlen($password) > $this->_pswdLengthMax || strlen($password) < $this->_pswdLengthMin ) {
            if ($isReturn) $this->error("请输入正确的密码，{$this->_pswdLengthMin} - {$this->_pswdLengthMax}位字符");
            return false;
        } else {
            $this->_password = strval($password);
            return true;
        }
    }

    /**
     * 忘记密码-- 确认账号
     */
    public function contactAccount(){
        if ($this->request->isAjax()){
            $username = input('post.username' , '' );
            if (empty($username)) $this->error('用户名不能为空');

            $Ticket   = $this->request->param('ticket' , '' );
            $Randstr  = $this->request->param('randstr' , '' );
            $slider = $this->sliderVelidate($Ticket,$Randstr);
            if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

            $info     = model('Members')->field('id,email,mobile,username,flag')->where(["username" => $username])->find();
            if (empty($info)) $this->error('用户名不存在');
            if ( !empty($info['flag']) ) $this->error('该用户已被冻结，请联系客服');

            if ( !empty($info['email'])) $info['email'] = mailObfuscation($info['email']);
            if ( !empty($info['mobile'])) $info['mobile'] = stringObfuscation($info['mobile'] , 3);
            session('check_uid',$info);
            $this->success('',getForgetPwdUrl(2));
        }
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch('contact_account');
    }

    /**
     * 忘记密码--提供验证方式
     */
    public function contactWay() {

        $info = session('check_uid');
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }

        if (!$info) $this->error('非法请求');

        $this->assign('info',$info);
        return $this->fetch('contact_way');
    }

    /**
     * 忘记密码--提供验证方式--邮箱或者手机号验证
     */
    public function contact() {
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        $typeArr = ['phone' , 'email'];
        $type    = $this->request->param('type' , '' );
        $info = session('check_uid');

        if (!$info || !in_array($type,$typeArr)) $this->redirect(getForgetPwdUrl(2));

        $this->assign('info',$info);
        $this->assign('type',$type);
        return $this->fetch();
    }

    /**
     * 忘记密码--发送验证码
     */
    public function resetPwdSendCode(){
        $type    = input('post.type' , '' );
        $id      = session('check_uid')['id'];
        $typeArr = ['phone' , 'email'];

        if (!in_array($type,$typeArr) || empty($id)) $this->error('非法请求');
        $info     = model('Members')->field('id,username,email,mobile')->where(["id" => $id , 'flag' => 0])->find();

        if ($type == $typeArr[0]){
            $result = (new \app\common\library\Sms)->sendCode($info['mobile'],$this->_findPwdMsgCode);

        }else{
            $result = (new \app\common\library\Mail)->sendCodeMailByType($info['username'], $info['email']);
        }

        if(isset($result) && $result['status']){
            $this->jsonResult([],1,'发送成功');
        }else{
            $this->jsonResult([],0,$result['msg']);
        }
    }

    /**
     * 忘记密码--验证验证码
     */
    public function resetPwdCheckCode() {
        $id      = session('check_uid')['id'];
        $code    = input('post.code' , 0 , 'intval');
        $type    = input('post.type' , '' );
        $typeArr = ['phone' , 'email'];

        if ( !in_array($type , $typeArr) || empty($id)) $this->jsonResult([],0,'非法请求');
        if (empty($code)) $this->jsonResult([],0,'请输入验证码');
        $info = model('Members')->field('id,username,email,mobile')->where(["id" => $id , 'flag' => 0])->find();

        if ($type == $typeArr[0]) {
            $result = (new \app\common\library\Sms)->checkCode($info['mobile'] , $code);

        } else {
            $result = (new \app\common\library\Mail)->checkCode($info['username'] , $info['email'] , $code);
        }

        if (isset($result) && $result['status']) {
            session('hasCheck',true);
            $this->success('验证成功',getForgetPwdUrl(3));
        }
        $errMsg = $result['msg'];
        $this->jsonResult([],0,$errMsg);
    }

    /**
     * 忘记密码--重置密码
     */
    public function forgetPwdToReset(){
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        if ($this->request->isAjax()){
            $id        = session('check_uid')['id'];
            $password1 = input('post.password1' , '' );
            $password2 = input('post.password2' , '' );

            if (empty($id)) $this->jsonResult([],0,'非法请求');
            if (empty($password1)) $this->jsonResult([],-1,'请输入新密码，'.$this->_pswdLengthMin.'-'.$this->_pswdLengthMax.'位字符');
            if (empty($password2)) $this->jsonResult([],-2,'请再次输入密码');
            //if (!session('hasCheck')) $this->error('请先通过身份验证');
            if ($password1 != $password2) $this->jsonResult([],-2,'两次输入的密码不一致');

            $res = $this->resetPassword($id,$password1);
            if ($res == 3) $this->jsonResult([],-1,'请输入正确的密码，'.$this->_pswdLengthMin.'-'.$this->_pswdLengthMax.'位字符');
            if ($res == 1 || $res == 2) {
                session('hasCheck',null);
                session('check_uid',null);

                $this->success('',getForgetPwdUrl('4.'.$res));
            }
            if ($res == 0) $this->jsonResult([],-2,'新密码不能与旧密码相同');
        }
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch('forget_pwd_to_reset');
    }

    /**
     * 重置密码
     *$flag: 账号是否解冻 1解冻  0不解冻
     * return：0新密码不能与旧密码相同，1密码重置成功，2密码重置失败，3密码格式不正确
     */
    public function resetPassword($id,$password1,$flag = 0){

        $res1 = $this->checkPassword($password1,false);
        if (!$res1) return 3;

        $info = model('Members')->field('id,username,email,mobile,password')->where(["id" => $id])->find();

        $this->_passwordEncrypt = $this->getPassword();
        if ($this->_passwordEncrypt == $info['password']) return 0;
        $data = ['password'  => $this->_passwordEncrypt];
        if ($flag == 1) $data['flag'] = 0;

        $res = model('Members')->save($data,['id' => $id]);

        if ($res){
            session(null);
            session_destroy();
            setcookie('t', '', time()-1, '/', SECOND_DOMAIN);

            $record = [
                'userid' => $id ,
                'ip' => GetIP(1) ,
                'password' => $this->_passwordEncrypt ,
                'create_time' => NOW_TIMESTAMP,
            ];
            model('MemberHistory')->insertGetId($record);
            return 1;
        }
        return 2;
    }

    /**
     * 忘记密码--重置结果
     */
    public function forgetPwdResult(){
        if (session('front_userid')){
            $this->redirect($this->defaultPage);exit;
        }
        $res = input('res',2);

        $this->assign('res',$res);
        return $this->fetch('forget_pwd_result');
    }

    /**
     * 获取用户资料
     */
    public function getUserInfo(){
        $id = session('front_userid');
        $info = model('Members')->alias('m')
            ->join('cy_memberstwo t','m.id=t.userid','left')
            ->field('m.id,m.username,m.nickname,t.sex,t.qq,t.birthday,t.zipcode,t.address,t.id as memberstwo_id,m.avatar')
            ->where(["m.id" => $id , 'm.flag' => 0])
            ->find();
        return $info;
    }

    /**
     * 个人中心--个人资料
     */
    public function personalInfo(){
        $this->_isLogin();
        $defaultBrith = '0000-00-00';
        $info = $this->getUserInfo();

        if ($info['birthday'] == $defaultBrith) $info['birthday'] = null;
        $this->assign('info',$info);
        return $this->fetch('personal_info');
    }

    /**
     * 个人资料编辑
     */
    public function personalInfoEdit(){
        $this->_isLogin();
        $defaultBrith = '0000-00-00';
        $info = $this->getUserInfo();

        /*dump($info['avatar']);*/
        if ($this->request->isPost()){
            $qq = input('post.qq', '');
            $sex = input('post.sex', '');
            $birthday = input('post.birthday', '');
            $address = input('post.address', '');
            $zipcode = input('post.zipcode', '');
            $nickname = input('post.nickname', '');

            $birthday_bool = false;
            if ((empty($birthday) && $info['birthday'] == $defaultBrith) || $birthday == $info['birthday']){
                $birthday_bool = true;
            }

            // 是否修改了用户资料
            $isUpate1 = true;
            $isUpate2 = true;
            if ($qq == $info['qq'] &&$sex==$info['sex'] &&$birthday_bool &&$zipcode==$info['zipcode'] && $address==$info['address']){
                $isUpate1 = false;
            }
            if ($nickname == $info['nickname'] ){
                $isUpate2 = false;
            }

            if (!$isUpate1 && !$isUpate2) $this->jsonResult([],1,'请修改内容后保存');

            if ($isUpate2 ){
                $res = validataNickname($nickname);
                if (!$res['code']) $this->jsonResult('nickname',-4,$res['msg']);
            }

            if(!empty($qq) && 0 == preg_match('/^\d*$/', $qq)) {
                $this->jsonResult('',-1,'请输入正确的QQ号');
            }
            if(!empty($zipcode) && 0 == preg_match('/^(\d{6})?$/', $zipcode)) {
                $this->jsonResult('',-2,'请输入正确的邮政编码');
            }
            if(!empty($address) && iconv_strlen($address,"UTF-8") >40) {
                $this->jsonResult('',-3,'联系地址不能超过40个字');
            }
            $data = [
                'sex'      => $sex ,
                'qq'       => $qq ,
                'address'  => $address ,
                'zipcode'  => $zipcode ,
                'birthday' => $birthday ,
            ];

            $id = session('front_userid');

            // 启动事务
            Db::startTrans();

            // 判断是否有添加过 cy_memberstwo
            if ( !empty($info['memberstwo_id'])) {
                $res = true;
                if ($isUpate1 ){
                    $res = model('MembersTwo')->save($data , ['userid' => $id]);
                }

            } else {
                $data['userid']   = $id;
                $data['username'] = session('front_account');
                $res              = model('MembersTwo')->insertGetId($data);
            }

            $rest = true;
            if ($isUpate2){
                $rest = model('Members')->save(['nickname'=>$nickname] , ['id' => $id]);
            }

            if ($res && $rest){
                Db::commit();
                // 更新个人资料缓存
                /*$data['nickname'] = $nickname;
                $this->_updateUserInfo($data);*/
                $this->jsonResult([],1,'个人资料修改成功');
            }
            Db::rollback();

            $this->jsonResult([],0,'操作失败');
        }
        if ($info['birthday'] == $defaultBrith) $info['birthday'] = '';
        $this->assign('info',$info);
        return $this->fetch('personal_info_edit');
    }

    /**
     * 查询条件--时间格式化
     * @param $start
     * @param $end
     * @param $fieldTime    字段名
     * @param $today   是否默认查询今天
     * @return array
     */
    public function _setParamTime($start,$end,$fieldTime='p.create_time',$today = true){
        $condition = [];
        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $condition[$fieldTime] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $condition[$fieldTime] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $condition[$fieldTime] = ['<=', strtotime($end . ' 23:59:59')];
        } else {
            if ($today){
                $end   = date('Y-m-d', time());

                $condition[$fieldTime] = [
                    ['>=', strtotime($start)],
                    ['<=', strtotime($end . ' 23:59:59')],
                ];
            }
        }

        return ['condition'=>$condition,'start'=>$start,'end'=>$end];
    }

    /**
     * 个人中心--充值记录
     */
    public function personalPay(){
        $this->_isLogin();
        $start          = $this->request->param('start');
        $end            = $this->request->param('end');
        $nickname         = $this->request->param('nickname', '');
        $orderid        = $this->request->param('orderid', '');
        $status        = $this->request->param('status', '');

        $condition = ['p.userid'=>session('front_userid')];
        if (!empty($nickname)) {
            $condition['g.nickname'] = ['like','%'.$nickname.'%'];
        }
        if (!empty($orderid)) {
            $condition['p.orderid'] = $orderid;
        }
        if ($status != '') {
            $condition['p.status'] = $status;
        }

        $timeArr = $this->_setParamTime($start,$end);
        $condition = array_merge($condition,$timeArr['condition']);

        // 统一显示为“微信支付”。
        $wxzfArr = ['wxpay','swiftwxpay','wxpay-h5','ipaywxpay','ios-wxpay'];
        // 统一显示为“微信混合支付”。
        $wxhhzfArr = ['mix-swiftwxpay','mix-ipaywxpay','mix-wxpay-h5'];
        // 统一显示为“支付宝支付”。
        $zfbzfArr = ['zfb','ios-zfb'];
        // 平台币支付显示规则
        $lbArr = array_merge(['ptb','mix-zfb'],$wxhhzfArr);
        // 第三方支付显示规则
        $thirdArr = array_merge(['mix-zfb'],$wxhhzfArr,$wxzfArr,$zfbzfArr);

        $PayModel = model('Common/Pay');

        $list = $PayModel->alias('p')->field('p.id,p.orderid,p.amount,p.real_ptb,p.paytype,p.real_amount,p.productname,p.status,p.pay_time,p.create_time,g.nickname')
            ->join('cy_game g','p.gameid=g.id','left')
            ->where($condition)
            ->order('p.create_time desc')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function($item, $key) use($wxzfArr,$wxhhzfArr,$lbArr,$thirdArr,$zfbzfArr){
                $item['lb_show'] = 0;
                $item['third_show'] = 0;
                if (in_array($item['paytype'],$lbArr)) $item['lb_show'] = 1;
                if (in_array($item['paytype'],$thirdArr)) $item['third_show'] = 1;

                if (in_array($item['paytype'],$wxzfArr)){
                    $item['paytype'] = '微信支付';
                }elseif (in_array($item['paytype'],$wxhhzfArr)){
                    $item['paytype'] = '微信混合支付';
                }elseif (in_array($item['paytype'],$zfbzfArr)){
                    $item['paytype'] = '支付宝';
                }else{
                    $item['paytype'] = config('paytype.'.$item['paytype']);
                }

                return $item;
            });

        $this->assign('start',$timeArr['start']);
        $this->assign('end',$timeArr['end']);
        $this->assign('list',$list);
        $this->assign('page', $list->render());
        $this->assign('game_list',model('Common/Game')->getAllByCondition('id,name'));
        return $this->fetch();
    }

    /**
     * 个人中心--存号箱
     */
    public function memberGift(){
        $this->_isLogin();
        $title = input('title' , '' );
        $start = $this->request->param('start');
        $end   = $this->request->param('end');

        $condition = ['a.username'=>session('front_account')];
        if (!empty($title)){
            $condition['l.title'] = ['like','%'.$title.'%'];
        }

        $timeArr = $this->_setParamTime($start,$end,'a.create_time');
        $condition = array_merge($condition,$timeArr['condition']);
        $list = model('Libaolog')->alias('a')
            ->join('cy_libaoinfo l','l.id=a.libaoid')
            ->join('cy_game g','g.id=l.gameid','left')
            ->field('a.id,l.title,a.username,a.create_time,g.name,a.code,l.content,l.starttime,l.endtime')
            ->where($condition)
            ->order('a.create_time desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $this->assign('start',$timeArr['start']);
        $this->assign('end',$timeArr['end']);
        $this->assign('list',$list);
        $this->assign('page', $list->render());
        return $this->fetch('member_gift');
    }

    /**
     * 个人中心--账号安全
     */
    public function personalAccount() {
        $this->_isLogin();
        $id   = session('front_userid');
        $info = model('Members')->alias('m')
            ->join('cy_memberstwo t' , 'm.id=t.userid' , 'left')
            ->field('m.id,m.email,m.mobile,t.idcard,t.realname')
            ->where(["m.id" => $id , 'm.flag' => 0])
            ->find();

        $info['isVerified'] = 1;
        if (empty($info['realname']) || empty($info['idcard']) || ((- 1 == $info['realname']) && (- 1 == $info['idcard']))) {
            $info['isVerified'] = 0;
        }

        if ( !empty($info['email'])) $info['email'] = mailObfuscation($info['email']);
        if ( !empty($info['mobile'])) $info['mobile'] = mobileObfuscation($info['mobile']);

        $this->assign('info',$info);
        return $this->fetch();
    }

    /**
     * 个人中心--修改密码
     */
    public function personalChangePwd(){
        $this->_isLogin();
        if($this->request->isPost() ){
            $oldPwd    = input('post.oldPwd' , '' );
            $password1 = input('post.password1' , '' );
            $password2 = input('post.password2' , '' );

            if (empty($oldPwd)) $this->jsonResult([],0,'请输入旧密码');
            if (empty($password1)) $this->jsonResult([],-1,'请输入密码，6-15位字符');
            if (empty($password2)) $this->jsonResult([],-2,'请再次输入密码');

            if ($oldPwd == $password1) $this->jsonResult([],-1,'新密码不能与旧密码相同');
            if ($password1 != $password2) $this->jsonResult([],-2,'两次输入的密码不一致');
            $this->checkPassword($password1);

            $id = session('front_userid');
            $info = model('Members')->field('id,username,email,mobile,password')->where(["id" => $id, 'flag' => 0])->find();

            $this->_password = $oldPwd;
            $this->_passwordEncrypt = $this->getPassword();

            if ($this->_passwordEncrypt != $info['password']) $this->jsonResult([],0,'输入的旧密码错误');

            $this->_password = $password1;$this->_passwordEncrypt = '';
            $this->_passwordEncrypt = $this->getPassword();

            $res = model('Members')->save(['password'  => $this->_passwordEncrypt],['id' => $id]);
            model('MemberHistory')->insertGetId(['password'  => $this->_passwordEncrypt,'userid'=> $id,'ip' => GetIP(1) ,'create_time' => NOW_TIMESTAMP]);

            if ($res) $this->success('修改成功',getUserCenterUrl('4'));

            $this->jsonResult([],0,'修改失败');
        }
        return $this->fetch();
    }

    /**
     * 个人中心--实名认证--页面
     */
    public function personalIdentityPage(){
        $this->_isLogin();

        $id   = session('front_userid');
        $info = model('Members')->alias('m')
            ->join('cy_memberstwo t' , 'm.id=t.userid' , 'left')
            ->field('m.id,m.email,m.mobile,t.idcard,t.realname')
            ->where(["m.id" => $id , 'm.flag' => 0])
            ->find();

        if (empty($info['realname']) || empty($info['idcard']) || ((- 1 == $info['realname']) && (- 1 == $info['idcard']))) {
            // 未实名认证
            return $this->fetch('identity');
        }

        if ( !empty($info['realname'])) $info['realname'] = usernameObfuscation($info['realname']);
        if ( !empty($info['idcard'])) $info['idcard'] = idcardObfuscation($info['idcard']);

        $this->assign('info',$info);
        return $this->fetch('identity_info');
    }

    /**
     * 个人中心--实名认证
     */
    public function personalIdentity(){
        $this->_isLogin();

        $data = [
            'realname' => input('post.realname', ''),
            'idcard'   => input('post.idcard', ''),
        ];
        $result = $this->validate($data, [
            ['realname', 'require|length:2,10|chs', '请输入姓名|姓名只能是2-10位的汉字|姓名只能是2-10位的汉字'],
            ['idcard', 'require|identityNumber', '请输入身份证号码|身份证号码格式不正确'],
        ]);
        if (true !== $result) $this->jsonResult($result,0,$result);

        // 通过身份证验证年龄
        if (!isMeetAgeByIDCard($data['idcard'],9,120)) $this->jsonResult([], 0, '您未满足年龄要求（9-120岁），无法通过认证');

        $res = model('MembersTwo')->updateInfo($data, session('front_userid'));

        if ($res) $this->success('实名认证成功',getUserCenterUrl('4'));
        $this->jsonResult('', 0, '认证失败');
    }

    /**
     * 个人中心--绑定手机或邮箱--1
     */
    public function toBindOne(){
        $this->_isLogin();
        $type = input('type', '');
        if (!in_array($type,['mobile','email'])) {
            $this->error('请求错误');
        }

        $id   = session('front_userid');
        $info = model('Members')->alias('m')
            ->field('m.id,m.email,m.mobile')
            ->where(["m.id" => $id , 'm.flag' => 0])
            ->find();

        // 请求绑定手机，就判断是否绑定邮箱；请求绑定邮箱，就判断是否绑定手机
        $runable = 'no'; // 是否去先验证另外一个联系方式
        /*$lxfs = '';
        $infoData = '';*/

        if ($type == 'mobile' && (!empty($info['email']) || !empty($info['mobile'])) ) {
            $runable = 'email';
           /* $lxfs = $info['email'];*/
           /* $infoData = stringObfuscation($info['email'] , 3);*/
        }
        if ($type == 'email' && (!empty($info['email']) || !empty($info['mobile']))) {
            $runable = 'mobile';
           /* $lxfs = $info['mobile'];*/
            /*$infoData = stringObfuscation($info['mobile'] , 3);*/
        }

        if ($runable == 'no'){
            $appid        = Config::get('SLIDER_VERIFICATION.APPID');
            $this->assign('appid',$appid);
            $this->assign('type',$type);
            return $this->fetch('bind_page');
        }

        session('velidate_info',['type'=>$type,'info_data'=>$info,'velidate_type'=>'']);
        $this->assign('type',$type);
        /*$this->assign('info_data',$infoData);*/
        $this->assign('info',$info);
        return $this->fetch('bind_other');
    }

    /**
     * 个人中心--绑定手机或邮箱--2  验证其他方式的页面
     */
    public function toVelidatePage(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->redirect(getUserCenterUrl(1));
        $type = input('type', '');
        if (!in_array($type,['mobile','email'])) {
            $this->redirect(getUserCenterUrl(1));
        }

        $velidate_info['velidate_type'] = $type;
        session('velidate_info',$velidate_info);
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        $this->assign('info',$velidate_info);
        return $this->fetch('velidate_page');
    }

    /**
     * 个人中心--绑定手机或邮箱--3  发送验证码
     */
    public function toVelidateSendCode(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->error('系统繁忙！');

        $Ticket   = $this->request->param('ticket' , '' );
        $Randstr  = $this->request->param('randstr' , '' );
        $slider = $this->sliderVelidate($Ticket,$Randstr);
        if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

        $temple = ''; // 短信或邮件模板
        if ($velidate_info['velidate_type'] == 'mobile'){

            if ($velidate_info['type'] == 'mobile'){
                $temple = $this->_bindMobileVLMobile;
            }else{
                if (empty($velidate_info['info_data']['email'])){
                    $temple = $this->_bindEmail;
                }else{
                    $temple = $this->_bindEmailVLMobile;
                }
            }
            $result = (new \app\common\library\Sms)->sendCode($velidate_info['info_data']['mobile'],$temple);

        }else{
            if ($velidate_info['type'] == 'email'){
                $temple = 'bindEmailVLEmail';
            }else{
                if (empty($velidate_info['info_data']['mobile'])){
                    $temple = 'bindMobil';
                }else{
                    $temple = 'bindMobilVLEmail';
                }
            }
            $result = (new \app\common\library\Mail)->sendCodeMailByType(session('front_account'), $velidate_info['info_data']['email'],$temple);
        }

        if(isset($result) && $result['status']){
            $this->jsonResult([],1,'发送成功');
        }else{
            $this->jsonResult([],0,$result['msg']);
        }
    }

    /**
     * 个人中心--绑定手机或邮箱--4 验证 手机或邮箱验证码
     */
    public function velidateCode(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->redirect(getUserCenterUrl(1));
        $code    = input('post.code' , 0 , 'intval');

        if (empty($code)) $this->jsonResult([],0,'请输入验证码');

        if ($velidate_info['velidate_type'] == 'mobile') {
            $result = (new \app\common\library\Sms)->checkCode($velidate_info['info_data']['mobile'] , $code);

        } else {
            $result = (new \app\common\library\Mail)->checkCode(session('front_account') , $velidate_info['info_data']['email'] , $code);
        }

        if (isset($result) && $result['status']) {
            $this->success('验证成功',getAccountUrl(2.2) );
        }

        $this->jsonResult([],0,$result['msg']);
    }

    /**
     * 个人中心--绑定手机或邮箱--5  直接绑定的页面
     */
    public function bindPage(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->redirect(getUserCenterUrl(1));
        $this->assign('type',$velidate_info['type']);
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch();
    }

    /**
     * 个人中心--绑定手机或邮箱--6  发送验证码
     */
    public function bindSendCode(){

        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->error('系统繁忙');
        $this->directBindSendCode($velidate_info['type']);
    }

    /**
     * 个人中心--绑定手机或邮箱--直接绑定页面--6  发送验证码
     */
    public function directBindSendCode($type=''){

        $contact = $this->request->param('contact');
        if (empty($type)) $this->error('系统繁忙');
        if (empty($contact)) {
            if ($type == 'mobile') $this->error('请输入正确的手机号');
            $this->error('请输入正确的邮箱');
        }

        $Ticket   = $this->request->param('ticket' , '' );
        $Randstr  = $this->request->param('randstr' , '' );
        $slider = $this->sliderVelidate($Ticket,$Randstr);
        if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

        $velidate_info = session('velidate_info');
        if ($type == 'mobile' && $contact == $velidate_info['info_data']['mobile']){
            $this->error('新旧手机号不能相同');
        }
        if ($type == 'email' && $contact == $velidate_info['info_data']['email']){
            $this->error('新旧邮箱不能相同');
        }

        if ($type == 'mobile'){
            $result = (new \app\common\library\Sms)->sendCode($contact,$this->_bindMobile);

        }else{
            $result = (new \app\common\library\Mail)->sendCodeMailByType(session('front_account'), $contact,'bindEmail');
        }

        if(isset($result) && $result['status']){
            $this->jsonResult([],1,'发送成功');
        }
        $this->jsonResult([],0,$result['msg']);
    }

    /**
     * 个人中心--绑定手机或邮箱--7  进行绑定操作
     */
    public function bindContact(){
        $velidate_info = session('velidate_info');
        if (empty($velidate_info)) $this->error('系统繁忙');
        $this->directBindContact($velidate_info['type']);
    }

    /**
     * 个人中心--绑定手机或邮箱--7  进行绑定操作(直接进行绑定)
     */
    public function directBindContact($type = ''){
        $contact = $this->request->param('contact');
        $code    = $this->request->param('code' , 0 , 'intval');
        if (!in_array($type,['mobile','email'])) $this->error('系统繁忙');
        if (empty($contact)) {
            if ($type == 'mobile') $this->error('请输入正确的手机号');
            $this->error('请输入正确的邮箱');
        }
        if (empty($code)) $this->jsonResult([],0,'请输入验证码');


        if ($type == 'mobile') {
            $result = (new \app\common\library\Sms)->checkCode($contact , $code);

        } else {
            $result = (new \app\common\library\Mail)->checkCode(session('front_account') , $contact , $code);
        }

        if (isset($result) && $result['status']) {

            $userid = session('front_userid');
            $res = model('Members')->save([$type  => $contact],['id' => $userid]);
            model('MemberHistory')->insertGetId([$type  => $contact,'userid'=> $userid,'ip' => GetIP(1),'create_time' => NOW_TIMESTAMP]);

            session('velidate_info',null);
            $this->success('绑定成功',getUserCenterUrl(4));
        }
        $this->jsonResult([],0,$result['msg'] );
    }

    /**
     * 滑块验证
     * @param $Ticket
     * @param $Randstr
     * @return array
     */
    public function sliderVelidate($Ticket,$Randstr){
        // todo 开发环境跳过滑块验证
        if (APP_STATUS == 'dev') return ['code'=>1,'msg'=>''];

        if ( empty($Ticket) || empty($Randstr) ) return ['code'=>0,'msg'=>'非法请求'];

        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $AppSecretKey = Config::get('SLIDER_VERIFICATION.AppSecretKey');
        $url          = Config::get('SLIDER_VERIFICATION.VERIFYURL');
        $UserIP       = GetIP(1);

        $params = array(
            "aid"          => "$appid" ,
            "AppSecretKey" => $AppSecretKey ,
            "Ticket"       => $Ticket ,
            "Randstr"      => $Randstr ,
            "UserIP"       => $UserIP
        );
        $paramstring = http_build_query($params);
        $content = txcurl($url,$paramstring);
        $result = json_decode($content,true);
        if($result){
            if($result['response'] == 1){
                return ['code'=>1,'msg'=>''];
            }else{
                return ['code'=>0,'msg'=>'系统繁忙，请稍后再试！'];
            }
        }else{
            return ['code'=>0,'msg'=>'系统繁忙，请稍后再试！'];
        }
    }

    /**
     * 我的游戏--搜索条件
     */
    private function _paramMyGame(){
        $keyword = input('title','');
        $status = input('status',0);

        $where   = [
            'a.member_id' => session('front_userid') ,
        ];

        if ($keyword) $where['c.nickname'] = ['like','%'.$keyword.'%'];
        if ($status == 1){
            $where['c.cooperation_status'] = ['in',[0,1,2]];
            $where['c.is_show'] = 1;
        }elseif ($status == 2){
            $where['c.cooperation_status'] = ['in',[0,1,2]];
            $where['c.is_show'] = 0;
        }elseif ($status == 3){
            $where['c.cooperation_status'] = 3;
        }

        return $where;
    }

    /**
     * 我的游戏
     */
    public function mygame($copydata = 0){
        $this->_isLogin();
        $GameCtr = (new \app\home\controller\Game);

        $where   = $this->_paramMyGame();
        $num = 10;
        $mobile = 1;
        if ($copydata) {
            $num = 8;
            $mobile = 2;
        }

        $listArr = $GameCtr->_getMyGame($where,$mobile);
        $list    = $listArr['list'];

        $gameidArr = $typeArr = $subjectArr = [];
        if (count($list)) {
            $data       = $list->toArray()['data'];
            $typeArr    = array_unique(array_column($data , 'type'));
            $subjectArr = array_unique(array_column($data , 'subject'));
            $gameidArr = array_unique(array_column($data , 'id'));
        }

        $isrand    = 1;
        $condition = [];
        if (count($typeArr) || count($subjectArr) || count($gameidArr)) {
            $isrand     = 0;
            $typeArr    = array_merge(array_diff($typeArr , array(0)));
            $subjectArr = array_merge(array_diff($subjectArr , array(0)));
            $gameidArr = array_merge(array_diff($gameidArr , array(0)));
            $condition  = [
                'game.id'    => ['not in' , $gameidArr] ,
                'game.type'    => ['in' , $typeArr] ,
                'game.subject' => ['in' , $subjectArr] ,
            ];
        }
        $guesList = $GameCtr->_guessLikeGame($isrand , $condition,$num,$mobile);

        if ($copydata) return ['list'=>$list, 'page'=>$listArr['page'], 'guesList'=>$guesList];

        $this->assign('list' , $list);
        $this->assign('page' , $listArr['page']);
        $this->assign('guesList' , $guesList);
        return $this->fetch();
    }

    /**
     * 我的游戏分页
     */
    public function ajaxMyGame(){
        $where   = $this->_paramMyGame();
        $GameCtr = (new \app\home\controller\Game);
        $listArr = $GameCtr->_getMyGame($where);
        if ($this->request->isAjax()) $this->jsonResult($listArr,1);
        $this->jsonResult([],0,'请求错误！');
    }

    /**
     * 用户头像上传
     * 由于用户头像经过裁剪，前端传过来的是base64位的数据流，故要先保存在服务器，然后调接口上传到华为云，最后删除服务器上的图片。
     * @return bool
     */
    public function uploadUserImg(){
        $img = input('file','');
        if (empty($img)) $this->error('请上传图片');

        $urlData = $this->base64_image_content($img);

        if (!$urlData['code']) $this->error($urlData['msg']);

        $obs = new HuaweiObs();
        $fileName = explode('usericon/',$urlData['url'])[1];
        $dir = 'image/members/icons/';

        $result = $obs->putObject($urlData['url'],$fileName,$dir);

        unlink($urlData['url']);
        if($result){
            if($result['HttpStatusCode']=='200'){
                // 上传成功，写库
                model('Members')->save(['avatar'=>$dir.$fileName],['id'=>session('front_userid')]);
                /*$this->_updateUserInfo(['avatar'=>$dir.$fileName]);*/
                $this->jsonResult($dir.$fileName,1,'上传成功');
            }
        }

        $this->error('网络出现错误，请稍后再试');
    }

    /**
     * base64格式编码转换为图片并保存对应文件夹
     * @param $base64_image_content
     * @param $path     runtime目录下
     * @param $type     图片格式要求
     * @param $max      图片大小，兆为单位
     * @return bool|string
     */
    public function base64_image_content($base64_image_content,$path = 'usericon',$typeArr=['jpg','png'],$max=3){
        $path = RUNTIME_PATH.$path;
        //匹配出图片的格式
        if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $base64_image_content, $result)){

            $type = $result[2];

            if(count($typeArr) && !in_array($type,$typeArr)) return ['code'=>0,'msg'=>'图片格式不对，请重新上传'];

            $new_file = $path."/";
            if(!file_exists($new_file)){
                //检查是否有该文件夹，如果没有就创建，并给予最高权限
                mkdir($new_file, 0700,true);
            }

            $url = date('YmdHi',time()).uniqid().rand(1,999).".{$type}";
            $new_file = $new_file.$url;

            if (file_put_contents($new_file, base64_decode(str_replace($result[1], '', $base64_image_content)))){

                if($max && ceil(filesize($new_file)/1024/1024) > $max) {
                    unlink($new_file);
                    return ['code'=>0,'msg'=>'图片过大，请重新上传'];
                }

                return ['code'=>1,'msg'=>'','url'=>$new_file];
            }else{
                return ['code'=>0,'msg'=>'上传失败，请稍后再试'];
            }
        }else{
            return ['code'=>0,'msg'=>'上传失败，请稍后再试'];
        }
    }

    /**
     * 我要反馈
     */
    public function feedback(){
        $kefu = model('Kefu')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->field('qq')->find();
        $this->assign('kefu',$kefu['qq']);
        return $this->fetch();
    }

    public function ajaxFeedback($from = ''){
        $data = [
            'type'    => input('post.type') ,
            'content' => input('post.content') ,
            'qq'      => input('post.qq') ,
        ];

        if (empty($data['type'])) $this->error('请选择反馈类型','','type');
        if (empty($data['content']) || mb_strlen($data['content'],'UTF8') < 5) $this->error('请至少输入5个字','','content');
        if (mb_strlen($data['content'],'UTF8') > 500) $this->error('不可超过500个字','','content');

        $regexp = "/^[1-9]\d{4,9}$/";
        if (!empty($qq) && !preg_match($regexp,$qq)) $this->error('请输入正确的QQ号','','qq');

        //图片上传
        $imgUrl = '';
        $imgobj = request()->file('file1');

        if (!empty($imgobj)){
            $file = new FileUpload();
            foreach ($imgobj as $value) {

                $bigimage_url = $file->set('allowExt' , 'jpg,jpeg,png')->set('maxsize' , 1024000 * 3)->set('dir' , 'image/webfeedback/')->upload($value);

                if ( !empty($value) && $bigimage_url == false) {
                    $this->error('图片上传错误，请稍后再试','','img');
                }

                if ( !empty($bigimage_url))
                    $imgUrl .= $bigimage_url.',';
            }
            $imgUrl = substr($imgUrl , 0 , - 1);
        }

        $data['img_url']     = $imgUrl;
        $data['create_time'] = NOW_TIMESTAMP;
        $data['order_num']   = date("YmdHis" , NOW_TIMESTAMP) . rand(100 , 999);
        if ( $from == 'mobile') $data['from'] = 2;
        if ( !empty(session('front_userid'))) $data['user_id'] = session('front_userid');
        model('WebFeedback')->insert($data);
        $this->success('提交成功，感谢您的反馈。');
    }
}
