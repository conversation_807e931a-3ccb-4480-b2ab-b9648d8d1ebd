{extend name="layout/pay" /}
{block name="title"}<title>祈盟文化</title>{/block}
{block name="header"}
<!-- 引入样式 -->
<!-- import CSS -->
<!--引入 element-ui 的样式，-->
<!--<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">-->
<!--&lt;!&ndash; 必须先引入vue，  后使用element-ui &ndash;&gt;-->
<!--<script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.js"></script>-->
<!--&lt;!&ndash; 引入element 的组件库&ndash;&gt;-->
<!--<script src="https://unpkg.com/element-ui/lib/index.js"></script>-->

<link rel="stylesheet" href="__STATIC__/vue/theme-chalk/index.css">
<!-- 必须先引入vue，  后使用element-ui -->
<script src="__STATIC__/vue/vue.js"></script>
<!-- 引入element 的组件库-->
<script src="__STATIC__/vue/theme-chalk/index.js"></script>

<script src="__STATIC__/js/jquery v1.11.3.min.js"></script>
<link rel="stylesheet" href="__STATIC__/css/coin/topup.css">
{/block}


{block name="content"}
<div id="app" class="coin" v-loading="loading" element-loading-text="支付中..." element-loading-spinner="el-icon-loading"
     element-loading-background="rgba(0, 0, 0, 0.8)">
    <div class="classifyincreased_b">
        <div class="box">
            <!--            <div class="title" @click="topHide">-->
            <!--                <img src="__STATIC__/images/coin/组 <EMAIL>" alt=""/>-->
            <!--            </div>-->
            <div class="outer-div">
                <header class="header">
                    <ul class="tab-tilte"  @click="handleTabs">
                        <li v-for="(item, index) in payList" :class="{ active: tab == item.tab }" @click="tab = item.tab">
                            <div class="li_a">
                                <img :src=item.image alt=""/><span>{{item.name}}</span>
                            </div>
                        </li>
                    </ul>
                </header>

                <div class="tab-content">
                    <div v-show="tab == 'zfbsmzf' || tab == 'kdsm' || tab =='kdh5' || tab=='wxsmzf' || tab == 'xzzfbzf' || tab=='wx-wap'">
                        <!-- 账号: -->
                        <div class="info">

                            <div>
                                <el-select v-model="accountType" class="info_select" placeholder="请选择账号" @change="handleValue"
                                >
                                    <el-option v-for="item in account" :key="item.value" :label="item.label"
                                               :value="item.value">
                                    </el-option>
                                </el-select>
                                <span class="b" v-if="accountType==1">{$userinfo['username'] ?? ''}</span>
                                <span v-if="accountType==2">
                                    <input type="text" placeholder="请输入账号" v-model="username" style="height: 34px;">
                                    确认账号: <input type="text" placeholder="确认账号" v-model="passusername"
                                                 @change="handleChangeUserName" style="height: 34px;">
                                </span>
                            </div>

                            <div v-if="accountType==1">
                                <span>平台币余额: </span><span span class="b" v-html="coin_amount"></span>
                            </div>
                            <div class="c">
                                <!--                                充值流水-->
                            </div>
                        </div>
                        <div class="but"></div>
                        <!-- 请选择充值对象: -->
                        <div class="info_item">请选择充值对象:</div>
                        <div class="xz">
                            <el-radio v-model="checkType" label="1">平台币</el-radio>
                            <el-radio v-model="checkType" label="2">游戏</el-radio>
                        </div>
                        <div class="ptb" v-show="checkType == 2">
                            <div class="ptb_item">请选择需要充值的游戏:</div>
                            <div class="ptb_index">
                                <el-select v-model="gameid" placeholder="请选择" @change="handleChangeGame">
                                    <el-option v-for="item in gameList" :key="item.id" :label="item.name"
                                               :value="item.id">
                                    </el-option>
                                </el-select>

                                <el-select v-model="serverid" placeholder="请选择区服" @change="handleChangeServer">
                                    <el-option v-for="item in serverList" :key="item.serverid" :label="item.servername"
                                               :value="item.serverid">
                                    </el-option>
                                </el-select>

                                <el-select v-model="roleid" placeholder="请选择角色">
                                    <el-option v-for="item in roleList" :key="item.roleid" :label="item.rolename"
                                               :value="item.roleid">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>

                        <!-- 请选择充值金额: -->
                        <div class="jo">
                            <div class="jo_a">
                                <img src="__STATIC__/images/coin/路径 <EMAIL>" alt=""/>
                                <div class="foot_item">
                                    <div class="foot">兑换比例: 1元=1平台币</div>
                                </div>
                            </div>
                            <span class="jo_b">请选择充值金额:
                                    <div class="foot_index">
                                        <div class="foot">金额将直接充值到所选游戏中</div>
                                    </div>
                                </span>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="10">10元</el-radio>
                                <el-radio :label="20" style="margin-left: 7px">20元</el-radio>
                                <el-radio :label="50" style="margin-left: 8px">50元</el-radio>
                                <el-radio :label="100" style="margin-left: 8px">100元</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="200">200元</el-radio>
                                <el-radio :label="400">400元</el-radio>
                                <el-radio :label="800">800元</el-radio>
                                <el-radio :label="1200">1200元</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="-1">其他金额:</el-radio>
                            </el-radio-group>
                            <div class="ji_index">￥<input type="text" v-model="amount" name="amount" id="amount"
                                                          @change="handleAmount" @focus="handleAmount"/></div>
                        </div>
                        <div class="butt" >
                            <div class="butt_item" @click="handleCenterDialogVisible">立即充值</div>
                        </div>
                        <el-dialog title="提示" :visible.sync="centerDialogVisible" append-to-body width="30%" center>
                            <span>需要注意的是内容是默认不居中的</span>
                            <span slot="footer" class="dialog-footer">
                                    <el-button @click="centerDialogVisible = false">取 消</el-button>
                                    <el-button type="primary" @click="loadingmen">确 定</el-button>
                                </span>
                        </el-dialog>
                    </div>

                    <div v-show="tab == 'coinpay'">
                        <!-- 账号: -->
                        <div class="info">
                            <div>
                                <span class="a">账号:</span><span class="b">{$userinfo['username']??''}</span>
                            </div>
                            <div>
                                <span>平台币余额:</span><span span class="b">{$userinfo['amount']??''}</span>
                            </div>
                            <div class="c">
                                <!--                                充值流水-->
                            </div>
                        </div>
                        <div class="but"></div>
                        <!-- 请选择充值对象: -->

                        <div class="ptb">
                            <div class="ptb_item">请选择需要充值的游戏:</div>
                            <div class="ptb_index">
                                <el-select v-model="gameid" placeholder="请选择" @change="handleChangeGame">
                                    <el-option v-for="item in gameList" :key="item.id" :label="item.name"
                                               :value="item.id">
                                    </el-option>
                                </el-select>

                                <el-select v-model="serverid" placeholder="请选择区服" @change="handleChangeServer">
                                    <el-option v-for="item in serverList" :key="item.serverid" :label="item.servername"
                                               :value="item.serverid">
                                    </el-option>
                                </el-select>

                                <el-select v-model="roleid" placeholder="请选择角色">
                                    <el-option v-for="item in roleList" :key="item.roleid" :label="item.rolename"
                                               :value="item.roleid">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <!-- 请选择充值金额: -->
                        <div class="jo">
                            <div class="jo_a">
                                <img src="__STATIC__/images/coin/路径 <EMAIL>" alt=""/>
                                <div class="foot_item">
                                    <div class="foot">兑换比例: 1元=1平台币</div>
                                </div>
                            </div>
                            <span class="jo_b">请选择充值金额:
                                    <div class="foot_index">
                                        <div class="foot">金额将直接充值到所选游戏中</div>
                                    </div>
                                </span>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="10">10元</el-radio>
                                <el-radio :label="20" style="margin-left: 7px">20元</el-radio>
                                <el-radio :label="50" style="margin-left: 8px">50元</el-radio>
                                <el-radio :label="100" style="margin-left: 8px">100元</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="200">200元</el-radio>
                                <el-radio :label="400">400元</el-radio>
                                <el-radio :label="800">800元</el-radio>
                                <el-radio :label="1200">1200元</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="-1">其他金额:</el-radio>
                            </el-radio-group>
                            <div class="ji_index">￥<input type="text" v-model="amount" name="amount" id="amount"
                                                          @change="handleAmount" @focus="handleAmount"/></div>
                        </div>


                        <div v-if="mixValue!==0">
                            <div class="mix">平台币余额不足，还需支付:<span v-html="pay_amount"></span>元</div>
                            <div class="mix_item">请选择充值方式:<span style="color: red">(平台币订单如果提交未付款余额将在半小时后退回账户中)   </span> </div>

                            <ul class="mix_index">
                                <li v-for="(item, index) in payCoinList" :class="{ actives: payRadio == item.tab }"
                                    @click="payRadio = item.tab">
                                    <img :src=item.image alt=""/>
                                </li>
                            </ul>

                            <!--                            <ul class="mix_index">-->
                            <!--                                <li :class="{ actives: tabs == 1 }" @click="tabs = 1"><img src="./assets//img/组 <EMAIL>" alt="" ></li>-->
                            <!--                                <li :class="{ actives: tabs == 2 }" @click="tabs = 2"><img src="./assets//img/组 <EMAIL>" alt=""></li>-->
                            <!--                            </ul>-->

                        </div>


                        <!--                        <div class="jo_item ">-->
                        <!--                            还需支付：<input type="text" v-model="pay_amount" name="pay_amount" id="pay_amount"/>-->

                        <!--                        </div>-->

                        <!--                        <div class="jo_item payCoinT">-->

                        <!--                            <el-radio-group v-model="payRadio">-->

                        <!--                                <el-radio :label='"zfbsmzf"'>微信</el-radio>-->
                        <!--                                <el-radio :label='"kdsm"'>支付宝</el-radio>-->

                        <!--                            </el-radio-group>-->
                        <!--                        </div>-->


                        <div class="butt" >
                            <div class="butt_item" @click="handleCenterDialogVisible">立即充值</div>
                        </div>

                        <el-dialog title="提示" :visible.sync="centerDialogVisible" append-to-body width="30%" center>
                            <span v-html="payHtml">需要注意的是内容是默认不居中的</span>
                            <span slot="footer" class="dialog-footer">
                                    <el-button @click="centerDialogVisible = false">取 消</el-button>
                                    <el-button type="primary" @click="loadingmen">确 定</el-button>
                                </span>
                        </el-dialog>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

{block name="detail_js"}
<script>
    username3 = "{$userinfo['username'] ?? ''}"
    new Vue({
        el: '#app',
        data: function () {
            return {
                istoday: true,
                tab: "zfbsmzf",
                radio: 10,
                amount: 10,   //总金额
                pay_amount: 0, //还需支付金额
                coin_amt: 0,   //平台币支付金额
                coin_amount: 0,  //平台币余额
                accountType: "1",
                isxsyc: false,
                checkType: "1",
                username: "",
                passusername: "",
                payRadio: 'zfbsmzf',
                mixValue: 0,
                account: [
                    {
                        value: '1',
                        label: "当前账号:"
                    },
                    {
                        value: '2',
                        label: "其他账号:"
                    },
                ],
                payList: [
                    {
                        tab: 'zfbsmzf',
                        name: "支付宝支付",//现在
                        image: "__STATIC__/images/coin/m1.png"
                    },
                    // {
                    //     tab: 'xzzfbzf',
                    //     name: "支付宝H5支付(现在)",
                    //     image: "__STATIC__/images/coin/m1.png"
                    // },

                    // {
                    //     tab: 'wxsmzf',
                    //     name: "微信支付",//（汇付宝）扫码
                    //     image: "__STATIC__/images/coin/m2.png"
                    // },
                    {
                        tab: 'kdsm',
                        name: "微信支付",//酷点扫码
                        image: "__STATIC__/images/coin/m2.png"
                    },
                    // {
                    //
                    //     tab: 'wx-wap',
                    //     name: "微信支付",//（汇付宝）H5
                    //     image: "__STATIC__/images/coin/m2.png"
                    // },
                    // {
                    //     tab: 'kdh5',
                    //     name: "微信支付", //酷点H5
                    //     image: "__STATIC__/images/coin/m2.png"
                    // },
                    {
                        tab: 'coinpay',
                        name: "平台币支付",
                        image: "__STATIC__/images/coin/m3.png"
                    },
                ],
                payCoinList: [
                    {
                        tab: 'zfbsmzf',
                        name: "支付宝支付",//现在
                        image: "__STATIC__/images/coin/zfb.png"
                    },
                    {
                        tab: 'kdsm',
                        name: "微信支付",//酷点扫码
                        image: "__STATIC__/images/coin/wx.png"
                    },

                ],
                gameList: [],
                serverList: [],
                roleList: [],
                options: [],
                gameid: "",
                serverid: "",
                roleid: "",
                verifyUserNameStatus: 0,
                loading: false,
                centerDialogVisible: false,
                payHtml:''
            };
        },
        components: {},
        mounted() {
            // this.getPaytype()
            this.getGameList()
            this.pay_amount_fun()
            this.getCoin()
        },
        methods: {
            handleTabs(val) {
                console.log(val)
                console.log(this.tab)
                if(this.tab=='coinpay'){
                    if (this.accountType != 1) {
                        that = this
                        that.gameid = ''
                        that.serverid = ''
                        that.roleid = ''
                        that.serverName = ""
                        that.roleName = ""
                    }

                    if (!username3) {
                        alert('当前未登录请先登陆')
                    }
                    this.value = '当前账号'
                    this.accountType = "1"
                    this.pay_amount_fun()
                }
            },
            handleValue() {
                if (this.accountType == 1) {
                    if (!username3) {
                        alert('当前未登录请先登陆')
                    }
                    this.username = ''
                    this.passusername = ''
                    that.verifyUserNameStatus = 0
                }

                this.gameid = ''
                this.serverid = ''
                this.roleid = ''
                this.gameName = ""
                this.serverName = ""
                this.roleName = ""
                this.serverList = []
                this.roleList = []
            },
            getCoin() {
                $.ajax({
                    url: '/coin/getCoin',
                    type: 'post',
                    data: {},
                    success: function (data) {
                        if (data.code == 1) {
                            that.coin_amount = data.data.amount
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },
            pay_amount_fun() { // 还需支付金额
                money = parseFloat(this.amount);
                if(!money){
                    money = 0;
                }
                if (money <= parseFloat(this.coin_amount) && this.accountType == 1) {
                    this.pay_amount = 0
                    this.coin_amt = this.amount
                    this.mixValue = 0
                } else {
                    if (this.accountType == 2) {
                        counp_money = 0;
                    } else {
                        counp_money = parseFloat(this.coin_amount);
                    }
                    real_money = money - counp_money;
                    real_money = real_money.toFixed(2);
                    this.pay_amount = real_money;
                    this.coin_amt = this.coin_amount
                    this.mixValue = 1
                }
            },
            handleAmount() {
                if (this.amount && parseFloat(this.amount) > 0) {
                    this.amount = parseFloat(this.amount).toFixed(0);
                }

                this.radio = -1
                this.pay_amount_fun()
            },

            handleChange(val) {
                if (parseFloat(val) > 0) {
                    this.amount = parseFloat(val).toFixed(0);
                    this.pay_amount_fun()
                } else {
                    this.amount = null
                }
            },
            handleChangeGame(val) {
                that = this
                that.serverid = ''
                that.roleid = ''
                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                $.ajax({
                    url: '/coin/getServerList',
                    type: 'post',
                    data: {gameid: that.gameid},
                    success: function (data) {
                        if (data.code == 1) {
                            that.serverList = data.data
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },
            handleChangeServer() {
                that = this
                that.roleid = ''
                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                if(this.accountType == 1 && !username3){
                    alert('未登录，请先登录')
                    return
                }
                $.ajax({
                    url: '/coin/getRoleList',
                    type: 'post',
                    data: {
                        gameid: that.gameid,
                        serverid: that.serverid,
                        accountType: that.accountType,
                        username: that.username
                    },
                    success: function (data) {
                        if (data.code == 1) {
                            that.roleList = data.data
                            that.pay_amount_fun()
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },
            handleChangeUserName() {
                that = this
                that.verifyUserNameStatus = 0
                if (that.username != that.passusername) {
                    alert("两次输入的账号不一致")
                } else {
                    that = this
                    that.gameid = ''
                    that.serverid = ''
                    that.roleid = ''
                    $.ajax({
                        url: '/coin/verifyUserName',
                        type: 'post',
                        data: {username: that.username},
                        success: function (data) {
                            if (data.code == 1) {
                                that.verifyUserNameStatus = 1
                                that.pay_amount_fun()
                            } else {
                                alert(data.msg)
                            }
                        }
                    })
                }
            },
            getGameList() {
                that = this
                $.ajax({
                    url: '/coin/getGameList',
                    type: 'post',
                    success: function (data) {
                        if (data.code == 1) {
                            that.gameList = data.data
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },

            // getPaytype() {
            //     $.ajax({
            //         url: '/coin/getPayType',
            //         type: 'post',
            //         success: function (data) {
            //             console.log(data)
            //         }
            //     })
            // },
            // 隐藏与显示
            changeIxsyc() {
                this.isxsyc = !this.isxsyc;
            },
            topHide() {
                $('.coin').hide();
            },
            handleCenterDialogVisible(done) {
                that = this
                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                console.log(this.accountType)
                if (this.accountType == 2){
                    username = that.username
                }else{
                    username = username3
                }
                if (that.tab == 'coinpay') {
                    if (!username3) {
                        alert('未登录，请先登录')
                        return
                    }
                    if (that.pay_amount > 0) {
                        if (that.payRadio == '') {
                            alert('请先输入选择支付方式')
                            return
                        }
                    }
                }
                if(!that.amount){
                    alert('请先输入金额')
                    return
                }
                this.payHtml = " 您要充值的账号：<span style='color: #00a0e9'><b>"+username+"</b></span> 金额：<span style='color: #00a0e9'><b>"+that.amount+"</b></span>元"
                this.centerDialogVisible=true
            },
            loadingmen(){
                this.centerDialogVisible=false
                this.loading=true
                that.pay()
            },

            pay() {

                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                that = this
                var tab = that.tab
                if (that.tab == 'coinpay') {
                    if(!username3){
                        alert('未登录，请先登录')
                        return
                    }
                    if (that.pay_amount > 0) {
                        if (that.payRadio == '') {
                            alert('请先输入选择支付方式')
                            return
                        }
                        tab = 'coin-' + that.payRadio
                    }
                    that.checkType = 2
                }else{
                    that.coin_amt = 0;
                }
                // if(that.checkType == 1 || that.accountType == 2){
                //     that.coin_amt = 0;
                // }
                $.ajax({
                    url: '/coin/pay',
                    type: 'post',
                    data: {
                        type: that.checkType,
                        paytype: tab,
                        amount: that.amount,
                        coin_amt: that.coin_amt,
                        accountType: that.accountType,
                        username: that.username,
                        gameid: that.gameid,
                        serverid: that.serverid,
                        roleid: that.roleid
                    },
                    success: function (data) {
                        this.loading=false
                        paytype = tab
                        if (data.code == 1) {
                            if (paytype == 'zfbsmzf' || paytype == 'coin-zfbsmzf') { //支付宝扫码(支付宝官方扫码)
                                const div = document.createElement('div')
                                div.innerHTML = data.data.zfbsmzf_param.mweb_url
                                console.log(data.data.zfbsmzf_param)
                                document.body.appendChild(div)
                                document.forms[0].submit()
                            } else if (paytype == 'xzzfbzf') {  //支付宝 扫码 （现在支付）
                                window.location.href = data.data.xzzfbzf_param.mweb_url
                            } else if (paytype == 'wxsmzf') { //微信扫码（汇付宝)
                                window.location.href = data.data.wxpaysm_param.mweb_url
                            } else if (paytype == 'kdsm' || paytype == 'coin-kdsm') { //微信扫码（ku_param)
                                window.location.href = data.data.kdsm_param.mweb_url
                            } else if (paytype == 'coinpay') {
                                alert(data.msg)
                                window.location.href = window.location.href
                            }
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            }
        },
    })
</script>
{/block}
