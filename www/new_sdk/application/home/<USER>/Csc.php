<?php
/** 客服中心 -- 控制器
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/24
 * Time: 10:47
 */

namespace app\home\controller;

use think\Config;
use think\Db;
class Csc extends Home {
    private $_type
        = [
            '1' => '账号问题' ,
            '2' => '充值问题' ,
            '3' => '申诉问题' ,
            '4' => '人工服务' ,
            '5' => '其他' ,
        ];
    private $_appealMsgCode = 'SMS_192715035';   //'【祈盟文化游戏】您正在申诉账号，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';
    /**
     * 初始化操作
     */
    protected function _initialize() {
        parent::_initialize();
        $this->cscModel = model('CpsProblem');
    }

    /**
     * 常见问题
     */
    public function index() {
        $type = $this->request->param('type' , 0 , 'intval');

        $typeArr = $this->_type;
        foreach ($typeArr as $k => $v) {
            $num = $this->judgeHasByType($k);
            if ( !$num) unset($typeArr[$k]);
        }
        $runable = true;

        if ( !$type || !isset($typeArr[$type])) {
            reset($typeArr);
            $type = key($typeArr);
        }

        if (( !count($typeArr) || !isset($typeArr[$type])) && $type != 0) {
            $runable = false;
        }
        $page_num = 10;
        if ($runable) {

            $list = Db::connect(config('database_slave'))->table('cy_csc_problem')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
                ->field('id,title,answer')
                ->where('type' , $type)
                ->order('order asc, create_time asc')
                ->paginate($page_num , false , array('query' => input('get.')));
        }

        $this->assign('runable' , $runable);
        $this->assign('typeList' , $runable ? $typeArr : []);
        $this->assign('type' , $runable ? $type : 0);
        $this->assign('list' , $runable ? $list : []);
        $this->assign('current_num' , $runable ? count($list) ? ($list->toArray()['current_page'] - 1) * $page_num : 0 : 0);
        $this->assign('page' , $runable ? $list->render() : null);
        return $this->fetch();
    }

    /**
     * 判断 某个类型下 是否有问题
     */
    public function judgeHasByType($type) {
        $num =  Db::connect(config('database_slave'))->table('cy_csc_problem')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->where('type' , $type)->count('id');
        return $num;
    }

    /**
     * w微信申诉页面
     */
    public function wechatAppeal() {
        return $this->fetch();
    }

    /**
     * 申诉查询
     */
    public function appealResult() {

        if ($this->request->isAjax()) {
            $code = $this->request->param('code');
            if ( !$code) $this->jsonResult([] , 0 , '请输入申诉编号');

            $Ticket   = $this->request->param('ticket' , '' );
            $Randstr  = $this->request->param('randstr' , '' );
            $slider = (new \app\home\controller\Member)->sliderVelidate($Ticket,$Randstr);
            if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);

            $res = model('CscAppeal')
                ->field('id,userid,username,mobile,mail,status,code,certificate')
                ->where('code' , $code)->find();

            if (empty($res)) $this->jsonResult([] , 0 , '不存在该申诉编号');

            // 0:未审核  1:成功 2:失败 3:成功改完密码状态 4驳回',
            if ($res['status'] == 4) $res['status'] = 2;
            session('appeal_rest_pwd_info' , $res);

            if ( !empty($res['mobile'])) $res['mobile'] = stringObfuscation($res['mobile']);
            if ( !empty($res['mail'])) $res['mail'] = mailObfuscation($res['mail']);

            $this->success('','appealResultPage');
        }

        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch();
    }
    /**
     * 申诉查询--结果页面
     */
    public function appealResultPage(){
        if (!session('appeal_rest_pwd_info')) $this->redirect(getServiceUrl(3));
        if ($this->request->isAjax()){

            $certificate = $this->request->param('certificate');
            if ( !$certificate) $this->jsonResult([] , 0 , '请输入成功凭证');
            $appealInfo = session('appeal_rest_pwd_info');

            if (empty($appealInfo) || $certificate != $appealInfo['certificate']) {
                $this->jsonResult([] , 0 , '成功凭证不正确');
            }
            $this->success('','resetPwdPage');
        }

        $info = session('appeal_rest_pwd_info');

        /*$info['status'] = 1;*/
        $this->assign('info',$info);
        return $this->fetch();
    }

    /**
     * 通过申诉凭证 -- 充值密码页面
     */
    public function resetPwdPage() {

        if (!session('appeal_rest_pwd_info')) $this->redirect(getServiceUrl(3));

        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        return $this->fetch();
    }

    /**
     * 重置密码
     */
    public function resetPwd() {
        $appealInfo = session('appeal_rest_pwd_info');
        if (empty($appealInfo)) $this->jsonResult([] , 0 , '系统繁忙，请稍后再试');

        $password1 = input('post.password1' , '' );
        $password2 = input('post.password2' , '' );

        if (empty($password1)) $this->jsonResult([] , -1 , '请输入密码');
        if (empty($password2)) $this->jsonResult([] , -2 , '请再次输入密码');
        if ($password1 != $password2) $this->jsonResult([] , -2 , '两次输入的密码不一致');

        $userid = model('Members')->where(['username'=>$appealInfo['username']])->value('id');
        $memberCTR = new Member();
        $res       = $memberCTR->resetPassword($userid , $password1,1);

        if ($res == 0) $this->jsonResult([] , -2 , '新密码不能与旧密码相同');
        if ($res == 3) $this->jsonResult([] , -1 , '请输入正确的密码，6-15位字符');
        if ($res == 2) $this->jsonResult([] , -2 , '密码重置失败，请稍后在试！');

        $result = model('CscAppeal')->save(['status' => 3] , ['id' => $appealInfo['id']]);

        session('appeal_rest_pwd_info' , null);
        if ($result || $appealInfo['status'] == 3) {
            $this->success('密码重置成功',url('resetPwdResult',['suc'=>1]));
        }
        $this->success('密码重置失败',url('resetPwdResult'));
    }
    /**
     * 重置密码--结果页
     */
    public function resetPwdResult(){

        $suc = $this->request->param('suc',0,'intval');
        $this->assign('suc',$suc);
        return $this->fetch();
    }

    /**
     * 账号申诉---确认申诉账号
     */
    public function appeal() {
        if (session('appeal')) {
            session('appeal' , null);
        }

        if ($this->request->isAjax()) {
            $username = input('post.username' , '' );
            $type     = input('post.type' , '' );
            $number   = input('post.number' , '' );
            $reason   = input('post.reason' , '' , 'intval');
            $agreexy   = input('post.reason' );
            $userid   = session('front_userid'); //登录帐号id（不是申诉号码id）
            $typeArr  = ['mobile' , 'email'];
            $data     = [];

            if (empty($username)) $this->error('请输入申诉账号');
            if ( !in_array($type , $typeArr)) $this->error('请选择申诉接收方式');
            if ($type == $typeArr[0]) {
                $data['mobile'] = $number;
                if (empty($number)) $this->error('请输入手机号');
                if ( !$this->isMobile($number)) $this->error('请输入正确的手机号');
            }
            if ($type == $typeArr[1]) {
                $data['mail'] = $number;
                if (empty($number)) $this->error('请输入邮箱');
                if ( !$this->isEmail($number)) $this->error('请输入正确的邮箱');
            }
            if (empty($reason)) $this->error('请选择申诉原因');
            if (!$agreexy) $this->error('请先同意祈盟文化服务使用协议');

            $condition['username'] = $username;
            $id                    = model('Members')->where($condition)->value('id');
            if (empty($id)) $this->error('帐号不存在');

            $data['userid']         = $userid;
            $result                 = Db::table('cy_csc_appeal')->where($condition)->select();


            if (!empty($result)){
                foreach ($result as $v){
                    if ($v['status'] == 0) $this->error('该账号还有申诉处理中，请勿重复申诉');
                    if ($v['status'] == 1) {
                        $url = getServiceUrl(3);
                        $this->error('该账号有申诉已通过，但未重置密码，请前往<a href="' . $url . '" style="color:blue">"申诉查询"</a> 进行操作');
                    }
                    if (NOW_TIMESTAMP - $v['create_time'] <= 3600) $this->error('该账号距离上一次申诉未到1小时，请稍后再试，或联系祈盟文化客服');
                }
            }

            if ($reason == 2) {
                $condition['status']   = ['in',['3',4]];
                $condition['reason']   = 2;
                $condition['create_time'] = ['egt' , NOW_TIMESTAMP - 180 * 24 * 60 * 60];
                $result                   = model('CscAppeal')->where($condition)->order('id desc')->find();
                if ($result['status'] == 3 || $result['status'] == 4) $this->error('该账号在近期已有账号被盗受理完成，不可再申诉，请联系祈盟文化客服咨询');
            }

            $data['reason'] = $reason;
            $data['username'] = $username;

            session('appeal',$data);
            $this->success('','Csc/check');
        }

        return $this->fetch();
    }

    /**
     * 账号申诉---手机/邮箱验证
     */
    public function check(){
        if (!session('appeal')){
            $this->redirect(getServiceUrl('2'));
        }
        $appid        = Config::get('SLIDER_VERIFICATION.APPID');
        $this->assign('appid',$appid);
        $this->assign('info',session('appeal'));
        return $this->fetch();
    }

    /**
     * 账号申诉---发送验证码
     */
    public function sendCode(){
        $type    = input('post.type' , '' );
        $info      = session('appeal');
        $typeArr = ['mobile' , 'email'];

        if (!$info) $this->redirect(getServiceUrl('2'));

        if (!in_array($type,$typeArr) || empty($info)) $this->error('非法请求');

        $Ticket   = $this->request->param('ticket' , '' );
        $Randstr  = $this->request->param('randstr' , '' );
        $slider = (new \app\home\controller\Member)->sliderVelidate($Ticket,$Randstr);
        if (!$slider['code']) $this->jsonResult([],0,$slider['msg']);


        if ($type == $typeArr[0]){
            $result = (new \app\common\library\Sms)->sendCode($info['mobile'],$this->_appealMsgCode);

        }else{
            $result = (new \app\common\library\Mail)->sendCodeMailByType($info['username'], $info['mail'],'appeal');
        }

        if(isset($result) && $result['status']){
            $this->jsonResult([],1,'发送成功');
        }else{
            $this->jsonResult([],0,$result['msg']);
        }
    }

    /**
     * 账号申诉---验证码验证
     */
    public function appealCheckCode(){
        $info      = session('appeal');
        $code    = input('post.code' , 0 , 'intval');
        $type    = input('post.type' , '' );
        $typeArr = ['mobile' , 'email'];

        if ( !in_array($type , $typeArr) || empty($info)) $this->jsonResult([],0,'非法请求');
        if (empty($code)) $this->jsonResult([],0,'验证不能为空');

        if ($type == $typeArr[0]) {
            $result = (new \app\common\library\Sms)->checkCode($info['mobile'] , $code);

        } else {
            $result = (new \app\common\library\Mail)->checkCode($info['username'] , $info['mail'] , $code);
        }

        if ( isset($result) && $result['status']) {
            session('apeealHasCheck',true);
            $this->success('验证成功','appealAccount');
        }
        $this->jsonResult([],0,$result['msg']);
    }

    /**
     * 账号申诉---账号信息
     */
    public function appealAccount(){
        if(!session('apeealHasCheck')){
            $this->redirect('sendCode');
        }
        if(session('apeealUserinfo')){
            session('apeealUserinfo',null);
        }
        /*$data     = session('appeal');*/
        $province = Db::table('cy_city')->where('parent_id=1')->select();
        $gameWhere = [
            'cooperation_status' => ['neq',0],
            'nickname' => ['neq',''],
        ];
        $gamelist = model('Game')->field('id,nickname')->where($gameWhere)->group('nickname')->order('id desc')->select();

        $this->assign('province',$province);
        $this->assign('gamelist',$gamelist);
        return $this->fetch('appeal_account');
    }

    /**
     * 按省份获取市
     */
    public function getAddr(){
        $id = $this->request->param('id','','intval');
        if (!$id) $this->error('非法请求');
        $data = Db::table('cy_city')->where('parent_id='.$id)->select();
        $this->success('','',$data);
    }

    /**
     * 账号申诉---账号信息---提交
     */
    public function appealAccountTij(){

        $regtime = strtotime(input('post.regtime', ''));
        $imei = input('post.imei', '');
        $addr = input('post.addr', '');
        $reggameid = input('post.reggameid', '');
        $regmobile = input('post.regmobile', '');
        $totalrecharge = input('post.totalrecharge', '', 'intval');
        $firstrechargegameid = input('post.firstrechargegameid', '');

        if (empty($regtime)) $this->jsonResult('',-1,'请选择注册时间');
        /*if (!empty($imei)) {
            if (strlen($imei) != 15) {
                $this->jsonResult('',-2,'imei格式错误');
            }
        }*/
        if (empty($addr)) $this->jsonResult('',-3,'请选择注册地址');
        if (is_numeric($addr)) $this->jsonResult('',-3,'地址格式不能为数字');
        if ($reggameid == '') $this->jsonResult('',-4,'请选择注册游戏');
        if ( $firstrechargegameid == '') $this->jsonResult('',-5,'请选择首次充值游戏');
        if (!empty($regmobile)) {
            if ($regmobile == '无') {

            } else {
                if (!$this->isMobile($regmobile)) $this->jsonResult('',-6,'请输入正确的手机号');
            }
        }else{
            $this->jsonResult('',-6,'请输入注册时手机号');
        }

        if ($totalrecharge != 0 && empty($totalrecharge)) $this->jsonResult('',-7,'请输入累积充值金额');
        if (!is_numeric($totalrecharge)) $this->jsonResult('',-7,'请输入正确的金额');

        $data = session('appeal');
        $data['regtime'] = $regtime;
        $data['imei'] = $imei;
        $data['addr'] = $addr;
        $data['reggameid'] = $reggameid;
        if ($regmobile != '无') {
            $data['regmobile'] = $regmobile;
        }else{
            $data['regmobile'] = '';
        }
        $data['totalrecharge'] = $totalrecharge;
        $data['firstrechargegameid'] = $firstrechargegameid;

        if(session('apeealUserinfo')){
            session('apeealUserinfo',null);
        }
        session('apeealUserinfo',$data);
        $this->success('','appealHistory');
    }

    /**
     * 账号申诉---账号历史信息
     */
    public function appealHistory(){
        if (!session('apeealUserinfo')){
            $this->redirect('appealAccount');
        }

        $gameWhere = [
            'cooperation_status' => ['neq',0],
            'nickname' => ['neq',''],
        ];
        $gamelist = model('Game')->field('id,nickname')->where($gameWhere)->group('nickname')->order('id desc')->select();
        $this->assign('gamelist',$gamelist);
        return $this->fetch('appeal_history');
    }

    /**
     * 账号申诉---账号历史信息--提交
     */
    public function appealHistoryTij(){

        $regchannelname = $this->request->param('regchannelname', '');
        $oldmail = !empty($this->request->param('oldmail/a')) ? array_flip(array_flip(array_filter($this->request->param('oldmail/a')))) : [];
        $oldmobile = !empty($this->request->param('oldmobile/a')) ? array_flip(array_flip(array_filter($this->request->param('oldmobile/a')))) : [];
        $boundgameid = !is_null($this->request->param('boundgameid/a')) ? array_flip(array_flip(array_filter($this->request->param('boundgameid/a')))) : '';
        // 旧密码 充值时间 和 充值金额可能重复，不能用array_flip进行数组翻转（会过滤掉值一样的key）
        $rechargetime = !empty($this->request->param('rechargetime/a')) ? array_filter($this->request->param('rechargetime/a')) : [];
        $rechargeamount = !is_null($this->request->param('rechargeamount/a')) ? array_filter($this->request->param('rechargeamount/a')) : [];
        $oldpwd = !empty($this->request->param('oldpwd/a')) ? array_filter($this->request->param('oldpwd/a')) : [];


        if (empty($oldpwd)) {
            $this->jsonResult(0,-1,'密码不能为空');
        }else{
            $pwdpreg = '/^[a-zA-Z0-9]{6,15}$/';
            foreach ($oldpwd as $k => $oldpwdinfo) {
                if (0 == preg_match($pwdpreg, $oldpwdinfo)) {
                    $this->jsonResult($k,-1,'密码长度为6-15位字符！');
                }
            }
        }

        if ( !empty($oldmail)) {
            foreach ($oldmail as $k => $oldmailinfo) {
                if (!empty($oldmailinfo) && !$this->isEmail($oldmailinfo)) $this->jsonResult($k,-2,'请输入正确的邮箱');
            }
        }

        if (!empty($oldmobile)) {
            foreach ($oldmobile as $k => $oldmobileinfo) {
                if (!empty($oldmobileinfo) && !$this->isMobile($oldmobileinfo)) $this->jsonResult($k,-3,'请输入正确的手机号');
            }
        }

        if (empty($rechargetime) && !empty($rechargeamount)) $this->jsonResult(0,-5,'请输入充值时间');
        if (!empty($rechargetime) && empty($rechargeamount)) $this->jsonResult(0,-6,'请输入累积充值金额');
        if (!empty($rechargeamount)) {
            foreach ($rechargeamount as $k => $rechargeamountinfo) {
                if (!isset($rechargetime[$k]) || empty($rechargetime[$k])) $this->jsonResult($k,-5,'请输入充值时间');
                if (!is_numeric($rechargeamountinfo)) $this->jsonResult($k,-6,'请输入正确的充值金额');
            }
        }
        if (!empty($rechargetime)) {
            foreach ($rechargetime as $k => $rechargetimeinfo) {
                if (!isset($rechargeamount[$k]) || !is_numeric($rechargeamount[$k])) $this->jsonResult($k,-6,'请输入正确的充值金额');
                if (empty($rechargetimeinfo)) $this->jsonResult($k,-5,'请输入充值时间');
            }
        }

        if ($boundgameid == '') $this->jsonResult(0,-7,'请选择登录过的游戏');
        if (!empty($boundgameid)) {
            foreach ($boundgameid as $k => $boundgameidinfo) {
                if (!is_numeric($boundgameidinfo)) $this->jsonResult($k,-7,'登录过的游戏数据出错');
            }
        }

        $regchannelid = model('Channel')->where(['name'=>$regchannelname])->value('id');
        $arr['oldpwd'] = serialize($oldpwd);
        $arr['oldmail'] = serialize($oldmail);
        $arr['oldmobile'] = serialize($oldmobile);
        $arr['boundgameid'] = serialize($boundgameid);
        $arr['rechargetime'] = serialize($rechargetime);
        $arr['rechargeamount'] = serialize($rechargeamount);
        $arr['regchannelid'] = $regchannelid;
        $arr['create_time'] = date('Y-m-d H:i:s',NOW_TIMESTAMP); // model('CscAppeal')->getAppealScore($data) 里面时间是日期格式

        $data = session('apeealUserinfo');
        $data = array_merge($data, $arr);

        // 判断是否已经申诉了,防止同时申诉
        $result                 = Db::table('cy_csc_appeal')->where(['username'=>$data['username'],'status'=>['in',[0,1,2]]])->select();
        $re_arr = [];
        if (!empty($result)){
            foreach ($result as $v){
                if ($v['status'] ==2 && (NOW_TIMESTAMP - $v['create_time'] <= 3600)) $this->error('该账号距离上一次申诉未到1小时，请稍后再试，或联系祈盟文化客服');
                if ($v['status'] !=2 ) $re_arr[] = $v;
            }
            if (!empty($re_arr)) $this->error('该账号还有申诉处理中，请勿重复申诉');
        }

        $showtime = date("YmdHis", time());
        $data['code'] = $showtime . rand(0, 9); //申述编码

        $data['score'] = model('CscAppeal')->getAppealScore($data);
        $scoreLow = model('CscSetting')->where('name="MARK_APPEAL"')->value('value');

        $appealInfo = session('appeal');
        $data['reason'] = $appealInfo['reason']; //申述原因
        $data['userid'] = $appealInfo['userid'];
        $data['create_time'] = strtotime($data['create_time']); // 申诉时间

        $is_suc = true;
        if ($data['score'] < $scoreLow){
            $data['status'] = 2;
            $data['operate_time'] = NOW_TIMESTAMP;
            $data['operate_id'] = 1;
            $data['notice_time'] = $data['create_time'];

            $is_suc = false;
            if (isset($appealInfo['mobile']) && !empty($appealInfo['mobile'])){
                $result = (new \app\common\library\Sms)->sendAppealCode($appealInfo['username'],$data['code'],$appealInfo['mobile'],'','',4);

            }elseif(isset($appealInfo['mail']) && !empty($appealInfo['mail'])){
                $result = (new \app\common\library\Mail)->sendAppealCodeMail($appealInfo['username'], $data['code'],$appealInfo['mail'],'','',4);
            }

            if(isset($result) && $result['status']){
                $data['is_finish'] = 1;
            }else{
                $data['remark'] = '短信发送失败：'.$result['msg'];
            }
        }else{

            if ($data['reason'] == 2) {
                // 清空手机和邮箱
                $update = [
                    'flag'   => 1 ,
                    'email'  => '' ,
                    'mobile' => '' ,
                ];
                model('Members')->save($update,['username' => $appealInfo['username']]);
                // 记录用户冻结历史记录
                $userid = model('Members')->where(['username' => $appealInfo['username']])->value('id');
                model('FrozenHistory')->addData($userid,1,0);
            }

            if (isset($appealInfo['mobile']) && !empty($appealInfo['mobile'])){
                $data['notice_time'] = $data['create_time'] + 86400 * 3;
                $result = (new \app\common\library\Sms)->sendAppealCode($appealInfo['username'],$data['code'],$appealInfo['mobile'],'三','',0);

            }elseif(isset($appealInfo['mail']) && !empty($appealInfo['mail'])){
                $data['notice_time'] = $data['create_time'] + 3600 * 6;
                $result = (new \app\common\library\Mail)->sendAppealCodeMail($appealInfo['username'], $data['code'],$appealInfo['mail'],'一','',0);
            }
        }

        $appealId = model('CscAppeal')->insertGetId($data);
        if ($appealId) {
            session('appealResult',['username'=>$appealInfo['username'],'code'=>$data['code'],'status'=>$is_suc]);
            $this->success('','appealEnding');
        }

        $this->error('系统繁忙，请稍后再试！');
    }

    /**
     * 申诉结果
     */
    public function appealEnding(){
        $info = session('appealResult');
        session('apeealUserinfo',null);
        session('apeeal',null);
        session('apeealHasCheck',null);

        $this->assign('info',$info);
        return $this->fetch();
    }

}
