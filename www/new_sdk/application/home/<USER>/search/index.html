{extend name="layout/base" /}
{block name="title"}
<title>祈盟文化手机游戏_资讯_开服开测_礼包搜索_祈盟文化</title>
<meta name="keywords" content="手机游戏搜索,手机游戏资讯搜索,手机游戏开服开测搜索,手机游戏礼包搜索"/>
<meta name="description" content="祈盟文化搜索功能，帮您找到想要的手机游戏以及全面的新闻资讯、游戏礼包等信息，输入关键字即可快速搜索。"/>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/game-news.css">
<link rel="stylesheet" href="__STATIC__/css/home/<USER>">
{/block}

{block name="content"}
<!-- 页面导航 -->
<div class="curLoc">
    <div class="container">
    <span class="txt">当前位置：</span><a href="/" title="祈盟文化游戏">首页</a> &gt; <span>搜索结果</span>
    </div>
</div>


<div class="warp_search">
    <div class="container">
        <!-- 左侧 -->
        <div class="search_left">
          
            <!-- 搜索相关游戏 -->
        <h2>搜到<span class="keyword">"{$keyword}"</span>相关游戏{$list->total()}个</h2>
        {empty name="list"}
            <div class="no_content" >
                <img src="__STATIC__/images/icon/no-game.png" style="width: 190px;">
                <p>抱歉，未搜索到相关游戏，换个关键词试试吧~</p>
            </div>

        {else /}
        <div class="relate_game">
            {volist name="list" id="vo"}
            <div class="game_cont">
                <div class="search_game_img">
                    <a href="{$vo.gameUrl}" title="{$vo.nickname}" target="_blank"><img lazy-src="{$vo.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon"></a>
                    {if($vo.libaonum)}
                    <a href="{$vo.gameUrl}" target="_blank"><img class="havegift" src="__STATIC__/images/icon/gift.png"></a>
                    {/if}
                </div>
                <h4><a href="{$vo.gameUrl}" title="{$vo.nickname}" target="_blank">{$vo.nickname}</a></h4>
                <p>{$vo.size}</p>
                <p>{$vo.subject}<span>|</span>{$vo.type}</p>

                {if($vo.download)}<div class="download">{else}<div class="no_download">{/if}
                    <a href="javascript:">立即下载</a>
                    <span id="text-1" style="display: none;">{$vo.download}</span>
                    <span id="text-2" style="display: none;">{$vo.nickname}</span>
                    <span id="download-page" style="display: none;">http://wl.weilongwl.com/download/{$vo.id}.html</span>
                </div>
            </div>
            {/volist}

              <div class="page ">
                {$page}
                <div class="rows">共 <b>{$list->total()}</b>条 / <b>{$list->toArray()['last_page']}</b>页 记录 </div>
              </div>
        </div> 
        {/empty} 
        
    

            <!-- 搜索相关资讯 -->
            <h2>搜到<span>"{$keyword}"</span>相关资讯{$newsList->total()}条</h2>

         {empty name="newsList"}
               <div class="no_content">
                  <img src="__STATIC__/images/icon/no-content.png">
                  <p>抱歉，未搜索到相关资讯，换个关键词试试吧~</p>
              </div>
          
          {else /}

            <div class="new_news">
                {volist name="newsList" id="vo"}
                    <div class="game_detail">
                       <div class="content_left"> 
                           <a href="{$vo.url}" title="{$vo.title}" target="_blank"><img lazy-src="{$vo.image}" src="__STATIC__/images/icon/990-625.png"></a>
                       </div>
                       <div class="content_right"> 
                           <h4><a href="{$vo.url}" title="{$vo.title}" target="_blank">{$vo.title}</a></h4>
                           <p><span>发布时间：{$vo.createtime}</span></p>

                           <div class="text">
                             <p> 
                                {$vo.cutcontent}
                             </p>
                          </div>
                       </div>
                   </div>
                {/volist}
     
                <div class="page">
                    {$newsPage}
                    <div class="rows">共 <b>{$newsList->total()}</b>条 / <b>{$newsList->toArray()['last_page']}</b>页 记录 </div>
                </div>
                                     
   
            </div>
        {/empty}


        </div>
        <!-- 右侧 -->
        <div class="search_right">

        <!-- 手游排行 -->
        <div class="rank">
                <h4 title="手游排行">
                    <span>手游排行</span>
                </h4>
                <div class="rank_list" id="J_rank_list">
                    
                    
                </div>
            </div>



             
         <!-- 两张广告图片 -->
       
        <div class="wx_img">
            <img lazy-src="__STATIC__/images/wx.png" src="__STATIC__/images/icon/990-625.png">
        </div>
        <div class="article_adv" title="{$adInfo.title}">
            <a href="{$adInfo.url}"  target="_blank"><img lazy-src="{$Think.STATIC_DOMAIN}{$adInfo.image}" src="__STATIC__/images/icon/990-625.png"></a>
            <a href="{$adInfo.url}"  target="_blank"> <div><span>{$adInfo.title}</span></div></a>
        </div>
       <!-- 两张广告图片end -->


        </div>
    </div>
</div>


 
    <!-- 下载弹窗 start -->
    <div class="warp_download_popup">
        <div class="download_popup" >
            <div class="download_title"><span>下载</span></div>
         <div class="game_title"> <p>传奇帝国之荣耀骑士传奇，传奇帝国之荣耀骑士传奇传奇帝国之荣耀骑士传奇...</p></div>
            <div class="az_download">
                <div class="az_hover">
            <a href="" >
                <img src="__STATIC__/images/home/<USER>">
                <span>安卓版本下载</span>
            </a>
                </div>
                <div class="az_code"><img src="__STATIC__/images/home/<USER>"></div>
            </div>
            <div class="big_az_code">

               <div id="qrcode"></div>
               <div class="scan_code">
               <img src="__STATIC__/images/icon/scan.png">
               <span>扫码立即下载</span>
               </div>
               <div class="border-left-empty">
                <span></span>
                </div>
           </div>
           <div class="close">
            </div>
        </div>
    </div>
    <div class="warp_no_download_popup">
        <div class="no_download_popup" >
        <div class="tip"><span>提示</span><div class="close">
        </div></div>
        <div class="tip_txt">
            <p>该游戏暂无下载链接，稍后再试</p>
            <div class="tip_btn">
            <div class="determine"><span>确定</span></div>
            <div class="cancel"><span>取消</span></div>
          </div>
        </div>
        </div>
    </div>
    
        <!-- 下载弹窗 end -->



{/block}

{block name="detail_js"} 
  <script src="__STATIC__/js/common.js"></script>
<script src="__STATIC__/js/qrcode.min.js" ></script>
<script src="__STATIC__/js/search/index.js" ></script>

{/block}