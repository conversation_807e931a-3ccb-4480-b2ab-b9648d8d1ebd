<?php
/** 礼包 -- 控制器
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/24
 * Time: 10:47
 */

namespace app\home\controller;

use think\Db;
class Gift extends Home
{

    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 礼包--热门礼包
     */
    public function index(){
        $this->getList(1);

        return $this->fetch();
    }

    /**
     * 礼包--最新礼包
     */
    public function news(){
        $this->getList(0);
        return $this->fetch('index');
    }

    /**
     * 礼包--列表
     */
    public function getList($type) {
        /*$type = $this->request->param('t',1,'intval');*/
        if (!in_array($type,[0,1])) $type = 1;

        $where = [
            /*'a.is_top'    =>  $type,*/
            'a.isdelete'  =>  0,
            'a.endtime'  =>  ['gt',NOW_TIMESTAMP],
            'b.is_show' => 1,
            'b.cooperation_status'  => ['in',[0,1,2]]
        ];
        if ($type){
            $orderStr = ' a.used / a.total desc, a.endtime asc';
        }else{
            $orderStr = 'a.create_time desc';
        }

        $list = Db::table('cy_libaoinfo')->alias('a')
            ->join('cy_game b','a.gameid = b.id')
            ->join('cy_gameinfo info', 'b.id=info.game_id', 'left')
            ->field('a.id,a.gameid,a.title,a.content,a.total,a.used,a.starttime,a.endtime,info.mobileicon')
            ->where($where)
            ->where('a.total > a.used')
            ->orderRaw($orderStr)
            ->paginate(10, false, array('query' => input('get.')))
            ->each(function ($item, $key){

                $used = $item['used'];
                $total    = $item['total'];
                $item['percent'] = '100%';
                if ( $used >= $total ) {
                    $item['percent'] = '0%';
                } else if ( $used && $total ) {
                    $item['percent'] =  (bcsub( 1, bcdiv($used, $total, 2), 2 ) * 100) . '%';
                }

                $item['mobileicon'] = STATIC_DOMAIN.$item['mobileicon'];

                return $item;
            });

        $adModel = Db::name('cy_ad');
        $adInfo = $adModel->where(['type'=>99])->order('sort asc,create_time asc')->find();

        $this->assign('adInfo',$adInfo);
        $this->assign('list',$list);
        $this->assign('type',$type);
        $this->assign('page',$list->render());
    }

    /**
     * 礼包详情页
     */
    public function detail(){
        $id = $this->request->param('id',0,'intval');
        if (empty($id)) $this->_abort404();

        $where = [
            'a.id'                 => $id ,
            'b.is_show'            => 1 ,
            'b.cooperation_status' => ['in' , [0 , 1 , 2]]
        ];
        $info = Db::table('cy_libaoinfo')->alias('a')
            ->join('cy_game b','a.gameid = b.id')
            ->join('cy_gameinfo info', 'b.id=info.game_id', 'left')
            ->field('a.id,a.gameid,a.title,a.content,a.total,a.used,a.starttime,a.endtime,info.mobileicon,b.nickname')
            ->where($where)
            ->find();
        if (empty($info)) $this->_abort404();

        $info['mobileicon'] = STATIC_DOMAIN.$info['mobileicon'];
        $info['game_url'] = getGameUrl($info['gameid']);

        $info['percent'] = '100%';
        if ( $info['used'] >= $info['total'] ) {
            $info['percent'] = '0%';
        } else if ( $info['used'] && $info['total'] ) {
            $info['percent'] =  (bcsub( 1, bcdiv($info['used'], $info['total'], 2), 2 ) * 100) . '%';
        }

        $libaoList = Db::table('cy_libaoinfo')
            ->field('id,title')
            ->where(['id'=>['neq',$id],'isdelete'=>0,'endtime'  =>  ['gt',NOW_TIMESTAMP],'gameid'=>$info['gameid']])
            ->where('total > used')
            ->order('create_time desc')
            ->limit(10)
            ->select();

        $downPage = model('Setting')->where("name = 'APP_LINK'")->value('value');
        $adModel = Db::name('cy_ad');
        $adInfo = $adModel->where(['type'=>99])->order('sort asc,create_time asc')->find();

        $this->assign('adInfo',$adInfo);
        $this->assign('libaoList',$libaoList);
        $this->assign('downPage',$downPage);
        $this->assign('info',$info);
        return $this->fetch();
    }

    /**
     * 领取礼包
     */
    public function receiveGift(){
        $this->error('请到游戏中领取！');
        $id = $this->request->param('id',0,'intval');
        if (empty($id)) $this->error('参数错误！');

        if (empty(session('?front_userid'))) $this->jsonResult(getEncodeUrl('login'),-3,'请先登录');

        $username = session('front_account');

        // 1 判断是否领取过
        $hasReceive = Db::table('cy_libaolog')->alias('a')
            ->field('a.code ,a.id as logid')
            ->where(['a.libaoid'=>$id,'a.username'=>$username])
            ->find();

        if (!empty($hasReceive)) $this->jsonResult($hasReceive['code'],2,'您已领取过该礼包');

        $info = Db::table('cy_libaoinfo')->alias('a')
            ->join('cy_libao b','a.id = b.infoid','left')
            ->field('b.code,a.endtime,a.used,a.total,b.id as lid')
            ->where(['a.id'=>$id,'b.status'=>0])->find();
        if ($info['used'] >= $info['total'] || empty($info['code'])) $this->jsonResult([],-2,'该礼包已被领取完');
        if ($info['endtime'] <= NOW_TIMESTAMP) $this->jsonResult([],-1,'该礼包已过期');

        Db::startTrans();

        try{
            Db::table('cy_libao')->where( array('id'=>$info['lid']) )->update( array('status'=>1,'update_time'=>NOW_TIMESTAMP) );
            Db::table('cy_libaoinfo')->where( array('id'=>$id) )->setInc('used');
            Db::table('cy_libaolog')->insertGetId( array('username'=>$username,'libaoid'=>$id,'code'=>$info['code'],'create_time'=>NOW_TIMESTAMP) );
            // 提交事务
            Db::commit();

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->jsonResult([],-2,'该礼包该礼包暂时无法领取或已被领取完'.$e);
        }

        $this->jsonResult($info['code'],1,'领取成功！');
    }

}
