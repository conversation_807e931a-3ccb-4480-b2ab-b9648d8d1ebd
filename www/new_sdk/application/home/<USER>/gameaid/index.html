{extend name="layout/base" /}
{block name="title"}
<title>手机游戏加速_手机游戏辅助_祈盟文化</title>
<meta name="keywords" content="加速游戏,手机游戏加速,手机游戏辅助"/>
<meta name="description" content="祈盟文化提供加速版手机游戏,手机游戏自动加速，让您更加畅爽地享受游戏的乐趣。"/>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/home/<USER>">
<link rel="stylesheet" href="__STATIC__/css/game-list.css">
{/block}

{block name="content"}

<!-- 页面导航 -->
<div class="curLoc">
    <div class="container">
    <span class="txt">当前位置：</span><a href="{:getIndex()}"  title="祈盟文化游戏">首页</a> &gt; <span>加速游戏</span>
    </div>
</div>

<div class="warp_fastgame">
    <div class="container">
        <!-- 左侧 -->
        <div class="fastgame_left">
            <h2>加速游戏</h2>
           <div class="warp_fastgame_cont"> 
            {volist name="list" id="vo"}
            <div class="fastgame_cont">
                <div class="title">
                    <img title="{$vo.nickname}"  lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" >
                    <div>
                        <h3 title="{$vo.nickname}">{$vo.nickname}</h3>
                        <p>{$type[$vo.type]}</p>
                    </div>
                    <div class=" {empty name='$vo.filename'} no_download {else /} download {/empty} ">
                    <a href="javascript:">下载</a>
                    <span id="text-1" style="display: none;">{$Think.APK_DOWN_DOMAIN . '/sygame/' . $vo.pinyin .'/'.$vo.filename}</span>
                    <span id="text-2" style="display: none;">{$vo.nickname}</span>
                    <span id="download-page" style="display: none;">{$Think.WEBSITE_DOMAIN . '/download/fastgame/' .$vo.gameid.'.html'}</span>

                    </div>
                </div>
                <div class="fastgame_txt">
                    {$vo.content}
                </div>
            </div>
            {/volist}
        </div>
            <div class="page ">
                {notempty name="list"}
                {$page}
                <div class="rows">共 <b>{$list->total()}</b>条 / <b>{$list->toArray()['last_page']}</b>页 记录 </div>
                {/notempty}
            </div>
        </div>
        <!-- 右侧 -->
        <div class="fastgame_right">

                 <!-- 手游排行 -->
        <div class="rank">
                <h4 title="手游排行">
                    <span>手游排行</span>
                </h4>
                <div class="rank_list" id="J_rank_list">
                    
                    
                </div>
            </div>


  
     <!-- 两张广告图片 -->
<!--        
     <div class="wx_img">
            <img  lazy-src="__STATIC__/images/wx.png" src="__STATIC__/images/icon/990-625.png">
        </div> -->
        <div class="article_adv" title="{$adInfo.title}">
            <a href="{$adInfo.url}"  target="_blank"><img lazy-src="{$Think.STATIC_DOMAIN}{$adInfo.image}" src="__STATIC__/images/icon/990-625.png"></a>
            <a href="{$adInfo.url}"  target="_blank"> <div><span>{$adInfo.title}</span></div></a>
        </div>
       <!-- 两张广告图片end -->
    

        </div>
    </div>
</div>



  
   
    <!-- 下载弹窗 start -->
    <div class="warp_download_popup">
            <div class="download_popup" >
                <div class="download_title"><span>下载</span></div>
             <div class="game_title"> <p>传奇帝国之荣耀骑士传奇，传奇帝国之荣耀骑士传奇传奇帝国之荣耀骑士传奇...</p></div>
                <div class="az_download">
                    <div class="az_hover">
                <a href="" >
                    <img src="__STATIC__/images/home/<USER>">
                    <span>安卓版本下载</span>
                </a>
                    </div>
                    <div class="az_code"><img src="__STATIC__/images/home/<USER>"></div>
                </div>
                <div class="big_az_code">

                   <div id="qrcode"></div>
                   <div class="scan_code">
                   <img src="__STATIC__/images/icon/scan.png">
                   <span>扫码立即下载</span>
                   </div>
                   <div class="border-left-empty">
                    <span></span>
                    </div>
               </div>
               <div class="close">
                </div>
            </div>
        </div>
        <div class="warp_no_download_popup">
            <div class="no_download_popup" >
            <div class="tip"><span>提示</span><div class="close">
            </div></div>
            <div class="tip_txt">
                <p>该游戏暂无下载链接，稍后再试</p>
                <div class="tip_btn">
                <div class="determine"><span>确定</span></div>
                <div class="cancel"><span>取消</span></div>
              </div>
            </div>
            </div>
        </div>
        
            <!-- 下载弹窗 end -->



{/block}

{block name="detail_js"}

<script src="__STATIC__/js/common.js"></script>
<script type="text/javascript" src="__STATIC__/js/qrcode.min.js" ></script>
<script>
    $(function () {
        $("#J_rank_list").load("{:url('Layout/gameRankList')}");
        $(".public_nav li").removeClass('current_page').eq(5).addClass('current_page')
    });
     // url生成二维码
    var qrcode = new QRCode(document.getElementById("qrcode"), {
	width : 100,
	height : 100
   });

     $(".download").click( function() {
    var p_text = $(this).find("#text-1").text(); 
    var p_title = $(this).find("#text-2").text();
    var p_page = $(this).find("#download-page").text();
    qrcode.makeCode(p_page);
    $('.az_hover a').attr('href',p_text);
    $('.download_popup p').html(p_title);
    });
    
    $(".fastgame_txt").hover(function(){
    $(this).css("overflow","auto")
    },function(){
    $(this).css("overflow","hidden")
    })
   </script>
{/block}