<?php

namespace app\crontab\controller;

use think\Db;
use think\Env;
use think\Log;

class DealGameChannelDivide
{
    private $data;

    // 执行过期回收操作
    public function doAction()
    {
        //命令行
        if (request()->isCli()) {   
            Log::info("-----处理游戏渠道分成结算单开始-------");
            $isJobDone = $this->doJob();
            Log::info("-----处理游戏渠道分成结算单结束-------");
            die;
        }
        else{
            echo '非命令行请求，请求被禁止';
        }
    }

    /**
     * 自动处理财务结算源数据
     */
    private function doJob()
    {
		//钉钉通知地址
		$this->_ddurl = Env::get('dingtalk.warning_url');
        set_time_limit(0);
		$executionTime = input('executionTime');

		echo $executionTime."---executionTime-----<br>";
        // 获取日期
        $this->_date = $executionTime ? $executionTime : date("Y-m-d");

		if(date('Y-m-d',strtotime($this->_date))<>$this->_date){
			$templateMsg = "公会游戏分成脚本Start Time:".date("Y-m-d H:i:s")."\r\n";
			$templateMsg .= "日期错误,请确认输入正确的日期! time:".$this->_date."\r\n";
			$this->curlDD($templateMsg, $this->_ddurl,true);
            echo 'date error,please confirm!'; die();
		}
		//如果是星期一则跑(开始时间上周一,结束时间上周日)
		if(date('w',strtotime($this->_date)) != 1){
			$templateMsg = "公会游戏分成脚本Start Time:".date("Y-m-d H:i:s")."\r\n";
			$templateMsg .= "请确认所有公会都已经配置好相关游戏的分成比例,以免造成结算单无法生成!! time:".$this->_date."\r\n";
			$this->curlDD($templateMsg, $this->_ddurl,true);
			echo 'not monday,no data to run!!!'; die();
		}
		$templateMsg = "公会游戏分成脚本Start Time:".date("Y-m-d H:i:s")."\r\n";

        $this->startTime = strtotime($this->_date) - 3600*24*7;  //上周开始时间
        $this->lastTime = strtotime($this->_date) - 1;  //上周结束时间
		echo $this->startTime."-----startTime-----".$this->lastTime."-----lastTime----------<br>";

	    $this->startDateTime = date('Y-m-d H:i:s',$this->startTime);   //上周开始时间
        $this->toDateTime = date('Y-m-d H:i:s',$this->lastTime);  //上周结束时间
		echo $this->startDateTime."----startDateTime----".$this->toDateTime."----toDateTime-------<br>";

		$this->currentWeek = date("oW",$this->startTime);   //归属第几周
		
		//删除掉重新统计
		Db::startTrans();
		$sql = "delete from nw_game_channel_divide_data where week='".$this->currentWeek."'";
		Db::execute($sql);
		
        $game_divides = Db::table('nw_game_channel_divide')->select();
		while(list($key,$val)=@each($game_divides)){
			$GameId = $val['game_id'];
			$ChannelId = $val['channel_id'];
			$DivideId = $val['id'];

			$where = array();
			$where['status'] = 1;
			$where['create_time'] = array(['>=', $this->startTime ],['<=', $this->lastTime]);
			$where['gameid'] = $GameId;
			//所有下级渠道(包括自己)
			$channelIds = get_child_channel_arr($ChannelId);
			array_push($channelIds,$ChannelId);
			$where['channel_id'] = ['in',$channelIds];
			
		//	var_dump($where);
			$GameChannelData = Db::table('cy_pay')
							->field("gameid AS game_id,count(*) as pay_cnt,SUM(amount) AS pay_amt,SUM(real_amount) AS pay_real_amt,SUM(real_ptb) AS pay_real_ptb")
							->where($where)
							->find();
			echo Db::table('cy_pay')->getLastSql()."----lastsql----2222222222-------<br>";
			if(intval($GameChannelData['pay_cnt'])){
				$AddData = array();
				$AddData['week']					= $this->currentWeek;
				$AddData['divide_id']				= $DivideId;
				$AddData['begin_time']				= $this->startDateTime;
				$AddData['end_time']				= $this->toDateTime;
				$AddData['game_id']					= $GameId;
				$AddData['channel_id']				= $ChannelId;
				$AddData['ratio']					= $val['ratio'];
				$AddData['pay_cnt']					= intval($GameChannelData['pay_cnt']);
				$AddData['pay_amt']					= floatval($GameChannelData['pay_amt']);
				$AddData['divide_amt']				= floatval(intval($GameChannelData['pay_amt']*$val['ratio'])/100);
				$AddData['pay_real_amt']			= floatval($GameChannelData['pay_real_amt']);
				$AddData['pay_real_ptb']			= floatval($GameChannelData['pay_real_ptb']);
				$AddData['update_time']				= time();
				var_dump($AddData);
				$insertId = Db::table('nw_game_channel_divide_data')->insertGetId($AddData);
				
				if($insertId){
					$UpdData = array();
					$UpdData['divide_channel_id']		= $ChannelId;
					$UpdData['divide_data_id']			= $insertId ;
					$UpdData['divide_update_time']		= time();
					var_dump($UpdData);
					$updResult = Db::table('cy_pay')->where($where)->update($UpdData);
				}
			}
		}
        Db::commit();

		$templateMsg .= "公会游戏分成脚本End Time:".date("Y-m-d H:i:s")."\r\n";
		$this->curlDD($templateMsg, $this->_ddurl,true);
		echo $templateMsg."----templateMsg------";die();
    }


	/**
	 * 调用钉钉机器人函数
	 *
	 */
	function curlDD($msg,$url,$all=false){
		return true;
		if($all){
			$postData = [
				'msgtype' => 'text',
				'text' => [
					'content' =>mb_convert_encoding($msg,  "UTF-8", "auto"),
				],
				'at' => [
					'isAtAll' => true
				]
			];
		}
		else{
			$postData = [
				'msgtype' => 'text',
				'text' => [
					'content' =>mb_convert_encoding($msg,  "UTF-8", "auto"),
				]
			];
		}
		 
		$postString = json_encode($postData);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 对认证证书来源的检查
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $postString);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			"Content-Type: application/json"
		));
		 
		$result = curl_exec($ch);
		 
		$HTTP_CODE= curl_getinfo($ch,CURLINFO_HTTP_CODE);
		 
		curl_close($ch);
		if($HTTP_CODE<>200){
			$arr=array("state"=>-100);
			echo json_encode($arr);
			exit;
		}elseif($result==""){
			$arr=array("state"=>0,"errMsg"=>"接口返回为空");
			echo json_encode($arr);
			exit;
		}
		return $result;
	}
}