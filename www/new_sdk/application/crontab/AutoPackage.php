<?php
/**
 * 定时更新游戏定时器脚本
 * 
 */
namespace app\crontab;

set_time_limit(0);

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\logic\SubPackage as SubChannel;
use app\common\model\Game;
use app\common\model\AutoPackage as AutoPackageModel;

class AutoPackage extends Command {
    
    protected $now_time;                                    //当前时间
    protected $_output;

    protected function configure() {
        $this->setName('AutoPackage')->setDescription('定时更新游戏');
    }

    protected function execute(Input $input, Output $output) {
        
        ini_set('memory_limit', '1024M');
        
        $this->now_time         = time();
        $this->_output          = $output;

        $output->writeln(date('Y-m-d H:i:s')." AutoPackage start\r\n");
        
        $this->updateGamePackage();

        $output->writeln(date('Y-m-d H:i:s')." AutoPackage end\r\n");
        
        exit;
    }
    
    /**
     * 更新游戏包体处理
     *
     */
    private function updateGamePackage()
    {
        $gameModel          = new Game;
        $subChannel         = new SubChannel;
        $autoPackageModel   = new AutoPackageModel;
        
        $autoPackageList = $autoPackageModel->table('nw_auto_package a,cy_game g')->field('a.*,g.pinyin')->where('a.game_id=g.id')->where(['a.status'=>0,'a.auto_update_time'=>['<=',$this->now_time]])->select();
        
        if(!empty($autoPackageList)){
            
            foreach($autoPackageList as $value){
                
                //光环加速包
                if($value['package_type']==1){
                    
                    $path = '/data/www/aoyou/download/sygameguanghuan/'.$value['pinyin'].'/';
                }
                else{
                    $path = '/data/www/aoyou/download/sygame/'.$value['pinyin'].'/';
                }
                
                if($this->checkPackageFile($path,$value['file_name'])){
                    
                    $path_parts = pathinfo($value['file_name']);
                    
                    //待更新的包替换成游戏拼音包
                    if(rename($path.$value['file_name'], $path.$value['pinyin'].".".$path_parts['extension'])){
                        
                        $gameData['android_sdk_id'] = $value['android_sdk_id'];
                        
                        //强更
                        if($value['update_type']==1){
                            
                            $gameData['channel_version'] = $value['channel_version'];
                        }
                        
                        //更新游戏信息
                        $gameModel->update($gameData,['id'=>$value['game_id']]);
                        
                        $res = $subChannel->batchSubpackageByActiveDay($value['game_id'], $value['active_day'], $value['package_type'], $value['admin_id']);
                        
                        //更新定时更新游戏的记录状态
                        $autoPackageModel->update(['status'=>1,'update_time'=>$this->now_time],['id'=>$value['id']]);
                        
                        $this->_output->writeln("游戏ID：".$value['game_id']."的批量分包结果:".print_r($res,true)."  \r\n");
                    }
                    else{
                        
                        $autoPackageModel->update(['status'=>2,'update_time'=>$this->now_time,'result'=>'待更新的包替换成游戏拼音母包失败'],['id'=>$value['id']]);
                        
                        $this->_output->writeln("待更新的包替换成游戏拼音包失败: ".$value['file_name'].",定时更新游戏的记录ID：".$value['id']."  \r\n");
                    }
                }
                else{
                    
                    $autoPackageModel->update(['status'=>2,'update_time'=>$this->now_time,'result'=>'待更新的包不存在'],['id'=>$value['id']]);
                    
                    $this->_output->writeln("待更新的包不存在: ".$value['file_name'].",定时更新游戏的记录ID：".$value['id']."  \r\n");
                }
            }
            
        }
    }
    
    
    /**
     * 获得指定时间内有流水的渠道ID
     *
     * @param $path 
     * @param $file_name int 指定的天数
     *
     * @return boolean
     * 
     */
    private function checkPackageFile($path,$file_name)
    {
        if (file_exists($path.$file_name)) {
            return true;
        } else {
            return false;
        }
    }
}