<?php

namespace app\service;

use app\common\factory\Pay;
use app\common\library\WeixinPay;
use think\Config;
use think\Env;
use think\Exception;

class PayService
{

    /**
     * 支付宝扫码 (官方支付宝扫码)
     * @param  $orderid
     * @param  $real_amount
     * @param  $productname
     * @param  $productdesc
     */
    public function chargeByZfbWap($orderid, $real_amount, $productname, $productdesc = '', $notify_url = '')
    {
        $config = Config::get('ali_pay_config')['configs'][Config::get('ali_pay_config')['default']['zfb']];

        vendor('alipayAop.AopSdk');
        $aop = new \AopClient();
        $aop->gatewayUrl = "https://openapi.alipay.com/gateway.do"; //网关地址要使用沙箱网关alipaydev
        $aop->appId = $config['app_id'];
        $aop->rsaPrivateKey = $config['private_key'];
        $aop->alipayrsaPublicKey = $config['public_key'];
        $aop->format = "json";
        $aop->apiVersion = '1.0';
        $aop->postCharset = 'UTF-8';
        $aop->signType = Config::get('ali_pay_config')['sign_type'];
        //实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
        $request = new \AlipayTradePagePayRequest();

        $productname = preg_replace("/[\&\+]+/", '', $productname);      // 主题(过滤特殊字符)
        $subject = preg_replace("/[\&\+]+/", '', $productdesc);          // 商品描述信息(过滤特殊字符)

        //SDK已经封装掉了公共参数，这里只需要传入业务参数
        $info = json_encode(['body' => $subject, 'subject' => $productname, 'out_trade_no' => $orderid,
            'timeout_express' => '30m', 'total_amount' => $real_amount, 'product_code' => 'FAST_INSTANT_TRADE_PAY'], JSON_UNESCAPED_UNICODE);
        $request->setBizContent($info);
        //        $request->setReturnUrl("http://" . $_SERVER['HTTP_HOST'] . "/v1.user_center/returnUrl.html");
        //        $request->setNotifyUrl("http://" . $_SERVER['HTTP_HOST'] . "/v1.platform_pay/notify.html");
        $request->setNotifyUrl($notify_url);
        $result = $aop->pageExecute($request);
        return $result;
    }


    /**
     * 支付宝APP/扫码（官方支付宝快捷支付）
     *
     * @param string $orderid 订单号
     * @param float $real_amount 支付金额
     * @param string $productname 商品名称
     * @param string $productdesc 描述
     * @param string $notify_url 回调地址
     *
     * @return string 支付参数字符串
     */
    public function getAlipayAopParam($orderid, $real_amount, $productname, $productdesc = '', $notify_url = '')
    {
        $config = Config::get('ali_pay_config')['configs'][Config::get('ali_pay_config')['default']['zfb']];

        //Alipay Aop Sdk支付发起
        vendor('alipayAop.AopSdk');
        $aop = new \AopClient();

        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $config['app_id'];
        $aop->rsaPrivateKey = $config['private_key'];
        $aop->alipayrsaPublicKey = $config['public_key'];
        $aop->apiVersion = '1.0';
        $aop->signType = Config::get('ali_pay_config')['sign_type'];
        $aop->postCharset = 'utf-8';
        $aop->format = 'json';

        $request = new \AlipayTradeAppPayRequest();
        $bizcontent = array();
        $bizcontent['subject'] = preg_replace("/[\&\+]+/", '', $productname);           // 主题(过滤特殊字符)
        $bizcontent['body'] = preg_replace("/[\&\+]+/", '', $productdesc);              // 商品描述信息(过滤特殊字符)
        $bizcontent['out_trade_no'] = $orderid;                                         // 订单号
        $bizcontent['timeout_express'] = '30m';                                         // 超时时间
        $bizcontent['total_amount'] = $real_amount;                                     // 支付金额
        $bizcontent['product_code'] = 'QUICK_MSECURITY_PAY';                            // 支付方式

        $request->setBizContent(json_encode($bizcontent));
        $request->setNotifyUrl($notify_url);
        $result = $aop->sdkExecute($request);

        //	echo htmlspecialchars($result);
        // return ['error' => true, 'msg' => '支付方式发起下单异常！'];

        return ['error' => false, 'data' => $result];
    }

    /**
     * 支付宝 h5（官方支付宝h5支付）
     * @param $orderid
     * @param $real_amount
     * @param $productname
     * @param string $productdesc
     * @return string|\提交表单HTML文本|\构建好的、签名后的最终跳转URL（GET）或String形式的form（POST）
     * @throws \Exception
     */
    public function getAlipayWapAopParam($orderid, $real_amount, $productname, $productdesc = '', $notify_url = '')
    {
        //Alipay Aop Sdk支付发起
        vendor('alipayAop.AopSdk');
        $aop = new \AopClient();

        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = Config::get('alipayAop')['appId'];
        $aop->rsaPrivateKey = file_get_contents(Config::get('alipayAop')['private_key']);
        $aop->alipayrsaPublicKey = file_get_contents(Config::get('alipayAop')['alipay_public_key']);
        $aop->apiVersion = '1.0';
        $aop->signType = Config::get('alipayAop')['sign_type'];
        $aop->postCharset = 'utf-8';
        $aop->format = 'json';

        $request = new \AlipayTradeWapPayRequest();
        $bizcontent = array();
        $bizcontent['subject'] = preg_replace("/[\&\+]+/", '', $productname);           // 主题(过滤特殊字符)
        $bizcontent['body'] = preg_replace("/[\&\+]+/", '', $productdesc);              // 商品描述信息(过滤特殊字符)
        $bizcontent['out_trade_no'] = $orderid;                                         // 订单号
        $bizcontent['timeout_express'] = '30m';                                         // 超时时间
        $bizcontent['total_amount'] = $real_amount;                                     // 支付金额
        $bizcontent['product_code'] = 'QUICK_MSECURITY_PAY';                            // 支付方式

        $request->setBizContent(json_encode($bizcontent));
        $request->setNotifyUrl($notify_url);
        $result = $aop->pageExecute($request, "GET");

        //	echo htmlspecialchars($result);
        return $result;
    }

    /**
     * 支付宝 h5（官方支付宝h5支付）带referer
     * @param $orderid
     * @param $real_amount
     * @param $productname
     * @param string $productdesc
     * @return array
     * @throws \Exception
     */
    public function getAlipayWapPostAopParam($orderid, $real_amount, $productname, $productdesc = '', $notify_url = '')
    {
        $config = Config::get('ali_pay_config')['configs'][Config::get('ali_pay_config')['default']['old-zfb-wap']];

        //Alipay Aop Sdk支付发起
        vendor('alipayAop.AopSdk');
        $aop = new \AopClient();

        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
        $aop->appId = $config['app_id'];
        $aop->rsaPrivateKey = $config['private_key'];
        $aop->alipayrsaPublicKey = $config['public_key'];
        $aop->apiVersion = '1.0';
        $aop->signType = Config::get('ali_pay_config')['sign_type'];
        $aop->postCharset = 'utf-8';
        $aop->format = 'json';

        $request = new \AlipayTradeWapPayRequest();
        $bizcontent = array();
        $bizcontent['subject'] = preg_replace("/[\&\+]+/", '', $productname);           // 主题(过滤特殊字符)
        $bizcontent['body'] = preg_replace("/[\&\+]+/", '', $productdesc);              // 商品描述信息(过滤特殊字符)
        $bizcontent['out_trade_no'] = $orderid;                                         // 订单号
        $bizcontent['timeout_express'] = '30m';                                         // 超时时间
        $bizcontent['total_amount'] = $real_amount;                                     // 支付金额
        $bizcontent['product_code'] = 'QUICK_MSECURITY_PAY';                            // 支付方式

        $request->setBizContent(json_encode($bizcontent));
        $request->setNotifyUrl($notify_url);
        $result = $aop->pageExecute($request);

        //	echo htmlspecialchars($result);
        $pay_return['mweb_url'] = $result;
        $pay_return['referer'] = "com.yiyou.zfb";

        // return ['error' => true, 'msg' => '支付方式发起下单异常！'];
        return ['error' => false, 'data' => $pay_return];
    }

    /**
     * 第三方现金支付 支付宝 h5 (现在支付)
     * @return string
     */
    public function chargeByxzzfbzf($orderid, $real_amount, $productname, $productdesc = '', $notify_url = '')
    {
        $productdesc = "订单支付";
        $productname = preg_replace("/[\&\+]+/", '', $productname);      // 主题(过滤特殊字符)
        $subject = preg_replace("/[\&\+]+/", '', $productdesc);          // 商品描述信息(过滤特殊字符)

        $consumerCreateIp = $this->getUserIp(); //需上传用户真实ip
        $key = Config::get('h5_alipay_config')['key'];
        $req = array();
        $req["appId"] = Config::get('h5_alipay_config')['zfbAppId'];
        $req["deviceType"] = '0601';                                            //h5支付 0601  扫码 04
        $req["frontNotifyUrl"] = Config::get('h5_alipay_config')['return_url'];;//'http://sdkapi.46yx.com/v1.demo/zfbalipays.html';//前台通知，三方支付宝0模式有效，官方支付宝0、1模式有效，微信0模式有效
        $req["funcode"] = 'WP001';
        $req["mhtCharset"] = 'UTF-8';
        $req["mhtCurrencyType"] = '156';
        $req["mhtOrderAmt"] = $real_amount * 100;
        $req["mhtOrderDetail"] = $subject;
        $req["mhtOrderName"] = $productname;
        $req["mhtOrderNo"] = $orderid;
        $req["mhtOrderStartTime"] = date("YmdHis");
        $req["mhtOrderTimeOut"] = '3600';
        $req["mhtOrderType"] = '01';
        $req["mhtReserved"] = "test";
        $req["mhtSignType"] = 'MD5';
        $req["notifyUrl"] = $notify_url;
        $req["outputType"] = 1;      //   0 默认值
        $req["payChannelType"] = 12; //12 支付宝  //13 微信 //20 银联  //25  手Q
        $req["version"] = "1.0.0";
        $req["consumerCreateIp"] = $consumerCreateIp; //微信必填// outputType=2时 无须上送该值

        $req_str = $this->getToStr($req, $key);
        $service = 'https://pay.ipaynow.cn';
        $res = $this->xzzfbzfpost($service, $req_str);
        $code = (bool)stripos($res, '&tn=');
        if ($code) {
            $arr = explode('&', $res);
            $gettn = '';
            foreach ($arr as $v) {
                $tn = explode('=', $v);
                if ($tn[0] == 'tn') {
                    $gettn = $tn[1];
                }
            }
            $tn = urldecode($gettn);

            $pay_return['mweb_url'] = $tn;
            $pay_return['referer'] = 'com.yiyou.zfb';
            return $pay_return;
//            return  $tn;
        } else {
            return false;
        }
    }


    /**
     * 第三方现金支付 支付宝 h5 （现在支付）
     * @return string
     */
    public function chargeByxzzfsmf($orderid, $real_amount, $productname, $productdesc = '', $notify_url = '')
    {
        $productdesc = "订单支付";
        $productname = preg_replace("/[\&\+]+/", '', $productname);      // 主题(过滤特殊字符)
        $subject = preg_replace("/[\&\+]+/", '', $productdesc);          // 商品描述信息(过滤特殊字符)

        $consumerCreateIp = $this->getUserIp(); //需上传用户真实ip
        $key = Config::get('h5_alipay_config')['key'];
        $req = array();
        $req["appId"] = Config::get('h5_alipay_config')['zfbAppId'];
        $req["deviceType"] = '04';                                             //h5支付 0601  扫码 04
        $req["frontNotifyUrl"] = Config::get('h5_alipay_config')['return_url'];//'http://sdkapi.46yx.com/v1.demo/zfbalipays.html';//前台通知，三方支付宝0模式有效，官方支付宝0、1模式有效，微信0模式有效
        $req["funcode"] = 'WP001';
        $req["mhtCharset"] = 'UTF-8';
        $req["mhtCurrencyType"] = '156';
        $req["mhtOrderAmt"] = $real_amount * 100;
        $req["mhtOrderDetail"] = $subject;
        $req["mhtOrderName"] = $productname;
        $req["mhtOrderNo"] = $orderid;
        $req["mhtOrderStartTime"] = date("YmdHis");
        $req["mhtOrderTimeOut"] = '3600';
        $req["mhtOrderType"] = '01';
        $req["mhtReserved"] = "test";
        $req["mhtSignType"] = 'MD5';
        $req["notifyUrl"] = $notify_url;
        $req["outputType"] = 1;      //   0 默认值
        $req["payChannelType"] = 12; //12 支付宝  //13 微信 //20 银联  //25  手Q
        $req["version"] = "1.0.0";
        $req["consumerCreateIp"] = $consumerCreateIp; //微信必填// outputType=2时 无须上送该值

        $req_str = $this->getToStr($req, $key);
        $service = 'https://pay.ipaynow.cn';
        $res = $this->xzzfbzfpost($service, $req_str);
        $code = (bool)stripos($res, '&tn=');
        if ($code) {
            $arr = explode('&', $res);
            $gettn = '';
            foreach ($arr as $v) {
                $tn = explode('=', $v);
                if ($tn[0] == 'tn') {
                    $gettn = $tn[1];
                }
            }
            $tn = urldecode($gettn);

            $pay_return['mweb_url'] = $tn;
            $pay_return['referer'] = 'com.yiyou.zfb';
            return $pay_return;
//            return  $tn;
        } else {
            return false;
        }
    }

    /**
     * 获取现在支付参数
     *
     * @param string $orderid 订单号
     * @param float $real_amount 支付金额
     * @param string $productname 商品名称
     *
     * @return array 支付参数数组
     */
    public function getIpaynowParam($orderid, $real_amount, $productname, $notify_url = '')
    {
        $ipay['mhtOrderNo'] = $orderid;                                                // 商户订单号
        $ipay['appId'] = Config::get('ipaynow')['appid'];                              // 商户应用唯一标识
        $ipay['mhtOrderAmt'] = $real_amount * 100;                                     // 支付金额,以分为单位
        $ipay['notifyUrl'] = $notify_url;                                              // 回调地址

        $ipay['funcode'] = 'WP001';
        $ipay['version'] = '1.0.0';
        $ipay["payChannelType"] = "13";
        $ipay["mhtOrderName"] = $productname;
        $ipay["mhtOrderType"] = "01";
        $ipay["mhtCurrencyType"] = '156';
        $ipay["mhtOrderStartTime"] = date('YmdHis');
        $ipay['mhtCharset'] = "UTF-8";
        $ipay['deviceType'] = "01";
        $ipay['mhtOrderDetail'] = $productname;
        $ipay['mhtSignType'] = 'MD5';

        ksort($ipay);   //按key升序排列
        reset($ipay);

        //拼接待签名字符串
        $arg = "";
        while (list ($key, $val) = each($ipay)) {
            $arg .= $key . "=" . $val . "&";
        }

        //如果存在转义字符，那么去掉转义
        if (get_magic_quotes_gpc()) {
            $arg = stripslashes($arg);
        }

        //生成签名
        $mhtSignature = md5($arg . md5(Config::get('ipaynow')['key']));

        return $arg . 'mhtSignature=' . $mhtSignature;
    }

    /**
     * 汇付宝 微信扫码支付
     * @param $orderid
     * @param $real_amount
     * @param $productname
     * @param string $productdesc
     * @return mixed|string
     */
    public function getWxWapsmPostAopParam($orderid, $real_amount, $productname, $productdesc = '', $user_ip = '127.0.0.1', $notify_url = '')
    {
        //获取用户IP
        $user_ip = $user_ip;
        $version = "1";
        $agent_id = Config::get('wxwappay-h5')['mch_id'];                                 //商户编号，（汇付宝商户内码：七位整数数字）
        $sign_key = Config::get('wxwappay-h5')['key'];
        //        $notify_url = $notify_url;//支付后返回的商户处理页面，URL参数是以http://或https://开头的完整URL地址(后台处理)，提交的url地址必须外网能访问到，否则无法通知商户。值可以为空，但不可以为null。
        $scene = "qr";                                                                    //支付方式，h5表示h5，qr表示扫码（如支付类型为扫码，payment_mode传UnifiedOrder返回二维码url），jsapi表示公众号（微信内拉起），app表示app拉起支付(值均为小写)
        $pay_type = "30";                                                                 //支付类型30
        $agent_bill_id = $orderid;                                                        //商户系统内部的订单号（要保证唯一）
        $agent_bill_time = date('YmdHis', time());                                        //提交单据的时间yyyyMMddHHmmss，该参数共计14位，当时不满14位时，在后面加0补足14位
        $pay_amt = $real_amount;                                                          //订单总金额 不可为空。小数点后保留两位
        $return_url = Config::get('wxwappay-h5')['return_url'] . "?URLscheme=yiyouweixin";//支付后返回的商户显示页面，URL参数是以http://或https://开头的完整URL地址(前台显示)，原则上该参数与notify_url提交的参数不一致。值可以为空，但不可以为null。
        $user_ip = str_replace(".", "_", $user_ip);                                       //用户所在客户端的真实ip其中的“.”替换为“_” 。因为近期我方发现用户在提交数据时，user_ip在网络层被篡改，导致签名错误，所以我们规定使用这种格式。
        $goods_name = $productname;                                                       //商品名称，不能为空（不参加签名）
        $goods_name = iconv("UTF-8", "gbk", $goods_name);
        $remark = "";//商户自定义，原样返回,可以为空。
        $goods_note = "";
        $payment_mode = "cashier";//收银台=cashier，接口=UnifiedOrder（目前支持扫码，app）
        $n = "南京麻花网络";
        $fileName = iconv("UTF-8", "gbk", $n);
        $strs = '{"s":"WAP","n":"' . $fileName . '","id":"http://46yx.com"}';
        $str = base64_encode($strs);
        $meta_option = urlencode($str);//加密后的结果
        $bank_card_type = "-1";        //银行类型：未知=-1，储蓄卡=0，信用卡=1。
        $timestamp = "";               //时间戳，传了订单有效时间上下1min，从1970年1月1日0时到现在的毫秒数，传了参与签名，签名位置在key前面。
        $sign_type = "MD5";            //签名类型，MD5\RSA\RSA2

        $data['version'] = $version;
        $data['scene'] = $scene;
        $data['payment_mode'] = $payment_mode;
        $data['agent_id'] = $agent_id;
        $data['agent_bill_id'] = $agent_bill_id;
        $data['agent_bill_time'] = $agent_bill_time;
        $data['pay_type'] = $pay_type;
        $data['pay_amt'] = $pay_amt;
        $data['notify_url'] = $notify_url;
        $data['return_url'] = $return_url;
        $data['user_ip'] = $user_ip;
        $data['goods_name'] = $goods_name;
        $data['goods_note'] = $goods_note;
        $data['remark'] = $remark;
        $data['meta_option'] = $meta_option;
        $data['bank_card_type'] = $bank_card_type;

        $sign_str = '';
        $sign_str = $sign_str . 'version=' . $version;
        $sign_str = $sign_str . '&agent_id=' . $agent_id;
        $sign_str = $sign_str . '&agent_bill_id=' . $agent_bill_id;
        $sign_str = $sign_str . '&agent_bill_time=' . $agent_bill_time;
        $sign_str = $sign_str . '&pay_type=' . $pay_type;
        $sign_str = $sign_str . '&pay_amt=' . $pay_amt;
        $sign_str = $sign_str . '&notify_url=' . $notify_url;
        $sign_str = $sign_str . '&return_url=' . $return_url;
        $sign_str = $sign_str . '&user_ip=' . $user_ip;
        $sign_str = $sign_str . '&bank_card_type=' . $bank_card_type;
        $sign_str = $sign_str . '&remark=' . urlencode($remark);
        $sign_str = $sign_str . '&key=' . $sign_key;
        $sign = md5($sign_str); ////MD5\RSA\RSA2签名结果
        $data['sign'] = $sign;
        $urls = Config::get('wxwappay-h5')['pay_url'];
        $response = $this->postXmlCurl($data, $urls);
        $redirectUrl = "";
        if (!empty($response)) {
            if ($response['ret_code'] == '0000') {
                $redirectUrl = $response['redirectUrl'];
                $ret_code = $response['ret_code'];
                $ret_msg = $response['ret_msg'];
                $hf_sign = md5('redirectUrl=' . $redirectUrl . '&ret_code=' . $ret_code . '&ret_msg=' . $ret_msg . '&key=' . $sign_key);
                if ($hf_sign != $response['sign']) {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }

        $pay_return['mweb_url'] = $redirectUrl;
        $pay_return['referer'] = Config::get('wxpay-h5')['referer'];
        return $pay_return;
    }

    /**
     * 汇付宝 微信H5支付
     * @param $orderid
     * @param $real_amount
     * @param $productname
     * @param string $productdesc
     * @return mixed|string
     */
    public function getWxWapPostAopParam($orderid, $real_amount, $productname, $productdesc = '', $user_ip = '127.0.0.1', $notify_url = '')
    {
        //获取用户IP
        $user_ip = $user_ip;
        $version = "1";
        $agent_id = Config::get('wxwappay-h5')['mch_id'];                                 //商户编号，（汇付宝商户内码：七位整数数字）
        $sign_key = Config::get('wxwappay-h5')['key'];
        //        $notify_url = $notify_url;//支付后返回的商户处理页面，URL参数是以http://或https://开头的完整URL地址(后台处理)，提交的url地址必须外网能访问到，否则无法通知商户。值可以为空，但不可以为null。
        $scene = "h5";                                                                    //支付方式，h5表示h5，qr表示扫码（如支付类型为扫码，payment_mode传UnifiedOrder返回二维码url），jsapi表示公众号（微信内拉起），app表示app拉起支付(值均为小写)
        $pay_type = "30";                                                                 //支付类型30
        $agent_bill_id = $orderid;                                                        //商户系统内部的订单号（要保证唯一）
        $agent_bill_time = date('YmdHis', time());                                        //提交单据的时间yyyyMMddHHmmss，该参数共计14位，当时不满14位时，在后面加0补足14位
        $pay_amt = $real_amount;                                                          //订单总金额 不可为空。小数点后保留两位
        $return_url = Config::get('wxwappay-h5')['return_url'] . "?URLscheme=yiyouweixin";//支付后返回的商户显示页面，URL参数是以http://或https://开头的完整URL地址(前台显示)，原则上该参数与notify_url提交的参数不一致。值可以为空，但不可以为null。
        $user_ip = str_replace(".", "_", $user_ip);                                       //用户所在客户端的真实ip其中的“.”替换为“_” 。因为近期我方发现用户在提交数据时，user_ip在网络层被篡改，导致签名错误，所以我们规定使用这种格式。
        $productname = iconv("UTF-8", "gbk", $productname);
        $goods_name = $productname;//商品名称，不能为空（不参加签名）
        $remark = "";              //商户自定义，原样返回,可以为空。
        $goods_note = "";
        $payment_mode = "cashier";//收银台=cashier，接口=UnifiedOrder（目前支持扫码，app）
        $n = "南京麻花网络";
        $fileName = iconv("UTF-8", "gbk", $n);
        $strs = '{"s":"WAP","n":"' . $fileName . '","id":"http://46yx.com"}';
        $str = base64_encode($strs);
        $meta_option = urlencode($str);//加密后的结果
        $bank_card_type = "-1";        //银行类型：未知=-1，储蓄卡=0，信用卡=1。
        $timestamp = "";               //时间戳，传了订单有效时间上下1min，从1970年1月1日0时到现在的毫秒数，传了参与签名，签名位置在key前面。
        $sign_type = "MD5";            //签名类型，MD5\RSA\RSA2

        $data['version'] = $version;
        $data['scene'] = $scene;
        $data['payment_mode'] = $payment_mode;
        $data['agent_id'] = $agent_id;
        $data['agent_bill_id'] = $agent_bill_id;
        $data['agent_bill_time'] = $agent_bill_time;
        $data['pay_type'] = $pay_type;
        $data['pay_amt'] = $pay_amt;
        $data['notify_url'] = $notify_url;
        $data['return_url'] = $return_url;
        $data['user_ip'] = $user_ip;
        $data['goods_name'] = $goods_name;
        $data['goods_note'] = $goods_note;
        $data['remark'] = $remark;
        $data['meta_option'] = $meta_option;
        $data['bank_card_type'] = $bank_card_type;

        $sign_str = '';
        $sign_str = $sign_str . 'version=' . $version;
        $sign_str = $sign_str . '&agent_id=' . $agent_id;
        $sign_str = $sign_str . '&agent_bill_id=' . $agent_bill_id;
        $sign_str = $sign_str . '&agent_bill_time=' . $agent_bill_time;
        $sign_str = $sign_str . '&pay_type=' . $pay_type;
        $sign_str = $sign_str . '&pay_amt=' . $pay_amt;
        $sign_str = $sign_str . '&notify_url=' . $notify_url;
        $sign_str = $sign_str . '&return_url=' . $return_url;
        $sign_str = $sign_str . '&user_ip=' . $user_ip;
        $sign_str = $sign_str . '&bank_card_type=' . $bank_card_type;
        $sign_str = $sign_str . '&remark=' . urlencode($remark);
        $sign_str = $sign_str . '&key=' . $sign_key;
        $sign = md5($sign_str); ////MD5\RSA\RSA2签名结果
        $data['sign'] = $sign;
        $urls = Config::get('wxwappay-h5')['pay_url'];
        $response = $this->postXmlCurl($data, $urls);
        $redirectUrl = "";
        if (!empty($response)) {
            if ($response['ret_code'] == '0000') {
                $redirectUrl = $response['redirectUrl'];
                $ret_code = $response['ret_code'];
                $ret_msg = $response['ret_msg'];
                $hf_sign = md5('redirectUrl=' . $redirectUrl . '&ret_code=' . $ret_code . '&ret_msg=' . $ret_msg . '&key=' . $sign_key);
                if ($hf_sign != $response['sign']) {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
        $pay_return['mweb_url'] = $redirectUrl;
        $pay_return['referer'] = Config::get('wxpay-h5')['referer'];
        return $pay_return;
    }


    /**
     * 微信官方h5支付
     *
     * @param string $orderid 订单号
     * @param float $real_amount 支付金额
     * @param string $body 商品名称
     *
     * @return array 支付参数数组
     */
    public function getWxpayH5Param($orderid, $real_amount, $body, $ip = '', $notify_url = '', $gameid='')
    {
        $pay_return = false;
        $body = '充值';                         // 防止商品名展示游戏信息，统一为充值。
        $wap_url = 'http://' . HTTP_HOST_URL; // 支付成功后打开的网址跳回游戏
        if ($gameid) {
            $wap_url .= '?gameid=' . $gameid;
        }

        $wxPay = new WeixinPay();
        $params['appid'] = Config::get('wxpay-h5')['app_id'];
        $params['mch_id'] = Config::get('wxpay-h5')['mch_id'];
        $params['nonce_str'] = random(10);
        $params['body'] = $body;
        $params['out_trade_no'] = $orderid;
        $params['total_fee'] = $real_amount * 100;                                     // 支付金额,以分为单位
        $params['spbill_create_ip'] = $ip;
        $params['notify_url'] = $notify_url;
        $params['trade_type'] = 'MWEB';
        $params['scene_info'] = '{"h5_info":{"type":"Wap","wap_url":"'.$wap_url.'","wap_name":"祈盟"}}';
        //获取签名数据
        $params['sign'] = $wxPay->MakeSign($params, Config::get('wxpay-h5')['key']);

        $xml = dataToXml($params);
        $response = $wxPay->postXmlCurl($xml);
        $result = $wxPay->xmlToArray($response);
        if ($result['return_code'] != 'SUCCESS') {
            return ['error' => true, 'msg' => $result['return_msg']];
        }

        if (array_key_exists("return_code", $result) && $result["return_code"] == "SUCCESS" && array_key_exists("result_code", $result) && $result["result_code"] == "SUCCESS") {
            $pay_return['prepay_id'] = $result['prepay_id'];
            $pay_return['trade_type'] = $result['trade_type'];
            if (strpos($_SERVER['HTTP_USER_AGENT'],"Darwin") > -1) {
                $pay_return['mweb_url'] = 'http://sdkapi.' . QM_DOMAIN_URL . '/jump?key=' . base64_encode($result['mweb_url']);
            } else {
                $pay_return['mweb_url'] = $result['mweb_url'];
            }
            $pay_return['referer'] = Config::get('wxpay-h5')['referer'];
        } else {
            return ['error' => true, 'msg' => '支付方式发起下单异常！'];
        }

        return ['error' => false, 'data' => $pay_return];
    }


    /**
     * 酷点微信h5支付
     *
     * @param string $orderid 订单号
     * @param float $real_amount 支付金额
     * @param string $body 商品名称
     *
     * @return array 支付参数数组
     */
    public function getkdh5weixinParam($orderid, $real_amount, $body, $notify_url = '')
    {
        $pay_return = false;
        vendor('kdpaySdk.des');
        $kdh5pay = new \kdpay();

        $return_url = Config::get('kdh5')['return_url'] . "?orderid=" . $orderid;
//        $notify_url = Config::get('kdh5')['notify_url'];

        log_message('打印2:' . $return_url, 'log', LOG_PATH . 'apilog/');
        $response = $kdh5pay->oil_fee_jsapi_get($body, $orderid, $real_amount, $return_url, $notify_url);
        if (!$response) {
            return false;
        }
        $result = $response;

        $pay_return['mweb_url'] = $result;
        $pay_return['referer'] = Config::get('kdh5')['referer'];

        return $pay_return;
    }

    public function getkdsmweixinParam($orderid, $real_amount, $body, $notify_url = '')
    {
        $pay_return = false;
        vendor('kdpaySdk.des');
        $kdh5pay = new \kdpay();

        $return_url = Config::get('kdh5')['return_url'] . "?orderid=" . $orderid;
//        $notify_url = Config::get('kdh5')['notify_url'];

        log_message('打印2:' . $return_url, 'log', LOG_PATH . 'apilog/');
        $response = $kdh5pay->oil_fee_sm_get($body, $orderid, $real_amount, $return_url, $notify_url);
        if (!$response) {
            return false;
        }
        $result = $response;

        $pay_return['mweb_url'] = $result;
        $pay_return['referer'] = Config::get('kdh5')['referer'];

        return $pay_return;
    }


    /**
     * TODO: 全民付 h5
     * @param $orderid
     * @param $real_amount
     * @param $productname
     * @param string $productdesc
     * @return string 发起跳转的URL
     * @throws \Exception
     */
    public function getQmfWapH5Param($paytype, $orderid, $real_amount, $productname, $productdesc = '', $notify_url = '')
    {
        /**
         * https://test-api-open.chinaums.com/v1/netpay/trade/h5-pay
         * ?appId=10037ca75e6125aa015e9e12a89b001b
         * &timestamp=20170606135700
         * &nonce=99930a147f5353dd8a8f29a5329f37e9
         * &content={"requestTimestamp":"2018-06-14 14:31:36","sign":"88FDA8C5105C6F1091BD3EA7CDDBEFC9","mid":"898201612345678","subOrders":[{"mid":"988460101800202","totalAmount":1},{"mid":"988460101800203","totalAmount":1}],"tid%22:"88880001","instMid":"YUEDANDEFAULT","platformAmount":"1"%2C"srcReserve":"reserve","totalAmount":"3","limitCreditCard":%22","divisionFlag":"true","notifyUrl":"https://qrtest2.chinaums.com/netpayportal/test/notifyUrl.do","returnUrl":"https://qrtest2.chinaums.com/netpayportal/test/returnUrl.do","merOrderId":"1000201806141434075636511"}
         * &signature=IPmdGHYCcfN+mto0/02ZkwoUf1NT3YqPKaUyKMaec1I
         */
        $config = Config::get('qmf_pay');
        $paytypes = mb_substr($paytype, 4);
        $api_url = $config['api_url'][$paytypes];

        $content = json_encode([
            "requestTimestamp" => date("Y-m-d H:i:s", time()),
            "merOrderId" => $orderid,                       // 商户订单号
            // "srcReserve" => "xxxx", // 预留参数
            "mid" => "xxxx",                                // 商户号
            "tid" => "xxxx",                                // 终端号
            "instMid" => "xxxx",                            // 业务类型
            "totalAmount" => formatYuanToFen($real_amount), // 支付总金额(分)
            "notifyUrl" => $config['notify_url'],
        ]);

        $result = [
            "authorization" => "OPEN-FORM-PARAM",
            "appId" => "xxx",
            "timestamp" => date('YmdHis', time()), // yyyyMMddHHmmss
            "nonce" => random(32),                 // 随机数
            "content" => $content,
        ];
        $result["signature"] = $this->paySign($config['app_id'], $result['timestamp'], $result['nonce'], $content, $config['app_key']);

        // dump(http_build_query($result));
        return createRefererUrl($api_url, $result);
    }

    public function paySign($appid, $timestamp, $nonce, $content, $appkey){
        $hashCont = hash('sha256', $content);
        return base64_encode(hash_hmac("sha256", $appid.$timestamp.$nonce.$hashCont, $appkey));
    }

    /**
     * 易宝wxmp-h5
     *
     * @param $paytype 支付方式  1：支付宝   2：微信小程序    22:微信扫码    999 :小程序调试参数 41:微信H5小程序
     * @param string $orderid 订单号
     * @param $real_amount 订单金额
     * @param string $productname 商品名
     * @param $notify_url 支付成功回调地址
     *
     * @return array
     */
    public function getYbzfParam($paytype, $orderid, $real_amount, $productname, $notify_url = '')
    {
        $config = Config::get('ybzf_pay');
        // $order_type = $config['order_type'];

        // 判断支付方式是否存在
        if (!in_array($paytype, ['ybzf_wxmp_h5', 'coin-ybzf_wxmp_h5', 'mix-ybzf_wxmp_h5'])) {
            return ['error' => true, 'msg' => '支付方式有误！'];
        }
        
        $orderData = [
            'pollingid' => $config['polling_id'],            // 轮询ID
            'orderno' => $orderid,                           // 订单号
            'productname' => $productname,                   // 商品名
            'productprice' => formatYuanToFen($real_amount), // 商品价格（单位：分）
            'order_type' => 41,                              // 支付方式 = 微信H5跳小程序
            'notifyurl' => $notify_url,                      // 支付成功回调地址
            'returnurl' => $config['return_url'],            // 页面回跳地址
        ];

        $res = get_http_response('https://pccp.everwings.online/matrix/getPollingPayInfo', $orderData, 'post');
        if (empty($res)) { // 一般是对方系统升级问题
            curlDD("易宝wxmp-h5 - 支付渠道异常，请迅速联系易宝平台！", Env::get('dingtalk.warning_url'));
            return ['error' => true, 'msg' => '支付平台下单异常.！'];
        }
        if ($res == 'error') {
            return ['error' => true, 'msg' => '支付方式平台异常..！'];
        }
        // if (in_array($order_type, [99])) {
        //     $res = json_decode($res, true);
        //     if ($res['code'] != '00000') {
        //         return ['error' => true, 'msg' => '支付方式平台下单失败...!'];
        //     }
        // }
        return ['error' => false, 'data' => ['mweb_url' => $res]];
    }


    /**
     * 联动优势-支付宝
     */
    public function getldysParam($data, $way)
    {
        return Pay::makePay('ldys_pay', $data, $way);
    }

    /**
     * 趣智连支付
     */
    public function getqzlParam($data, $way)
    {
        return Pay::makePay('qzl_pay', $data, $way);
    }

    /**
     * 优亿支付
     */
    public function getYyYbPayParam($data, $way)
    {
        return Pay::makePay('yyyb_pay', $data, $way);
    }

    /**
     * 喜钛游
     */
    public function getXtyPayParam($data, $way)
    {
        return Pay::makePay('xty_pay', $data, $way);
    }

    //获取真实IP，可以根据实际情况自行获取
    function getUserIp()
    {
        return request()->ip();
        try {
            $info = getCurl('https://myip.ipip.net');
            if (!$info) {
                $info = file_get_contents('http://myip.ipip.net');
            }

            $ipstr = explode('：', $info);
            $ip = explode(' ', $ipstr[1]);
            return $ip[0];
        } catch (Exception $e) {
            return request()->ip();
        }
    }

    //获取请求参数串
    function getToStr($arr, $key)
    {
        $date = $this->getDate($arr);
        $info = $this->createSignaTure($arr, $key);
        $str = '';
        foreach ($date as $k => $v) {
            if ($v != '') {
                $str .= $k . '=' . urlencode($v) . '&';
            }
        }
        $str .= 'mhtSignature' . '=' . $info;
        log_message($str . "\r\n", 'log', LOG_PATH . '../paylog/');
        //file_put_contents('../baowen.log', $str."\r\n");
        return $str;
    }

    //获取参数
    function getDate($arr)
    {
        $result = array();
        $funcode = $arr['funcode'];
        foreach ($arr as $key => $value) {
            if (($funcode == 'WP001') && !($key == 'mhtSignature' || $key == 'signature')) {
                $result[$key] = $value;
                continue;
            }
            if (($funcode == 'N001' || $funcode == 'N002') && !($key == 'signature')) {
                $result[$key] = $value;
                continue;
            }
            if (($funcode == 'MQ002') && !($key == 'mhtSignature' || $key == 'signature')) {
                $result[$key] = $value;
                continue;
            }
        }
        return $result;
    }

    //获取签名
    function createSignaTure($arr, $key)
    {
        $date = $this->getDate($arr);
        ksort($date);
        $str = '';
        foreach ($date as $k => $v) {
            if ($v != '') {
                $str .= $k . '=' . $v . '&';
            }
        }
        $str .= strtolower(md5($key));
        return (strtolower(md5($str)));
    }

    public function xzzfbzfpost($url, $data)
    {
        $curl = curl_init();                                                // 启动一个CURL会话
        curl_setopt($curl, CURLOPT_URL, $url);                              // 要访问的地址
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);                      // 对认证证书来源的检查
        //curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1); // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
        //curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);                         // 自动设置Referer
        curl_setopt($curl, CURLOPT_POST, 1);                                // 发送一个常规的Post请求
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);                      // Post提交的数据包
        curl_setopt($curl, CURLOPT_TIMEOUT, 40);                            // 设置超时限制防止死循环
        curl_setopt($curl, CURLOPT_HEADER, 0);                              // 显示返回的Header区域内容
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);                      // 获取的信息以文件流的形式返回
        $tmpInfo = curl_exec($curl);                                        // 执行操作
        log_message($tmpInfo, 'log', LOG_PATH . '../paylog/');
        //        if (curl_errno($curl)) {
        //            echo 'Errno'.curl_error($curl);
        //            echo  curl_errno($curl);//捕抓异常
        //        }
        curl_close($curl);                                                  // 关闭CURL会话
        return $tmpInfo;                                                    // 返回数据
    }

    /**
     *    作用：以post方式提交xml到对应的接口url
     *
     * @param $xml string xml的报文内容
     * @param $second int 请求的超时时间
     *
     */
    public function postXmlCurl($post_data, $url = '', $second = 30)
    {
        if ($url == '') {
            $url = $this->url;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        // post数据
        curl_setopt($ch, CURLOPT_POST, 1);
        // post的变量
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        //执行请求
        $output = curl_exec($ch);
        //打印获得的数据
        $xml = $this->xmlToArray($output);
        return $xml;
        curl_close($ch);
    }

    /**
     * 将xml转为array
     * @param string $xml
     *
     * @return array
     */
    public function xmlToArray($xml)
    {
        if (!$xml) {
            return false;
        }
        $data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        return $data;
    }
}
