{extend name="layout/content" /}
{block name="header"}
<title>礼包列表</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }
    .clearfix:before,
    .clearfix:after {
        content: " ";
        display: table;
    }

    .clearfix:after {
        clear: both;
    }
    .showPassword{
        cursor: pointer;
        text-decoration: underline;
        color: blue;
    }
</style>
{/block}
{block name="content"}
<div class="x-body">

    <form class="layui-form">

        <div style="float: right;">

            <div class="layui-inline">
                <label class="layui-form-label">用户名：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="username" placeholder="请输入用户名" value="{$Request.get.username}">
                </div>
            </div>

            <div class="layui-inline">
                <label for="J_gameid">游戏名：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='J_gameid' name="gameid" value="{$Request.get.gameid}" />
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">礼包名：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="title" placeholder="请输入礼包名" value="{$Request.get.title}">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" style="width:114px;">创建时间：</label>
                <input class="layui-input" placeholder="开始日" name="cr_start" id="cr_start" value="{:input('request.cr_start',date('Y-m-d'))}" autocomplete="off" style="width:130px;display:inline-block">
                <span>-</span>
                <input class="layui-input" placeholder="截止日" name="cr_end" id="cr_end" value="{:input('request.cr_end',date('Y-m-d'))}" autocomplete="off" style="width:130px;display:inline-block">
            </div>

            <div class="layui-inline">
                <button class="layui-btn layui-btn-radius" lay-submit>查询</button>
            </div>

        </div>



        <div style="clear:both"></div>

        <table class="layui-table">
            <thead>
            <tr>
                <th>用户名</th>
                <th>游戏名</th>
                <th>礼包名</th>
                <th>礼包码</th>
                <th>领取时间</th>
            </tr>
            </thead>
            <tbody>
            {empty name="list"}
            <tr>
                <td colspan="20" style="text-align: center;">暂无数据</td>
            </tr>
            {else/}
            {volist name="list" id="vo"}
            <tr>
                <td>
                    <a class="showPassword" data-id="{$vo.id}">
                        {:escape($vo.username)}
                    </a>
                </td>
                <td>{$vo.name}</td>
                <td>{$vo.title}</td>

                <td>{$vo.code}</td>
                <td>{:date('Y-m-d H:i:s', $vo.create_time)}</td>
            </tr>
            {/volist}{/empty}
            </tbody>
        </table>


        <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>


    </form>



</div>
{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $("#J_gameid").FuzzySearch({
        inputID: 'J_gameid',
        title: '请输入游戏名',
        data: {:json_encode($game_list)},
    searchBtn: 'J_search_submit',
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;

        //执行一个laydate实例
        laydate.render({
            elem: '#cr_start', //指定元素
            type: 'date'
        });

        //执行一个laydate实例
        laydate.render({
            elem: '#cr_end', //指定元素
            type: 'date'
        });
    });

    // 查看明文
    $(".showPassword").click(function (event) {
        var id = $(this).data('id');
        var _that=$(this);
        $.ajax({
            type: "post",
            url: "{:url('checkReceiveUser')}",
            async: false,
            data: {'id':id},
            dataType: 'json',
            timeout: 5000,
            success: function (data) {
                if (data['code']) {
                    _that.text(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    })
</script>
{/block}