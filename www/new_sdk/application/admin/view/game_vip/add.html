{extend name="layout/content" /}
{block name="header"}
<title>新增VIP</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
 <style>
     .layui-form-label {
         width: 92px;
     }

     .layui-input-block {
         margin-left: 122px;
         min-height: 36px;
     }
 </style>
<script type="text/javascript" src="__STATIC__/js/admin/html5.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/admin/respond.min.js" charset="utf-8"></script>
{/block} {block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('add')}" method="post" enctype="multipart/form-data">

        <div class="layui-form-item">
            <label class="layui-form-label">游戏名：</label>
            <div class="layui-input-inline FuzzySearch_Container" style="width: 190px;">
	             <div>
		              <input type="hidden" id='J_gameid' name="game_id" value="{$Request.get.game_id}" />
		         </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">VIP价格：</label>
            <div class="layui-input-inline" style="width:auto">
                <div id="localImag">
                    <img src="" id="preview" alt="">
                </div>
                <input type="file" name="img" onchange="setImagePreview(this)">
            </div>

            <div class="layui-form-mid layui-word-aux">
                <span class="x-red">请上传jpg、png的图片</span>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="game_online_date" class="layui-form-label">游戏上线时间：</label>

            <div class="layui-input-inline" style="width:auto">
                <input class="layui-input" name="game_online_date" id="game_online_date" autocomplete="off">
            </div>
        </div>


        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" id="J_submit">确定</button>
                <a href="{:url('index')}" class="layui-btn layui-btn-primary" >返回</a>
            </div>
        </div>
    </form>
</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
	$("#J_gameid").FuzzySearch({
		 inputID    : 'J_gameid',
		 title   	: '请输入游戏名称',
		 data       :{:json_encode($game_list)},
		 searchBtn	:'J_submit',
	});

    layui.use(['laydate'], function () {
        var laydate = layui.laydate;

        //执行一个laydate实例
        laydate.render({
            type: 'datetime',
            elem: '#game_online_date', //指定元素
            format:"yyyy-MM-dd HH:mm"
        });
    });

    function setImagePreview(avalue) {
        var docObj = avalue;
        //img
        var imgObjPreview = document.getElementById("preview");
        //div
        var divs = document.getElementById("localImag");
        if (docObj.files && docObj.files[0]) {
            //imgObjPreview.src = docObj.files[0].getAsDataURL();
            //火狐7以上版本不能用上面的getAsDataURL()方式获取，需要一下方式
            imgObjPreview.src = window.URL.createObjectURL(docObj.files[0]);
        } else {
            //IE下，使用滤镜
            docObj.select();
            var imgSrc = document.selection.createRange().text;
            var localImagId = document.getElementById("localImag");
            //图片异常的捕捉，防止用户修改后缀来伪造图片
            try {
                localImagId.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)"
                localImagId.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = imgSrc;
            } catch(e) {
                alert("您上传的图片格式不正确，请重新选择!");
                return false;
            }
            imgObjPreview.style.display = 'none';
            document.selection.empty();
        }
        return true;
    }
</script>
{/block}