{extend name="layout/content" /} {block name="header"}
<title>角色管理</title>

{/block} 
{block name="content"}
<div class="x-body">
	
	<div class="toolWrap">
		<a href="{:url('roleadd')}" class="layui-btn layui-btn-radius">
			<i class="layui-icon">&#xe654;</i>添加角色
		</a>
	</div>

	<table class="layui-table">
		<thead>
			<tr>
				<th>ID</th>
				<th>角色名称</th>
				<th>角色描述</th>
				<th>状态</th>
				<th>创建时间</th>
				<th>操作</th>
			</tr>
		</thead>
		<tbody>
			{foreach name="roles" item="vo"}
			<tr>
				<td>{$vo.id}</td>
				<td style="text-align: left !important;">{$vo.name}</td>
				<td>{$vo.remark}</td>
				<td>
					{if condition="$vo['status'] eq 1"}
					<font color="red">√</font>
					{else /}
					<font color="red">╳</font>
					{/if}
				</td>
				<td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
				<td>
					<a href="{:url('roleadd',['id'=>$vo.id])}" class="layui-btn">
						<i class="layui-icon">&#xe642;</i>新增下级角色</a>
					<a href="{:url('roleedit',['id'=>$vo.id])}" class="layui-btn">
						<i class="layui-icon">&#xe642;</i>编辑</a>
					<a href="{:url('authorize',['id'=>$vo.id])}" class="layui-btn layui-btn-normal">
						<i class="layui-icon">&#xe614;</i>权限设置</a>
					
					<a href="javascript:;" class="layui-btn  layui-btn-danger delete" data-id ="{$vo.id}">
						<i class="layui-icon">&#xe640;</i>删除</a>
				</td>
			</tr>
			{/foreach}
		</tbody>
	</table>
	
	


</div>
{/block} 
	
{block name="footer"}
	<script>
		
		$(".delete").click(function(){
			var id = $(this).data("id");
			var url = "{:url('roledelete')}?id=" + id;

			console.log(id,url);

			layer.confirm('确认要删除吗？', {
		        shade: 0.4
		    }, function (index) {
		        $.getJSON(url, function (res) {
		
		            if (res.code) {

		            	if ( res.msg == '删除成功！') {
		            		layer.msg(res.msg, {
		                    icon: 1,
		                    shade: 0.4,
		                    time: 1000
			                }, function () {
			                    location.href = res.url;
			                });

		            	}else{
		            	    /*console.log('{:url(\'roledelete\')}?id='+id+'&type=1');
		            	    return;*/


		            		del_roleinfo(this,'{:url(\'roledelete\')}?id='+id+'&type=1');
		            	}
		                
		            } else {
		                layer.msg(res.msg, {
		                    icon: 5,
		                    shade: 0.4,
		                    time: 1000
		                });
		            }
		        });
		
		    });
			
			
		})
		
	function del_roleinfo(obj, url) {
	    layer.confirm('是否删除该角色？删除后它的所有下级角色也将被删除。', {
	        shade: 0.4
	    }, function (index) {
	        $.getJSON(url, function (res) {
	
	            if (res.code) {
	                layer.msg(res.msg, {
	                    icon: 1,
	                    shade: 0.4,
	                    time: 1000
	                }, function () {
	                    location.href = res.url;
	                });
	            } else {
	                layer.msg(res.msg, {
	                    icon: 5,
	                    shade: 0.4,
	                    time: 1000
	                });
	            }
	        });
	
	    });
	}
	
	</script>
{/block}