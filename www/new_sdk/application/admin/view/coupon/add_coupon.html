{extend name="layout/content" /}
{block name="header"}
<title>添加菜单</title>
{/block}
{block name="content"}
<style type="text/css">
    .img {
        width: 88px;
        height: 88px;
        cursor: pointer;
    }
</style>
<body>

<div class="x-body">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label">添加数量：</label>
            <div class="layui-input-inline">
                <input type="text" name="number" id="number" value="" class="layui-input" required="" lay-verify="required">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" id="examine" val="{$id}">确定</button>
                <a type="button" class="layui-btn layui-btn-primary" id="cancell">取消</a>
            </div>
        </div>


    </form>
</div>
</body>
{/block}


{block name="footer"}
<script type="text/javascript">
    $(document).on('click', '#examine', function (event) {
        var id = $(this).attr('val'), data = {};
        data.number = $("#number").val();
        data.id = id;
        $.ajax({
            type: "post",
            url: "{:url('addCouponSave')}",
            /*async: false,*/
            data: data,
            dataType: 'json',
            success: function (data) {
                layer.msg(data['msg']);
                setTimeout(function () {
                    parent.window.location.href = "{:url('index')}";
                }, 1500);

            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    });
    //取消按钮
    $(document).on('click', '#cancell', function (event) {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    });
</script>
{/block}
