{{extend name="layout/content" /} {block name="header"}
<title>广告列表</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">

<style>
    .layui-table th,
    .layui-table td {
        text-align: center;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .check_photo + img {
        display: none;
    }

</style>
{/block}

{block name="content"}
<div class="x-body">

    <form class="layui-form" method='get' id="J_search_form">

        <div style="float:left">
            <a class="layui-btn layui-btn-primary" href="{:url('index')}">
               返回
            </a>
        </div>
        <div style="float: right;">
            <div class="layui-inline">
                <label>账号</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name='username' value="{$Request.get.username}"/>
                </div>
            </div>
            <div class="layui-inline">
                <label>代金券名称</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name='name' value="{$Request.get.name}"/>
                </div>
            </div>
            <div class="layui-inline">
                <label>使用状态：</label>
                <div class="layui-input-inline">

                    <select lay-search name="is_use" id="grant_result" class="layui-form-select"
                            >
                        <option value="">请选择类型</option>
                        <option value="1" {if condition="$Request.get.is_use eq 1" }selected="selected"  {/if}>未使用</option>
                        <option value="2" {if condition="$Request.get.is_use eq 2" }selected="selected"  {/if}>使用中</option>
                        <option value="3" {if condition="$Request.get.is_use eq 3" }selected="selected"  {/if}>已使用</option>

                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label>有效状态：</label>
                <div class="layui-input-inline">

                    <select lay-search name="state" id="grant_result" class="layui-form-select"
                            >
                        <option value="">请选择类型</option>
                        <option value="1" {if condition="$Request.get.state eq 1" }selected="selected"  {/if}>有效</option>
                        <option value="2" {if condition="$Request.get.state eq 2" }selected="selected"  {/if}>冻结</option>

                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
            </div>
        </div>
    </form>
    <div style="float: right;">
    </div>
    </br>
    </br>
    </br>

    <div class="demoTable margin-top-10">

        <button class="layui-btn layui-btn-danger" data-type="getCheckData" id="multi_approved">冻结</button>
        <button class="layui-btn layui-btn-normal" data-type="getCheckData" id="multi_refuse">解冻</button>
    </div>
    <div style="clear:both"></div>

    <table class="layui-table">
        <thead>
        <tr>
            <th><input type="checkbox" name="check_all" value=""></th>
            <th>ID</th>

            <th>代金券</th>
            <th>key</th>
            <th>面值</th>
            <th>有效时间</th>
            <th>账号</th>
            <th>状态</th>
            <th>使用状态</th>
            <th>使用时间</th>
            <th>操作</th>
        </tr>
        </thead>

        <tbody id="viewer_wrap">
        {notempty name="list"}
        {volist name="list" id="vo"}
        <tr>
            <td><input type="checkbox" name="check_item" value="{$vo.id}"></td>
            <td>{$vo.id}</td>

            <td>{$vo.name}</td>
            <td>{$vo.code}</td>
            <td>
                面值:{$vo.money}（最低使用金额： {$vo.min_money}）
            </td>

            <td>
                {switch name="$vo.type_id" }
                {case value="1"}
                {:date("Y-m-d H:i:s",$vo.coupon_start_time)}-{:date("Y-m-d 23:59:59",$vo.coupon_end_time)}
                {/case}
                {case value="2"}{$vo.start_time}-{$vo.end_time}{/case}
                {/switch}

            </td>
            <td>{$vo.username}</td>
            <td>{$vo.state == 1 ?"有效":"冻结"}{$vo.state_str}</td>
            <td> {switch name="$vo.is_use" }
                {case value="1"}未使用{/case}
                {case value="2"}使用中{/case}
                {case value="3"}已使用{/case}
                {/switch}
            </td>
            <td>{$vo.use_time}</td>
            <td class="td-manage">
                {if condition="$vo.state eq 1"}
                <a href="{:url('frozen',['id'=>$vo.id, 'coupon_id'=>$vo.coupon_id])}" class="layui-btn layui-btn-danger"><i
                        class="layui-icon">&#xe63c;</i>冻结</a>
                {else/}
                <a href="{:url('thaw',['id'=>$vo.id, 'coupon_id'=>$vo.coupon_id])}" class="layui-btn layui-btn-normal"><i
                        class="layui-icon">&#xe63c;</i>解冻</a>
                {/if}
                <a href="javascript:(0)" class="layui-btn tapCopy" data-clipboard-text=" {$vo.code}">复制key</a>
            </td>
        </tr>
        {/volist}
        {/notempty}
        </tbody>
    </table>

    <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
        {$page}
        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page"
                   onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
        </div>
    </div>


</div>

{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/bootstrap-table.min.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script type="text/javascript" src="__STATIC__/js/clipboard.min.js"></script>
<script>
    $(function(){
        var clipboard = new ClipboardJS('.tapCopy',{
            text: function(trigger) {
                alert("复制成功！");
                return trigger.getAttribute('data-clipboard-text');
            }
        });
    });
    var $table = $('#objTable');
    var url = "{:url('approved')}";
    $table.bootstrapTable({});

    // 批量拒绝
    $('#multi_refuse').on('click', function () {

        layer.confirm('确定批量解冻？', {shade: 0.4}, function () {

            handleFrozen(1);
        });
    });

    // 批量通过
    $('#multi_approved').on('click', function () {
        layer.confirm('确定批量冻结？', {shade: 0.4}, function () {
            handleFrozen(2);
        });

    });

    var inputs = document.querySelectorAll('input[type=checkbox]');

    // 【全选】
    function checkAll() {
        var num = 0;
        for (var i = 1; i < inputs.length; i++) {
            // 如果当前选框被选中，那么个数+1
            if (inputs[i].checked == true) {
                num++;
            }
        }

        inputs[0].checked = (num === inputs.length - 1);
    }

    // 对【全选】input做监听  更改全选/非全选状态和文字
    inputs[0].onclick = function () {
        for (var i = 1; i < inputs.length; i++) {
            // 将全选框的状态赋给每一个选项框
            inputs[i].checked = this.checked;
        }
        // 改状态
        checkAll();
    };

    // 对每一个input做监听事件
    for (var i = 1; i < inputs.length; i++) {
        inputs[i].onclick = function () {
            // 改状态
            checkAll();
        }
    }

    /**
     * 获取选中id
     * @returns {string}
     */
    function getCheckedId() {
        var ids = '';
        for (var i = 0; i < inputs.length; i++) {
            if (inputs[i].checked) {
                ids ? ids += ',' : '';
                ids += inputs[i].value;
            }
        }

        return ids;
    }

    // 统一处理
    function handleFrozen(state) {
        var ids = getCheckedId();
        var _that = $(this);

        if (!ids) {
            layer.msg('请先选择审核记录');
            return false;
        }
        getPost(ids, state)
    }

    function getPost(ids, state) {
        $.ajax({
            type: "post",
            url: url,
            /*async: false,*/
            data: {ids: ids, state: state},
            dataType: 'json',
            success: function (data) {
                layer.msg(data['msg']);
                window.location.reload();

            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    }


</script>
{/block}
