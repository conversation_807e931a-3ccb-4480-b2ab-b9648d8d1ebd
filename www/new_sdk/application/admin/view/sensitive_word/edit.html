{extend name="layout/content" /}
{block name="header"}
<title>编辑敏感词</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<style>
	.layui-form-label {
		width: 178px;
	}
	.tip{
	color: #888888;
	padding-left: 122px;	
	}

</style>
{/block}
{block name="content"}
<body>
<div class="x-body">
	<form class="layui-form" action=" " method="post">

		<div class="layui-form-item">
			<label for="word" class="layui-form-label">敏感词<b style="color: red;">*</b>：</label>
			<div class="layui-input-inline">
				<input type="text" name="word" id="word" required autocomplete="off" class="layui-input" placeholder="" value="{$info.word}">
			</div>
			
		</div>
		<p class="tip"> 每个敏感词不可超过30个字符，中文算2个字符。</p>

		<div class="layui-form-item">
			<div class="layui-input-block">
					<a href="#" onClick="javascript :history.back(-1);" class="layui-btn layui-btn-primary" >返回</a>
				<button type="submit" class="layui-btn">确定</button>
			
			</div>
		</div>


	</form>
</div>
{/block}


{block name="footer"}
<script>
	$(".layui-btn").click(function(){
		var word = $("#word").val();
		var reg = /^[\da-z\u2E80-\u9FFF]{2,30}$/i;
		if(word==""){
			layer.alert('请输入敏感词')
		}else if(!reg.test(str)){
			layer.alert('每个敏感词不可超过30个字符，中文算2个字符。')
		}else{
			//提交
		}
	})
</script>
{/block}