<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="renderer" content="webkit">
	<title>祈盟网络游戏推广平台_祈盟网络游戏平台</title>
	<meta name="description" content="祈盟网络游戏推广平台_祈盟网络游戏平台">
	<meta name="keywords" content="祈盟网络游戏推广平台">
	<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
	<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap-theme.min.css">
</head>
<body>
<div class="wrap">
	<ul class="nav nav-tabs">
		<li><a href="{:url('channel_frozen/index')}">渠道冻结列表</a></li>
		<li><a href="{:url('channel_frozen/add')}">添加冻结渠道</a></li>
		<li class="active"><a>编辑冻结渠道</a></li>
	</ul>
	<form method="post" class="form-horizontal js-ajax-form margin-top-20" action="{:url('channel_frozen/addpost')}">
		<div class="form-group">
			<label class="col-sm-2 control-label"><span class="form-required">*</span>游戏</label>
			<select name="game_id">
				<option selected value>请选择游戏</option>
				{foreach name="game_list" item="vo"}
				<option value="{$vo.id}" {if condition="$vo['id'] eq $channel_frozen['game_id']"} selected="selected" {/if}>{$vo.name}</option>
				{/foreach}
			</select>

		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label"><span class="form-required">*</span>渠道</label>
			<select name="channel_id">
				<option selected value>请选择渠道</option>
				{foreach name="channel_list" item="vo"}
				<option value="{$vo.id}" {if condition="$vo['id'] eq $channel_frozen['channel_id']"} selected="selected" {/if}>{$vo.username}</option>
				{/foreach}
			</select>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label"><span class="form-required">*</span>分包</label>
			<div class="col-md-6 col-sm-10">
				<select name="subpackage">
					<option value="1" {if condition="$channel_frozen['subpackage'] eq 0"} selected="selected" {/if}>开启</option>
					<option value="0" {if condition="$channel_frozen['subpackage'] eq 1"} selected="selected" {/if}>禁止</option>
				</select>
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label"><span class="form-required">*</span>发币</label>
			<div class="col-md-6 col-sm-10">
				<select name="grant_money">
					<option value="1" {if condition="$channel_frozen['grant_money'] eq 0"} selected="selected" {/if}>开启</option>
					<option value="0" {if condition="$channel_frozen['grant_money'] eq 1"} selected="selected" {/if}>禁止</option>
				</select>
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label"><span class="form-required">*</span>消费</label>
			<div class="col-md-6 col-sm-10">
				<select name="consume">
					<option value="1" {if condition="$channel_frozen['consume'] eq 0"} selected="selected" {/if}>开启</option>
					<option value="0" {if condition="$channel_frozen['consume'] eq 1"} selected="selected" {/if}>禁止</option>
				</select>
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label"><span class="form-required">*</span>登录 </label>
			<div class="col-md-6 col-sm-10">
				<select name="member_login">
					<option value="1" {if condition="$channel_frozen['member_login'] eq 0"} selected="selected" {/if}>开启</option>
					<option value="0" {if condition="$channel_frozen['member_login'] eq 1"} selected="selected" {/if}>禁止</option>
				</select>
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label"><span class="form-required">*</span>新增</label>
			<div class="col-md-6 col-sm-10">
				<select name="register">
					<option value="1" {if condition="$channel_frozen['register'] eq 0"} selected="selected" {/if}>开启</option>
					<option value="0" {if condition="$channel_frozen['register'] eq 1"} selected="selected" {/if}>禁止</option>
				</select>
			</div>
		</div>

		<div class="form-group">
			<div class="col-sm-offset-2 col-sm-10">
				<button type="submit" class="btn btn-primary js-ajax-submit">添加</button>
			</div>
		</div>
	</form>
</div>
</body>
</html>