{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>添加礼包</title>
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('add')}" method="post">


        <div class="layui-inline">
            <label class="layui-form-label">后台用户：</label>
            <div class="layui-input-inline FuzzySearch_Container">
                <select name="admin_id" id="admin_id" lay-search="true"  lay-verify=“required”>
                    {volist name="admin_list" id="vo"}
                    <option value="{$vo.id}" >{$vo.username}</option>
                    {/volist}
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"> <span class="x-red">*</span>所属商务：</label>
            <div class="layui-input-inline" style="width: 40%">
                <div id="channel_ids"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回
                </button>
            </div>
        </div>

    </form>
</div>

{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/lib/xm-select.js"></script>
<script>
    var tagData1 = {:json_encode($channel_list)}; // 获取到后台给出的数据-PHP写法

    var tagIns1 = xmSelect.render({
        el: '#channel_ids', // div的id值
        toolbar: { // 工具条【‘全选’，‘清空’】
            show: true, // 开启工具条
            showIcon: false, // 隐藏工具条的图标
        },
        autoRow: true,
        // tips: '选择校区', // 让默认值不是“请选择”，而是“选择校区”
        filterable: true, // 开启搜索模式，默认按照name进行搜索
        paging: true, // 启用分页
        pageSize: 100, // 每页的数据个数
        data: tagData1,
        prop: { // 也许你的数据库返回的并不是name和value, 也许你提交的时候不止name和value, 怎么办? 自定义就行
            name: 'name',
            value: 'id'
        },
    })
</script>
{/block}
