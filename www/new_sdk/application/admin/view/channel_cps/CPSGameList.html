{extend name="layout/content" /}{block name="header"}
<title>游戏列表</title>
<style>

</style>
{/block}{block name="content"}
<div class="x-body">
	<div class="toolWrap">
		<a href="{:url('channelListTg')}" class="layui-btn layui-btn-radius">
			返回上一页
		</a>

		<form class="layui-form" method="post" action="{:url('CPSGameList',['channel_id'=>$channel_id])}" style="float: right;">
			<div class="layui-form-item">
				<div class="layui-inline">
					<select name="game_type" id="J_channel_id"  lay-search lay-filter="changeChannel">
						<option value="0" >请选择游戏</option>
						<option value="1" {eq name="game_type" value="1"} selected="selected" {/eq}>最新游戏</option>
						<option value="2" {eq name="game_type" value="2"} selected="selected" {/eq}>热门推荐游戏</option>
					</select>
				</div>

				<button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">搜索</button>
			</div>
		</form>

	</div>
	<table class="layui-table">
		<thead>
		<tr>
			<th>游戏名称</th>
			<th>游戏类型</th>
			<th>游戏排序</th>
		</tr>
		</thead>
		<tbody>
		{notempty name="list"}
		{volist name="list" id="vo" key="k"}
		<tr>
			<td>{$vo.game_name}</td>
			<td>
				{eq name="vo.game_type" value="1"} 最新游戏 {else} 热门推荐游戏 {/eq}
			</td>
			<td>
				{$vo.sort}
			</td>
		</tr>
		{/volist}
		{/notempty}
		{empty name="list"}
		<tr>
			<td colspan="3">
				暂无数据
			</td>
		</tr>
		{/empty}
		</tbody>
	</table>

	<div class="pager-container">
		<span>
		  {$list->total()}条记录
		</span>
		{$page}
	</div>
	
</div>


{/block}

{block name="footer"}
<script type="text/javascript">

</script>
{/block}