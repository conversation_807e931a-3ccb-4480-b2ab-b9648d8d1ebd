{extend name="layout/content" /}
{block name="header"}

<title>游戏冻结</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        line-height: 1;
        
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .edit {
        padding: 0 8.5px;
        height: 30px;
        line-height: 30px;
        border-radius: 5px;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">
       <form class="layui-form" method="get">

            <div class="layui-inline" style="float:left;">
                <a href="{:url('game/bannedCreate')}" class="layui-btn layui-btn-radius"><i class="layui-icon">&#xe654;</i>新增冻结</a>
            </div>

            <div style="float: right;">

                <div class="layui-inline">
                    <label>游戏名称：</label>
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_gameid' name="game_id" value="{$Request.get.game_id}" />
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <label>冻结类型：</label>
                    <div class="layui-input-inline">
                        <select name="banned_type">
                            <option value="">- 请选择冻结类型 -</option>
                            <option {if condition="input('banned_type') eq 'ip'" }selected="selected" {/if} value="ip">IP</option>
                            <option {if condition="input('banned_type') eq 'imeil'" }selected="selected" {/if} value="imeil">IMEI</option>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label>冻结IP/IMEI：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" name="banned_device" value="{:input('banned_device')}" placeholder="请输入IP或IMEI">
                    </div>
                </div>

                <div class="layui-inline">
                    <label>操作时间：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="开始时间" name="start" id="start" readonly='readonly' value="{:input('start')}" autocomplete="off">
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="结束时间" name="end" id="end" readonly='readonly' value="{:input('end')}" autocomplete="off">
                    </div>
                </div>
      
				<div class="layui-inline">
                    <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
                </div>

        </div>

        <div style="clear:both"></div>

        <table class="layui-table">
            <thead>
            <tr>
                <th>游戏名称</th>
                <th>冻结类型</th>
                <th>冻结IP/IMEI</th>
                <th>备注</th>
                <th>添加时间</th>
                <th>操作</th>
            </tr>
            </thead>

            <tbody>
            {notempty name="banned_list"}
            {volist name="banned_list" id="vo"}

            <tr>
                <td>
                    {if condition="$vo.game_id eq 0"}
                    <font>所有游戏</font>
                    {else}
                    {:escape($vo.game_name)}
                    {/if}&nbsp;
                </td>
                <td>
                    {if condition="$vo.banned_type eq 'ip'"}
                    <font>IP</font>
                    {/if}
                    {if condition="$vo.banned_type eq 'imeil'"}
                    <font>IMEI</font>
                    {/if}
                </td>
                <td>{:escape($vo.banned_device)}</td>
                <td>{:escape($vo.notes)}</td>
                <td>{:date('Y-m-d H:i:s',$vo['create_time'])}</td>
                <td class="td-manage">
                    <a href="{:url('bannedEdit',['id'=>$vo.id])}" class="layui-btn btn-info">
                        <i class="icon-edit"></i>编辑
                    </a>
                    <a href='javascript:void(0);' onclick="del_info(this,'{:url('bannedDelete',['id'=>$vo.id])}')" data-url="/CSC/reject" class="layui-btn layui-btn-danger">
                        删除
                    </a>
                </td>
            </tr>
            {/volist}
            {/notempty}
            </tbody>
        </table>

        <!-- <div class="pager-container">
                <span>
                  {$banned_list->total()}条记录
                </span>
            {$page}
        </div> -->


        <div class="pager-container">
            <span>
                {$banned_list->total()}条记录
            </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>

    </form>
</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">
 $(document).ready(function () { 
    $("#J_gameid").FuzzySearch({
    	 
    	 inputID    : 'J_gameid',
		 title   	: '请输入游戏名称',
		 data       :{:json_encode($gameList)},
		 searchBtn	:'J_search_submit',
	    });
     
    $(function () {
        $("#J_gameid_show").attr('name','game_name');
        $("#J_gameid_show").val("{:input('get.game_name')}");
    })

    layui.use(['form', 'laydate'], function () {
        var laydate = layui.laydate;
        var form 	= layui.form;

        var starttime = laydate.render({
            elem: '#start',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });
        
    });
});
</script>
{/block}