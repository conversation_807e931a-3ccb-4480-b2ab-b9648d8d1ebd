{extend name="layout/content" /}
{block name="header"}
<title>添加菜单</title>
{/block}
{block name="content"}
<style type="text/css">
	.img{
		width: 88px;
		height: 88px;
		cursor: pointer;
	}
</style>
<body>

<div class="x-body">
		<form class="layui-form">
			<!-- 未审核 -->
			{if condition="0 eq $list.apply_status" }
				<div class="layui-form-item">
					<label class="layui-form-label">审核结果：</label>
					<div class="layui-input-block">
						<input type="radio" name="status" value="1" title="通过" checked > 
						<input type="radio" name="status" value="2" title="驳回">
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label" {$applyid}>审核备注：</label>
					<textarea name="" style="width: 270px;height: 70px;" id="remark"></textarea>
				</div>

				<div class="layui-form-item">
					<div class="layui-input-block">
						<button type="button" class="layui-btn" id="examine" val = "{$applyid}">确定</button>
						<a type="button" class="layui-btn layui-btn-primary" id="cancell">取消</a>
					</div>
				</div>
			<!-- 审核通过/驳回 -->
			{elseif condition="0 neq $list.apply_status"}	
				<div class="layui-form-item">
					<label class="layui-form-label">审核结果：</label>
					<div class="layui-input-block">
						<input type="radio" name="status" value="1" title="通过" disabled="" {if condition="$list.apply_status eq 1" }checked{/if}>
						<input type="radio" name="status" value="2" title="驳回" disabled="" {if condition="$list.apply_status eq 2" }checked{/if}>
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">审核备注：</label>
					<textarea name="" style="width: 270px;height: 70px;" id="remark" disabled="" placeholder="{$list.remark}"></textarea>
				</div>
			{/if}
			
		</form>
	</div>
</body>
{/block}


{block name="footer"}
<script type="text/javascript">
	$(document).on('click', '#examine', function(event) {
		var applyid = $(this).attr('val'),data = {};
		data.apply_status = $("input[type='radio']:checked").val();
		data.remark = $("#remark").val();
		data.applyid = applyid;
		$.ajax({
			type: "post",
			url: "{:url('doExamine')}",
			/*async: false,*/
			data: data,
			dataType: 'json',
			success: function (data) {
				layer.msg(data['msg']);
				setTimeout(function(){parent.window.location.href = "{:url('packageApply')}";}, 1500);
				
			},
			error: function () {
				layer.alert('网络错误，请重试');
			},
		});
	});
	//取消按钮
	$(document).on('click', '#cancell', function(event) {
		var index=parent.layer.getFrameIndex(window.name);
		parent.layer.close(index);
	});
</script>
{/block}