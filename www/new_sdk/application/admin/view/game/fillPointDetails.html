{extend name="layout/content" /}{block name="header"}

<title>游戏补点详情页</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">

<link rel="stylesheet" href="__STATIC__/css/bootstrap-table.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    /* .layui-icon {
        vertical-align: bottom;
    } */

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 60px;
        font-weight: 400;
        text-align: right;
    }
    .wenzi-explain{
        font-size: 15px;
        margin-top: 5px;
    }
    .wenzi-red{
        color: red;
    }

    .layui-form-label{
        margin: 0;
    }

    .layui-form-item .layui-inline {
        margin: 0px;
    }

    .hide{
        display: none;
    }

    .pager-container{
        margin-top: 10px;
    }

    .detail-view td p{
        text-align: left;
    }

    table th,table td{
        text-align: center;
    }
</style>
{/block}{block name="content"}
<div class="x-body">

   
       
        <form class="layui-form" method="post" action="{:url('fillPointDetails')}" id="J_search_form" >


            <div class="layui-form-item">

                <div class="layui-inline">
                    <button type="button" class="layui-btn layui-btn-radius layui-btn-normal" onclick="goBackToDetail()">返回</button>
                </div>


                <div class="layui-inline">
                    <button type="button" class="layui-btn layui-btn-radius J_add_list" ><i class="layui-icon">&#xe654;</i>添加</button>
                    <!--<a class="layui-btn layui-btn-radius " href="{:url('fillPointDetailsDown')}"><i class="fa fa-download"></i>下载报表</a>-->
                </div>


        
                <div class="layui-inline">
                    <label class="layui-form-label">游戏:</label>
                    <div class="layui-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_gameid' name="game_id" value="{$game_id}" />
                        </div>
                    </div>
                </div>
                

                <div class="layui-inline">
                    <label class="layui-form-label">渠道:</label>
                    <div class="layui-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_channel_id' name="channel_id" value="{$channel_id}" />
                        </div>
                    </div>
                </div>

                <!--<div class="layui-inline">
                    <label class="layui-form-label">时间:</label>
                    <div class="layui-inline">
                        <input type="text" name="dateTime" class="layui-input"/>
                    </div>
                </div>-->

                <div class="layui-inline">
                    <label class="layui-form-label" style="width:104px">结算类型：</label>
                    <div class="layui-input-inline">
                        <select name="type">
                            <option value=""> - 结算类型 - </option>
                            <option {if condition="input('type') eq '1'" }selected="selected" {/if} value="1">折扣类结算</option>
                            <option {if condition="input('type') eq '2'" }selected="selected" {/if} value="2">自然类结算</option>
                            <option {if condition="input('type') eq '3'" }selected="selected" {/if} value="3">首充补点结算</option>
                            <option {if condition="input('type') eq '4'" }selected="selected" {/if} value="4">新用户补点结算</option>
                        </select>
                    </div>  
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label" style="width:66px">状态：</label>
                    <div class="layui-input-inline">
                        <select name="status">
                            <option value=""> - 请选择状态 - </option>
                            <option {if condition="input('status') eq '0'" }selected="selected" {/if} value="0">未生效</option>
                            <option {if condition="input('status') eq '1'" }selected="selected" {/if} value="1">审核中</option>
                            <option {if condition="input('status') eq '2'" }selected="selected" {/if} value="2">生效中</option>
                            <option {if condition="input('status') eq '3'" }selected="selected" {/if} value="3">已失效</option>
                            <option {if condition="input('status') eq '4'" }selected="selected" {/if} value="4">已拒绝</option>
                        </select>
                    </div>  
                </div>

                <div class="layui-inline">
                    <button class="layui-btn layui-btn-normal" lay-submit id="J_search_submit">搜索</button>
                    <a class="layui-btn layui-btn-primary" href="{:url('fillPointDetails')}">清空</a>
                </div>
       

            </div>
        
               

            <div>
                <div class="wenzi-explain wenzi-red">注：所配置渠道包含其下属所有渠道</div>
                <div class="wenzi-explain">当前月份：{php} echo $YearMonth {/php}</div>
            </div>





                <!-- <table class="layui-table"> -->
            <table id="objTable" data-detail-view="true" data-detail-formatter="detailFormatter">
                    <thead>
                        <!-- <tr class="sortable" sort="" sortImg="" currentOrder=""> -->
                        <tr>
                            <th>月份</th>
                            <!-- <th order="gp_create_time" data-hide="tablet">游戏</th> -->
                            <th>游戏</th>
                            <th>渠道</th>
                            <th>结算模式</th>
                            <th>首充补点（平台币）</th>
                            <th>续充补点（平台币）</th>

                            <th>现金返利</th>
                            <th>现金补点</th>
                            
                            <th>充值总额（首充）</th>
                            <th>充值总额（续充）</th>
                            <th>充值总额返利</th>
                            <th>充值总额补点</th>
                            <th>生效时间</th>
                            <th>状态</th>
                            <th>操作</th>

                            <th class="hide">首充时间</th>
                            <th class="hide">续充时间</th>
                            <th class="hide">备注</th>
                            
                        </tr>
                    </thead>


                    <tbody>

                    {notempty name="list"} {volist name="list" id="vo"}
                        <tr>
                            <td>{$vo.year_month}</td>
                            <td>{$vo.game_name}</td>
                            <td>{$vo.channel_name}</td>
                            <td>
                                {switch name="$vo.type" }
                                {case value="1"}折扣类结算{/case}
                                {case value="2"}自然类结算{/case}
                                {case value="3"}首充补点结算{/case}
                                {case value="4"}新用户补点结算{/case}
                                {default /} -
                                {/switch}
                            </td>
                            <td>
                                {if condition="($vo['type'] == 1) OR ($vo['type'] == 3) "}
                                    {$vo.first_point}%
                                {/if}
                            </td>
                            <td>
                                {if condition="($vo['type'] == 1) OR ($vo['type'] == 3) "}
                                {$vo.common_point}%
                                {/if}
                            </td>

                            <td>
                                {if condition="($vo['type'] == 1) OR ($vo['type'] == 3) "}
                                {$vo.extra_profit_point}%
                                {/if}
                            </td>
                            
                            <td>
                                {if condition="($vo['type'] == 1) OR ($vo['type'] == 3) "}
                                {$vo.extra_point}%
                                {/if}
                            </td>
                           
                            <td>
                                {if condition="($vo['type'] == 4)  "}
                                {$vo.first_point}%
                                {/if}
                            </td>
                            <td>
                                {if condition="($vo['type'] == 4) "}
                                {$vo.common_point}%
                                {/if}
                            </td>
                           

                            <td>
                                {if condition="($vo['type'] == 2) OR ($vo['type'] == 4 )"}
                                {$vo.extra_profit_point}%
                                {/if}
                            </td>

                            <td>
                                {if condition="$vo['type'] == 2"}
                                {$vo.extra_point}%
                                {/if}
                            </td>

                            <td>
                                {:date('Y-m-d H:i:s',$vo.begin_time)}
                            </td>
                            <td>
                                {switch name="$vo.status" }
                                {case value="0"}未生效{/case}
                                {case value="1"}审核中{/case}
                                {case value="2"}生效中{/case}
                                {case value="3"}已失效{/case}
                                {case value="4"}已拒绝{/case}
                                {case value="5"}已失效{/case}
                                {default /} -
                                {/switch}
                            </td>
                            <td>
                                {switch name="$vo.status" }
                                {case value="0"}<button type="button" class="layui-btn layui-btn-sm layui-btn toExamine" data-id="{$vo.id}">提审</button>{/case}
                                {case value="1"}<button type="button" class="layui-btn layui-btn-sm layui-btn-normal pass" data-id="{$vo.id}">通过</button>
                                <button type="button" class="layui-btn layui-btn-sm layui-btn-danger refuse" data-id="{$vo.id}">拒绝</button>{/case}
                                {default /} -
                                {/switch}
                            </td>

                            <td>
                                {switch name="$vo.type" }
                                {case value="3"}首充补点时间：{:date('Y-m-d H:i:s',$vo.first_begin_time)} ~ {:date('Y-m-d H:i:s',$vo.first_end_time)}{/case}
                                {case value="4"}新用户补点时间：{:date('Y-m-d H:i:s',$vo.first_begin_time)} ~ {:date('Y-m-d H:i:s',$vo.first_end_time)}{/case}
                                {default /}
                                {/switch}
                            </td>
                            <td>审批：{$vo.remark}</td>
                            <td></td>

                        </tr>
                    {/volist} {/notempty}
                    
                    <!-- {empty name="list"}
                    <tr>
                        <td colspan="15">暂无数据</td>
                    </tr>
                    {/empty} -->


                    </tbody>
                    


                    <!-- <tfoot>
                        <tr class="last">
        
                        </tr>
                    </tfoot> -->



                </table>
    
    


                <div class="pager-container">
                    <span>
                        {$list->total()}条记录
                    </span>
                    {$page}
                    <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                        <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                    </div>
                </div>



          
        </form>
    
    


   
</div>

{/block}

{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/bootstrap-table.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">
    $(function() {

 

        var yearAndMonth="{php} echo $YearMonth {/php}"

        $("#J_gameid").FuzzySearch({
            inputID     : 'J_gameid',
            title   	: '请输入游戏名称',
            data        :{:json_encode($game_list)},
            searchBtn	:'J_search_submit',
        });

        $("#J_channel_id").FuzzySearch({
            inputID     : 'J_channel_id',
            title   	: '请输入渠道名称',
            searchBtn	:'J_search_submit',
            ajaxUrl	    : '{:url("Pay/ajaxGetNoComplexChannel")}',
        });


        var $table = $('#objTable');
        $table.bootstrapTable({
            formatNoMatches: function () {
                return "暂无数据";
            }
        });


        var limitMonth=yearAndMonth.substr(yearAndMonth.length-2);
        var thisYear=new Date().getFullYear();
        var lastDay= new Date(thisYear,limitMonth,0).getDate();
     
        layui.use(['form','laydate','layer'], function () {
            var form = layui.form,
                layer=layui.layer,
                laydate = layui.laydate;

            /*laydate.render({
                type:"datetime",
                elem: 'input[name=dateTime]', 
                min: yearAndMonth+'-01',
                max: yearAndMonth+'-'+lastDay
            });*/

        })


        

        $("tbody").on("click",".toExamine",function () {
            var id = $(this).data('id');
            $.ajax({
                type: "post",
                url: "{:url('examinePoint')}",
                async: false,
                data: {id:id,type:'toexamine'},
                dataType: 'json',
                timeout: 5000,
                success: function (data) {
                    layer.msg(data['msg']);
                    if (data['code']) {
                        setTimeout("window.location.reload()",2000);
                    }
                },
                error: function () {
                    layer.alert('网络错误，请重试');
                },
            });
        })

        $("tbody").on("click",".pass",function () {
            var id = $(this).data('id');
            layer.open({
                title: '通过备注',
                btn:["提交","取消"],
                content: '<form class="layui-form"><div class="layui-inline"><label class="layui-form-label" style="width:66px;">备注：</label><div class="layui-input-inline"><textarea name="pass_desc" class="pass_desc" rows="5" cols="40" placeholder="请输入内容" class="layui-textarea"></textarea></div></div></form>',
                area:["400px","260px"],
                yes: function(index, layero){
                    var remark = $(".pass_desc").val();

                    //按钮【按钮一】的回调
                    $.ajax({
                        url:"{:url('examineAction')}",
                        type:"POST",
                        data:{id:id,type:'pass',remark:remark},
                        dataType:"json",
                        success:function(res){
                            if (res.code == 1){
                                layer.msg(res.msg);
                                setTimeout("window.location.reload()",2000);
                            }else if(res.code == 2){
                                layer.open({
                                    title: '提示',
                                    btn:["提交","取消"],
                                    content:res.msg,
                                    area:["400px","260px"],
                                    yes: function(index, layero){
                                        $.ajax({
                                            url: "{:url('examineActionDo')}",
                                            type: "POST",
                                            data: {id: id, type: 'pass', remark: remark},
                                            dataType: "json",
                                            success: function (ret) {
                                                layer.msg(ret.msg);
                                                if (ret.code == 1){
                                                    setTimeout("window.location.reload()",2000);
                                                }
                                            },
                                            error: function (jqXHR, textStatus, errorThrown) {}
                                        })
                                    },
                                    btn2: function(index, layero){

                                    }
                                });
                            }else {
                                layer.msg(res.msg);
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {

                        },
                    })
                },
                btn2: function(index, layero){
                    //按钮【按钮二】的回调
                    
                    //return false 开启该代码可禁止点击该按钮关闭
                }
            });   
        })

        $("tbody").on("click",".refuse",function () {
            var id = $(this).data('id');
            layer.open({
                title: '通过备注',
                btn:["提交","取消"],
                content: '<form class="layui-form"><div class="layui-inline"><label class="layui-form-label" style="width:66px;">备注：</label><div class="layui-input-inline"><textarea name="pass_desc" class="pass_desc" rows="5" cols="40" placeholder="请输入内容" class="layui-textarea"></textarea></div></div></form>',
                area:["400px","260px"],
                yes: function(index, layero){
                    var remark = $(".pass_desc").val();
                    //按钮【按钮一】的回调
                    $.ajax({
                        url:"{:url('examineAction')}",
                        type:"POST",
                        data:{id:id,type:'refuse',remark:remark},
                        dataType:"json",
                        success:function(res){
                            layer.msg(res['msg']);
                            if (res['code']) {
                                setTimeout("window.location.reload()",2000);
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {

                        },
                    })
                },
                btn2: function(index, layero){
                    //按钮【按钮二】的回调
                    
                    //return false 开启该代码可禁止点击该按钮关闭
                }

            });   
        })







        $('.discount-field').each(function (index, element) {
            var $this = $(element);
            var thisText = $this.text();
            thisText = rtrim(thisText, '0');
            thisText = rtrim(thisText, '\\\\.');
            $this.text(thisText);
        });


        // 操作--改变状态
        $(".change_status").change(function () {
            var dataid = $(this).attr("data-id");
            var channel_id = $(this).attr("data-channel_id");
            var game_id = $(this).attr("data-game_id");
            var status = $(this).val();

            $.ajax({
                type: "post",
                data: {'dataid':dataid,'status':status,'departid':channel_id,'game_id':game_id},
                url: "{:url('changePointRatioStatus')}",
                dataType: "json",
                timeOut: 10,
                success: function (msg) {
                    //console.log(msg);
                    if(msg.code) {
                        layer.msg(msg.msg);
                    }else{
                        layer.alert(msg.msg, {icon: 5});
                        setTimeout("window.location.reload()",1500)
                    }
                },
                error: function () {
                    layer.alert('网络错误，请刷新页面重试！', {icon: 2});
                }
            });
        })

        // 添加新的补点记录
        $('.J_add_list').click(function () {
            window.location.href = "{:url('fillPointAddPage')}";
        });

        /**
         * 实现下载
         */
        $('.download-report').on('click', function(){

            window.location.href = "{:url('fillPointDetailsDown')}";return;
            $.ajax({
                type: "post",
                data: $("#J_search_form").serialize(),
                url: "{:url('fillPointDetailsDown')}",
                dataType: "json",
                timeOut: 10,
                success: function (msg) {
                    if(msg.code) {
                        layer.msg(msg.msg);
                    }else{
                        layer.alert(msg.msg, {icon: 5});
                    }
                },
                error: function () {
                    layer.alert('网络错误，请刷新页面重试！', {icon: 2});
                }
            });
            return false;
        });




















    })



    
    function detailFormatter(index, row) {
            // console.log(row);
            var html = [];

            // var obj = {
            //     0:"月份",
            //     1: "游戏",
            //     2: "渠道",
            //     3: "结算模式",
            //     4:"首充补点（平台币）",
            //     5: "续充补点（平台币）",
            //     6: "现金补点",
            //     7: "现金返利",
            //     8: "充值总额（首充）",
            //     9: "充值总额（续充）",
            //     10: "充值总额补点",
            //     11: "充值总额返利",
            //     12: "生效时间",
            //     13: "状态",
            //     14:"操作",
            //     15: "首充",
            //     16: "续充",
            //     17:"备注"
            // }

            $.each(row, function (key, value) {
                console.log(value);
                if (key == 15 || key == 16 || key == 17) {
                    html.push('<p><b></b> ' + value + '</p>');
                } else {
                    html.push('<p class="hide"><b></b> ' + value + '</p>');
                }


            });
            return html.join('');
    }



    function goBackToDetail() {
        window.location.href="{:url('fillpoint')}"
    }










</script>
{/block}