{extend name="layout/base" /}

{block name="content"}
<body class="page-container-bg-solid page-boxed">
	<!-- BEGIN CONTAINER -->
	<div class="page-container">
		<!-- BEGIN CONTENT -->
		<div class="page-content-wrapper">
			<!-- BEGIN PAGE CONTENT BODY -->
			<div class="page-content" style="min-height: 938px;">
				<!-- div class="container" -->
				<div style="margin-left: 10px;">
					<ul class="page-breadcrumb breadcrumb">
						<li>
							<a href="{:url('Index/index')}" target="_top">Home</a>
							<i class="fa fa-circle"></i>
						</li>
					</ul>

					<!-- BEGIN PAGE CONTENT INNER -->
					<div class="page-content-inner">
						<div class="portlet light portlet-fit ">
							<div class="portlet-title">
								<div class="caption">
									<span class="caption-subject font-green-sharp bold uppercase"><i class="fa fa-edit"></i>游戏推荐</span>
								</div>
							</div>

							<div class="portlet-body">
							    <div class="note note-success">
							        <h4 class="block">新增文章</h4>
							    </div>

							    <form action="{:url('game/articleCreate')}" method="POST" class="form-horizontal">
							        <div class="form-group">
							            <label class="col-md-3 control-label">标题:</label>
							            <div class="col-md-5">
							                <input type="text" name="title" class="form-control" value="" maxlength="32" required>
							            </div>
							        </div>
							        
							        <div class="form-group">
							            <label class="col-md-3 control-label">上架时间:</label>
							            <div class="col-xs-2">
							            	<input type="text" id="J_open_time" class="form-control" value="{:date('Y-m-d H:i:s')}" name="open_time" required>
							            </div>
							        </div>
							        
							        <div class="form-group">
							            <label class="col-md-3 control-label">排序:</label>
							            <div class="col-xs-2">
							            	<input type="text" class="form-control" value="0" name="sort" required>
							            </div>
							        </div>
							        
							        <div class="form-group status2">
							            <label class="col-md-3 control-label">状态:</label>
							            <div class="col-md-5">
							            	<select name="status" class="form-control input-small input-inline select2" style="width: 250px;" required>
												<option value="0">等待上架中</option>
												<option value="1">上架</option>
												<option value="2">下架</option>
											</select>
							            </div>
							        </div>
							        
							        <div class="form-group">
										<label class="col-md-3 control-label" for="title">内容</label>
										<div class="col-md-5">
											<textarea name="content" id="J_content" style="width: 100%;"></textarea>
										</div>
									</div>
							        
							        <div class="form-group">
							            <label class="col-md-3 control-label" for="title">操作:</label>
							            <div class="col-md-5">
							                <input type="submit" class="btn btn-outline btn-circle purple-sharp blockui uppercase" name="Submit" value="提交">
							                <input type="reset" name="Reset" value="返回" class="btn btn-danger btn-circle" data-title="您确定要放弃新增吗？" data-confirmation-by="cancel-edit" data-placement="right" data-btn-ok-label="确定放弃" data-btn-ok-icon="icon-like" data-btn-ok-class="btn-success" data-btn-cancel-label="继续新增" data-btn-cancel-icon="icon-close" data-btn-cancel-class="btn-danger" data-original-title="" title="">
							            </div>
							        </div>
							    </form>
							</div>
					</div>
				</div>
			</div>
		</div>
		</div>
	</div>
{/block}

{block name="detail_css"}
<link href="__STATIC__/metronic4.5.2/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css" />
<link href="__STATIC__/metronic4.5.2/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css" />
{/block} 

{block name="detail_js"}
		<!-- BEGIN PAGE LEVEL PLUGINS -->
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-confirmation/bootstrap-confirmation.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/moment.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-editable/bootstrap-editable/js/bootstrap-editable.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-editable/inputs-ext/address/address.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-editable/inputs-ext/wysihtml5/wysihtml5.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-typeahead/bootstrap3-typeahead.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-suggest/bootstrap-suggest.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/jquery.pulsate.min.js" type="text/javascript"></script>

		<!-- END PAGE LEVEL PLUGINS -->
		<!-- BEGIN THEME GLOBAL SCRIPTS -->
		<script src="__STATIC__/metronic4.5.2/global/scripts/app.js" type="text/javascript"></script>
		<!-- END THEME GLOBAL SCRIPTS -->
		<!-- BEGIN THEME LAYOUT SCRIPTS -->
		<script src="__STATIC__/metronic4.5.2/layouts/layout3/scripts/layout.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/layouts/layout3/scripts/demo.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/layouts/global/scripts/quick-sidebar.min.js" type="text/javascript"></script>

		<link href="__STATIC__/footable/footable.core.css" rel="stylesheet">
		<script type="text/javascript" src="__STATIC__/footable/footable.js"></script>

		<link href="__STATIC__/toastr/build/toastr.min.css" rel="stylesheet">
		<script type="text/javascript" src="__STATIC__/toastr/build/toastr.min.js"></script>
		<script type="text/javascript" src="__STATIC__/laydate5/laydate.js"></script>
		<!-- END THEME LAYOUT SCRIPTS -->

		<script src="__STATIC__/js/admin/function.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/common.js?v={$Think.STATIC_VERSION}" type="text/javascript"></script>
		<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.config.js?v={$Think.STATIC_VERSION}"></script>
		<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.all.min.js"> </script>
		<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/lang/zh-cn/zh-cn.js"></script>
		
		<script>
			$(function(){
				//富文本
				var ue = UE.getEditor('J_content');
				
				$("select").select2();
				
				laydate.render({
			        elem: '#J_open_time',
			        type: 'datetime',
			        format: 'yyyy-MM-dd HH:mm:ss',
			        max: '2099-06-16', //最大日期
			    });
			});
	    </script>
{/block}