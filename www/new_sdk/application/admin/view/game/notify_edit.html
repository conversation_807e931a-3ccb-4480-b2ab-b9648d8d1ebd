{extend name="layout/content" /}
{block name="header"}
<title>编辑公告</title>
<!-- <link href="__STATIC__/js/select2/css/select2.min.css" rel="stylesheet" type="text/css"/> -->
<!-- <link href="__STATIC__/js/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css"/> -->
<link href="__STATIC__/lib/layui_formSelect/formSelects-v4.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    .layui-form-label {
        width: 180px;
    }

    .select2 + .layui-form-select {
        display: none;
    }
    
    img#local_preview {
		width: 100px;
		height: 100px;
    }
    
    .xm-select-parent .xm-form-select dl{
        z-index: 999999;
    }
    .layui-form-item .layui-input-inline {
        width: 210px;
    }

    .layui-form-select dl {
        z-index: 8999999;
    }

	.preview{
		    display: block;
		    float: left;
		    background: #169bd5;
		    color: #fff;
		    padding: 5px 10px;
		    border-radius: 5px;
			border: none;
	}
    .preview_box{
            z-index: 9999;
            display: none;
            position: fixed;
            bottom: 20%;
            left: 35%;
            width: 310px;
            height: 325px;
            border:2px solid #cdcdcd;
            border-radius:5px;
		    background: #fff;
		    color: #000;
	}
    .pre_img {
            margin: 7px 0 0 95px;
            width: 110px;
    }
    .close_icon {
            margin: 0 0 17px 70px;
            width: 14px;
    }
    .pre_title {
            letter-spacing: 3px;
            text-align: center;
            margin-top: 13px;
            font-size: 18px;
    }
    .pre_time {
            text-align: center;
            font-size: 15px;
            margin: 15px 0 30px 0;
    }
    .pre_content {
            margin:0 8px 0 23px ;
            padding: 0 5px 0 0;
            height: 150px;
            width: 270px;
            overflow: auto;
            word-wrap:break-word;
            word-break: break-all;      
    }
    .pre_content p {
        margin-bottom: 20px;
        line-height: 180%;
    } 
    .pre_content::-webkit-scrollbar{
            width: 5px;
            height: 5px;
    }
    .pre_content::-webkit-scrollbar-thumb{
            border-radius: 1em;
            background-color: rgba(50,50,50,.3);
    }
    .pre_content::-webkit-scrollbar-track{
            border-radius: 1em;
            background-color: rgba(50,50,50,.1);
    }


</style>
{/block}
{block name="content"}

<div class="x-body">

    <form class="layui-form" action="{:url('notifyEdit',['id'=>$data.id])}" method="post" enctype="multipart/form-data">

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>通知类型：</label>
            <div class="layui-input-inline">
                <select name="notify_type">
                    <option value="1" {if condition="$data.notify_type eq 1" }selected="selected" {/if}> 公告</option>
                    <!-- <option value="2" {if condition="$data.notify_type eq 2" }selected="selected" {/if}> 广告</option> -->
                    <option value="3" {if condition="$data.notify_type eq 3" }selected="selected" {/if}> 游戏强更</option>
                </select>
            </div>

            <div class="layui-form-mid layui-word-aux msg_notify_type" style="display: none;">
                <span class="x-red">在玩家进入游戏时会强制玩家下载最新版本游戏</span>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>推送游戏：</label>
            <!-- <div class="layui-input-inline">
                <select name="gameid" lay-search>
                    <option value="">所有游戏</option>
                    {volist name="game_list" id="g"}
                    <option value="{$g.id}" {if condition="$data.game_id eq $g.id" }selected="selected" {/if}> {$g.name}</option>
                    {/volist}
                </select>
            </div> -->
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <input type="hidden" id='gameid' name="gameid" value="{$data.game_id}" placeholder="请输入游戏名并选择"  />
                </div>
            </div>
            <!-- <div class="layui-form-mid layui-word-aux"> -->
            <!--     <span class="x-red">不选择时为所有游戏</span> -->
            <!-- </div> -->
        </div>
        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>通知标题：</label>
            <div class="layui-input-inline">
                <input type="text" name="title" lay-verify="required|titleMax" placeholder="请输入通知标题"
                       autocomplete="off" class="layui-input" id="notify_title" value="{$data.title}">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>限制方式：</label>
            <div class="layui-input-inline">
                <select name="deny_type" lay-filter="filter-deny_type">
                    <option value="0" {if condition="$data.deny_type eq 0" }selected="selected" {/if}>黑名单</option>
                    <option value="1" {if condition="$data.deny_type eq 1" }selected="selected" {/if}>白名单</option>
                </select>
            </div>
			<div class="layui-form-mid layui-word-aux" style="margin-top: -10px;">
                <span class="x-red">黑名单：以下限制渠道不接收公告；<br />白名单：仅以下限制渠道接收公告。</span>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>限制类型：</label>
            <div class="layui-input-inline">
                <select name="data_type" lay-filter="filter-deny_type">
                    <option value="1" {if condition="$data.data_type eq 1" }selected="selected" {/if}>渠道</option>
                    <option value="2" {if condition="$data.data_type eq 2" }selected="selected" {/if}>用户</option>
                </select>
            </div>
        </div>


        <div class="layui-form-item handle_deny_type_channel">
            <label for="" class="layui-form-label">限制数据（渠道）：</label>
            <div class="layui-input-inline">
                <select name="denied_channel[]" xm-select="select1" id="channel_id" xm-select-search="" placeholder="请选择渠道账号"></select>
            </div>
        </div>

        <div class="layui-form-item handle_deny_type_username">
            <label for="" class="layui-form-label">限制数据（玩家）：</label>
            <div class="layui-input-inline">
                <div id="select_user_id"></div>
            </div>
        </div>

		<!-- <div class="layui-form-item"  id="notify_status_div" {if condition="$data.status neq '1'" } style="display:none" {/if}> -->
		<!-- 	<label for="" class="layui-form-label">推送时间：</label> -->
        <!--     <div class="layui-input-inline"> -->
        <!--         <input class="layui-input" placeholder="开始时间" name="begin_time" id="begin_time" readonly='readonly' value="{if condition="$data.begin_time" }{:date('Y-m-d H:i', $data.begin_time)}{/if}" autocomplete="off"> -->
        <!--     </div> -->
        <!--     <div class="layui-input-inline" style="width:10px"> -->
        <!--        <span style="line-height: 28px;">_</span> -->
        <!--     </div> -->
        <!--     <div class="layui-input-inline"> -->
        <!--         <input class="layui-input" placeholder="结束时间" name="end_time" id="end_time" readonly='readonly' value="{if condition="$data.end_time" }{:date('Y-m-d H:i', $data.end_time)}{/if}" autocomplete="off"> -->
        <!--     </div> -->
        <!-- </div> -->

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>状态：</label>
            <div class="layui-input-inline">
                <select name="status" lay-filter="changeType-filter" lay-verify="required">
                    <option value="0" {if condition="$data.status eq 0" }selected="selected" {/if}>关闭</option>
                    <option value="1" {if condition="$data.status eq 1" }selected="selected" {/if}>启用</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>公告内容：</label>
            <div class="layui-input-inline" style="width: max-content; z-index:10;">
                <textarea name="content" id="J_content" style="width: max-content;" lay-verify="required|contentMax">{$data.content}</textarea>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">标点符号和相关字符不计入字数总数。</span>
                </div>
            </div>
            <input type="button" class="preview" value="预览">
        </div>
        
		<!--
        <div class="layui-form-item">
            <label for="" class="layui-form-label">上传图片:</label>
            <div class="layui-input-inline" style="width:50%">
            	{if(!empty($data.img_url))}
            	<div id="localImag">
					<img src="{$Think.STATIC_DOMAIN}{$data.img_url}" id="local_preview">
				</div>
				{/if}
                <input name="img" type="file" value="{$data.img_url}">
            </div>
        </div>
		-->
        <div class="layui-form-item">
            <label for="" class="layui-form-label">操作：</label>
            <div class="layui-input-block">
                <!-- type="submit" -->
                <button class="layui-btn" lay-submit lay-filter="formSubmit" id="J_submit_btn">提交</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn layui-btn-primary">返回
                </button>
            </div>
        </div>


    </form>
    <div class="preview_box">
        <img class="pre_img" src="__STATIC__/images/logo-2.png">
        <img class="close_icon" src="__STATIC__/images/icon_top_close.png">
        <div class="pre_title"></div>
        <div class="pre_time"></div>
        <div class="pre_content"></div>
        <div></div>
    </div>

</div>


{/block}

{block name="footer"}

<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.config.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/lang/zh-cn/zh-cn.js"></script>
<!-- <script src="__STATIC__/js/select2/js/select2.full.min.js" type="text/javascript"></script> -->
<script src="__STATIC__/lib/layui/layui.all.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/lib/layui_formSelect/formSelects-v4.min.js"></script>
<script src="__STATIC__/lib/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script>

    var data_type = "{$data.data_type}";
    if(data_type == 0 || data_type == 1) {
        $(".handle_deny_type_channel").show();
        $(".handle_deny_type_username").hide();
    }else{
        $(".handle_deny_type_channel").hide();
        $(".handle_deny_type_username").show();
    }

    $(document).ready(function () {
        var notifyStatus = $('select[name=status]').val();
	//	alert(notifyStatus);
        if (notifyStatus == 1) {
			 $("#notify_status_div").show();
        }
		else{
			 $("#notify_status_div").hide();
		}
    });

    $(document).ready(function(){
	    $(".preview").click(function(){
            $(".pre_title").text($("#notify_title").val());
            var begintime = $("#begin_time").val(); 
            if(begintime == "") {
                var myDate = new Date();            
                var year=myDate.getFullYear();        //获取当前年
                var month=myDate.getMonth()+1;        //获取当前月
                var date=myDate.getDate();            //获取当前日
                var now=year+'-'+getNow(month)+"-"+getNow(date);
                $(".pre_time").text(now);
            }else{
                $(".pre_time").text($("#begin_time").val().substring(0,10));
            }
            var ue = UE.getEditor('J_content');
            var html = ue.getContent();
            $(".pre_content").html(html);
			$(".preview_box").css("display","block");  //设置预览窗口显示
        });
        $(".close_icon").click(function(){
            $(".preview_box").css("display","none"); 
        })
        UE.getEditor('J_content').addListener('focus',function(editor){
		    $(".preview_box").css("display","none");
        });
    });
    $(document).mouseup(function(e){     //点击预览窗口外即可关闭预览
        var _con = $(".preview_box");   // 设置目标区域
        if(!_con.is(e.target) && _con.has(e.target).length === 0){ // Mark 1
            $(".preview_box").css("display","none");
        }
    });

    //判断是否在前面加0
    function getNow(s) {
    return s < 10 ? '0' + s: s;
    }

    $("#gameid").FuzzySearch({
        inputID     : 'gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_submit_btn',
    });

    function stripHtml(html) {
        // Create a new div element
        var temporalDivElement = document.createElement("div");
        // Set the HTML content with the providen
        temporalDivElement.innerHTML = html;
        // Retrieve the text property of the element (cross-browser support)
        return temporalDivElement.textContent || temporalDivElement.innerText || "";
    }

    layui.use(['form', 'layer', 'laydate'], function () {
        // var ue = UE.getEditor('J_content');
        // var html = ue.getContent();

        var denied_channel = "{$denied_channel}";
        var new_denied_channel  =denied_channel.substring(0, denied_channel.length - 1);//去除最后一个逗号

        var denied_channel_arr = new Array();
        denied_channel_arr = new_denied_channel.split(","); //字符分割

        var html, selected, option = '';

        var form = layui.form,
            layer = layui.layer,
            laydate = layui.laydate;

        var starttime = laydate.render({
            elem: '#begin_time',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

        form.on('select(filter-deny_type)', function(data){
            console.log("# data: ", data)
            if (data.value == 2) {
                $(".handle_deny_type_username").show();
                $(".handle_deny_type_channel").hide();
            }else{
                $(".handle_deny_type_channel").show();
                $(".handle_deny_type_username").hide();
            }
        });
        form.on('select(notify_type-filter)', function (data) {
            if (data.value == 3) {
                $(".msg_notify_type").show();
            }else{
                $(".msg_notify_type").hide();
            }
        });
        //自定义验证规则
        form.verify({
            titleMax: function(value){
                if(value.length > 12){
                    return '通知标题-最多12个字符！';
                }
            },
            contentMax: function(value){
                var handleValue = stripHtml(value)
                // handleValue = handleValue.replace(/[^\u4E00-\u9FA5\w]/g, ''); // 去除非中文或英文的
                if(handleValue.length > 2000){
                    return '公告内容-最多2000个字符！';
                }
            }
        });
        form.on('submit(formSubmit)', function(data){
            if (!data.field.gameid){
                layer.alert("请选择一个游戏！", {
                    icon: 5
                })
                return false;
            }
            if (data.field.content){
                var handleValue = stripHtml(data.field.content)
                // handleValue = handleValue.replace(/[^\u4E00-\u9FA5\w]/g, ''); // 去除非中文或英文的
                if(handleValue.length > 2000){
                    layer.alert("公告内容-最多2000个字符！", {
                        icon: 5
                    })
                    return false;
                }
            }
            return true;
        });

        // layedit = layui.layedit;
        // laydate = layui.laydate,
        // laydate.render({
        //     elem: '#datePicker'
        // });

        var option = '';
        var objStr="["

        $.getJSON("{:url('pay/getChannel')}", function (res) {
            // console.log(res);
            // $.each(res, function (index, value) {
            //     if ($.inArray(index, denied_channel_arr) != -1) {
            //         selected = 'selected';
            //         option = "<option " + selected + " value='" + index + "'>" + value + "</option>";
            //     } else {
            //         option = "<option value='" + index + "'>" + value + "</option>";
            //     }

            //     // option = "<option value='" + index + "'>" + value + "</option>";
            //     html += option;
            // })

            $.each(res, function (index, value) {
                option=res;
                
            })

            for(var key in option){
                objStr+="{value:"+key+",name:'"+option[key]+"'},"
            }
            // objStr.substring(0, objStr.length - 1);//去除最后一个逗号
            objStr+="]"

            var formSelects = layui.formSelects;
       
            formSelects.data('select1', 'local', {
                arr: eval(objStr)
            });
            formSelects.value('select1', denied_channel_arr); 

            // $('#channel_id').append(html);
            // $("#channel_id").select2();
        });

        // form.on('select(changeType-filter)', function (data) {
        // 	if (data.value == 1) {
        // 		 $("#notify_status_div").show();
        // 	}
        // 	else{
        // 		 $("#notify_status_div").hide();
        // 	}
        // });
    });

    // 搜索用户
    xmSelectDemo = xmSelect.render({
        el: '#select_user_id',
        autoRow: true,
        toolbar: { show: true },
        filterable: true,
        remoteSearch: true,
        name: 'select_user',
        remoteMethod: function(val, cb, show){
            //这里如果val为空, 则不触发搜索
            if(!val){
                return cb([]);
            }

            $.ajax({
                type: "get",
                data: {'username':val},
                url: "{:url('game/getUserList')}",
                dataType: "json",
                timeOut: 10,
                success: function (result) {
                    if(result.code == 1) {
                        var res = result.data;
                        cb(res)
                    }else{
                        cb([]);
                        layer.alert(result.msg, {icon: 5});
                    }
                },
                error: function () {
                    cb([]);
                    layer.alert('网络错误，请刷新页面重试！', {icon: 2});
                }
            });
        },
    })
    selectDSata = {$userList};
    // console.log(" selectDSata: ", selectDSata)
    if(selectDSata != ""){
        xmSelectDemo.setValue(selectDSata)
    }

</script>
{/block}