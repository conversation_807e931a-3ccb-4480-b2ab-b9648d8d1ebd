{extend name="layout/content" /}{block name="header"}
<title>新增补点</title>
<!-- <link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css"> -->

<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/css/admin/paginator.css?v={$Think.STATIC_VERSION}">
<style>
    /* .select2 {
        display: inline-block;
    }
    .is_display >div{
        display: inline-block;
    } */

    .x-red{
        color: red !important;
    }
    .layui-form-label{
        width: 90px;
    }

    .point-container{
        /* display: none; */
    }

    .show{
        display: block;
    }
    .hide{
        display: none;
    }

    .layui-form-item .layui-inline {
        margin: 0
    }

    .pd10{
        padding: 10px;
    }

    .layui-form-label {
        width: 114px;
    }



</style>
{/block}{block name="content"}
<!-- <div class="toolWrap">
    <div class="pd10">
        <input type="button" class="btn btn-info btn-circle J_add_list "  value="刷新" >
    
    </div>
</div> -->

    <div class="layui-form-item">
        <label class="layui-form-label">当前月份：</label>
        <div class="layui-input-inline">
            <input type="text" value="{php} echo $YearMonth {/php}" class="layui-input" readonly style="border:0"/>
        </div>  
    </div>  






    <!-- <form action="" method="post" target="nm_iframe" id="registSubmit" class="layui-form"> -->
        
    <form action="" method="post" class="layui-form">
           
            <div class="layui-form-item">
                <label class="layui-form-label">选择游戏：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='game_id' name="game_id" value="" />
                    </div>
                </div>
            </div>  

            <div class="layui-form-item">
                <label class="layui-form-label">选择渠道：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='channel_id' name="channel_id" value="" />
                    </div>
                </div>  
            </div>  


            <div class="layui-form-item">
                <label class="layui-form-label">结算类型：</label>
                <div class="layui-input-inline">
                    <select name="type" lay-filter="countType-filter">
                        <option value="1">折扣结算</option>
                        <option value="2">自然类结算</option>
                        <option value="3">首充补点结算</option>
                        <option value="4">新用户补点结算</option>
                    </select>
                </div>  
            </div>










            <!-- 折扣类结算 -->
            <div class="point-container">

                    <div class="layui-form-item" style="margin:0">
                        <label class="layui-form-label"></label>
                        <label class="layui-form-label">平台币充值</label>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">首充补点：</label>
                        <div class="layui-input-inline">
                            <input type="text" lay-verify="required" class="layui-input" name="first_point" oninput="clearNoNum(this)"/>
                        </div>  
                        <div class="layui-form-mid layui-word-aux">%</div>
                    </div>

                    <div class="layui-form-item" style="margin:0">
                        <label class="layui-form-label">续充补点：</label>
                        <div class="layui-input-inline">
                            <input type="text" lay-verify="required" class="layui-input" name="common_point" oninput="clearNoNum(this)"/>
                        </div>  
                        <div class="layui-form-mid layui-word-aux">%</div>
                    </div>


                    <div class="layui-form-item" style="margin:0">
                        <label class="layui-form-label"></label>
                        <label class="layui-form-label">现金充值</label>
                    </div>


                    <div class="layui-form-item">
                        <label class="layui-form-label">充值返利：</label>
                        <div class="layui-input-inline">
                            <input type="text" lay-verify="required" class="layui-input" name="extra_profit_point" oninput="clearNoNum(this)"/>
                        </div>  
                        <div class="layui-form-mid layui-word-aux">%</div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">充值补点：</label>
                        <div class="layui-input-inline">
                            <input type="text" lay-verify="required" class="layui-input" name="extra_point" oninput="clearNoNum(this)"/>
                        </div>  
                        <div class="layui-form-mid layui-word-aux">%</div>
                    </div>

            </div>




         











            <div class="layui-form-item">
                <label class="layui-form-label">生效时间：</label>
                <div class="layui-input-inline">
                    <input type="text" lay-verify="required" class="layui-input" name="datarange" readonly>
                </div>  

                <div class="layui-form-mid layui-word-aux x-red">*精确到秒，不得小于当天0点</div>
            </div>


    
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline">
                    <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="*">提交</button>
                    <button type="button" class="layui-btn layui-btn-primary"  id="goBack">返回</button>
                </div>  
              
            </div>


          


    </form>






  
</div>

{/block}

{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">


$(function() {


    $("#game_id").FuzzySearch({
        inputID     : 'game_id',
        title   	: '请输入游戏名称',
        data        :{:json_encode($gamelist)},
        searchBtn	:'J_submit_btn',
    });


    $("#channel_id").FuzzySearch({
        inputID     : 'channel_id',
        title   	: '请输入渠道名称',
        data        :{:json_encode($departmentlist)},
        searchBtn	:'J_submit_btn',
    });





    var yearAndMonth="{php} echo $YearMonth {/php}";

    var limitMonth=yearAndMonth.substr(yearAndMonth.length-2);
    var thisYear=new Date().getFullYear();
    var lastDay= new Date(thisYear,limitMonth,0).getDate();


        // 获取选择的游戏和渠道
    layui.use(['form','laydate'], function () {
        var form = layui.form,
            laydate = layui.laydate;

        // 生效时间
        laydate.render({
            type:"datetime",
            elem: 'input[name=datarange]', //指定元素
            min: yearAndMonth+'-01',
            max: yearAndMonth+'-'+lastDay
          
        });


 
  



        form.on('select(countType-filter)', function (data) {
            // console.log(data.elem); //得到select原始DOM对象
            // console.log(data.value); //得到被选中的值
            // console.log(data.othis); //得到美化后的DOM对象
    

            if(data.value==1){
              var html='<div class="layui-form-item" style="margin:0">'+
                            '<label class="layui-form-label"></label>'+
                            '<label class="layui-form-label">平台币充值</label>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            '<label class="layui-form-label">首充补点：</label>'+
                            '<div class="layui-input-inline">'+
                                '<input type="text" lay-verify="required" class="layui-input" name="first_point" oninput="clearNoNum(this)"/>'+
                            '</div>'+
                            '<div class="layui-form-mid layui-word-aux">%</div>'+
                        '</div>'+
                        '<div class="layui-form-item" style="margin:0">'+
                            '<label class="layui-form-label">续充补点：</label>'+
                            '<div class="layui-input-inline">'+
                                '<input type="text" lay-verify="required" class="layui-input" name="common_point" oninput="clearNoNum(this)"/>'+
                            '</div>'+
                            '<div class="layui-form-mid layui-word-aux">%</div>'+
                        '</div>'+
                        '<div class="layui-form-item" style="margin:0">'+
                            '<label class="layui-form-label"></label>'+
                            '<label class="layui-form-label">现金充值</label>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            '<label class="layui-form-label">充值返利：</label>'+
                            '<div class="layui-input-inline">'+
                                '<input type="text" lay-verify="required" class="layui-input" name="extra_profit_point" oninput="clearNoNum(this)"/>'+
                            '</div>'+
                            '<div class="layui-form-mid layui-word-aux">%</div>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            '<label class="layui-form-label">充值补点：</label>'+
                            '<div class="layui-input-inline">'+
                                '<input type="text" lay-verify="required" class="layui-input" name="extra_point" oninput="clearNoNum(this)"/>'+
                            '</div>'+
                            '<div class="layui-form-mid layui-word-aux">%</div>'+
                        '</div>'
            }else if(data.value==2){

                var html='<div class="layui-form-item" style="margin:0">'+
                            '<label class="layui-form-label"></label>'+
                            '<label class="layui-form-label">充值金额</label>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            '<label class="layui-form-label" style="width:114px">充值总额返利：</label>'+
                            '<div class="layui-input-inline">'+
                                '<input type="text" oninput="clearNoNum(this)" lay-verify="required" class="layui-input" name="extra_profit_point"/>'+
                            '</div>'+
                            '<div class="layui-form-mid layui-word-aux">%</div>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            '<label class=" layui-form-label" style="width:114px">充值总额补点：</label>'+
                            '<div class="layui-input-inline">'+
                                '<input type="text" oninput="clearNoNum(this)" lay-verify="required" class="layui-input" name="extra_point"/>'+
                            '</div>'+
                            '<div class="layui-form-mid layui-word-aux">%</div>'+
                        '</div>';



            }else if(data.value==3){


                var html='<div class="layui-form-item" style="margin:0">'+
                            '<label class="layui-form-label"></label>'+
                            '<label class="layui-form-label">平台币充值</label>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            
                            '<div class="layui-inline">'+
                                '<label class="layui-form-label">首充补点：</label>'+
                                '<div class="layui-input-inline">'+
                                    '<input type="text" oninput="clearNoNum(this)" lay-verify="required" class="layui-input" name="first_point"/>'+
                                '</div>'+
                                '<div class="layui-form-mid layui-word-aux">%</div>'+
                            '</div>'+

                            '<div class="layui-inline" style="width:346px">'+
                                '<label class="layui-form-label" style="width:90px">选择时间：</label>'+
                                '<div class="layui-input-inline">'+
                                    '<input type="text" lay-verify="required" class="layui-input" name="sc_supply_date" readonly placeholder="时间区间选择" style="width:256px"/>'+
                                '</div>'+
                            '</div>'+

                        '</div>'+
                        '<div class="layui-form-item" style="margin:0">'+
                            // '<div class="layui-inline" style="width:346px">'+
                            //     '<label class="layui-form-label">选择时间：</label>'+
                            //     '<div class="layui-input-inline">'+
                            //         '<input type="text" lay-verify="required" class="layui-input" name="xc_supply_date" readonly placeholder="时间区间选择" style="width:256px"/>'+
                            //     '</div>'+
                            // '</div>'+
                            '<div class="layui-inline">'+
                                '<label class="layui-form-label">续充补点：</label>'+
                                '<div class="layui-input-inline">'+
                                    '<input type="text" oninput="clearNoNum(this)" lay-verify="required" class="layui-input" name="common_point"/>'+
                                '</div>'+
                                '<div class="layui-form-mid layui-word-aux">%</div>'+
                            '</div>'+
                        '</div>'+

                        '<div class="layui-form-item" style="margin:0">'+
                            '<label class="layui-form-label"></label>'+
                            '<label class="layui-form-label">现金充值</label>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            '<label class="layui-form-label">充值返利：</label>'+
                            '<div class="layui-input-inline">'+
                                '<input type="text" oninput="clearNoNum(this)" lay-verify="required" class="layui-input" name="extra_profit_point"/>'+
                            '</div>'+
                            '<div class="layui-form-mid layui-word-aux">%</div>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            '<label class="layui-form-label">充值补点：</label>'+
                            '<div class="layui-input-inline">'+
                                '<input type="text" oninput="clearNoNum(this)" lay-verify="required" class="layui-input" name="extra_point"/>'+
                            '</div>'+
                            '<div class="layui-form-mid layui-word-aux">%</div>'+
                        '</div>'

             


            }else if(data.value==4){
                var html='<div class="layui-form-item" style="margin:0">'+
                            '<label class="layui-form-label"></label>'+
                            '<label class="layui-form-label" style="width:103px">充值总额补点</label>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            '<div class="layui-inline">'+
                                '<label class="layui-form-label">首充补点：</label>'+
                                '<div class="layui-input-inline">'+
                                    '<input type="text" lay-verify="required" class="layui-input" name="first_point" oninput="clearNoNum(this)"/>'+
                                '</div>'+
                                '<div class="layui-form-mid layui-word-aux">%</div>'+
                            '</div>'+
                            '<div class="layui-inline" style="width:346px">'+
                                '<label class="layui-form-label" style="width:90px">选择时间：</label>'+
                                '<div class="layui-input-inline">'+
                                    '<input type="text" lay-verify="required" class="layui-input" name="sc_supply_date" placeholder="时间区间选择" style="width:256px" readonly/>'+
                                '</div>'+
                            '</div>'+
                           
                        '</div>'+
                        '<div class="layui-form-item">'+
                            // '<div class="layui-inline" style="width:346px">'+
                            //     '<label class="layui-form-label">选择时间：</label>'+
                            //     '<div class="layui-input-inline">'+
                            //         '<input type="text" lay-verify="required" class="layui-input" name="xc_supply_date" placeholder="时间区间选择" style="width:256px" readonly/>'+
                            //     '</div>'+
                            // '</div>'+
                            '<div class="layui-inline">'+
                                '<label class="layui-form-label">续充补点：</label>'+
                                '<div class="layui-input-inline">'+
                                    '<input type="text" lay-verify="required" class="layui-input" name="common_point" oninput="clearNoNum(this)"/>'+
                                '</div>'+
                                '<div class="layui-form-mid layui-word-aux">%</div>'+
                            '</div>'+
                        '</div>'+
                        '<div class="layui-form-item" style="margin:0">'+
                            '<label class="layui-form-label"></label>'+
                            '<label class="layui-form-label" style="width:103px">充值总额</label>'+
                        '</div>'+
                        '<div class="layui-form-item">'+
                            // '<div class="layui-inline" style="width:346px">'+
                            //     '<label class="layui-form-label">选择时间：</label>'+
                            //     '<div class="layui-input-inline">'+
                            //         '<input type="text" lay-verify="required" class="layui-input" name="recharge_all_backing_data" placeholder="时间区间选择" style="width:256px"/>'+
                            //     '</div>'+
                            // '</div>'+
                            '<div class="layui-inline">'+
                                '<label class="layui-form-label" style="width:114px">充值总额返利：</label>'+
                                '<div class="layui-input-inline">'+
                                    '<input type="text" oninput="clearNoNum(this)" lay-verify="required" class="layui-input" name="extra_profit_point"/>'+
                                '</div>'+
                                '<div class="layui-form-mid layui-word-aux">%</div>'+
                            '</div>'+
                        '</div>'

               

            }


                

            $(".point-container").html(html)


                laydate.render({
                    type:"datetime",
                    elem: 'input[name=sc_supply_date]', 
                    range: true,
                    min: yearAndMonth+'-01',
                    max: yearAndMonth+'-'+lastDay
                });


                laydate.render({
                    type:"datetime",
                    elem: 'input[name=xc_supply_date]', 
                    range: true,
                    min: yearAndMonth+'-01',
                    max: yearAndMonth+'-'+lastDay
                });


                laydate.render({
                    type:"datetime",
                    elem: 'input[name=recharge_all_backing_data]',
                    range: true,
                    min: yearAndMonth+'-01',
                    max: yearAndMonth+'-'+lastDay
                });











        })





        form.on('submit(*)', function(data){
            //console.log(data.elem) //被执行事件的元素DOM对象，一般为button对象
            //console.log(data.form) //被执行提交的form对象，一般在存在form标签时才会返回
            //console.log(data.field) //当前容器的全部表单字段，名值对形式：{name: value}

            //询问框
            layer.confirm('请再次确认当前提交信息，一经提交无法修改！', {
                btn: ['确定','取消'] //按钮
            }, function(){
                $.ajax({
                    type: "post",
                    url: "{:url('batchAddPointList')}",
                    async: false,
                    data: data.field,
                    dataType: 'json',
                    timeout: 5000,
                    success: function (data) {
                        layer.alert(data['msg']);
                        if (data['code']) {
                            setTimeout(window.location.href = data['url'],2000);
                        }
                    },
                    error: function () {
                        layer.alert('网络错误，请重试');
                    }
                });
            }, function(){});

            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });







        $("#goBack").on("click",function () {
            var url = "{:url('fillpointdetails')}";
            window.location.href=url;
        })








    });
























//     $('.discount-field').each(function (index, element) {
//         var $this = $(element);
//         var thisText = $this.text();
//         thisText = rtrim(thisText, '0');
//         thisText = rtrim(thisText, '\\\\.');
//         $this.text(thisText);
//     });

//     // 显示游戏select 模态框
//     var index=0;
//     $(".selGame_1").on('click',function(){
//         index = $(".selGame_1").index(this)
//         $(".resetSel").click();

//         $('#show_sel_game_qd').modal();
//     })
//     $(".selQd_1").on('click',function(){
//         index = $(".selQd_1").index(this)
//         $(".resetSel").click();

//         $('#show_sel_game_qd').modal();
//     })




//     // 计数，有几组数据
//     var listNum = 1;
//     // 操作--改变状态
//     $(".addList").click(function () {
//         if(listNum > 19 ){
//             layer.alert('一次最多添加20条记录');
//             return;
//         }
//         var addList = $(".divList > div").eq(0).clone(true);
//         $(".divList").append(addList);
//         $(".del_list").eq(listNum).css('display','block');


//         $(".divList > div").eq(listNum).find('input').each(function(){
//             if ($(this).val() != '移除') {
//                 $(this).val('')
//             }
//         })
//         $(".divList > div").eq(listNum).find('textarea').val('');


//         listNum ++;
//         $("input[name='listNum']").val(listNum);
//     });


//     $(".toSubmit").click(function () {
//         var url = $(this).data("href");
//         // 消除背景色
//         $(".divList > div").css('background-color',"");
//         var obj = $('#registSubmit').serialize();

//         $.ajax({
//             type: "post",
//             data: obj,
//             url: "{:url('batchAddPointList')}",
//             dataType: "json",
//             timeOut: 10,
//             success: function (res) {

//                 if (res.code) {
//                     window.location.href = url;

//                     return;
//                 }else{
//                     layer.alert(res.msg, {icon: 5});
//                     var divList = $(".divList > div");
//                     $(".divList > div").eq(res.data.i).css('background-color',"orange");
//                 }
//             },
//             error: function () {
//                 layer.alert('网络错误，请刷新页面重试！', {icon: 2});

//             }
//         });
//     });

//     // 添加新的补点记录
//     $('.J_add_list').click(function () {
//         window.location.reload();
//     });
//     // 移除该记录
//     $(".del_list").click(function(){
//         listNum --;
//         $("input[name='listNum']").val(listNum);

//         $(this).parent().parent().parent().remove();
//     })




})




    function clearNoNum(obj) {

        //如果大于99，则清空
        if(parseFloat(obj.value)>99){
            obj.value = obj.value.replace(/[\d]/g, "");  
        }

        //如果以.开头则清空
        if(obj.value.indexOf(".")==0){
            obj.value = obj.value.replace(/[\.]/g, "");  
        }

        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符   

        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的   
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数   
        if (obj.value.indexOf(".") < 0 && obj.value != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额  
            obj.value = parseFloat(obj.value);
        }
    }





</script>
{/block}
