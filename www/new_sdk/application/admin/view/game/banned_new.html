{extend name="layout/content" /}
{block name="header"}
<title>新增冻结</title>
<link href="__STATIC__/lib/layui_formSelect/formSelects-v4.css" rel="stylesheet" type="text/css">
<link href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}" rel="stylesheet" type="text/css">
<style>
    .layui-form-label {
        width: 180px;
    }

    .select2 + .layui-form-select {
        display: none;
    }
    .layui-form-select dl{
        z-index: 1000;
    }
    .xm-select-parent .xm-form-select dl{
        z-index: 999999;
    }
    .layui-form-item .layui-input-inline {
        width: 210px;
    }
    .layui-form-select dl {
        z-index: 8999999;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">

    <form class="layui-form" action="{:url('bannedCreate')}" method="post" enctype="multipart/form-data">

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>游戏名称：</label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <input type="hidden" id='J_gameid' name="game_id" value="{$Request.get.game_id}" />
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>冻结类型：</label>
            <div class="layui-input-inline">
                <select name="banned_type">
                    <option value="ip">IP</option>
                    <option value="imeil">IMEI</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label"><span style="color: #FF5722;"> * </span>冻结IP/IMEI：</label>
            <div class="layui-input-inline">
                <input type="text" name="banned_device" required="" lay-verify="required" placeholder="请输入要冻结的IP或IMEI" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">备注：</label>
            <div class="layui-input-inline">
                <textarea name="notes" placeholder="输入备注内容" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
<!--            <label for="" class="layui-form-label">操作：</label>-->
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit type="submit" lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn layui-btn-primary">返回
                </button>
            </div>
        </div>


    </form>
</div>

{/block}

{block name="footer"}

<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.config.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/lib/layui_formSelect/formSelects-v4.min.js"></script>
<script>
    $("#J_gameid").FuzzySearch({
        inputID     : 'J_gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_submit_btn',
    });
    
</script>
{/block}