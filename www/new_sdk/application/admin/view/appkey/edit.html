{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>编辑SDK密匙</title>
<style>
    .layui-form-label {
        width: 157px;
    }

    header {
        background: #EFEFEF;
        padding: 10px 20px 10px 10px;
        border-bottom: 1px solid lightgray;
        /* border-top-left-radius: 5px;
        border-top-right-radius: 5px; */
    }

    header h1 {
        text-align: right;
        font-weight: 700;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">


    <form class="layui-form" action="{:url('edit')}" method="POST">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">请选择游戏：</label>
            <!-- <div class="layui-input-inline">
                <select name="gameid" lay-search>
                    <option value="">请选择游戏名称</option>
                    {volist name="game_list" id="g"}
                    <option value="{$g.id}" {if condition="$data.gameid eq $g.id" }selected="selected" {/if}>{$g.name}</option>
                    {/volist}
                </select>
            </div> -->
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <input type="hidden" id='gameid' name="gameid" value="{$data.gameid}" />
                </div>
            </div>

        </div>


        <div class="layui-form-item">
            <label for="" class="layui-form-label">请填写AppKey标示符：</label>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" name="appid" required="" value="{$data.appid}" autocomplete="off">
            </div>

            <div class="layui-form-mid layui-word-aux">
                <span class="x-red">由拼音和数字组成，比如游戏: <br>仙剑奇缘(安卓) = xjqy_az、仙剑奇缘(ios) = xjqy_ios，不能为空</span>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">请填写备注：</label>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="" name="beizhu" value="{$data.beizhu}">
            </div>

            <div class=" layui-form-mid layui-word-aux">
                <span class="x-red">比如游戏仙剑奇缘可以写成：仙剑奇缘</span>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label"></label>
            <div class="layui-input-inline">
                <button type="submit" class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回</button>
            </div>
        </div>

        <input type="hidden" name="id" value="{$data.id}">

    </form>

</div>
<script type="text/javascript" src="__STATIC__/js/jquery_v3.3.1.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $("#gameid").FuzzySearch({
        inputID: 'gameid',
        title: '请输入游戏名称',
        data: {:json_encode($game_list)},
        searchBtn: 'J_submit_btn',
        handlerFun:"copy"
    });

    function copy() {
        $("input[name=beizhu]").val($("#gameid_show").val());
    }
    
</script>
{/block}

{block name="footer"}


{/blcok}