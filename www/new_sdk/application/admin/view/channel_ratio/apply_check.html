{extend name="layout/content" /} {block name="header"}
<title>充值管理</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block} 
{block name="content"}
<style type="text/css">
    .layui-btn+.layui-btn{
        margin-left: 0px;!important;
    }
</style>
<div class="x-body">

        

        <form class="layui-form" method="get" action="{:url('applyCheck')}">

            

            <div style="float: right;">
                <div class="layui-inline">
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_channel_id' name="channel_id" value="{:input('request.channel_id', '')}"/>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <div>
                            <input type="text" class="layui-input" id='orderid' name="orderid" value="{$Request.get.orderid}" placeholder="请输入流水号" />
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline">
                      <select name="recharge_type" lay-filter="recharge_type" value="{:input('request.recharge_type')}">
                            <option value="">选择类型</option>
                            <option value="1" {if condition="input('request.recharge_type') eq 1" }selected="selected" {/if} >申请</option>
                            <option value="2" {if condition="input('request.recharge_type') eq 2" }selected="selected" {/if} >直充</option>
                            <option value="3" {if condition="input('request.recharge_type') eq 3" }selected="selected" {/if} >结算币</option>
                      </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline">
                      <select name="status" lay-filter="recharge_type" value="{:input('request.status')}">
                            <option value="">审核状态</option>
                            <option value="0" {if condition="input('request.status') eq '0'" }selected="selected" {/if} >未审核</option>
                            <option value="1" {if condition="input('request.status') eq 1" }selected="selected" {/if} >审核通过</option>
                            <option value="2" {if condition="input('request.status') eq 2" }selected="selected" {/if} >审核拒绝</option>
                      </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">搜索</button>
                    <a class="layui-btn layui-btn-radius" href="{:url('applyCheck')}">清空</a>
                </div>
                

            </div>


            <div style="clear:both"></div>

            <table class="layui-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>公会账号</th>
                        <th>流水号</th>
                        <th>状态</th>
                        <th>到账金额</th>
                        <th>交易金额</th>
                        <th>交易类型</th>
                        <th>折扣</th>
                        <th style="width: 10%" class="tip">交易凭证 <i class="layui-icon layui-icon-about"></i></th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="list" id="vo"}
                    <tr>
                        <td>{$vo.id}</td>
                        <td>{$vo.channel_name}</td>
                        <td>{$vo.orderid}</td>
                        <td>{$vo.status}</td>
                        <td>{$vo.amount}</td>
                        <td>{$vo.real_amount}</td>
                        <th>
                            {switch name="$vo.recharge_type"} 
                                {case value="2"}
                                    直充
                                {/case} 
                                {case value="1"}
                                    申请
                                {/case}
                                {default /} 
                                   结算币
                            {/switch}
                        </th>
                        <td>{$vo.ratio}%</td>
                        <td>{$vo.out_order_no}</td>
                        <td>
                            {switch name="$vo.recharge_type"} 
                                {case value="2"}
                                    <a title="编辑" href="{:url('examine',['id'=>$vo.id])}" class="layui-btn layui-btn edit">
                                                <i class="layui-icon">&#xe63c;</i>直充记录
                                            </a>
                                {/case} 
                                {case value="1"}
                                   {switch name="$vo.status"} 
                                        {case value="未审核"}
                                            <a title="编辑" href="{:url('examine',['id'=>$vo.id])}" class="layui-btn layui-btn-normal edit">
                                                <i class="layui-icon">&#xe63c;</i>审核
                                            </a>
                                        {/case} 
                                        {default /} 
                                            <a title="编辑" href="{:url('examine',['id'=>$vo.id])}" class="layui-btn layui-btn-warm edit">
                                                <i class="layui-icon">&#xe63c;</i>审核记录
                                            </a>
                                    {/switch}
                                {/case} 
                                {default /} 
                                   <a title="编辑" href="{:url('examine',['id'=>$vo.id])}" class="layui-btn layui-btn edit">
                                                <i class="layui-icon">&#xe63c;</i>结算币记录
                                            </a>
                            {/switch}
                            
                        </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>
            
            <div class="pager-container">
                <span>
                  {$list->total()}条记录
                </span>
                {$page}
                <div class="layui-inline" style="margin-left:10px;width:80px;">
                        <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                </div>
            </div>
                
              

            
        
        </form>



</div>
{/block} 

{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
$("#J_channel_id").FuzzySearch({
     inputID    : 'J_channel_id',
     title      : '请输入公会账号',
     searchBtn  :'J_search_submit',
     data       :{:json_encode($channel_list)},
});
layui.use('layer', function(){ 
  layer = layui.layer; 
  $(document).on('mouseenter', '.layui-icon-about', function(event) {
        layer.tips('交易凭证号：会长提交的相关充值凭证', '.tip', {
          tips: [4, 'balck'] //还可配置颜色
        });
    });
})

</script>
{/block}