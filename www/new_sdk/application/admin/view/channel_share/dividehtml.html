{extend name="layout/content" /} {block name="header"}
<title>公会充值比例管理</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block} 
{block name="content"}
<style type="text/css">
    .layui-btn+.layui-btn{
        margin-left: 0px!important;
    }
    .layui-layer-content{
        text-align: center;
    }
    a:hover{
        text-decoration:underline;
        color: #C20C0C;
        cursor: pointer;
    }
</style>
<div class="x-body">

        

        <form class="layui-form" method="get" action="{:url('dividehtml')}">

            <div style="float: left;">
                <div class="layui-inline">
                    <a href="{:url('adddividehtml')}" class="layui-btn layui-btn-radius"><i class="layui-icon">&#xe654;</i>添加补点比例
                    </a>
                </div>
            </div>
            

            <div style="float: right;">
            
                <div class="layui-inline">
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_gameid' name="game_id" value="{$game_id}" />
                        </div>
                    </div>
                </div>


                <div class="layui-inline">
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_channel_id' name="channel_id" value="{$channel_id}" />
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">搜索</button>
                    <a class="layui-btn layui-btn-radius" href="{:url('dividehtml')}">清空</a>
                </div>
                

            </div>


            <div style="clear:both"></div>

            <table class="layui-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>创建时间</th>
                        <th>游戏集名称</th>
                        <th>公会账号</th>
                        <th>补点比例</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="list" id="vo"}
                    <tr>
                        <td>{$vo.id}</td>
                        <td>{$vo.create_time}</td>
                        <td>{$vo.extra_game_name}</td>
                        <td>{$vo.c_name}</td>
                        <td>{php}
                            $divide = json_decode($vo['divide'],1);
                            $str = '';
                            foreach ($divide as $k => $value) {
                                if ($k == count($divide)-1) {
                                    $str .= '阶梯分成'.($k+1).'：'.$value[0].'元≤月流水 补点:'.$value[2].'</br>';
                                }else{
                                    $str .= '阶梯分成'.($k+1).'：'.$value[0].'元≤月流水<'.$value[1].'元 补点:'.$value[2].'</br>';
                                }
                            }
                        {/php}
                        
                        {$str}</td>
                        <td>
                        <!-- <a href="#" class="layui-btn layui-btn layui-btn-normal checkdivide" style="display: inline-block;" val = '{$vo.divide}'>查看补点</a>   -->
                        <a href='{:url("adddividehtml")}?game_agg_id={$vo.extra_game_id}&&channel_id={$vo.channel_id}&&divide={$vo.divide}' class="layui-btn edit"  id="edit" style="display: inline-block;" val = "{$vo.id}">编辑</a>
                        <!-- <button class="layui-btn layui-btn-danger checkdel"  value="{$vo.id}">删除</button> -->
                        <a href="#" class="layui-btn layui-btn-danger checkdel" value="{$vo.id}">删除</a>
                        <a href="#" class="layui-btn layui-btn-warm check" gameid="{$vo.extra_game_id}" channelid="{$vo.channel_id}">查看修改记录</a>
                    </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>
            
                <div class="pager-container">
                    <span>
                      {$list->total()}条记录
                    </span>
                    {$page}
                    <div class="layui-inline" style="margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                    </div>
                </div>
                
              

            
        
        </form>



</div>
{/block} 

{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

$("#J_gameid").FuzzySearch({
     inputID    : 'J_gameid',
     title      : '请输入游戏名称',
     data       :{:json_encode($game_list)},
     searchBtn  :'J_search_submit',
});
$("#J_channel_id").FuzzySearch({
     inputID    : 'J_channel_id',
     title      : '请输入公会账号',
     searchBtn  :'J_search_submit',
     data       :{:json_encode($channel_list)},
});
layui.use('layer', function(){ 
  layer = layui.layer; 
})
//查看修改记录
$('.check').click(function(event) {
    var gameid     = $(this).attr('gameid');
    var channelid = $(this).attr('channelid');
    $.ajax({
        type: "POST",
        url: "{:url('checkdividelog')}",
        data: {"game_id":gameid,"channel_id":channelid},
        dataType: "json",
        success: function(res) {
            var list ='';
            var reg = new RegExp('"',"g")
            if (res.code == 1) {
                $.each(res.data, function(index, val) {
                    var divide = (val.divide).replace(reg,"-")
                    list += '<tr><td>'+val.id+'</td><td>'+val.create_time+'</td><td>'+val.type+'补点比例</td><td><a class="checkdivi" val="'+val.id+'" data="'+divide+'">查看</a></td></tr><tr id = checkdivi'+val.id+' style="display:none"><td colspan = 5 id = showdivi'+val.id+'></td></tr>'
                });
            }else{
                list = '<tr><td colspan=4>暂无记录</td></tr>';
            }
            layer.open({
              title: '记录',
              type: 1,
              skin: 'layui-layer-rim', //加上边框
              area: ['840px', '480px'], //宽高
              content: '<table class="layui-table"><thead><tr><th>ID</th><th>操作时间</th><th>操作类型</th><th>操作</th></tr></thead><tbody>'+list+'</tbody></table>'
            });
        },
    });
});

//查看补点信息
$('.checkdivide').click(function (e) { 
    var divide = $(this).attr('val');
    var data = JSON.parse(divide);
    var str = '';
    $.each(data, function (indexInArray, valueOfElement) { 
        if (indexInArray == data.length-1) {
            str += '阶梯分成'+(indexInArray+1)+'：'+valueOfElement[0]+'元≤月流水 补点:'+valueOfElement[2]+'</br>';
        }else{
            str += '阶梯分成'+(indexInArray+1)+'：'+valueOfElement[0]+'元≤月流水<'+valueOfElement[1]+'元 补点:'+valueOfElement[2]+'</br>';
        }
    });
    layer.open({
    type: 1,
    skin: 'layui-layer-rim', //加上边框
    area: ['420px', '240px'], //宽高
    content: str
    });
});

//删除

$('.checkdel').click(function (e) { 
    var obj = {};
    var id = $(this).attr('value');
    obj.id = id;
    layer.confirm('操作后会清除该游戏的补点数据,是否确认', {
    btn: ['确认','取消'] //按钮
    }, function(){
        $.ajax({
            type: "POST",
            url: "{:url('deldividepost')}",
            data: obj,
            dataType: "json",
            success: function(res) {
                layer.msg(res.msg)
                if (res.code){
                    setTimeout("window.location.reload()",1500)
                }
            },
        });
    }, function(){
        layer.msg('取消操作')
    });
});
$(document).on('click','.checkdivi', function () {
    //display:table-row
    var id = $(this).attr('val');
    var temp_data = $(this).attr('data');
    var reg = new RegExp("-","g");
    var divide = temp_data.replace(reg,'"');
    var data = JSON.parse(divide);
    var boxs = $(document).find('#checkdivi'+id);
    var str = '';
    $.each(data, function (indexInArray, valueOfElement) { 
        if (indexInArray == data.length-1) {
            str += '阶梯分成'+(indexInArray+1)+'：'+valueOfElement[0]+'元≤月流水 补点:'+valueOfElement[2]+'</br>';
        }else{
            str += '阶梯分成'+(indexInArray+1)+'：'+valueOfElement[0]+'元≤月流水<'+valueOfElement[1]+'元 补点:'+valueOfElement[2]+'</br>';
        }
    });
    $('#showdivi'+id).html(str);
    if (boxs.eq(0).css('display') == 'none') {
        boxs.eq(0).css('display','table-row')

    }else{
        boxs.eq(0).css('display','none')
    }
});
</script>
{/block}