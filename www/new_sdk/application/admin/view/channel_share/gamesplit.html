{extend name="layout/content" /} {block name="header"}
<title>游戏默认分成比例管理</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block} 
{block name="content"}
<style type="text/css">
    .layui-btn+.layui-btn{
        margin-left: 0px;!important;
    }
</style>
<div class="x-body">

        <form class="layui-form" method="get" action="{:url('gamesplit')}">

            <div style="float: right;">
            
                <div class="layui-inline">
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_gameid' name="game_id" value="{$game_id}" />
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">搜索</button>
                    <a class="layui-btn layui-btn-radius" href="{:url('gamesplit')}">清空</a>
                </div>
                

            </div>


            <div style="clear:both"></div>

            <table class="layui-table">
                <thead>
                    <tr>
                        <th>游戏ID</th>
                        <th>游戏名称</th>
                        <th>对接状态</th>
                        <th>区服类型</th>
                        <th style="width: 10%">分成比例%</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="list" id="vo"}
                    <tr>
                        <td>{$vo.id}</td>
                        <td>{$vo.game_name}</td>
                        <td class="td-status">
                            {if($vo.cooperation_status==0)}
								<span class="layui-btn layui-btn-normal layui-btn-mini layui-btn-radius" style="background:#BAD3D0">对接中</span>
                            {elseif($vo.cooperation_status==1)}
								<span class="layui-btn layui-bg-blue layui-btn-normal layui-btn-mini layui-btn-radius" style="background:#BAD3D0">上线</span>
                            {elseif($vo.cooperation_status==2)}
                                <span class="layui-btn  layui-bg-red layui-btn-normal layui-btn-mini layui-btn-radius" style="background:#BAD3D0">白名单限制</span>
                            {elseif($vo.cooperation_status==3)}
                                <span class="layui-btn  layui-bg-red layui-btn-normal layui-btn-mini layui-btn-radius" style="background:#BAD3D0">下架</span>
                            {/if}
                        </td>
                        <td>
							{if($vo.game_kind == 1)}
								专服
							{else}
								混服
							{/if}
						</td>
                        <td class="{$vo['id']}">
                            <input type="text" name="ratio" class="layui-input sid{$vo.id}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" lay-verify="required" style="width: 100%" value="{$vo.channel_split_ratio}" disabled="" >
                        </td>
                        <td>
                        <a href="#" class="layui-btn edit"  id="edit{$vo['id']}" style="display: inline-block;" val = "{$vo.id}">编辑</a>
                        <a href="#" class="layui-btn layui-btn-normal save" id="save{$vo['id']}" val="{$vo.id}" gameid="{$vo.id}" style="display: none">保存</a>
                        <a href="#" class="layui-btn layui-btn-warm check" gameid="{$vo.id}">查看修改记录</a>
                    </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>
            
                <div class="pager-container">
                    <span>
                      {$list->total()}条记录
                    </span>
                    {$page}
                    <div class="layui-inline" style="margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                    </div>
                </div>
                
              

            
        
        </form>



</div>
{/block} 

{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

$("#J_gameid").FuzzySearch({
     inputID    : 'J_gameid',
     title      : '请输入游戏名称',
     data       :{:json_encode($game_list)},
     searchBtn  :'J_search_submit',
});
layui.use('layer', function(){ 
  layer = layui.layer; 
})
$('.save').click(function(event) {
    var id         = $(this).attr('val');
    var self       = this;
    var value      = $('.sid'+id).val();
    var gameid     = $(this).attr('gameid');
    $.ajax({
        type: "POST",
        url: "{:url('editGameSplit')}",
        data: {"value":value,"id":id,"gameid":gameid},
        dataType: "json",
        success: function(res) {
            console.log(res)
            layer.msg(res.msg)
            if (res.code == 1) {
                $(self).css('display', 'none');
                $('#edit'+id).css('display', 'inline-block');
                $('.sid'+id).attr('disabled', true);
            }
        },
    });
});

$('.edit').click(function(event) {
    var id = $(this).attr('val');
    $(this).css('display', 'none');
    $('#save'+id).css('display', 'inline-block');
    $('.sid'+id).attr('disabled', false);
});
$('.check').click(function(event) {
    var gameid     = $(this).attr('gameid');
    $.ajax({
        type: "POST",
        url: "{:url('gameSplitLog')}",
        data: {"gameid":gameid},
        dataType: "json",
        success: function(res) {
            var list ='';
            if (res.code == 1) {
                console.log(res.data)
                $.each(res.data, function(index, val) {
                    list += '<tr><td>'+val.id+'</td><td>'+val.create_time+'</td><td>'+val.type+'分成比例</td><td>'+val.ratio+'%</td></tr>'
                });
            }else{
                list = '<tr><td colspan=4>暂无记录</td></tr>';
            }
            layer.open({
              title: '记录',
              type: 1,
              skin: 'layui-layer-rim', //加上边框
              area: ['840px', '480px'], //宽高
              content: '<table class="layui-table"><thead><tr><th>ID</th><th>操作时间</th><th>操作</th><th>比例</th></tr></thead><tbody>'+list+'</tbody></table>'
            });
        },
    });
});
</script>
{/block}