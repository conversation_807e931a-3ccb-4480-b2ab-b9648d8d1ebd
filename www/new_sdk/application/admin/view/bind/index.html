{{extend name="layout/content" /} {block name="header"}
<title>广告列表</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">

<style>
    .layui-table th,
    .layui-table td {
        text-align: center;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .check_photo + img {
        display: none;
    }

</style>
{/block}

{block name="content"}
<div class="x-body">

    <form class="layui-form" method='get' id="J_search_form">
        <div style="float:left">
            <a class="layui-btn layui-btn-radius" href="{:url('add')}">
                <i class="layui-icon">&#xe654;</i>换绑
            </a>
        </div>
        <div style="float: right;">

            <div class="layui-inline">
                <label>订单号</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name='orderid' value="{$Request.get.orderid}"/>
                </div>
            </div>

            <div class="layui-inline">
                <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
            </div>
        </div>
        <div style="float: right;">
        </div>

        <div style="clear:both"></div>

        <table class="layui-table">
            <thead>
            <tr>
                <th>ID</th>
                <th>订单号</th>
                <th>原推广员</th>
                <th>新推广员</th>
                <th>换绑时间</th>
                <th>操作人</th>
            </tr>
            </thead>

            <tbody id="viewer_wrap">
            {notempty name="list"}
            {volist name="list" id="vo"}
            <tr>
                <td>{$vo.id}</td>
                <td>{$vo.orderid}</td>
                <td>{$vo.old_channel_name}</td>
                <td>{$vo.new_channel_name}</td>
                <td>{$vo.create_time}</td>
                <td>{$vo.admin_name}</td>
            </tr>
            {/volist}
            {/notempty}
            </tbody>
        </table>

        <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page"
                       onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>
    </form>

</div>

{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script type="text/javascript">
    //审核
    $('.toGameEnter').click(function () {
        var id = $(this).attr('data-id');
        layer.open({
            name: id,
            type: 2,
            area: ['450px', '350px'],
            shadeClose: true,
            fixed: false, //不固定
            maxmin: true,
            content: 'addCoupon?id=' + id
        });
    });
</script>
{/block}
