{{extend name="layout/content" /} {block name="header"}
<title>广告列表</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">

<style>
  .layui-table th,
  .layui-table td {
    text-align: center;
  }

  .layui-icon {
    vertical-align: bottom;
  }

  .td-manage button {
    border: 0;
    padding: 5px;
    border-radius: 5px;
    color: #FFFFFF;
  }

  .layui-form-label {
    float: left;
    display: block;
    padding: 9px 15px;
    width: 48px;
    font-weight: 400;
    text-align: right;
  }

  .layui-form-item .layui-input-inline {
    float: none;
    margin-left: 10px;
  }

  .check_photo + img {
    display: none;
  }

</style>
{/block}

{block name="content"}
<div class="x-body">

  <form class="layui-form" id="J_search_form" action="{:url('confirm')}" method="post">
    <input type="hidden" name="orderids" value="{$orderids}">
    <input type="hidden" name="new_channel_id" value="{$channel.id}">
    <input type="hidden" name="old_channel_id" value="{$old_channel_id}">

    <span> 订单换绑成：{$channel.name},当前可换绑订单号：<span style="color: #0bb20c">{$orderids}</span></span>
    <table class="layui-table">
      <thead>
      <tr>
        <th>订单</th>
        <th>新推广员</th>
        <th>状态</th>
      </tr>
      </thead>

      <tbody id="viewer_wrap">
      {notempty name="list"}
      {volist name="list" id="vo"}
      <tr>
        <td>{$vo.orderid}</td>
        <td>{$vo.name}</td>
        <td>{$vo.state}</td>
      </tr>
      {/volist}
      {/notempty}
      </tbody>
    </table>
    <div class="layui-form-item">
      <div class="layui-input-block">
        {if condition="$orderids neq '' "}
        <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">确认</button>
        {/if}
        <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回
        </button>
      </div>
    </div>
  </form>

</div>

{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
{/block}
