{extend name="layout/base" /}

{block name="detail_css"}
<title>渠道游戏列表 添加</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>

.layui-form-item .layui-input-inline {
    float: left;
    width: 290px;
    margin-right: 10px;
}

.layui-form-label {
    width: 140px;
}
.render_part_service,.render_part{
    width: 520px;
    position: relative;
    margin: 40px 0;
}
.render_part_service p,.render_part p{
	    background: #fff;
	    width: 94px;
	    text-align: center;
	    position: absolute;
	    top: -10px;
	    font-size: 14px;
	    left: 20px;
}
</style>
{/block}

{block name="content"}

    <div class="x-body">
        <form method="post" class="layui-form" action="{:url('gameAdd')}">
            <div class="layui-form-item">
                <label for="" class="layui-form-label">请选择游戏：</label>
                <!-- <div class="layui-input-inline">
                    <select name="game_id" id="J_game_id" lay-verify="required" lay-search>
                        <option value="">请选择游戏</option>
                        {notempty name="game_list"} {volist name="game_list" id="gvo"}
                        <option value="{$gvo.id}">{:escape($gvo.name)}</option>
                        {/volist} {/notempty}
                    </select>
                </div> -->
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='J_game_id' name="game_id" value="{:input('game_id')}" />
                    </div>
                </div>

                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>（必填）
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label">请选择渠道：</label>
                <!-- <div class="layui-input-inline">
                    <select name="channel_id" id="J_channel_id" lay-verify="required" lay-search lay-filter="changeChannel">
                        <option value="">请选择渠道</option>
                        {notempty name="channel_list"} {volist name="channel_list" id="cvo"}
                        <option value="{$cvo.id}">{:escape($cvo.name)}</option>
                        {/volist} {/notempty}
                    </select>
                </div> -->
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='J_channel_id' name="channel_id" value="{:input('channel_id')}" />
                    </div>
                </div>

                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span>（必填）
                </div>
            </div>
            <div class="layui-form-item">
                <label for="" class="layui-form-label">服务端配置参数：</label>
                <div class="layui-input-inline"  style="width: 80%">
                    <input type="text" name="param" value="" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    备注：
                </label>
                <div class="layui-input-inline">
                    <input type="text" name="remark" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="render_part"></div>
            <div class="render_part_service"></div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                    <a href="{:url('gameList')}" class="layui-btn layui-btn-primary">返回</a>
                </div>
            </div>

        </form>
    </div>

{/block} {block name="detail_js"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

    $("#J_game_id").FuzzySearch({
        inputID     : 'J_game_id',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_submit_btn',
    });


    $("#J_channel_id").FuzzySearch({
        inputID     : 'J_channel_id',
        title   	: '请输入渠道名称',
        data        :{:json_encode($channel_list)},
        searchBtn	:'J_submit_btn',
        handlerFun  :"handler"
    });


    // layui.use(['form'], function () {
        // var form = layui.form;
        // form.on('select(changeChannel)', function (data) {

        // })

    // })



    function handler(){
        $.ajax({
            	beforeSend: function () {
            		$('.render_part').html('');
            		$('.render_part_service').html('');
            	},
                type: "post",
                url: "{:url('ajaxGetChannelParamField')}",
                async: false,
                data: {
                    'channel_id': $("#J_channel_id").val()
                },
                dataType: 'json',
                timeout: 5000,
                success: function (data) {
                    if (data['code'] == 1) {

                        if (data.data['field_client'] != '' && data.data['field_client'] !=null) {

                            var strAry = getStr(data.data.field_client, ';');
                            var renderPart = document.getElementsByClassName("render_part")[0];

							$(".render_part").css({
		                    "border": "1px solid #cccccc",
		                    "padding": "30px 0px 16px"
	                            });

                            renderPart.innerHTML = "<p>客户端端参数: </p>";
                            for (var i = 0; i < strAry.length; i++) {

                                var obj_div = document.createElement("div");
                                obj_div.className = "layui-form-item";
                                obj_div.innerHTML =
                                    '<label class="layui-form-label"><span class="x-red"></span>' +
                                    strAry[i] +
                                    '</label><div class="layui-input-inline"><input type="text" name="param_client[]" autocomplete="off" class="layui-input"></div>'

                                renderPart.appendChild(obj_div)
                            }
                        }


                        if (data.data['param_field'] != '' && data.data['param_field'] !=null) {

                            var strAry = getStr(data.data.param_field, ';');
                            var renderPart = document.getElementsByClassName("render_part_service")[0];

							$(".render_part_service").css({
		                    "border": "1px solid #cccccc",
		                    "padding": "30px 0px 16px"
	                            });
                            renderPart.innerHTML = "<p>服务端参数: </p>";
                            for (var i = 0; i < strAry.length; i++) {

                                var obj_div = document.createElement("div");
                                obj_div.className = "layui-form-item";
                                obj_div.innerHTML =
                                    '<label class="layui-form-label"><span class="x-red"></span>' +
                                    strAry[i] +
                                    '</label><div class="layui-input-inline"><input type="text" name="param[]" autocomplete="off" class="layui-input"></div>'

                                renderPart.appendChild(obj_div)
                            }
                        }
                    }
                    else {
                        layer.alert(data['msg']);
                    }
                },
                error: function () {
                    layer.alert('网络错误，请重试');
                },
                //请求完成之后调用
                complete: function (XMLHttpRequest, textStatus) {

                }
            });
    }


    //用于将请求返回的字符串分隔成数组
    function getStr(string, str) {
        var ary = [];
        var result = string.split(str);
        for (var i = 0; i < result.length; i++) {
            ary.push(result[i])
        }
        return ary
    }


</script>
{/block}
