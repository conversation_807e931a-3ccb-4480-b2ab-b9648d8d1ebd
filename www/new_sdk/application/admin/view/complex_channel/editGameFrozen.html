{extend name="layout/content" /}
{block name="header"}
<title>配置游戏冻结</title>
{/block}
{block name="content"}
<body>
<div class="x-body">
		<form method="post" class="layui-form" action="{:url('editGameFrozen')}">
			<!--
			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red"></span>游戏名称：
				</label>
				<div class="layui-input-inline">
					<{$vo.game_name}>
				</div>
			</div>
			-->
			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red"></span>游戏名称：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="" autocomplete="off" class="layui-input" value="{$vo.game_name}" disabled>
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>预付状态：
				</label>
				<div class="layui-input-inline">
					<select class="form-control" name="status">
						<option value="1" {if condition="$vo['status'] eq 1"} selected="selected" {/if}>开启</option>
						<option value="0" {if condition="$vo['status'] eq 0"} selected="selected" {/if}>关闭</option>
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>分成配置:
				</label>
				<div class="layui-input-inline">
					<input type="text" name="divide_point" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$vo.divide_point}">
					<span class="label label-warning">祈盟网络方分成:0-50,可存在小数点</span>
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>禁止新增：
				</label>
				<div class="layui-input-inline">
					<select class="form-control" name="depf_register">
						<option value="0" {if condition="$vo['depf_register'] eq 0"} selected="selected" {/if}>否</option>
						<option value="1" {if condition="$vo['depf_register'] eq 1"} selected="selected" {/if}>是</option>
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>禁止登录：
				</label>
				<div class="layui-input-inline">
					<select class="form-control" name="depf_login">
						<option value="0" {if condition="$vo['depf_login'] eq 0"} selected="selected" {/if}>否</option>
						<option value="1" {if condition="$vo['depf_login'] eq 1"} selected="selected" {/if}>是</option>
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>禁止充值：
				</label>
				<div class="layui-input-inline">
					<select class="form-control" name="depf_consume">
						<option value="0" {if condition="$vo['depf_consume'] eq 0"} selected="selected" {/if}>否</option>
						<option value="1" {if condition="$vo['depf_consume'] eq 1"} selected="selected" {/if}>是</option>
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<div class="layui-input-block">
					<input type="hidden" name="id" value="{$vo.id}">
					<button class="layui-btn" lay-submit lay-filter="formDemo">确定修改</button>
					<a href="{:url('listGameFrozen', ['channel_id' => $vo['channel_id']])}" class="layui-btn layui-btn-primary">返回</a>
				</div>
			</div>
		</form>
	</div>
</body>
{/block}


{block name="footer"}
{/block}
