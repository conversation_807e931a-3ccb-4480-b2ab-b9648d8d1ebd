{extend name="layout/content" /}
{block name="header"}
<title>充值审核</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block}
{block name="content"}
<body>
<div class="x-body">
		<form method="post" class="layui-form" action="{:url('doAudit')}">
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">
					<span class="x-red">*</span>公会账号：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="channel_name" autocomplete="off" class="layui-input" value="{$list.channel_name}" disabled>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">流水号：</label>
				<div class="layui-input-inline">
					<input type="text" name="orderid" autocomplete="off" class="layui-input" value="{$list.orderid}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">到账金额：</label>
				<div class="layui-input-inline">
					<input type="text" name="amount"  autocomplete="off" class="layui-input" value="{$list.amount}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">支付金额：</label>
				<div class="layui-input-inline">
					<input type="text" name="real_amount"  autocomplete="off" class="layui-input" value="{$list.real_amount}" disabled="">
				</div>
			</div>

<!--			<div class="layui-form-item">-->
<!--				<label class="layui-form-label" style="width:160px;">折扣：</label>-->
<!--				<div class="layui-input-inline">-->
<!--					<input type="text" name="ratio"  autocomplete="off" class="layui-input" value="{$list.ratio}" disabled="">-->
<!--				</div>-->
<!--			</div>-->

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">交易凭证：</label>
				<div class="layui-input-inline">
					<input type="text" name="out_order_no"  autocomplete="off" class="layui-input" value="{$list.out_order_no}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">申请时间：</label>
				<div class="layui-input-inline">
					<input type="text" name="out_order_no"  autocomplete="off" class="layui-input" value="{$list.create_time}" disabled="">
				</div>
			</div>
			{switch name="$list.recharge_type"} 
	            {case value="2"}
					<div class="layui-form-item" style="margin-top: 20px;">
						<div class="layui-input-block">
							<a href="{:url('applyCheck')}" type="button" class="layui-btn layui-btn-primary">返回</a>
						</div>
					</div>
	            {/case} 
	            {case value="1"}
	                {switch name="$list.status"} 
					    {case value="0"}
					    	<div class="layui-form-item">
								<label class="layui-form-label" style="width:160px;">审核结果：</label>
								<div class="layui-input-inline">
									<input type="radio" name="status" value="1" title="通过" checked>
									<input type="radio" name="status" value="2" title="驳回">
								</div>
							</div>

							<div class="layui-form-item">
								<label class="layui-form-label" style="width:160px;">审核备注：</label>
								<div class="layui-input-inline">
									<textarea text="请输入内容" name="check_remark" class="layui-textarea">{$list.check_remark}</textarea>
								</div>
							</div>
							<input type="hidden" name="id" value="{$list.id}">
							<div class="layui-form-item">
								<div class="layui-input-block">
									<button class="layui-btn" lay-submit lay-filter="formDemo">提交</button>
									<a href="{:url('applyCheck')}" type="button" class="layui-btn layui-btn-primary">返回</a>
								</div>
							</div>
					    {/case} 
						{default /} 
							<div class="layui-form-item" style="margin-top: 20px;">
								<div class="layui-form-item">
									<label class="layui-form-label" style="width:160px;">审核备注：</label>
									<div class="layui-input-inline">
										<textarea text="请输入内容" name="check_remark" class="layui-textarea" placeholder="{$list.check_remark}"></textarea>
									</div>
								</div>
								<div class="layui-input-block">
									{switch name="$list.status"} 
									{case value="1"}<button type="button" class="layui-btn layui-btn-disabled">审核通过</button>{/case} 
									{case value="2"}<button type="button" class="layui-btn layui-btn-disabled">审核驳回</button>{/case} 
									{default /}
									{/switch}
									<a href="{:url('applyCheck')}" type="button" class="layui-btn layui-btn-primary">返回</a>
								</div>
							</div>
					{/switch}
	            {/case}
	            {default /} 
        			<div class="layui-form-item" style="margin-top: 20px;">
						<div class="layui-input-block">
							<a href="{:url('applyCheck')}" type="button" class="layui-btn layui-btn-primary">返回</a>
						</div>
					</div>
					
	        {/switch}

			
		</form>
	</div>
</body>
{/block}

{block name="footer"}

<script type="text/javascript" src="__STATIC__/js/jquery_v3.3.1.js"></script>
<script type="text/javascript" src="__STATIC__/lib/layui/layui.js?v={$Think.STATIC_VERSION}" charset="utf-8"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">

    layui.use(['form', 'layer'], function () {
        var form = layui.form,
            layer = layui.layer;


		form.on('select(login-ver-filter)', function (data) {
		
			//开启
			if(data.value==1){
				$("input[name=mobile]").attr("lay-verify","required");
				$("input[name=mobile]").parent().siblings().children("span").text("*");
			}else{
				//关闭
				$("input[name=mobile]").removeAttr("lay-verify")
				$("input[name=mobile]").parent().siblings().children("span").text("");
			}
		})


        form.on('submit(*)', function (data) {
            console.log(data.elem) //被执行事件的元素DOM对象，一般为button对象
            console.log(data.form) //被执行提交的form对象，一般在存在form标签时才会返回
            console.log(data.field) //当前容器的全部表单字段，名值对形式：{name: value}

        //    return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });





		







    })

</script>
{/block}