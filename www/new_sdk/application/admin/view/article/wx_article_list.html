{extend name="layout/content" /}
{block name="header"}
<title>公众号文章管理</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">
<!--<script type="text/javascript" src="__STATIC__/js/admin/html5.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/admin/respond.min.js" charset="utf-8"></script>-->
<style>

    .clearfix:before,
    .clearfix:after {
        content: " ";
        display: table;
    }

    .clearfix:after {
        clear: both;
    }

    .layui-form-label{
        margin: 0;
    }

    .check_photo+img {
        display: none;
    }
</style>

{/block} {block name="content"}
<body>
<div class="x-body">

    


<form class="layui-form clearfix">

    <div class="layui-inline">
        <a href="{:url('wxArticleAdd')}" class="layui-btn">添加</a>
    </div>


    <div style="float: right;">


        <div class="layui-inline">
            <label class="layui-form-label">标题：</label>
            <div class="layui-input-inline">
                <input class="layui-input" placeholder="请输入标题" id="title" name="title" value="{:input('title')}">
            </div>
        </div>

        <div class="layui-inline">
            <label class="">创建时间：</label>
            <div class="layui-input-inline">
                <input class="layui-input" placeholder="开始时间" name="start_time" id="cr_start" readonly='readonly' value="{$Request.get.start_time}" autocomplete="off">
            </div>
            <span>-</span>
            <div class="layui-input-inline">
                <input class="layui-input" placeholder="结束时间" name="end_time" id="cr_end" readonly='readonly' value="{$Request.get.end_time}" autocomplete="off">
            </div>
        </div>
       


        <div class="layui-inline">
            <button class="layui-btn layui-btn-radius" lay-submit>查询</button>
        </div>
        

    </div>

        <table class="layui-table">
                <thead>
                    <tr>
                        <th>文章名</th>
                        <th>文章链接</th>
                        <th>发布人</th>
                        <th>文章缩略图</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
            
                <tbody id="viewer_wrap">
                {notempty name="article"} {volist name="article" id="vo"}
                <tr>
                    <td>{$vo.title}</td>
                    <td>
                        {notempty name="$vo.short_link"}
                            {$wxlink}{$vo.short_link}
                        {/notempty}
                    </td>
                    <td>
                        {$vo.releaser}
                    </td>

                    <td>
                        {if(!empty($vo.image))}
                        <a href="javascript:;" class="check_photo">查看图片</a>
                        <img data-original="{$Think.STATIC_DOMAIN}{$vo.image}" src="{$Think.STATIC_DOMAIN}{$vo.image}">
                        {/if}
                    </td>

                    <td>{:isset($vo.create_time)?date('Y-m-d H:i:s',$vo.create_time) : ""}</td>

                    <td class="td-manage">
                        {notempty name="$vo.short_link"}
                            <a title="复制链接" class="layui-btn layui-btn-success copyUrl" data-clipboard-text="{$wxlink}{$vo.short_link}" ><i class="layui-icon">&#xe63c;</i>复制链接</a>
                        {/notempty}

                        <a title="编辑" href="{:url('wxArticleEdit',['id'=>$vo.id])}" class="layui-btn layui-btn-normal edit">
                            <i class="layui-icon">&#xe63c;</i>编辑
                        </a>
                        <a href="javascript:;" onclick="isSureDel('<?php echo $vo['id'] ?>')" class="layui-btn  layui-btn-danger">
                           <i class="layui-icon">&#xe640;</i>删除
                        </a>
                    </td>
                </tr>
                {/volist} {/notempty}
                </tbody>
            </table>
            
            <div class="pager-container">
                <span>
                    {$article->total()}条记录
                </span>
                {$page}

                <div class="layui-inline" style="margin-left:10px;width:80px;">
                    <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                </div>
            </div>
        
    </form>





</div>
</body>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/clipboard.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script>
    var viewer = new Viewer(document.getElementById('viewer_wrap'), {
        url: 'data-original'
    });

        var lastPage=$(".pagination").find("li").eq(-2).children("").text();
        $("input[name=page]").on("input",function () {
            var jupmToNum=parseFloat(this.value);
            var lastPageNum=parseFloat(lastPage);
            if(jupmToNum>lastPageNum){
                this.value=lastPageNum;
            }
        })


    var clipboard = new ClipboardJS('.copyUrl');
    clipboard.on('success', function(e) {
        layer.msg('复制成功');
        e.clearSelection();
    });
    clipboard.on('error', function(e) {
        layer.msg('抱歉，您的浏览器版本过低，复制失败', {icon: 5})
    });

    layui.use(['form', 'laydate'], function () {
        var laydate = layui.laydate;
        var form = layui.form;


        var cr_starttime = laydate.render({
            elem: '#cr_start',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                cr_endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var cr_endtime = laydate.render({
            elem: '#cr_end',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                cr_starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

    })

    $(".check_photo").on("click", function () {
        $(this).siblings().click()
    })

    function isSureDel(id) {
        $.ajax({
            type: "POST",
            timeOut: 10000,
            url: "{:url('wxArticleDel')}",
            data: {
                "id": id,
                "check": 'isRelation'
            },
            async: false,
            success: function (res) {
                layer.confirm(res.msg, {
                    btn: ['确认','取消'] //按钮
                }, function(){
                    delArticle(id,'sureDel');
                });
            },
            error: function () {
                layer.msg('网络错误，请刷新页面重试');
            }
        })
    }

    function delArticle(id,check) {
        $.ajax({
            type: "POST",
            timeOut: 10000,
            url: "{:url('wxArticleDel')}",
            data: {
                "id": id,
                "check": check
            },
            async: false,
            success: function (res) {
                layer.msg(res.msg);
                if (res.code){
                    setTimeout("window.location.reload()",1500)
                }
            },
            error: function () {
                layer.msg('网络错误，请刷新页面重试');
            }
        })
    }



</script>

{/block}