{extend name="layout/content"/} {block name="header"}
<title>渠道角色管理</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" method="GET" action="{:url('roleList')}">
        <div>
            <div class="layui-inline">
                <label class="layui-form-label">游戏：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='game_id' name="game_id" value="{$Request.get.game_id}" />
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">渠道：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='complex_id' name="complex_id" value="{$Request.get.complex_id}" />
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">区服名：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id="server_name" name="server_name" value="{$Request.get.server_name}" />
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">角色名：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="角色名" id="role_name" name="role_name" value="{$Request.get.role_name}">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">账号：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="账号" id="mg_username" name="mg_username" value="{$Request.get.mg_username}">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">时间：</label>
                <input class="layui-input" placeholder="开始时间" name="start_time" id="start_time" value="{:input('request.start_time')}" readonly='readonly' style="width:130px;display:inline-block">
                <span>-</span>
                <input class="layui-input" placeholder="结束时间" name="end_time" id="end_time" value="{:input('request.end_time')}" readonly='readonly' style="width:130px;display:inline-block">
            </div>

            <div class="layui-inline">
                <button class="layui-btn" type="submit" id="J_submit_btn">查询</button>
                <button class="layui-btn download" type="submit">下载报表</button>
            </div>
        </div>

        <table class="layui-table">
            <thead>
            <tr>
                <th>ID</th>
                <th>角色ID</th>
                <th>角色名</th>
                <th>角色等级</th>
                <th>帮会ID/帮会名</th>
                <th>区服ID/区服名</th>
                <th>账号</th>
                <th>渠道名</th>
                <th>游戏名</th>
                <th>添加时间</th>
                <th>更新时间</th>
            </tr>
            </thead>

            <tbody>
            {empty name="list"}
            <tr>
                <td colspan="20" style="text-align: center;">暂无数据</td>
            </tr>
            {else/} {volist name="list" id="vo"}
            <tr>
                <td>{$vo.id}</td>
                <td>{$vo.role_id}</td>
                <td>{$vo.role_name}</td>
                <td>{$vo.role_level}</td>
                <td>{$vo.role_party_id} / {$vo.role_party_name}</td>
                <td>{$vo.server_id} / {$vo.server_name}</td>
                <td>{$vo.mg_username}</td>
                <td>{$vo.complex_name}</td>
                <td>{$gameByIdList[$vo.game_id]}</td>
                <td>{$vo.create_time}</td>
                <td>{$vo.update_time}</td>
            </tr>
            {/volist} {/empty}
            </tbody>
        </table>

        <div class="pager-container">
            <span>
                {$total}条记录
            </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>
    </form>
</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $("#game_id").FuzzySearch({
        inputID     : 'game_id',
        title   	: '请输入游戏名称',
        data        :{:json_encode($gameList)},
    searchBtn	:'J_submit_btn',
    });

    $("#complex_id").FuzzySearch({
        inputID: 'complex_id',
        title: '请输入渠道名称',
        data: {:json_encode($complexList)},
    searchBtn: 'J_submit_btn',
    });

    $("#server_name").FuzzySearch({
        inputID     : 'server_name',
        title   	: '请输入区服名',
        data        :{:json_encode($serverList)},
    searchBtn	:'J_submit_btn',
    });

    layui.use(['form', 'laydate'], function () {
        var laydate = layui.laydate;

        var starttime = laydate.render({
            elem: '#start_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });
    })


    // 下载
    $('.download').on('click', function (event) {

        var start_time = $('#start_time').val();
        var end_time = $('#end_time').val();
        /*
        if (!end_time || !start_time) {
            layer.msg('请选择开始时间和结束时间');
            return;
        }*/
        /*
        if ((CompareDate(start_time, end_time)) > 7) {
            layer.msg('请选择7天以内的时间范围');
            return;
        }*/

        var url = $(this).data('url');
        var urlData = '';
        if (url == 1) {
            urlData = "{:url('download')}";
        } else if (url == 2) {
            urlData = "{:url('registDowmExcel')}";
        } else {
            layer.msg('参数错误！');
            return;
        }

        $.ajax({
            type: "post",
            url: urlData,
            async: false,
            data: $('.layui-form').serialize(),
            dataType: 'json',
            timeout: 5000,
            success: function (data) {

                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    });
</script>
{/block}
