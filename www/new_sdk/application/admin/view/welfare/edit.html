{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>添加礼包</title>
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('edit',['id'=>$data.id])}" method="post">

        <div class="layui-form-item">
            <label class="layui-form-label"> <span class="x-red">*</span>所属游戏：</label>
            <div class="layui-input-inline" style="width: 30%">
                <input type="text" id="name" name="" required="" disabled='disabled' lay-verify="required" value="{$game_name}" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>礼包名称：
            </label>
            <div class="layui-input-inline">
                <input type="text" id="name" name="name" required="" lay-verify="required" value="{$data['name']}" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">
                <span class="x-red">*</span>发放类型：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="grant_type_id" id="grant_type_id" class="layui-form-select"
                            lay-filter="selctOnchange">
                        <option value="">请选择类型</option>
                        {foreach name="grant_type_ids" item="vo" key='k'}
                        <option value="{$k}"  {if condition="$data.grant_type_id eq $k" }selected="selected"  {/if} >{$vo}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item frequency"  {if condition="$data.grant_type_id eq 3 or $data.grant_type_id eq 8 or $data.grant_type_id eq 9" } {else/} style="display: none" {/if}>
            <label class="layui-form-label">
                <span class="x-red">*</span>发放次数：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="frequency" id="frequency">
                        <option value="1" {if condition="$data.frequency eq 1" }selected="selected"  {/if}>一次</option>
                        <option value="2" {if condition="$data.frequency eq 2" }selected="selected"  {/if}>多次</option>
                    </select>
                </div>
            </div>
            </br> </br>
        </div>

        <div class="layui-form-item first-type" {if condition="$data.grant_type_id eq 7 "} {else/} style="display: none" {/if}>
            <label class="layui-form-label">
                <span class="x-red">*</span>首充判断规则：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="first_type" id="first_type" lay-verify="required">
                        <option value="1" {if condition="$data.first_type eq 1" }selected="selected"  {/if}>固定金额</option>
                        <option value="2" {if condition="$data.first_type eq 2" }selected="selected"  {/if}>大于某金额</option>
                    </select>
                </div>
            </div>
            </br> </br>
        </div>
        <div class="layui-form-item month-type" {if condition=" $data.grant_type_id eq 8 or $data.grant_type_id eq 9" } {else/} style="display: none" {/if}>
            <label class="layui-form-label">
                <span class="x-red">*</span>月卡周卡发放方式：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="month_type" id="month_type" lay-verify="required">
                        <option value="1" {if condition="$data.month_type eq 1" }selected="selected"  {/if}>当天</option>
                        <option value="2" {if condition="$data.month_type eq 2" }selected="selected"  {/if}>隔天</option>
                    </select>
                </div>
            </div>
            </br> </br>
        </div>
        <input type="hidden" name="grant_pattern" value="1">
<!--        <div class="layui-form-item">-->
<!--            <label class="layui-form-label">-->
<!--                <span class="x-red">*</span>是否严格按照条件发放：-->
<!--            </label>-->
<!--            <div class="layui-input-inline FuzzySearch_Container">-->
<!--                <div>-->
<!--                    <select lay-search name="grant_pattern" id="grant_pattern">-->
<!--                        <option value="2" {if condition="$data.grant_pattern eq 1" }selected="selected"  {/if} >是</option>-->
<!--                        <option value="1" {if condition="$data.grant_pattern eq 2" }selected="selected"  {/if} >否</option>-->
<!--                    </select>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
        <div class="layui-form-item">
            <label class="layui-form-label">
                <span class="x-red">*</span>是否需要审核：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="is_examine" id="is_examine">
                        <option value="1" {if condition="$data.is_examine eq 1" }selected="selected"  {/if} >不需要</option>
                        <option value="2" {if condition="$data.is_examine eq 2" }selected="selected"  {/if} >需要</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">
                <span class="x-red">*</span>cps是否显示：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="is_show" id="is_show" lay-verify="required">
                        <option value="1" {if condition="$data.is_show eq 1" }selected="selected"  {/if}>显示</option>
                        <option value="2" {if condition="$data.is_show eq 2" }selected="selected"  {/if}>不显示</option>
                    </select>
                </div>

            </div>  </br>
            <span>自动发放的可配置不显示</span>
        </div>



        <div class="layui-form-item">
            <label class="layui-form-label">
                <span class="x-red">*</span>福利礼包类型(领取条件)：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="welfare_gift_type_id" id="typeid">

                        {foreach name="welfare_gift_type" item="vo" key='k'}
                        <option value="{$vo.id}"  {if condition="$data.welfare_gift_type_id eq $vo.id" }selected="selected"  {/if} > {$vo.name}（使用条件：{if condition="$vo.type_id eq 1"}

                        {if condition="$vo.recharge_type eq 1"}最低充值金额{$vo.recharge_amount}
                        {else/}区间金额：{$vo.recharge_amount}-{$vo.max_recharge_amount}{/if}
                        时间段:
                        {if condition="$vo.time_type eq 1"}
                        全部
                        {elseif condition="$vo.time_type eq 2"}
                        日
                        {elseif condition="$vo.time_type eq 3"}
                        周
                        {elseif condition="$vo.time_type eq 4"}
                        月
                        {elseif condition="$vo.time_type eq 5"}
                        季
                        {elseif condition="$vo.time_type eq 6"}
                        年
                        {else/}
                        自定义
                        {/if}
                        {elseif condition="$vo.type_id eq 2"} 创角 {else/}
                        最低等级{$vo.role_level}{/if} ）
                        </option>
                        {/foreach}
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>说明内容：
            </label>
            <div class="layui-input-inline" style="width: 50%;">
                <textarea name="content" rows="5" class="layui-textarea" required="" lay-verify="required">{$data['content']}</textarea>

            </div>
        </div>

        <div class="layui-form-item" id="details_p">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>礼包内容：
            </label>
            <button type="button" class="layui-btn" onclick="add_div()" id="add_link">+</button>
            </br>
            </br>
            </br>
            <div id="details">
            </div>
            {foreach $resources_data as $k=>$v }
            <div class="form-group" id="details{$k}">
                <div class="layui-inline" style="margin-bottom: 20px;"><label class="layui-form-label">福利资源</label>
                   <div class="layui-input-inline FuzzySearch_Container" >
                        <select id="GJliebiao" name="welfare_resources_id[]" lay-verify="GJliebiao"  lay-search="true">
                            {foreach $resources as $kk=>$vv}
                                <option value="{$vv['id']}" {if condition="$vv['id'] eq $v.welfare_resources_id" }selected="selected"  {/if}>{$vv['name']}</option>
                            {/foreach}
                           </select>
                   </div>
                </div>

                <div class="layui-inline" style="margin-bottom: 20px;"><label class="layui-form-label">发放数量</label>
                    <div class="layui-input-inline">
                        <input type="text" name="number[]" placeholder="发放数量" class="layui-input awardIntegral" value="{$v['number']}">
                    </div>
                    {if condition="$k gt 0"}
                    <a class="layui-btn" onclick="del_div(this)">-</a>
                    {/if}
                </div>
                </br>
            </div>
           {/foreach}

        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回
                </button>
            </div>
        </div>

    </form>
</div>

{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/lib/xm-select.js"></script>
<script>
    layui.use(['form', 'layer', 'upload', 'jquery'], function () {
        var form = layui.form,
            layer = layui.layer,
            upload = layui.upload;
        form.on('select(selctOnchange)', function (data) {
            console.log(data.value); //得到被选中的值
            if (data.value == 3 || data.value == 8  || data.value == 9) {
                $('.frequency').show()
            } else {
                $('.frequency').hide()
            }
        });
    })


    function validate(str) {

        if(!str){
            return;
        }

        $.ajax({
            type: "post",
            url: '/welfare/getWelfareResources',
            async: false,
            data: {"game":str},
            dataType: 'json',
            timeout: 5000,
            success: function (data) {
                if(data.code){
                    LeftData=data.data
                }else{
                    alert(data.msg)
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            }
        });
    }

    var detail_div = {:count($resources_data)}




    function add_div() {
        var e = document.getElementById("details");
        var div = document.createElement("div");
        div.className = "form-group";
        div.id = "details" + detail_div;
        // div.find('.ranking').val(2)
        // var html = "<div class='layui-inline' style='margin-bottom: 20px;'><label class='layui-form-label'>排名</label>" +
        //     "<div class='layui-input-inline'><input type='text' value='" + (detail_div + 1) +
        //     "' name='ranking' disabled placeholder='请输入排名' class='layui-input ranking'></div></div>" +
        //     "<div class='layui-inline' style='margin-bottom: 20px;'><label class='layui-form-label'>奖励</label>" +
        //     "<div class='layui-input-inline'>" +
        //     "<input type='text' name='awardIntegral' placeholder='请输入奖励' class='layui-input awardIntegral'>" +
        //     "</div>" +
        //     "<button class='layui-btn' onclick='del_div(this)'>-</button>" +
        //     "</div>"


        var html = '<div class="layui-inline" style="margin-bottom: 20px;"><label class="layui-form-label">福利资源</label>' +
            '<div class="layui-input-inline FuzzySearch_Container" >' +
            '<select id="GJliebiao' + (detail_div + 1) + '" name="welfare_resources_id[]" lay-verify="GJliebiao"  lay-search="true">';
            for (var i = 0; i < LeftData.length; i++) {
                console.log(LeftData[i])
                html += ' <option value="' + LeftData[i].id + '">' + LeftData[i].name + '</option>';
            }
        html += '</select></div> </div>' +
            '<div class="layui-inline" style="margin-bottom: 20px;"><label class="layui-form-label">发放数量</label>' +
            '<div class="layui-input-inline">' +
            '<input type="text" name="number[]" placeholder="发放数量" class="layui-input awardIntegral" value="1">' +
            '</div>';


        if(detail_div>0){
            html += '<a class="layui-btn" onclick="del_div(this)">-</a>';
        }


        html += '</div></br>';

        div.innerHTML += html
        document.getElementById("details_p").appendChild(div);

        layui.form.render("select");

        detail_div++;
    }

    function del_div(obj) {
        var did_id = obj.parentNode.parentNode.id;
        var did = did_id.substring(7);
        if (detail_div > 1) {
            var id = "details" + did.toString();
            var e = document.getElementById(id);
            // console.log({id})
            $(".ranking").each(function (ite, ind) {
                if (ite > 1) {
                    // console.log({ite})
                    $(this).val(ite);
                }
            })
            document.getElementById("details_p").removeChild(e);
            detail_div--;
        }
    }

    window.onload = function () {

        validate('{$data["game_id"]}')
    }

</script>
{/block}
