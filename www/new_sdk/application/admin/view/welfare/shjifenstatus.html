{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>添加礼包</title>
<style>
.layui-form-label {
    width: 110px;
}
</style>
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('shjifen')}" method="post">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">审核状态：</label>
            <div class="layui-input-inline" style="width: 50%;">
                <input name="sh_status" value="1" type="radio" title="通过" checked>
                <input name="sh_status" value="2" type="radio" title="拒绝">

                <input name="id" value="{$info.id}" style="display: none">
            </div>
        </div>


        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回</button>
            </div>
        </div>

    </form>
</div>

{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    layui.use(['form', 'layer', 'upload'], function () {
        var form = layui.form,
            layer = layui.layer,
            upload = layui.upload;

        upload.render({
            elem: '#upload_file' //绑定元素
            ,url: '/welfare/uploadFile/' //上传接口
            ,accept: 'file' //允许上传的文件类型
            ,done: function(res){
                $('#result').hide();
                layer.msg(res.msg);
                if (res.code) {
                    $("#filename").val(res.data.filename);
                    $("#tmpname").val(res.data.tmpname);
                }
            }
            ,error: function(){
                layer.msg('上传文件出错，请稍后再试');
            }
        });

    })


    layui.use('laydate', function () {
        var laydate = layui.laydate;
        var starttime = laydate.render({
            elem: '#starttime',
            type: 'date',
            format: 'yyyy-MM-dd',
            max: '2099-06-16', //最大日期
            min: '',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#endtime',
            type: 'date',
            format: 'yyyy-MM-dd',
            max: '2099-06-16',
            min: '',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1,//关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });


    });
</script>
{/block}