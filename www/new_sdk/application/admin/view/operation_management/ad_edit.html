{extend name="layout/content" /}
{block name="header"}
<title>广告编辑管理</title>
<style>
    .layui-form-label {
        width: 84px;
    }

    .layui-textarea {
        width: 40%;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">
    <form class="layui-form" action="{:url('adEdit',['id'=>$info.id])}" method="POST" enctype="multipart/form-data">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">广告标题：<span class="x-red">*</span></label>
            <div class="layui-input-inline">
                <input type="text" name="title" value="{:escape($info.title)}" class="layui-input" lay-verify="required">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">投放位置：<span class="x-red">*</span></label>
            <div class="layui-input-inline">
            	<select name="type" lay-verify="required" lay-search>
                    <option value="">选择位置</option>
                    <option value="1" {if condition="$info.type eq 1"}selected="selected"{/if}>首页顶部</option>
                    <option value="2" {if condition="$info.type eq 2"}selected="selected"{/if}>轮播图右侧</option>
                    <option value="3" {if condition="$info.type eq 3"}selected="selected"{/if}>今日热点</option>
                    <option value="4" {if condition="$info.type eq 4"}selected="selected"{/if}>红字头条</option>
                    <option value="5" {if condition="$info.type eq 5"}selected="selected"{/if}>头条新闻</option>
                    <option value="6" {if condition="$info.type eq 6"}selected="selected"{/if}>手游评测</option>
                    <option value="7" {if condition="$info.type eq 7"}selected="selected"{/if}>手游攻略</option>
                    <option value="8" {if condition="$info.type eq 8"}selected="selected"{/if}>首页底部</option>
                    <option value="9" {if condition="$info.type eq 9"}selected="selected"{/if}>礼包发号</option>
                    <option value="10" {if condition="$info.type eq 10"}selected="selected"{/if}>手机底部</option>
                    <option value="11" {if condition="$info.type eq 11"}selected="selected"{/if}>首页轮播</option>
                    <option value="12" {if condition="$info.type eq 12"}selected="selected"{/if}>首页广告弹窗</option>
                    <option value="13" {if condition="$info.type eq 13"}selected="selected"{/if}>底部悬浮窗</option>
                    <option value="99" {if condition="$info.type eq 99"}selected="selected"{/if}>文章内页广告推荐</option>    
                    <option value="21" {if condition="$info.type eq 21"}selected="selected"{/if}>手机顶部轮播</option>    
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label for="" class="layui-form-label">跳转地址：<span class="x-red">*</span></label>
            <div class="layui-input-inline">
                <input type="text" name="url" value="{$info.url}" class="layui-input" lay-verify="required">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label for="" class="layui-form-label">广告图片：<span class="x-red">*</span></label>
            
            <div class="layui-input-block" style="width:auto">
            
            	{if(!empty($info.image))}
            	<div id="localImag">
					<img src="{$Think.STATIC_DOMAIN}{$info.image}" alt="广告图片" id="local_preview">
				</div>
				{/if}
            
				<div>
					<input type="file" name="image" onchange="setImagePreview(this,'localImag','local_preview')" accept=".jpg,.jpeg,.png,.gif">
                </div>
                
				<div class="layui-form-mid layui-word-aux margin-left-120">
					<span class="x-red">请上传jpg、png、gif的图片，最大不超过1M</span>
				</div>
			</div>
        </div>
        
        <div class="layui-form-item">
            <label for="" class="layui-form-label">排序：</label>
            <div class="layui-input-inline">
                <input type="text" name="sort" value="{$info.sort}" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">操作：</label>
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-radius" lay-submit lay-filter="">确定</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn  layui-btn-radius layui-btn-primary">返回
                </button>
            </div>
        </div>

    </form>
</div>

<script>
	function setImagePreview(avalue, div_id, preview_id) {
		var docObj = avalue;
		//img
		var imgObjPreview = document.getElementById(preview_id);
        
		if (docObj.files && docObj.files[0]) {
			//imgObjPreview.src = docObj.files[0].getAsDataURL();
			//火狐7以上版本不能用上面的getAsDataURL()方式获取，需要一下方式
			imgObjPreview.src = window.URL.createObjectURL(docObj.files[0]);
		} else {
			//IE下，使用滤镜
			docObj.select();
			var imgSrc = document.selection.createRange().text;
			var localImagId = document.getElementById(div_id);
			//图片异常的捕捉，防止用户修改后缀来伪造图片
			try {
				localImagId.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)"
				localImagId.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = imgSrc;
			} catch (e) {
				alert("您上传的图片格式不正确，请重新选择!");
				return false;
			}
			imgObjPreview.style.display = 'none';
			document.selection.empty();
		}
		return true;
	}
</script>
{/block}
{block name="footer"}

{/block}