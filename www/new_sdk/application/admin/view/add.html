{extend name="layout/content" /}
{block name="header"}
<title>增加cps 推广员和区服数据限制</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    .layui-form-label {
        width: 157px;
    }

    header {
        background: #EFEFEF;
        padding: 10px 20px 10px 10px;
        border-bottom: 1px solid lightgray;
        /* border-top-left-radius: 5px;
        border-top-right-radius: 5px; */
    }

    header h1 {
        text-align: right;
        font-weight: 700;
    }
</style>
{/block}
{block name="content"}
<div class="x-body">

    <form class="layui-form" action="{:url('add')}" method="POST">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">请选择推广员：</label>
            <div class="layui-input-inline">
                <select name="channel_id" lay-search lay-filter="tuiFilter" id="channel_id">
                    <option value="">请选择推广员</option>
                    {volist name="channel_list" id="g"}
                    <option value="{$g.id}">{$g.name}</option>
                    {/volist}
                </select>
            </div> <!-- <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_gameid' name="gameid" value="{$Request.get.gameid}" />
                        </div>
            </div> -->
        </div>
        <input type="hidden" id="hidden_channel_ids" name="hidden_channel_ids" value=""/>
<!--        <div class="layui-form-item">-->
<!--            <label class="layui-form-label">推广员数据：</label>-->
<!--            <div class="layui-input-inline">-->
<!--                <textarea style="width: 500px" placeholder="请输入内容" class="layui-textarea" name="channel_names"-->
<!--                          id="channel_names" readonly></textarea>-->
<!--            </div>-->
<!--        </div>-->

        <div class="layui-form-item">
            <label for="" class="layui-form-label"></label>
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit type="submit" lay-filter="formDemo" id="J_game_submit_btn">提交
                </button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn layui-btn-primary">返回
                </button>
            </div>
        </div>

    </form>

</div>
<script type="text/javascript" src="__STATIC__/js/jquery_v3.3.1.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">

    $(document).ready(function () {
        layui.use('form', function () {
            var form = layui.form;
            // form.on('select(tuiFilter)', function (data) {
            //     let channel_id = $("#channel_id").val();
            //     let channel_name = $("#channel_id").find("option:selected").text();
            //     let hidden_channel_ids = $("#hidden_channel_ids").val();
            //     let channel_names = $("#channel_names").text();
            //
            //     if (hidden_channel_ids.length != 0) {
            //         channel_ids_new_arr = []
            //         channel_ids_arr = hidden_channel_ids.split(",");
            //         channel_ids_arr.push(channel_id)
            //         channel_ids_arr.sort()
            //         $.unique(channel_ids_arr)
            //         channel_ids_result = channel_ids_arr.join(',');
            //         $("#hidden_channel_ids").val(channel_ids_result)
            //     }else {
            //         $("#hidden_channel_ids").val(channel_id)
            //     }
            //     if (channel_names.length != 0) {
            //         channel_names_new_arr = []
            //         channel_names_arr = channel_names.split("|");
            //         channel_names_arr.push(channel_name)
            //         channel_names_arr.sort()
            //         $.unique(channel_names_arr)
            //         channel_names_result = channel_names_arr.join('|');
            //         $("#channel_names").text(channel_names_result)
            //     } else {
            //         $("#channel_names").text(channel_name)
            //     }
            // });
        });
    })


</script>
{/block}

{block name="footer"} {/blcok}