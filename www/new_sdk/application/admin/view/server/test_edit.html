{extend name="layout/content" /}
{block name="header"}
<title>编辑开测</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
	.layui-form-label {
    	width: 110px;
	}
</style>
{/block}
{block name="content"}
<div class="x-body">

	<form class="layui-form" action="{:url('testEdit')}" method="post">


		<div class="layui-form-item">
			<label for="gameid" class="layui-form-label">游戏：</label>
			<div class="layui-input-inline">
				<select name="gameid_show" id="gameid_show" disabled>
					<option value="">请选择游戏</option>
					{foreach name="game_list" item="vo"}
					<option value="{$vo.id}" {if condition="$vo['id'] eq $serverInfo.gameid" } selected="selected" {/if}>{$vo.name}</option>
					{/foreach}
				</select>
				<input type="hidden" id='gameid' name="gameid" value="{$serverInfo.gameid}" />	
			</div>
			<!--
			<div class="layui-input-inline FuzzySearch_Container">
				<div>
					<input type="hidden" id='gameid' name="gameid" value="{$serverInfo.gameid}" />
				</div>
			</div>
			-->
		</div>

		<div class="layui-form-item">
			<label for="sername" class="layui-form-label">开测状态：</label>
			<div class="layui-input-inline">
				<select name="serstatus" lay-search>
					<option value="">请选择状态</option>
					<option value="3" {if condition="3 eq $serverInfo.serstatus" } selected="selected" {/if}>删档内测</option>
					<option value="4" {if condition="4 eq $serverInfo.serstatus" } selected="selected" {/if}>不删档内测</option>
					<option value="5" {if condition="5 eq $serverInfo.serstatus" } selected="selected" {/if}>公测</option>
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<label for="sertime" class="layui-form-label">开测时间：</label>
			<div class="layui-input-inline">
				<input type="text" name="sertime" id="sertime" required autocomplete="off" class="layui-input" placeholder="开测时间必须大于当前时间" value="{:date('Y-m-d H:i', $serverInfo.sertime)}">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-input-block">
				<input type="hidden" name="id" value="{$serverInfo.id}">
				<button type="submit" class="layui-btn" id="J_submit_btn">提交</button>
				<a class="layui-btn" href="{:url('testList')}">返回</a>
			</div>
		</div>


	</form>
</div>
{/block}

{block name="footer"}
<script type="text/javascript" charset="utf-8" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
	/*
	$("#gameid").FuzzySearch({
		inputID     : 'gameid',
		title   	: '请输入游戏名称',
		searchBtn	:'J_submit_btn',
		data        :{:json_encode($game_list)},
	});
	*/
    layui.use(['laydate', 'table', 'form', 'layer'], function () {
        var laydate = layui.laydate

        laydate.render({
            elem: '#sertime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm',
            max: '2099-06-16', //最大日期
        });
    });
</script>
{/block}