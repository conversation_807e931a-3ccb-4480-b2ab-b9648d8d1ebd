{extend name="layout/content" /}
{block name="header"}
<title>开测列表</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block}

{block name="content"}

<div class="x-body">
	

		


		<form class="layui-form" action="{:url('testList')}" method="get" >
			
			<div style="float: left;">
				<a href="{:url('testCreate')}" class="layui-btn">新增开测</a>
			</div>


			<div style="float: right;">

				<div class="layui-inline">
					<div class="layui-input-inline FuzzySearch_Container">
						<div>
							<input type="hidden" id='J_gameid' name="gameid" value="{:input('gameid')}" />
						</div>
					</div>
				</div>
				
		
				<div class="layui-inline">
					<div class="layui-input-inline">
						<select name="serstatus" lay-search>
							<option value="">请选择状态</option>
							<option value="3" {if condition="3 eq input('serstatus')" } selected="selected" {/if}>删档内测</option>
							<option value="4" {if condition="4 eq input('serstatus')" } selected="selected" {/if}>不删档内测</option>
							<option value="5" {if condition="5 eq input('serstatus')" } selected="selected" {/if}>公测</option>
						</select>
					</div>
				</div>
		
				<div class="layui-inline">
					<button type="submit" class="layui-btn" id="J_search_submit">查询</button>
				</div>
			</div>


			<div style="clear:both"></div>



			<table class="layui-table">
					<thead>
					<tr>
						<th>记录ID</th>
						<th>游戏名称</th>
						<th>开测状态</th>
						<th>开测时间</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody>
					{foreach name="list" id="vo"}
					<tr>
						<td>{$vo.id}</td>
						<td>{$vo.game_name}</td>
						<td>
							{if($vo.serstatus==3)}
							删档内测
							{elseif($vo.serstatus==4)}
							不删档内测
							{elseif($vo.serstatus==5)}
							公测
							{else}
								未设置
							{/if}
						</td>
						<td>{:date('Y-m-d H:i',$vo.sertime)}</td>
						<td>
							<a href="{:url('testEdit',['id'=>$vo.id])}" class="btn btn-info" ><i class="icon-edit"></i>编辑</a>
							<a href="javascript:;" onclick="del_info(this,'<?php echo url('testDel',['id'=>$vo['id']]); ?>')"class="layui-btn  layui-btn-danger">
							<i class="layui-icon">&#xe640;</i>删除</a>
						</td>
					</tr>
					{/foreach}
					</tbody>
				</table>
		
			


				<div class="pager-container" style="margin-top:10px;">
					<span>{$list->total()}条记录</span>
					{$page}
					<div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
						<input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
					</div>
				</div>


		</form>


		
	
</div>
{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
	    $("#J_gameid").FuzzySearch({
			 inputID    : 'J_gameid',
			 title   	: '请输入游戏名称',
			 data       :{:json_encode($game_list)},
			 searchBtn	:'J_search_submit',
		});
</script>
{/block}