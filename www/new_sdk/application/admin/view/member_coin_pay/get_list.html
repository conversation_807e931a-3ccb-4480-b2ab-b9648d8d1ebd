{extend name="layout/content"/} {block name="header"}
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap-table.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>充值记录管理</title>
<style>
    label {
        padding-top: 10px;
        margin-left: 10px;
    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }

    .layui-input {
        margin-right: 5px;
    }

    .set_width {
        margin-right: 5px;
    }

    .hide {
        display: none;
    }

    .glyphicon-plus:before {
        content: "\002b";
        font-size: 12px;
        font-weight: 900;
        width: 100%;
        display: inline-block;
    }

    .glyphicon-minus:before {
        content: "\2212";
        font-size: 12px;
        font-weight: 900;
        width: 100%;
        display: inline-block;
    }

    .table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
        padding: 8px;
        line-height: 1.42857143;
        vertical-align: middle !important;
        border-top: 1px solid #ddd;
    }

    table td,
    table th {
        text-align: center;
    }

    .detail-view td,
    .detail-view th {
        text-align: left;
    }


    .badge-default{
        color: #333;
        background-color: #fff;
    }
    .badge-success{
        background-color: #449d44;
    }
    .badge-danger{
        background-color: #d9534f;
    }

    :hover .noLine{
        text-decoration-line: none;
    }

    .layui-inline{
        margin-top: 10px;
    }
</style>

{/block} {block name="content"}
<div class="x-body">

    <form class="layui-form" method="GET" action="{:url('getList')}">

        <div class="layui-inline">
            <div class="layui-input-inline">
                <input class="layui-input" placeholder="开始时间" name="start_time" id="start" readonly='readonly' value="{$start_time}" autocomplete="off">
            </div>
            <span>-</span>
            <div class="layui-input-inline">
                <input class="layui-input" placeholder="结束时间" name="end_time" id="end" readonly='readonly' value="{$end_time}" autocomplete="off">
            </div>
        </div>



        <div class="layui-inline">
            <label>充值状态：</label>
            <div class="layui-input-inline">
                <select name="status">
                    <option value="">选择支付状态</option>
                    <option value="0" {if condition="$Request.get.status eq '0' " }selected="selected" {/if}>待支付</option>
                    <option value="1" {if condition="$Request.get.status eq 1 " }selected="selected" {/if}>支付成功</option>
                    <option value="2" {if condition="$Request.get.status eq 2 " }selected="selected" {/if}>支付失败</option>
                </select>
            </div>
        </div>



        <div class="layui-inline">
            <label>选择充值方式：</label>
            <div class="layui-input-inline">
                <select name="paytype">
                    <option value="">选择充值方式</option>
                    {volist name="paytype" id="pvo"}
                    <option value="{$key}" {if condition="$Request.get.paytype eq $key" }selected="selected" {/if}>{$pvo}</option>
                    {/volist}
                </select>
            </div>
        </div>

        <div class="layui-inline">
            <div class="layui-input-inline">
                <input class="layui-input" placeholder="订单号" id="orderid" name="orderid" value="{$Request.get.orderid}">
            </div>
        </div>

        <div class="layui-inline">
            <div class="layui-input-inline">
                <input class="layui-input" placeholder="玩家账号" name="username" value="{$Request.get.username}">
            </div>
        </div>




        <div class="layui-inline">
            <button class="layui-btn" type="submit" id="J_search_submit">查询</button>
            <a class="layui-btn" href="javascript:void(0);" onclick="reportDownload(0);">下载报表</a>
<!--            <a class="layui-btn" href="javascript:void(0);" onclick="reportDownload(1);">下载报表（高级）</a>-->
        </div>

        <!--
        <p style="margin-top:10px;">
            <span style="color: red">*</span><a class="noLine" style="color: #666"> 单次查询日期的最长跨度为31天</a>
        </p>
        -->


        <!-- data-toolbar="#toolbar" -->
        <table data-toggle="table" data-detail-view="true" data-detail-formatter="detailFormatter">
            <thead>
            <tr>
                <th class="hide">订单号</th>
                <th>账号</th>
                <th>充值金额</th>
                <th>第三方支付</th>

                <th>支付方式</th>
                <th>支付状态</th>
                <th>充值时间</th>
<!--                <th>操作</th>-->
            </tr>
            </thead>

            <tbody id="viewer_wrap">
            {empty name="list"}

            {else/} {volist name="list" id="vo"}
            <tr>
                <td>{$vo.orderid}</td>
                <td id="J_td_{$vo.id}"><a href="javascript:void(0);" onclick="showUsername({$vo.userid},{$vo.id})" style="text-decoration: underline;">{:stringObfuscation($vo.username)}</a></td>
                <td><i class="fa fa-cny"></i>{$vo.amount}</td>
                <td><i class="fa fa-cny"></i>{$vo.real_amount}</td>

                <td>{:config('paytype.'.$vo.paytype)}</td>
                <td>{switch name="vo.status"} {case value="0"}
                    <span  class="badge badge-default">待支付</span>
                    {/case} {case value="1"}
                    <span class="badge badge-success">成功</span>
                    {/case} {case value="2"}
                    <span class="badge badge-danger">失败</span>
                    {/case} {/switch}
                </td>

                <td>{$vo.create_time}</td>
<!--                <td>-->

<!--                </td>-->
            </tr>
            {/volist} {/empty}
            </tbody>
        </table>

        <div class="pager-container" style="margin-top:10px;">
                        <span>
                            充值完成总金额：{$totalAmount}元，{$total}条记录；   今日充值：{$todayTotalAmount}元 ； 昨日充值：{$lastDayTotalAmount}元
                        </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>
    </form>





</div>
{/block}
{block name="footer"}

<script type="text/javascript" src="__STATIC__/js/admin/bootstrap-table.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $(document).ready(function () {

        layui.use(['form', 'laydate'], function () {
            var laydate = layui.laydate;
            var form 	= layui.form;

            var starttime = laydate.render({
                elem: '#start',
                type: 'date',
                format: 'yyyy-MM-dd',
                done: function (value, dates) {
                    endtime.config.min = {
                        year: dates.year,
                        month: dates.month - 1, //关键
                        date: dates.date,
                        hours: 0,
                        minutes: 0,
                        seconds: 0
                    };
                }
            });
            var endtime = laydate.render({
                elem: '#end',
                type: 'date',
                format: 'yyyy-MM-dd',
                done: function (value, dates) {
                    starttime.config.max = {
                        year: dates.year,
                        month: dates.month - 1, //关键
                        date: dates.date,
                        hours: 0,
                        minutes: 0,
                        seconds: 0
                    }
                }
            });

            /**
             * 查看附加信息按钮点击事件
             */
            $('table tbody tr td').on('click', ".J_show_attach",function () {
                var orderid = $(this).data('orderid');
                $.ajax({
                    type: "post",
                    data: {'orderid':orderid},
                    url: "{:url('showAttach')}",
                    dataType: "json",
                    timeOut: 10,
                    success: function (msg) {
                        if(msg.code) {
                            var attach = msg.msg;
                            var html = '<div>';
                            html += attach;
                            html += '</div>';
                            layer.open({
                                type: 1,
                                title: '合作方订单号',
                                skin: 'layui-layer-rim',
                                area: ['420px', '240px'], //宽高
                                content: html,
                            });
                            if (window.top !== window.self) {
                                window.parent.$('html, body').animate({
                                    scrollTop: 0
                                }, 'slow');
                            }
                        }else{
                            layer.alert(msg.msg, {icon: 5});
                        }
                    },
                    error: function () {
                        layer.alert('网络错误，请刷新页面重试！', {icon: 2});
                    }
                });
            })
        });

    });
    /**
     * 下载
     */
    function reportDownload(is_show)
    {
        var start_time = $('#start').val();
        var end_time = $('#end').val();
        /*
        if (!end_time || !start_time) {
            layer.msg('请选择开始时间和结束时间');
            return;
        }
        */
        /*
        if ((CompareDate(start_time, end_time)) > 7) {
            layer.msg('请选择7天以内的时间范围');
            return;
        }*/

        $.ajax({
            type: "post",
            url: "{:url('getList')}",
            async: false,
            data: $('.layui-form').serialize()+'&download=1',
            dataType: 'json',
            timeout: 5000,
            success: function (data) {

                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    }

    var $table = $("table");
    $table.bootstrapTable({
        formatNoMatches: function () {
            return "暂无数据";
        }
    });


    function detailFormatter(index, row) {

        var html = [];

        var obj = {
            0: "订单号",
            1: "游戏名称",
            2: "账号",
            3: "注册时间",
            4: "充值金额",
            5: "平台币支付",
            6: "第三方支付",
            7: "支付方式",
            8: "支付状态",
            9: "支付状态",
            10: "推广员账号",
            11: "公会账号",
            12: "子公会账号",
            13: "商务账号",
            14: "区服ID",
            15: "注册时间",
            16: "角色ID",
            17: "子会长账号",
            18: "等级",
            19: "商品名",
        }

        $.each(row, function (key, value) {
            if (key == 0 || (key >= 14 && key <= 19)) {
                html.push('<p><b>' + obj[key] + ':</b> ' + value + '</p>');
            } else {
                html.push('<p class="hide"><b>' + obj[key] + ':</b> ' + value + '</p>');
            }
        });
        return html.join('');
    }

    //显示全部姓名
    function showUsername(userid,id)
    {
        $.ajax({
            type: "post",
            data: {'userid':userid,'id':id},
            url: "{:url('showUsername')}",
            dataType: "json",
            timeOut: 10,
            success: function (result) {
                if(result.code) {

                    $('#J_td_'+id).html(result.data);

                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            },
            error: function () {
                layer.alert('网络错误，请刷新页面重试！', {icon: 2});
            }
        });
    }
</script>
{/block}
