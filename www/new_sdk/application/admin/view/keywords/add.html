{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>添加关键词</title>
<style>
.layui-form-label {
    width: 110px;
}
</style>
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('add')}" method="post">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>规则名：
            </label>
            <div class="layui-input-inline">
                <input type="text" id="rule_name" name="rule_name" required="" lay-verify="required" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">规则名不可重复，且不超过60个字，如果回复类型是礼包，则规则名即相当于礼包名</div>
        </div>
        
        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>关键词类型：
            </label>
            <div class="layui-input-inline">
             	<select name="type" lay-search lay-filter="type">
                    <option value="0">常规关键词</option>
                    <option value="1">礼包关键词</option>
                </select>
            </div>
        </div>

        <div id='convention'>
            <div id='keywords' lay-filter="keyword">
                <div class="layui-form-item" data-num="1" id='keyword_1'>
                    <label for="" class="layui-form-label">
                        <span class="x-red">*</span>关键词：
                    </label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <select name="is_all[1]" lay-search>
                            <option value="0">半匹配</option>
                            <option value="1">全匹配</option>
                        </select>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" name="keyword[1]" placeholder="请输入关键词" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-input-inline" style="padding-top: 3px; ">
                        <button type="button" class="layui-btn layui-btn-sm" data-type="addRow" data-num="1">
                            <i class="layui-icon">&#xe654;</i>
                        </button> 
                    </div>
                    
                </div>

            </div>
            <div id='replays'>
                <div class="layui-form-item"  data-num="1" id='replays_1'>
                    <label for="" class="layui-form-label">
                        <span class="x-red">*</span>回复内容：
                    </label>
                    <div class="layui-input-inline" style="width: 30%">
                        <textarea name="replays[1]" rows="5" class="layui-textarea"></textarea>
                    </div>
                    <div class="layui-input-inline" style="margin: auto;">
                        <button type="button" class="layui-btn layui-btn-sm" data-type="addReplaysRow" data-num="1">
                            <i class="layui-icon">&#xe654;</i>
                        </button> 
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><span class="x-red">*</span>回复方式：</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="0" title="回复全部" checked>
                    <input type="radio" name="status" value="1" title="随机回复一条">
                </div>
            </div>

            


        </div>


        <div id='gift' hidden>
            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>关键词：
                </label>
                <div class="layui-input-inline">
                    <input type="text" name="gift_keyword" placeholder="请输入关键词" autocomplete="off" class="layui-input">
                </div>
                
            </div>
            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>回复内容：
                </label>
                <div class="layui-input-inline" style="width: 30%">
                    <textarea name="gift_replays" rows="5" class="layui-textarea"></textarea>
                </div>
                <div class="layui-form-mid layui-word-aux">礼包序列号占位符{<?php echo '$giftcode' ?>}，请正确填写占位符，否则将无法正常显示内容</div>

            </div>
            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>使用次数：
                </label>
                <div class="layui-input-inline">
                    <select name="gift_status" lay-search lay-filter="status">
                        <option value="1">1次</option>
                        <option value="0">无限次</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" id='gift_code_1'>
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>礼包序列号：
                </label>
                <div class="layui-input-inline" style="width: 30%">
                    <textarea name="gift_code_1" rows="5" class="layui-textarea"></textarea>
                </div>
                <div class="layui-form-mid layui-word-aux">礼包序列号按行分隔</div>

            </div>
            <div class="layui-form-item" id='gift_code_0' hidden>
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>礼包序列号：
                </label>
                <div class="layui-input-inline">
                    <input type="text" name="gift_code_0" autocomplete="off" class="layui-input">
                </div>
                
            </div>
            
        </div>




        <div class="layui-form-item">
            <label for="" class="layui-form-label">备注：</label>
            <div class="layui-input-inline" style="width: 50%;">
                <textarea name="remark" rows="5" class="layui-textarea"></textarea>
            </div>
        </div>




        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回</button>
            </div>
        </div>

    </form>
</div>

{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    layui.use('form', function () {
        var form = layui.form;

        form.on('submit(formDemo)', function(data){

            $.ajax({
                type: "post",
                url: "{:url('add')}",
                async: false,
                data: data.field,
                dataType: 'json',
                timeout: 5000,
                success: function (data) {

                    if(data.code == 1){
                        layer.msg(data.msg,{icon:1,time:1500,shade:0.4},function() {
                            location.href = data.url;
                        });
                    }else{
                        layer.msg(data.msg);
                    }

                },
                error: function () {
                    layer.alert('网络错误，请重试');
                },
            });

              return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });

        form.on('select(type)', function(data){
            if ( data.value == 1) {
                $("#convention").hide();
                $("#gift").show();
            }else{
                $("#gift").hide();
                $("#convention").show();
            }
            console.log(data.value); //得到被选中的值
        });

        form.on('select(status)', function(data){
            if ( data.value == 1) {
                $("#gift_code_0").hide();
                $("#gift_code_1").show();
            }else{
                $("#gift_code_1").hide();
                $("#gift_code_0").show();
            }
            console.log(data.value); //得到被选中的值
        });

        $('.layui-btn[data-type]').on('click', function () {
            var num = $(this).data('num');
            var type = $(this).data('type');
            console.log(type,num);
            activeByType(type,num);
        });

        //激活事件
        var activeByType = function (type, arg) {
            active[type] ? active[type].call(this, arg) : '';
        }

        window.btnRow = function(type,num) {
            var a = ['addRow','delRow','addReplaysRow','delReplaysRow'];
            activeByType(a[type],num);
        }

        //定义事件集合
        var active = {
            addRow: function(num){ //添加一行
                var keywords =  $("#keywords").children('div');

                if ( keywords.length >= 10 ) {
                    layer.msg('最多只能添加10个关键词');
                    return false;
                }
                var rel = num;

                for(var i=0;i<keywords.length;i++){ //遍历子元素
                    console.log(keywords.eq(i).data('num') )
                    if ( keywords.eq(i).data('num') > rel) {
                        rel = keywords.eq(i).data('num');
                    }

                }

                var rel = rel + 1;

                $('<div class="layui-form-item" data-num="' + rel + '" id="keyword_' + rel + '">' +
                    '<label for="" class="layui-form-label">' +
                    '</label>' +
                    '<div class="layui-input-inline" style="width: 100px;">' +
                        '<select name="is_all['+ rel +']" lay-search>' +
                            '<option value="0">半匹配</option>' +
                            '<option value="1">全匹配</option>' +
                        '</select>' +
                    '</div>' +
                    '<div class="layui-input-inline">' +
                        '<input type="text" name="keyword[' + rel + ']" placeholder="请输入关键词" autocomplete="off" class="layui-input">' +
                    '</div>' +
                    '<div class="layui-input-inline" style="padding-top: 3px; ">' +
                        '<button type="button" onclick="btnRow(0,'+ rel +')" class="layui-btn layui-btn-sm" data-type="addRow" data-num="' + rel + '">' +
                            '<i class="layui-icon">&#xe654;</i>' +
                        '</button>' +
                        '<button type="button" onclick="btnRow(1,'+ rel +')" class="layui-btn layui-btn-sm" data-type="delRow" data-num="' + rel + '">' +
                            '<i class="layui-icon">&#xe640;</i>' +
                        '</button>' +
                    '</div>' +
                    
                '</div>').insertAfter($("#keyword_" + num));


                form.render();

            },
            delRow: function(num){
                $("#keyword_" + num).remove();
            },
            addReplaysRow: function(num){
                var replays =  $("#replays").children('div');

                if ( replays.length >= 5 ) {
                    layer.msg('最多只能添加5个回复内容');
                    return false;
                }
                var rel = num;

                for(var i=0;i<replays.length;i++){ //遍历子元素
                    
                    if ( replays.eq(i).data('num') > rel) {
                        rel = replays.eq(i).data('num');
                    }

                }
                var rel = rel + 1;

                console.log(num)

                $('<div class="layui-form-item"  data-num="'+ rel +'" id="replays_'+ rel +'">' +
                    '<label for="" class="layui-form-label">' +
                    '</label>' +
                    '<div class="layui-input-inline" style="width: 30%">' +
                        '<textarea name="replays['+ rel +']" rows="5" class="layui-textarea"></textarea>' +
                    '</div>' +
                    '<div class="layui-input-inline" style="margin: auto;">' +
                        '<button type="button" onclick="btnRow(2,'+ rel +')" class="layui-btn layui-btn-sm" data-type="addReplaysRow" data-num="'+ rel +'">' +
                            '<i class="layui-icon">&#xe654;</i>' +
                        '</button>' +
                        '<button type="button" onclick="btnRow(3,'+ rel +')" class="layui-btn layui-btn-sm" data-type="delReplaysRow" data-num="' + rel + '">' +
                            '<i class="layui-icon">&#xe640;</i>' +
                        '</button>' +
                    '</div>' +
                '</div>').insertAfter($("#replays_" + num));

                form.render();
            },
            delReplaysRow: function(num){
                $("#replays_" + num).remove();
            },
            
        }

        
    });

    
</script>
{/block}