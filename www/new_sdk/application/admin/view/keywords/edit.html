{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>添加关键词</title>
<style>
.layui-form-label {
    width: 110px;
}
.addCode{
    color: #0099CC;
}
:hover > .addCode{
    cursor: pointer;
}
</style>
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('edit')}" method="post">
        <input type="hidden" name="id" value="{$info.id}">
        <input type="hidden" name="type" value="{$info.type}">
        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>规则名：
            </label>
            <div class="layui-input-inline">
                <input type="text" id="rule_name" name="rule_name" required="" lay-verify="required" class="layui-input" value='{$info.rule_name}'>
            </div>
            <div class="layui-form-mid layui-word-aux">规则名不可重复，且不超过60个字，如果回复类型是礼包，则规则名即相当于礼包名</div>
        </div>
        
        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>关键词类型：
            </label>
            <div class="layui-input-inline" style="padding-top: 10px; ">
                {if condition="$info.type eq 1"}
                    <p>礼包关键词</p>
                {else/}
                    <p>常规关键词</p>
                {/if}
            </div>
        </div>
        
        {if condition="$info.type eq 0"}
        <div id='convention'>
            <div id='keywords' lay-filter="keyword">

                {volist name='keywords' id='vo'}
                <div class="layui-form-item" data-num="{$key}" id='keyword_{$key}'>
                    <label for="" class="layui-form-label">
                        <span class="x-red">*</span>关键词：
                    </label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <select name="is_all[{$key}]" lay-search>
                            <option value="0" {if condition="$vo.is_all eq 0"}selected="selected"{/if}>半匹配</option>
                            <option value="1" {if condition="$vo.is_all eq 1"}selected="selected"{/if}>全匹配</option>
                        </select>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" name="keyword[{$key}]" placeholder="请输入关键词" autocomplete="off" class="layui-input" value='{$vo.keyword}'>
                    </div>
                    <div class="layui-input-inline" style="padding-top: 3px; ">
                        <button type="button" class="layui-btn layui-btn-sm" data-type="addRow" data-num="{$key}">
                            <i class="layui-icon">&#xe654;</i>
                        </button>
                        {if condition="$key gt 0"}
                        <button type="button" class="layui-btn layui-btn-sm" data-type="delRow" data-num="{$key}">
                            <i class="layui-icon">&#xe640;</i>
                        </button> 
                        {/if}
                    </div>
                    
                </div>
                {/volist}

            </div>
            <div id='replays'>
                {volist name="replays" id="vo"}
                <div class="layui-form-item"  data-num="{$key}" id='replays_{$key}'>
                    <label for="" class="layui-form-label">
                        <span class="x-red">*</span>回复内容：
                    </label>
                    <div class="layui-input-inline" style="width: 30%">
                        <textarea name="replays[{$key}]" rows="5" class="layui-textarea">{$vo}</textarea>
                    </div>
                    <div class="layui-input-inline" style="margin: auto;">
                        <button type="button" class="layui-btn layui-btn-sm" data-type="addReplaysRow" data-num="{$key}">
                            <i class="layui-icon">&#xe654;</i>
                        </button>
                        {if condition="$key gt 0"}
                        <button type="button" class="layui-btn layui-btn-sm" data-type="delReplaysRow" data-num="{$key}">
                            <i class="layui-icon">&#xe640;</i>
                        </button>
                        {/if}
                    </div>
                </div>
                {/volist}
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><span class="x-red">*</span>回复方式：</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="0" title="回复全部" {if condition="$info.status eq 0"} checked {/if}>
                    <input type="radio" name="status" value="1" title="随机回复一条" {if condition="$info.status eq 1"} checked {/if}>
                </div>
            </div>

            


        </div>
        {else/}

        <div id='gift'>
            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>关键词：
                </label>
                <div class="layui-input-inline">
                    <input type="text" name="gift_keyword" placeholder="请输入关键词" autocomplete="off" class="layui-input" value="{$keywords[0]['keyword']}">
                </div>
                
            </div>
            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>回复内容：
                </label>
                <div class="layui-input-inline" style="width: 30%">
                    <textarea name="gift_replays" rows="5" class="layui-textarea">{$replays[0]}</textarea>
                </div>
                <div class="layui-form-mid layui-word-aux">礼包序列号占位符{<?php echo '$giftcode' ?>}，请正确填写占位符，否则将无法正常显示内容</div>

            </div>
            <input type="hidden" name="gift_status" value="{$info.status}">
            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>使用次数：
                </label>
                <div class="layui-input-inline" style="padding-top: 10px; ">
                    {if condition="$info.status eq 1"}
                        <p>1次</p>
                    {else/}
                        <p>无限次</p>
                    {/if}
                </div>
            </div>
            {if condition="$info.status eq 1"}
            <div class="layui-form-item" id='gift_code_1'>
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>礼包序列号：
                </label>
                <div class="layui-input-inline" style="width: 30%">
                    <textarea name="gift_code_1" id="gift_code_text" rows="5" class="layui-textarea" readonly>{$code}</textarea>
                </div>
                <div class="layui-form-mid layui-word-aux">礼包序列号按行分隔 &nbsp;&nbsp;&nbsp;&nbsp;<span class="addCode">添加礼包序列号</span></div>

            </div>
            <div hidden>
                <textarea name="gift_code_old"  id="gift_code_old" rows="5" class="layui-textarea">{$code}</textarea>
                <textarea name="gift_code_add"  id="gift_code_add" rows="5" class="layui-textarea"></textarea>
            </div>
            
            {else/}
            <div class="layui-form-item" id='gift_code_0'>
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>礼包序列号：
                </label>
                <div class="layui-input-inline">
                    <input type="text" name="gift_code_0" autocomplete="off" class="layui-input" value="{$code}">
                </div>
                
            </div>
            {/if}
            
        </div>
        {/if}



        <div class="layui-form-item">
            <label for="" class="layui-form-label">备注：</label>
            <div class="layui-input-inline" style="width: 50%;">
                <textarea name="remark" rows="5" class="layui-textarea">{$info.remark}</textarea>
            </div>
        </div>




        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回</button>
            </div>
        </div>

    </form>
</div>

{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    layui.use('form', function () {
        var form = layui.form;

        form.on('submit(formDemo)', function(data){

            $.ajax({
                type: "post",
                url: "{:url('edit')}",
                async: false,
                data: data.field,
                dataType: 'json',
                timeout: 5000,
                success: function (data) {

                    if(data.code == 1){
                        layer.msg(data.msg,{icon:1,time:1500,shade:0.4},function() {
                            location.href = data.url;
                        });
                    }else{
                        layer.msg(data.msg);
                    }

                },
                error: function () {
                    layer.alert('网络错误，请重试');
                },
            });

              return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });

        

        $('.layui-btn[data-type]').on('click', function () {
            var num = $(this).data('num');
            var type = $(this).data('type');
            console.log(type,num);
            activeByType(type,num);
        });

        //激活事件
        var activeByType = function (type, arg) {
            active[type] ? active[type].call(this, arg) : '';
        }

        window.btnRow = function(type,num) {
            var a = ['addRow','delRow','addReplaysRow','delReplaysRow'];
            activeByType(a[type],num);
        }

        //定义事件集合
        var active = {
            addRow: function(num){ //添加一行
                var keywords =  $("#keywords").children('div');

                if ( keywords.length >= 10 ) {
                    layer.msg('最多只能添加10个关键词');
                    return false;
                }
                var rel = num;

                for(var i=0;i<keywords.length;i++){ //遍历子元素
                    
                    if ( keywords.eq(i).data('num') > rel) {
                        rel = keywords.eq(i).data('num');
                    }

                }

                var rel = rel + 1;

                $('<div class="layui-form-item" data-num="' + rel + '" id="keyword_' + rel + '">' +
                    '<label for="" class="layui-form-label">' +
                    '</label>' +
                    '<div class="layui-input-inline" style="width: 100px;">' +
                        '<select name="is_all['+ rel +']" lay-search>' +
                            '<option value="0">半匹配</option>' +
                            '<option value="1">全匹配</option>' +
                        '</select>' +
                    '</div>' +
                    '<div class="layui-input-inline">' +
                        '<input type="text" name="keyword[' + rel + ']" placeholder="请输入关键词" autocomplete="off" class="layui-input">' +
                    '</div>' +
                    '<div class="layui-input-inline" style="padding-top: 3px; ">' +
                        '<button type="button" onclick="btnRow(0,'+ rel +')" class="layui-btn layui-btn-sm" data-type="addRow" data-num="' + rel + '">' +
                            '<i class="layui-icon">&#xe654;</i>' +
                        '</button>' +
                        '<button type="button" onclick="btnRow(1,'+ rel +')" class="layui-btn layui-btn-sm" data-type="delRow" data-num="' + rel + '">' +
                            '<i class="layui-icon">&#xe640;</i>' +
                        '</button>' +
                    '</div>' +
                    
                '</div>').insertAfter($("#keyword_" + num));


                form.render();

            },
            delRow: function(num){
                $("#keyword_" + num).remove();
            },
            addReplaysRow: function(num){
                var replays =  $("#replays").children('div');

                if ( replays.length >= 5 ) {
                    layer.msg('最多只能添加5个回复内容');
                    return false;
                }
                var rel = num;

                for(var i=0;i<replays.length;i++){ //遍历子元素
                    
                    if ( replays.eq(i).data('num') > rel) {
                        rel = replays.eq(i).data('num');
                    }

                }
                var rel = rel + 1;

                console.log(num)

                $('<div class="layui-form-item"  data-num="'+ rel +'" id="replays_'+ rel +'">' +
                    '<label for="" class="layui-form-label">' +
                    '</label>' +
                    '<div class="layui-input-inline" style="width: 30%">' +
                        '<textarea name="replays['+ rel +']" rows="5" class="layui-textarea"></textarea>' +
                    '</div>' +
                    '<div class="layui-input-inline" style="margin: auto;">' +
                        '<button type="button" onclick="btnRow(2,'+ rel +')" class="layui-btn layui-btn-sm" data-type="addReplaysRow" data-num="'+ rel +'">' +
                            '<i class="layui-icon">&#xe654;</i>' +
                        '</button>' +
                        '<button type="button" onclick="btnRow(3,'+ rel +')" class="layui-btn layui-btn-sm" data-type="delReplaysRow" data-num="' + rel + '">' +
                            '<i class="layui-icon">&#xe640;</i>' +
                        '</button>' +
                    '</div>' +
                '</div>').insertAfter($("#replays_" + num));

                form.render();
            },
            delReplaysRow: function(num){
                $("#replays_" + num).remove();
            },
            
        }

        
    });

    $(".addCode").click(function () {

        var text = $("#gift_code_add").val()

        layer.prompt({
            formType: 2,
            value: text,
            title: '添加礼包序列号',
            area: ['500px', '250px'] //自定义文本域宽高
            ,success: function(layero){
                layero.find(".layui-layer-content").prepend('<div style="margin-bottom: 15px;">礼包序列号按行分隔</div>')
            }
        }, function(value, index, elem){

            $("#gift_code_add").val(value);

            var str = $("#gift_code_old").val() + '\r\n' + value;

            $("#gift_code_text").val(str);

            layer.msg('成功添加到礼包序列号中,点提交才是真正保存');

            layer.close(index);
        });
    })

    
</script>
{/block}