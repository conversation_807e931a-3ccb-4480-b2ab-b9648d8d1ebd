{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>添加礼包</title>
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('uplode',['id'=>$data.id])}" method="post">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>资源码：
            </label>
            <div class="layui-input-inline" style="width: 50%;">
                <textarea name="code" rows="20" class="layui-textarea" required="" lay-verify="required">{$code}</textarea>

            </div>

        </div>
        <div class="layui-form-item">
            <label for="" class="layui-form-label" style="width: 30%;">
                <span class="x-red">注：展示未使用的礼包码，一行一个码，换行切割</span>
            </label>

        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回</button>
            </div>
        </div>

    </form>
</div>

{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

</script>
{/block}
