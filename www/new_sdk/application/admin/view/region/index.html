{extend name="layout/content" /} {block name="header"}
<title>区服管理</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/lib/layui/css/layui.css">
<style>

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        font-weight: 400;
        text-align: right;
        width: 100px;
        margin: 0;
    }

    .layui-form-label{
        width: 90px;
        margin: 0;
    }
    .margin-left-20 {
        margin-left: 20px;
    }

    /*select[multiple]+.layui-form-select>.layui-select-title>input.layui-input{ border-bottom: 0}*/

</style>

{/block} {block name="content"}
    <div class="x-body">
        <form class="layui-form">
            <div style="float: left;">
                <div class="layui-inline">
                    <label class="layui-form-label">游戏名称：</label>
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_gameid' name="game_name" value="" />
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">区服ID：</label>
                    <div class="layui-input-inline">
                        <input type="text" id='J_serverid' name="serverid" placeholder="请输入区服ID"  autocomplete="off" class="layui-input" value="{$Request.get.serverid}">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">区服名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" id='J_servername' name="servername" placeholder="请输入区服名称"  autocomplete="off" class="layui-input" value="{$Request.get.servername}">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">显示状态：</label>
                    <div class="layui-input-inline">
                        <select name="status">
                            <option value="">- 选择状态 -</option>
                            <option {if condition="input('status') eq '1'" }selected="selected" {/if} value="1">显示</option>
                            <option {if condition="input('status') eq '0'" }selected="selected" {/if} value="0">隐藏</option>
                        </select>
                    </div>
                </div>

                <input type="hidden" name="status_str" value="" id="status">

                <div class="layui-inline">
                    <button class="layui-btn layui-btn-radius margin-left-20" lay-submit id="J_search_submit">查询</button>
                </div>
            </div>

            <div style="clear:both"></div>

            <table class="layui-table">
                <thead>
                    <tr>
                        <th>游戏ID</th>
                        <th>游戏名称</th>
                        <th>区服ID</th>
                        <th>区服名称</th>
                        <th>是否显示</th>
                    </tr>
                </thead>

                <tbody>
                    {volist name="list" id="vo"}
                    <tr>
                        <td>{$vo.id}</td>
                        <td>{$vo.name}</td>
                        <td>{$vo.serverid}</td>
                        <td>{$vo.servername}</td>
                        <td class="td-status">
                            {if condition="$vo['status'] eq 1"}
                            <a href="javascript:;" onclick="isShow(this,'{:url(\'show\',[\'id\'=>$vo.id,\'is_show\'=>0])}')"
                               title="是否显示开关">
                                <span class="layui-btn layui-btn-normal layui-btn-mini layui-btn-radius" style="background:#36C6D3">是</span>
                            </a>
                            {else}
                            <a href="javascript:;" onclick="isShow(this,'{:url(\'show\',[\'id\'=>$vo.id,\'is_show\'=>1])}')"
                               title="是否显示开关">
                                <span class="layui-btn layui-btn-normal layui-btn-mini layui-btn-radius layui-bg-gray" style="background:#36C6D3">否</span>
                            </a>
                            {/if}
                        </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>

            <div class="pager-container">
                <span>
                  {$list->total()}条记录
                </span>
                {$page}
                <div class="layui-inline" style="margin-left:10px;width:80px;">
                    <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                </div>
            </div>
        </form>
    </div>
{/block} 

{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" ></script>
<script type="text/javascript" src="__STATIC__/js/clipboard.min.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">

    $("#J_gameid").FuzzySearch({
        inputID     : 'J_gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($gameList)},
    searchBtn	:'J_search_submit',
    });

    $(function () {
        $("#J_gameid_show").attr('name','game_name');
        $("#J_gameid_show").val("{:input('get.game_name')}");
    })

/*    $("#J_servername").FuzzySearch({
        inputID    : 'J_servername',
        title   	: '请输入游戏名称',
        data       :{:json_encode($list)},
    searchBtn	:'J_search_submit',
    });*/
</script>
{/block}