{{extend name="layout/content" /} {block name="header"}
<title>广告列表</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}"
      xmlns="http://www.w3.org/1999/html">
<style>
    .layui-table th{
        font-size: 12px !important;
    },
    .layui-table td {
        text-align: center;
        font-size: 12px !important;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .check_photo + img {
        display: none;
    }

</style>
{/block}

{block name="content"}
<div class="x-body">

    <form class="layui-form" method='get' id="J_search_form">


        <div style="">

            <div class="layui-inline">
                <div class="layui-input-inline" style="width: 200px">
                    <div id="game"></div>
                    <input type="hidden" class="game_id" name='game_id' value="{$Request.get.game_id}"/>
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="开始时间" name="start" id="start" readonly='readonly'
                           value="{$Request.get.start}" autocomplete="off">
                </div>
                <span>-</span>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="结束时间" name="end" id="end" readonly='readonly'
                           value="{$Request.get.end}" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
                <a class="layui-btn" href="javascript:void(0);" onclick="reportDownload(0);">下载报表</a>
            </div>
        </div>

        <div style="clear:both"></div>

        <table class="layui-table" >
            <thead>
            <tr>
                <th>日期</th>
                <th>游戏名称</th>
                <th>玩家总数</th>
                <th>新增玩家</th>
                <th>新增角色</th>

                <th>活跃玩家</th>
                <th>充值金额</th>
                <th>付费人数</th>
                <th>付费率</th>
                <th>ARPU</th>

                <th>ARPPU</th>
                <th>次留</th>
                <th>3留</th>
                <th>4留</th>
                <th>5留</th>

                <th>6留</th>
                <th>7留</th>
                <th>15留</th>
                <th>30留</th>
            </tr>
            </thead>

            <tbody id="viewer_wrap">
            {notempty name="list"} {volist name="list" id="vo"}
            <tr>
                <td>{$vo.day}</td>
                <td>{:empty($game_list[$vo.game_id])? '-' : $game_list[$vo.game_id]['name']}</td>
                <td>{$vo.reg_total}</td>
                <td>{$vo.reg_num}</td>
                <td>{$vo['role_num']}</td>

                <td>{$vo['act_num']}</td>
                <td>{$vo['recharge_num']}</td>
                <td>{$vo['pay_num']}</td>
                <td>{$vo['rate']}%</td>
                <td>{$vo['arpu']}</td>

                <td>{$vo['arppu']}</td>
                <td><span style=" color: #122cff">{$vo['one_stay']}</span> <span style="font-size: 8px; color: #6e5454">({$vo['one_stay_rate']}%)</span></td>
                <td><span style=" color: #122cff">{$vo['three_stay']}</span><span style="font-size: 8px; color: #6e5454">({$vo['three_stay_rate']}%)</span></td>
                <td><span style=" color: #122cff">{$vo['four_stay']}</span><span style="font-size: 8px; color: #6e5454">({$vo['four_stay_rate']}%)</span></td>
                <td><span style=" color: #122cff">{$vo['five_stay']}</span><span style="font-size: 8px; color: #6e5454">({$vo['five_stay_rate']}%)</span></td>

                <td><span style=" color: #122cff">{$vo['six_stay']}</span><span style="font-size: 8px; color: #6e5454">({$vo['six_stay_rate']}%)</span></td>
                <td><span style=" color: #122cff">{$vo['seven_stay']}</span><span style="font-size: 8px; color: #6e5454">({$vo['seven_stay_rate']}%)</span></td>
                <td><span style=" color: #122cff">{$vo['fifteen_stay']}</span><span style="font-size: 8px; color: #6e5454">({$vo['fifteen_stay_rate']}%)</span></td>
                <td><span style=" color: #122cff">{$vo['thirty_stay']}</span><span style="font-size: 8px; color: #6e5454">({$vo['thirty_stay_rate']}%)</span></td>
            </tr>
            {/volist} {/notempty}
            </tbody>
        </table>

        <div class="pager-container">
               <span>
                            新增玩家:{$total['reg_num']},新增角色:{$total['role_num']},活跃玩家:{$total['act_num']},充值金额:{$total['recharge_num']}元   {$list->total()}条记录
                        </span>

            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page"
                       onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>
    </form>

</div>

{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/lib/xm-select.js"></script>
<script>
    var tagData1 = {:json_encode($gameArr)}; // 获取到后台给出的数据-PHP写法
    var LeftData = ''
    var tagIns1 = xmSelect.render({
        el: '#game', // div的id值
        toolbar: { // 工具条【‘全选’，‘清空’】
            show: true, // 开启工具条
            showIcon: false, // 隐藏工具条的图标
        },
        autoRow: true,
        // tips: '选择校区', // 让默认值不是“请选择”，而是“选择校区”
        filterable: true, // 开启搜索模式，默认按照name进行搜索
        paging: true, // 启用分页
        pageSize: 100, // 每页的数据个数
        data: tagData1,
        prop: { // 也许你的数据库返回的并不是name和value, 也许你提交的时候不止name和value, 怎么办? 自定义就行
            name: 'name',
            value: 'id'
        },
        initValue: [{$Request.get.game_id}],
        on: function (data) {

            var arr = data.arr;
            if (!arr) {
                return;
            }
            gameArr = []
            for (i = 0; i < arr.length; i++) {
                gameArr[i] = arr[i].id;
            }
            $('.game_id').val(gameArr.toString())


        },
    })

    layui.use(['form', 'laydate'], function () {
        var laydate = layui.laydate;
        var form = layui.form;

        var starttime = laydate.render({
            elem: '#start',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });
    })

    function reportDownload(is_show)
    {

        $.ajax({
            type: "post",
            url: "{:url('retaineGame')}",
            async: false,
            data: $('.layui-form').serialize(),
            dataType: 'json',
            timeout: 5000,
            success: function (data) {

                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    }
</script>
{/block}
