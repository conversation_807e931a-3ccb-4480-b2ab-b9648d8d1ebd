{extend name="layout/content" /} {block name="header"}
<title>游戏推荐</title>
<style>
    .layui-form-select dl {
        z-index: 9999;
    }
</style>
{/block} {block name="content"}

<body>
    <div class="x-body">
        <form class="layui-form" action="{:url('add')}" method="post">
            <div class="layui-form-item">
                <label for="title" class="layui-form-label">公告标题：</label>
                <div class="layui-input-inline" style="width: 400px;">
                    <input type="text" name="title" id="title" required autocomplete="off" class="layui-input" placeholder="请输入标题">
                </div>
            </div>

            <div class="layui-form-item">
                <label for="is_show" class="layui-form-label">公告状态：</label>
                <div class="layui-input-inline">
                    <select name="is_show" id="is_show" lay-search lay-verify="required">
                        <option value="">请选择公告状态</option>
                        <option value="1">显示</option>
                        <option value="0">隐藏</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label for="is_eject" class="layui-form-label">弹出状态：</label>
                <div class="layui-input-inline">
                    <select name="is_eject" id="is_eject" lay-search lay-verify="required">
                        <option value="">请选择弹出状态</option>
                        <option value="1">弹出</option>
                        <option value="0">不弹出</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label for="show_time" class="layui-form-label">显示时间：</label>
                <div class="layui-input-inline">
                    <input type="text" name="show_time" id="show_time" required autocomplete="off" class="layui-input" value="{$showTime}" placeholder="请选择时间">
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label">内容：</label>
                <div class="layui-input-inline" style="width:50%">
                    <textarea name="content" id="J_content" rows="30" cols="100" lay-verify="required"></textarea>
                </div>
            </div>



            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn">提交</button>
                    <a href="javascript:;" onclick="back_info(this)" class="layui-btn layui-btn-primary">返回</a>
                </div>
            </div>


        </form>
    </div>
</body>
{/block} {block name="footer"}
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.config.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/lang/zh-cn/zh-cn.js"></script>
<script>
    layui.use(['form', 'layer', 'laydate', 'layedit'], function () {
        var ue = UE.getEditor('J_content');
        var laydate = layui.laydate;

        laydate.render({
            elem: '#show_time',
            type: 'date',
            format: 'yyyy-MM-dd HH:mm:ss',
            done: function (value, dates) {

            }
        });
    });

    function back_info(obj) {
        layer.confirm('是否放弃添加？', {
            shade: 0.4
        }, function (index) {
            location.href = '{:url("index")}';
        });
    }
</script>
{/block}