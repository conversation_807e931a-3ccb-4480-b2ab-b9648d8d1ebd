{extend name="layout/content" /} {block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>账号游戏冻结</title>
<style>
    label {

        padding-top: 10px;
        margin-left: 10px;

    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }
     .showPassword{
        cursor: pointer;
        text-decoration: underline;
        color: blue;
    }
</style>
{/block} {block name="content"}

<body>
    <div class="x-body">
        <form class="layui-form">
            <!-- <div> -->
            <!--     <div class="layui-inline"> -->
            <!--         <label for="end">子账户号：</label> -->
            <!--         <div class="layui-input-inline"> -->
            <!--             <input class="layui-input" placeholder="请输入子账户号" name="sub_username" value="{$Request.get.sub_username}"> -->
            <!--         </div> -->
            <!--     </div> -->

                <div class="layui-inline">
                    <label for="end">账号：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="请输入账号" name="username" value="{$Request.get.username}">
                    </div>
                </div>
                    
                <div class="layui-inline">
                    <label for="end">IMEI：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" value="{$Request.get.imeil}" name="imeil">
                    </div>
                </div>

                <div class="layui-inline">
                    <label for="end">关联游戏：</label>
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" />
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <label for="start">开始时间：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="开始日" value="{$start_time}" name="start_time" id="start_time">
                    </div>
                </div>

                <div class="layui-inline">
                    <label for="end">结束时间：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="截止日" value="{$end_time}" name="end_time" id="end_time">
                    </div>
                </div>

                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="sreach">查询</button>
                    <a href="{:url()}" class="layui-btn layui-btn-primary">重置</a>           
                </div>     
            </div>

        <table class="layui-table">
                <thead>
                    <tr>
                        <!-- <th>子账户号</th> -->
                        <th>账号</th>
                        <th>游戏名称</th>
                        <th>IMEI</th>
                        <th>累充金额</th>
                        <th>累充次数</th>
                        <th>账号状态</th>
                        <th>推广员账号</th>
                        <th>子会长</th>
                        <th>公会账号</th>
                        <th>注册时间</th>
                        <th>最后登录时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {notempty name="list"} {volist name="list" id="vo"}
                    <tr>
                        <!-- <td>{$vo.sub_username}</td> -->
                        <td id="J_td_{$i}"><a class="showPassword" href="javascript:void(0);" onclick="showUsername({$vo.mid},{$i})">{:stringObfuscation($vo.username)}</a></td>
                        <!-- <td>{$vo.username}</td> -->
                        <td>{$gList[$vo.gameid]??'--'}</td>
                        <td>{:escape($vo.imeil)}</td>
                        <td>{$vo.total_pay_amount}</td>
                        <td>{if(isset($payArr[$vo.id]))}{$payArr[$vo.id]['paycount']}{else}0{/if}</td>
                        <td>
                            {if condition="$vo['flag'] eq 0"}
                            <span class="badge badge-danger">正常</span>
                            {else}
                            <span class="badge badge-default">冻结</span>
                            {/if}
                        </td>
                        <td>{$vo.name}</td>
                        <td>{$vo.bchild_name}</td>
                        <td>{$vo.top_channel_name}</td>
                        <td>{:date('Y-m-d H:i:s',$vo.reg_time)}</td>
                        <td>{:date('Y-m-d H:i:s',$vo.login_time)}</td>
    
                        <td class="td-manage">
                            <a href="javascript:;" onclick="member_stop_open(this,'{:url(\'frozenSubaccount\',[\'id\'=>$vo.id,\'flag\'=>$vo.flag-1])}')" title="{if condition="$vo['flag'] eq 0 "}冻结{else} 解冻 {/if}" onclick="member_stop_open(this,'要冻结的id')">
                                <i class="layui-icon">&#xe640;</i> {if condition="$vo['flag'] eq 0"}冻结账号{else} 解冻账号 {/if}
                            </a>
                        </td>
                    </tr>
                    {/volist}
                    {else /}
                    <tr>
                        <td colspan="20" style="text-align: center;">暂无数据</td>
                    </tr>
                    {/notempty}
                </tbody>
    
            </table>
            
          

            <div class="pager-container">
                <span>
                        {$total}条记录
                </span>
                {$page}
                <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                    <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                </div>
            </div>

               
        </form>
        


        
    </div>

</body>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $("#gameid").FuzzySearch({
        inputID     : 'gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_search_submit',
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;

        var starttime = laydate.render({
            elem: '#start_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

    });
    
  	//显示全部玩家姓名
	function showUsername(userid,id)
	{
		$.ajax({
            type: "post",
            data: {'userid':userid},
            url: "{:url('frozenUsername')}",
            dataType: "json",
            timeOut: 10,
            success: function (result) {
                if(result.code) {
                	
                	$('#J_td_'+id).html(result.data);
               
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            },
            error: function () {
                layer.alert('网络错误，请刷新页面重试！', {icon: 2});
            }
        });
	}
</script>
{/block}