{extend name="layout/content" /} {block name="header"}
<title>账号角色查询</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    label {
        padding-top: 10px;
        margin-left: 10px;
    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }
</style>
{/block} {block name="content"}


<div class="x-body">

    <form class="layui-form" method="get">


        <div>



            <input type="hidden" name="action" value="search">

            <div class="layui-inline">
                <label for="game_id">游戏名：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='game_id' name="game_id" value="{$Request.get.game_id}" />
                    </div>
                </div>
            </div>


            <div class="layui-inline">
                <label for="">用户名：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="username" value="{$Request.get.username}" placeholder="请输入用户名">
                </div>
            </div>


            <div class="layui-inline">
                <label for="">角色名：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="rolename" value="{$Request.get.rolename}" placeholder="请输入角色名">
                </div>
            </div>



            <div class="layui-inline">
                <button class="layui-btn" lay-submit lay-filter="search" id="J_submit">查询</button>
            </div>



        </div>



        <table class="layui-table">
            <thead>
            <tr>
                <th>游戏名</th>
                <th>用户名</th>
                <th>区服ID</th>
                <th>区服名称</th>
                <th>角色ID</th>
                <th>角色名</th>
                <th>等级</th>
                <th>上传时间</th>
            </tr>
            </thead>
            <tbody>

            {notempty name="list"} {volist name="list" id="vo"}
            <tr>
                <td>{$vo.name}</td>
                <td>{$vo.username}</td>
                <td>{$vo.serverid}</td>
                <td>{$vo.servername}</td>
                <td>{$vo.roleid}</td>
                <td>{$vo.rolename}</td>
                <td>{$vo.rolelevel}</td>
                <td>{$vo.create_time? date('Y-m-d H:i:s',$vo.create_time) : '' }</td>
            </tr>
            {/volist}
            {else/}
            <tr>
                <td colspan="5" style="text-align: center;">暂无数据</td>
            </tr>
            {/notempty}
            </tbody>

        </table>





        <div class="pager-container">
                            <span>
                                {:empty($list_count)?0:$list->total()}条记录
                            </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>
    </form>




</div>

{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $("#game_id").FuzzySearch({
        inputID     : 'game_id',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
    searchBtn	:'J_submit',
    });

    layui.use(['form', 'layer'], function () {
        var form = layui.form,
            layer = layui.layer;

        form.on('submit(search)', function () {

            var gameId = $("#game_id").val(),
                username = $("input[name='username']").val(),
                rolename = $("input[name='rolename']").val();

            if (!gameId) {
                layer.msg('请选择游戏');
                return false;
            }
            /*
            if (username.length < 1 && rolename.length < 1) {
                layer.msg('请输入用户名或角色名');
                return false;
            }
            */
        })
    });
</script>
{/block}