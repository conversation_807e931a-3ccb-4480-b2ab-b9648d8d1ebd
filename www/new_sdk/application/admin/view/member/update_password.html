{extend name="layout/content" /}
{block name="header"}
<title>密码编辑</title>

<style>
    label {
        padding-top: 10px;
        margin-left: 10px;

    }
</style>
{/block}

{block name="content"}
<body>
<div class="x-body">
    <form class="layui-form" method="POST">


        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                账号:
            </label>
            <div class="layui-input-inline">
                <input type="text" value="{:escape($memberInfo.username)}" readonly="readonly"
                       class="layui-input">
            </div>
        </div>


        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>请填写密码:
            </label>
            <div class="layui-input-inline">
                <input type="password" id="password" name="password" value="" required="" lay-verify=""
                       autocomplete="off"
                       class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>请确认密码：
            </label>
            <div class="layui-input-inline">
                <input type="password" id="confirm_password" name="confirm_password" value="" required="" lay-verify=""
                       autocomplete="off"
                       class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" type="submit">提交</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn layui-btn-primary">返回
                </button>
            </div>
        </div>


    </form>
</div>


</body>
{/block}


{block name="footer"}{/block}