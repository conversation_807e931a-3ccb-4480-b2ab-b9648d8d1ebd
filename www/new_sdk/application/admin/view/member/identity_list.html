{extend name="layout/content" /} {block name="header"}
<title>实名认证用户列表</title>
<style>
    label {
        padding-top: 10px;
        margin-left: 10px;
    }
    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }
    section {
        margin-top: 10px;
        display: flex;
    }
    .showPassword{
        cursor: pointer;
        text-decoration: underline;
        color: blue;
    }
</style>
{/block} {block name="content"}
<body>
    <div class="x-body">
            <form class="layui-form">
                <div>
                    <div class="layui-inline">
                        <label for="playerAcount">玩家账号：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="" name="username" value="{$Request.get.username}" id="username">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label for="playerAcount">姓名：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" value="{$Request.get.realname}" name="realname">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label for="playerAcount">身份证号：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="" name="idcard" value="{$Request.get.idcard}" id="idcard">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="sreach">查询</button>
                        <a href="{:url()}" class="layui-btn layui-btn-primary">重置</a>
                    </div>
                </div>

                <table class="layui-table">
                        <thead>
                            <tr>
                                <th>UID</th>
                                <th>所属游戏</th>
                                <th>玩家账号</th>
                                <th>姓名</th>
                                <th>身份证号</th>
                            </tr>
                        </thead>
                        <tbody>
            
                            {notempty name="list"} {volist name="list" id="vo"}
                            <tr>
                                <td>{:escape($vo.uid)}</td>
                                <td>{:escape($vo.cg_name)??'-'}</td>
                                <td>
                                    <a class="showPassword" data-id="{$vo.uid}" data-name="username">
                                        {:escape(stringObfuscation($vo.username,3))}
                                    </a>
                                </td>
                                <td>{:escape($vo.realname)}</td>
                                <td>
                                    <a class="showPassword" data-id="{$vo.uid}" data-name="idcard">
                                        {:escape(substr_replace($vo.idcard,'********',6,8))}
                                    </a>
                                </td>
                            </tr>
                            {/volist}
                            {else /}
                            <tr>
                                <td colspan="5" style="text-align: center;">暂无数据</td>
                            </tr>
                            {/notempty}
                        </tbody>
            
                    </table>
            
                   
            
                    <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>





            </form>
      


     
     
    </div>

</body>
{/block} {block name="footer"}
<script>

    // 查看明文
    $(".showPassword").click(function (event) {
        var id = $(this).data('id');
        var file = $(this).data('name');
        var _that=$(this);
        $.ajax({
            type: "post",
            url: "{:url('identityPassword')}",
            /*async: false,*/
            data: {'id':id,'file':file},
            dataType: 'json',
            /*timeout: 5000,*/
            success: function (data) {
                if (data['code']) {
                    _that.text(data['data']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    })
</script>
{/block}