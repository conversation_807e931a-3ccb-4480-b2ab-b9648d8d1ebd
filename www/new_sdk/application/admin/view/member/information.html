{extend name="layout/content" /} {block name="header"}
<!-- <link rel="stylesheet" href="https://cdn.bootcss.com/bootstrap/3.3.7/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.bootcss.com/bootstrap-table/1.12.1/bootstrap-table.css"> -->
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/bootstrap-table.min.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">
<title>(账号)玩家信息汇总</title>
<style>
    label {
        padding-top: 10px;
        margin-left: 10px;
    }
	a:focus, a:hover {
	    text-decoration:none;
	}

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }
    .hide{
        display: none;
    }

    th,tr{
        text-align: center;
    }

    .detail-view td{
        text-align: left;
    }
    .margin-top-10{
        margin-top: 10px;
    }

    .table>tbody>tr>td, 
    .table>tbody>tr>th, 
    .table>tfoot>tr>td, 
    .table>tfoot>tr>th, 
    .table>thead>tr>td, 
    .table>thead>tr>th {
    padding: 8px;
    line-height: 1.42857143;
    vertical-align: middle;
    border-top: 1px solid #ddd;
}
    .showPassword{
        cursor: pointer;
        text-decoration: underline;
    }
    :hover .noLine{
        text-decoration-line: none;
    }
    .layui-inline{
        margin-top: 10px;
    }
	.check_photo+img {
	    display: none;
	}
	
	.modal-header {
	    background: #f8f8f8;
	    padding: 10px;
	}
	.modal-content {
	    border-radius: inherit;
	}
	fieldset {
	    padding: .35em .625em .75em;
	    margin: 0 2px;
	    border: 1px solid silver;
		margin-bottom: 20px;
	}
	legend {
	    border-bottom: none;
	    width: auto;
	    font-size: 16px;
	    color: silver;
	    padding: 0 10px;
	}
	fieldset ul li {
	    width: 46%;
	    margin-left: 2%;
	    float: left;
	    font-size: 14px;
	    line-height: 32px;
	}
	.layui-btn-xs {
	    margin-bottom: 5px;
	    height: 38px;
	    line-height: 38px;
	    padding: 0 8px;
	    font-size: 13px;
	}
</style>
{/block} {block name="content"}

<body>
    <div class="x-body">
        <blockquote class="layui-elem-quote" style="font-size: 13px;">查询数据为 cy_members(玩家账户表)</blockquote>

        <form class="layui-form" id="submit-form" style="margin: 10px 0;">

            <div>
                 <div class="layui-inline">
                     <label>用户ID：</label>
                     <div class="layui-input-inline">
                         <input class="layui-input" placeholder="请输入用户ID" name="id" value="{$Request.get.id}">
                     </div>
                 </div>

                 <div class="layui-inline">
                     <label>注册账号：</label>
                     <div class="layui-input-inline">
                         <input class="layui-input" placeholder="请输入注册账号" name="username" value="{$Request.get.username}">
                     </div>
                 </div>


                <div class="layui-inline">
                    <label>用户昵称：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="请输入用户昵称" name="nickname" value="{$Request.get.nickname}">
                    </div>
                </div>

                <div class="layui-inline">
                    <label>手机号：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="请输入手机号" name="mobile" value="{$Request.get.mobile}">
                    </div>
                </div>

                <div class="layui-inline">
                    <label>邮箱：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="请输入邮箱" name="email" value="{$Request.get.email}">
                    </div>
                </div>

                <div class="layui-inline">
                    <label>注册IP：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input"  name="ip" value="{$Request.get.ip}">
                    </div>
                </div>


                <!-- <div class="layui-inline"> -->
                <!--     <label>注册游戏：</label> -->
                <!--     <div class="layui-input-inline FuzzySearch_Container"> -->
                <!--         <div> -->
                <!--             <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" /> -->
                <!--         </div> -->
                <!--     </div> -->
                <!-- </div> -->

                <div class="layui-inline">
                    <label class="layui-form-label" style="font-weight: bold;width: 95px;">账号状态：</label>
                    <div class="layui-input-inline">
                        <select name="flag" >
                            <option value="">- 请选择状态 -</option>
                            <option  value="0" {if condition="$Request.get.flag eq '0'" }selected="selected" {/if}>正常</option>
                            <option value="1" {if condition="$Request.get.flag eq '1'" }selected="selected" {/if}>冻结</option>
                        </select>
                    </div>
                </div>

                <!-- <div class="layui-inline"> -->
                <!--     <label>推广员账号：</label> -->
                <!--     <div class="layui-input-inline FuzzySearch_Container"> -->
                <!--         <div> -->
                <!--             <input type="hidden" id='J_channelid' name="channel_id" value="{$Request.get.channel_id}" /> -->
                <!--         </div> -->
                <!--     </div> -->
                <!-- </div> -->
                <!-- <div class="layui-inline"> -->
                <!--     <label>公会账号：</label> -->
                <!--     <div class="layui-input-inline FuzzySearch_Container"> -->
                <!--         <div> -->
                <!--             <input type="hidden" id='J_parent_channel_id' name="parent_channel_id" value="{$Request.get.parent_channel_id}" /> -->
                <!--         </div> -->
                <!--     </div> -->
                <!-- </div> -->
                <!-- <div class="layui-inline"> -->
                <!-- 	<label>商务账号：</label> -->
                <!-- 	<div class="layui-input-inline"> -->
                <!-- 	  <select name="bplus_channel_id" lay-filter="level" value="{:input('request.bplus_channel_id')}"> -->
                <!-- 		<option value="">选择商务</option> -->
                <!-- 		{volist name="uniom" id="vo"} -->
                <!-- 			<option value="{$vo.id}" {if condition="input('request.bplus_channel_id') eq $vo.id" }selected="selected" {/if} >{$vo.name}</option> -->
                <!-- 		{/volist} -->
                <!-- 	  </select> -->
                <!-- 	</div> -->
                <!-- </div> -->

                <div class="layui-inline">
                    <label>注册时间：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="" value="{$start_time}" name="start_time" id="start_time" autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label>-</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="" value="{$end_time}" name="end_time" id="end_time" autocomplete="off">
                    </div>
                </div>

                    <!-- 跳转用 -->
                    <div class="layui-inline">
                        <input type="hidden" name="page">
                    </div>


                    <div class="layui-inline">
                        <button class="layui-btn mg_L" id="J_search_submit" lay-submit lay-filter="">查询</button>
                    </div>

                    <div class="layui-inline">
                        <a href="{:url()}" class="layui-btn layui-btn-primary">重置</a>
                    </div>



                <!--<input type="text" name="complex_username" placeholder="请输入聚合账号" autocomplete="off" value="{$Request.get.complex_username}" class="layui-input mg_L">-->
                    </div>


                    <!--
                    <p style="margin-top:10px;">
                        <span style="color: red">*</span><a class="noLine" style="color: #666"> 单次查询日期的最长跨度为31天，用户ID、注册账号、用户昵称，查询不受时间限制</a>
                    </p>
                    -->
        </form>

        <!-- <table class="layui-table"> data-toolbar="#toolbar"-->
        <table id="objTable"  >
            <thead>
                <tr>
                    <th><input type="checkbox" name="check_all" value=""></th>
                    <th>用户ID</th>
                    <th>注册账号</th>
                    <!--<th>聚合账号</th>-->
					<th>用户昵称</th>
                    <!-- <th>注册游戏</th> -->
					<th>注册IP</th>
					<th>注册时间</th>
					<th>最近登录时间</th>

					<!-- <th>推广员账号</th> -->
                    <!-- <th>子会长</th> -->
                    <!-- <th>公会账号</th> -->
					<!-- <th>商务账号</th> -->

					<th>账号状态</th>
                    <th>账号信息</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>

                {notempty name="list"} {volist name="list" id="vo"}
                <tr>
                    <td><input type="checkbox" name="check_item" value="{$vo.id}"></td>
					<td>{$vo.id}</td>
                    <td>
                        <a class="showPassword" data-id="{$vo.id}" data-name="username">
                            {:escape($vo.mdusername)}
                        </a>
                    </td>
					<td>{$vo.nickname}</td>
                    <!-- <td>{$game_list[$vo.gameid]??''}</td> -->
					<td>{$vo.ip}</td>
					<td>{:date('Y-m-d H:i:s',$vo.reg_time)}</td>
					<td>{if condition="$vo['login_time'] gt 0"}{:date('Y-m-d H:i:s',$vo.login_time)}{else}--{/if}</td>
					<!-- <td>{$vo.dep_name}</td> -->
                    <!-- <td>{$vo.bchild_name}</td> -->
                    <!-- <td>{$vo.top_channel_name}</td> -->
					<!-- <td>{$vo.union_name}</td> -->

					<td class="account_status">
					    {if condition="$vo['flag'] eq 0"}
					    <span class="layui-btn  layui-btn-radius layui-btn-normal">正常</span>
					    {else}
					    <span class="layui-btn  layui-btn-radius layui-btn-danger">冻结</span>
					    {/if}
					</td>
					
                    <td>
						<a href="javascript:;" title="显示账号详情" class="J_show_pointHistory" data-id="{$vo.id}">
						    <span class="layui-btn layui-btn-normal layui-btn-xs">账号详细</span>
						</a>
						<a href="javascript:;" class="J_show_login" data-name="{$vo.username}" >
						    <span class="layui-btn layui-btn-normal layui-btn-xs" >登录记录</span>
						</a>
						<a href="javascript:;" class="J_show_pay" data-name="{$vo.username}" >
						    <span class="layui-btn layui-btn-normal layui-btn-xs" >充值记录</span>
						</a>
						<a href="javascript:;" title="显示冻结记录" class="J_show_frozen" data-id="{$vo.id}" >
						    <span class="layui-btn layui-btn-normal layui-btn-xs" >冻结记录</span>
						</a>
						<a href="javascript:;" title="显示修改记录" class="J_show_modify" data-id="{$vo.id}">
						    <span class="layui-btn layui-btn-normal layui-btn-xs">修改记录</span>
						</a>
					</td>
                   

                    <td class="td-manage">
                     
                        <a href="javascript:;" onclick="member_stop(this,'{:url(\'informationFrozen\',[\'id\'=>$vo.id,\'flag\'=>$vo.flag-1])}')" title="{if condition="$vo['flag'] eq 0"}冻结{else}解冻{/if}" onclick="member_stop(this,'要冻结的id')">
                            {if condition="$vo['flag'] eq 0"}
                                <span class="layui-btn layui-btn-normal layui-btn-xs">冻结账号</span>
                            {else}
                                <span class="layui-btn layui-btn-normal layui-btn-xs layui-btn-danger">解冻账号</span>
                            {/if}
                        </a>
						<a title="查看" href="{:url('Member/updateInformationPassword',['id'=>$vo.id])}">
						     <span class="layui-btn layui-btn-normal layui-btn-xs">  修改密码</span>
						</a>	
                    </td>

					

                </tr>
                {/volist}
                {else/}
				<tr>
				    <td colspan="20" style="text-align: center;">暂无数据</td>
				</tr>
				
                {/notempty}
            </tbody>

        </table>

        <div class="demoTable margin-top-10">
            <button class="layui-btn layui-btn-danger" data-type="getCheckData" id="multi_frozen">批量冻结</button>
            <button class="layui-btn layui-btn-normal" data-type="getCheckData" id="multi_unfrozen">批量解冻</button>
        </div>

		<div class="pager-container margin-top-10">
            <span>
                {$total}条记录
            </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page_show" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>
    </div>


    <!-- 静态模态框（Model） 账号详情 -->
    <div class="modal fade" id="J_show_pointHistory_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" id="viewer_wrap" style="width: 700px;">
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title point_title">账号详情</h4>
                </div>

                <div class="modal-body">
                    <fieldset>
                     <legend>注册信息:</legend>
                     <ul id="user_basic">
                         <li class="basic_id"></li>
                         <li>账号：<a class="showPassword basic_username" data-id="" data-name="username"></a></li>
                         <li class="basic_gamename"></li>
                         <li class="basic_nickname"></li>
                         <li class="basic_ip"></li>
                         <li class="basic_last_ip"></li>
                         <li class="basic_imeil"></li>
                         <li class="basic_last_imeil"></li>
                         <li class="basic_reg_time"></li>
                         <li class="basic_last_login_time"></li>

                         <li class="basic_dep_name"></li>
                         <li class="basic_top_channel_name"></li>
                         <li class="basic_total_pay_amount"></li>
                         <li class="basic_flag"></li>

                     </ul>
                    </fieldset>

                    <fieldset >
                     <legend>个人信息:</legend>
                     <ul>
                         <li>手机号：<a class="showPassword basic_mobile" data-id="" data-name="mobile"></a></li>
                         <li>邮箱：<a class="showPassword basic_email" data-id="" data-name="email"></a></li>
                         <li class="basic_realname"></li>
                         <li>身份证号：<a class="showPassword basic_idcard" data-id="" data-name="idcard"></a></li>
                         <li class="basic_sex"></li>
                         <li>头像：<a href="javascript:;" class="check_photo">点击查看</a><img data-original="" src="" style="display: none;" class="basic_avatar"></li>
                         <li class="basic_birthday"></li>
                         <li class="basic_qq"></li>
                         <li class="basic_address"></li>
                         <li class="basic_zipcode"></li>
                     </ul>
                    </fieldset>

                </div>

                <input type="hidden" name="id" value="0">


            </div><!-- /.modal-content -->
        </div>
    </div>
</body>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js"></script>
<!-- <script type="text/javascript" src="https://cdn.bootcss.com/bootstrap-table/1.12.1/bootstrap-table.js"></script> -->
<script type="text/javascript" src="__STATIC__/js/bootstrap-table.min.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script>
    

   // $("#gameid").FuzzySearch({
   //      inputID     : 'gameid',
   //      title   	: '请输入游戏名称',
   //      data        :{:json_encode($glist)},
   //      searchBtn	:'J_search_submit',
   //  });
    // $("#J_channelid").FuzzySearch({
    //     inputID    : 'J_channelid',
    //     title   	: '请输入推广员账号',
    //     searchBtn	:'J_search_submit',
    //     ajaxUrl	: '{:url("Pay/ajaxGetNoComplexChannel")}',
    // });
    //
    // $("#J_parent_channel_id").FuzzySearch({
    //     inputID    : 'J_parent_channel_id',
    //     title   	: '选择公会账号',
    //     searchBtn	:'J_search_submit',
    //     ajaxUrl	: '{:url("Pay/ajaxGetNoComplexChannel")}',
    // });

    var $table = $('#objTable');
    $table.bootstrapTable({});


    // $("#J_search_submit").on("mouseover",function () {
    //     var page=$("input[name=page_show]").val();
    //     if(page!=""){
    //         $("input[name=page]").val(page);
    //     }
    // })

    $("#J_search_submit").on("mouseover",function () { 
        if($("input[name=page_show]").val()!==""){
            var page_show=$("input[name=page_show]").val();
            $("input[name=page]").val(page_show)
        }
    })

    $(document).keydown(function(event){
　　　　if(event.keyCode == 13){
            if($("input[name=page_show]").val()!==""){
                var page_show=$("input[name=page_show]").val();
                $("input[name=page]").val(page_show)
            }
            $("#submit-form").submit()
　　　　}
　　});



    layui.use(['laydate', 'form'], function () {
        var laydate = layui.laydate;
        var form = layui.form;

        var starttime = laydate.render({
            elem: '#start_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
		
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });
		
		var starttime2 = laydate.render({
		    elem: '#starttime',
		    type: 'date',
		    format: 'yyyy-MM-dd',
		    done: function (value, dates) {
		        endtime.config.min = {
		            year: dates.year,
		            month: dates.month - 1, //关键
		            date: dates.date,
		            hours: 0,
		            minutes: 0,
		            seconds: 0
		        };
		    }
		});
		
		var endtime2 = laydate.render({
		    elem: '#endtime',
		    type: 'date',
		    format: 'yyyy-MM-dd',
		    done: function (value, dates) {
		        starttime.config.max = {
		            year: dates.year,
		            month: dates.month - 1, //关键
		            date: dates.date,
		            hours: 0,
		            minutes: 0,
		            seconds: 0
		        }
		    }
		});
		
		var starttime3 = laydate.render({
		    elem: '#starttime3',
		    type: 'date',
		    format: 'yyyy-MM-dd',
		    done: function (value, dates) {
		        endtime.config.min = {
		            year: dates.year,
		            month: dates.month - 1, //关键
		            date: dates.date,
		            hours: 0,
		            minutes: 0,
		            seconds: 0
		        };
		    }
		});
		
		var endtime3 = laydate.render({
		    elem: '#endtime3',
		    type: 'date',
		    format: 'yyyy-MM-dd',
		    done: function (value, dates) {
		        starttime.config.max = {
		            year: dates.year,
		            month: dates.month - 1, //关键
		            date: dates.date,
		            hours: 0,
		            minutes: 0,
		            seconds: 0
		        }
		    }
		});

        // 页面渲染后，在加载渠道，提升速度
        window.onload = function () {
            $.getJSON("{:url('ajaxGetChannel')}", function (result) {
                if (result.code) {
                    var html = '';

                    for (var i in result.data) {
                        var item = result.data[i];
                        html += '<option value="' + item.id + '">' + item.name + '</option>';
                    }

                    if (html) {
                        $("select[name='channel_id']").append(html);
                        form.render('select', 'channel');
                    }
                }
            });
        };
    });

    /*用户-停用*/
    function member_stop(obj, url) {
        layer.confirm('确认要冻结或者解冻吗？', {
            shade: 0.4
        }, function (index) {
            $.getJSON(url, function (res) {

                if (res.code) {
                    layer.msg(res.msg, {
                        icon: 1,
                        shade: 0.4,
                        time: 1000
                    }, function () {
                        location.href = res.url;
                    });
                } else {
                    layer.msg(res.msg, {
                        icon: 1,
                        shade: 0.4,
                        time: 1000
                    });
                }
            });

        });
    }

   


    // 冻结、解冻 统一处理
    function handleFrozen(url, html) {
        var ids = getCheckedId();
        var _that = $(this);

        if (!ids) {
            layer.msg('请先选择账号');
            return false;
        }

        _that.addClass('layui-btn-disabled');

        $.getJSON(url, {
            ids: ids
        }, function (result) {
            layer.msg(result.msg);
            _that.removeClass('layui-btn-disabled');
            window.location.reload();
            return false;
            /*if (result.code) {
                for(var i=0; i< inputs.length; i++) {
                    if (inputs[i].checked) {
                        inputs[i].checked = false;
                        $(inputs[i]).parents('tr').find('.account_status').html(html);
                    }
                }
            }*/
        });
    }

  

   
    // 查看明文
    $(".showPassword").click(function (event) {
        var id = $(this).data('id');
        var file = $(this).data('name');
        var _that=$(this);
        $.ajax({
            type: "post",
            url: "{:url('infomationPassword')}",
            /*async: false,*/
            data: {'id':id,'file':file},
            dataType: 'json',
            /*timeout: 5000,*/
            success: function (data) {
                if (data['code']) {
                    _that.text(data['data']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    })
	
	
	
	
	
	//【账号详情】
	$('.J_show_pointHistory').on('click', function () {
	    var id = $(this).data('id');
	    $.ajax({
	    	type: "post",
            data: {'id':id},
            url: "{:url('userInfo')}",
            dataType: "json",
            timeOut: 10,
            success: function (res) {
                var trStr = "",trStr2 = "";

                if(res.code == 0){
                	toastr.error('用户不存在');
                }
                // 注册信息
                $(".basic_id").html("用户ID：" + res.data.id);
                $(".basic_username").data("id",res.data.id);
                $(".basic_username").html(res.data.username); 
                $(".basic_gamename").html("注册游戏：" + res.data.gamename); 
                $(".basic_nickname").html("昵称：" + res.data.nickname); 
                $(".basic_ip").html("注册IP：" + res.data.ip); 
                $(".basic_last_ip").html("最后登录IP：" + res.data.last_ip); 
                $(".basic_imeil").html("注册IMEI：" + "<span style='font-size: 10px;'>"+res.data.imeil+"</span>");
                $(".basic_last_imeil").html("最后登录IMEI：" + "<span style='font-size: 10px;'>"+res.data.last_imeil+"</span>");
                $(".basic_reg_time").html("注册时间：" + res.data.reg_time); 
                $(".basic_last_login_time").html("最后登录时间：" + res.data.last_login_time); 
                $(".basic_dep_name").html("推广员账号：" + res.data.dep_name); 
                $(".basic_top_channel_name").html("公会账号：" + res.data.top_channel_name);
                $(".basic_total_pay_amount").html("累充金额：" + res.data.total_pay_amount); 
                $(".basic_flag").html("账号状态：" + res.data.flag);


                // 个人信息
                $(".basic_mobile").data("id",res.data.id);
                $(".basic_mobile").html(res.data.mobile); 
                $(".basic_email").data("id",res.data.id);
                $(".basic_email").html(res.data.email); 
                $(".basic_idcard").data("id",res.data.id);
                $(".basic_idcard").html(res.data.idcard); 
                $(".basic_realname").html("真实姓名：" + res.data.realname); 
                $(".basic_sex").html("性别：" + res.data.sex); 
                $(".basic_birthday").html("生日：" + res.data.birthday); 
                $(".basic_qq").html("QQ：" + res.data.qq); 
                $(".basic_address").html("联系地址：" + res.data.address); 
                $(".basic_zipcode").html("邮政编码：" + res.data.zipcode); 
                $(".basic_reg_time").html("注册时间：" + res.data.reg_time); 
                $(".basic_last_login_time").html("最后登录时间：" + res.data.last_login_time); 
                $(".basic_dep_name").html("推广员账号：" + res.data.dep_name); 
				
                if(res.data.avatar == ""){
                      $(".basic_avatar").prev().hide();
                  }else{
                      $(".basic_avatar").prev().show();
                      $(".basic_avatar").attr('src',res.data.avatar);
				  }
                $(".point_title").text('账号详情');
	    		$('#J_show_pointHistory_modal').modal();
            },
            error: function () {
                toastr.error('网络错误，请刷新页面重试！');
            }

	    });
	    
	});

	//【登录记录】
	$('.J_show_login').on('click', function () {

		var myDate = new Date;
    	var year = myDate.getFullYear(); //获取当前年
    	var mon = myDate.getMonth() + 1; //获取当前月
    	var day = myDate.getDate(); //获取当前日

    	var today = year + '-' + mon + '-' + day;
		//iframe窗
		var name = $(this).data('name');
		var toUrl = "{:url('Member/loginList')}?start_time=&end_time="+today+"&username=" + name;

		layer.open({
		  type: 2,
		  title: '登录记录',
		  shadeClose: true,
		  shade: 0.8,
		  area: ['85%', '80%'],
		  content: toUrl //iframe的url
		}); 
		 // $(".point_title").text('冻结记录');
	  //   $('#J_show_frozen_modal').modal();
	});


	//【充值记录】
	$('.J_show_pay').on('click', function () {
		var myDate = new Date;
    	var year = myDate.getFullYear(); //获取当前年
    	var mon = myDate.getMonth() + 1; //获取当前月
    	var day = myDate.getDate(); //获取当前日

    	var today = year + '-' + mon + '-' + day;
		//iframe窗
		var name = $(this).data('name');
		var toUrl = "{:url('pay/index')}?start=&end="+today+"&username=" + name;

		layer.open({
		  type: 2,
		  title: '充值记录',
		  shadeClose: true,
		  shade: 0.8,
		  area: ['85%', '80%'],
		  content: toUrl //iframe的url
		}); 
		 // $(".point_title").text('冻结记录');
	  //   $('#J_show_frozen_modal').modal();
	});


	
	//【冻结记录】
	$('.J_show_frozen').on('click', function () {
		//iframe窗
		var id = $(this).data('id');
		var toUrl = "{:url('frozenInfo',['ids'=>'NMID'])}";
     	toUrl = toUrl.replace('NMID',id)

		layer.open({
		  type: 2,
		  title: '冻结记录',
		  shadeClose: true,
		  shade: 0.8,
		  area: ['85%', '80%'],
		  content: toUrl //iframe的url
		}); 
		 // $(".point_title").text('冻结记录');
	  //   $('#J_show_frozen_modal').modal();
	});
	
	
	//【修改记录】
	$('.J_show_modify').on('click', function () {

		var id = $(this).data('id');
		var toUrl = "{:url('membersHistory',['ids'=>'NMID'])}";
     	toUrl = toUrl.replace('NMID',id)

		layer.open({
		  type: 2,
		  title: '修改记录',
		  shadeClose: true,
		  shade: 0.8,
		  area: ['85%', '80%'],
		  content: toUrl //iframe的url
		}); 
	});
	
	
	var viewer = new Viewer(document.getElementById('viewer_wrap'), {
	    url: 'data-original'
	});
	
	$(document).ready(function () {
	
	    $(".check_photo").on("click", function () {
	        $(this).siblings().click()
	    })
	
	});


   /**
    * 获取选中id
    * @returns {string}
    */
   function getCheckedId() {
       var ids = '';
       for (var i = 0; i < inputs.length; i++) {
           if (inputs[i].checked) {
               ids ? ids += ',' : '';
               ids += inputs[i].value;
           }
       }

       return ids;
   }

   // 冻结、解冻 统一处理
   function handleFrozen(url, html) {
       var ids = getCheckedId();
       var _that = $(this);

       if (!ids) {
           layer.msg('请先选择账号');
           return false;
       }

       _that.addClass('layui-btn-disabled');

       $.getJSON(url, {
           ids: ids
       }, function (result) {
           layer.msg(result.msg);
           _that.removeClass('layui-btn-disabled');
           window.location.reload();
           return false;
           /*if (result.code) {
               for(var i=0; i< inputs.length; i++) {
                   if (inputs[i].checked) {
                       inputs[i].checked = false;
                       $(inputs[i]).parents('tr').find('.account_status').html(html);
                   }
               }
           }*/
       });
   }
   // 批量解冻
   $('#multi_unfrozen').on('click', function () {
       layer.confirm('确定批量解冻账户？', function () {
           var url = "{:url('batchMemberThaw')}";
           var html = '<span class="layui-btn  layui-btn-radius layui-btn-normal">正常</span>';
           handleFrozen(url, html);
       });

   });
   // 批量冻结
   $('#multi_frozen').on('click', function () {
       layer.confirm('确定批量冻结账户？', function () {
           var url = "{:url('batchMemberFreeze')}";
           var html = '<span class="layui-btn  layui-btn-radius layui-btn-danger">冻结</span>';
           handleFrozen(url, html);
       });

   });

   // ## 选中按钮管理 ##
   var inputs = document.querySelectorAll('input[type=checkbox]');
   // 【全选】
   function checkAll() {
       var num = 0;
       for (var i = 1; i < inputs.length; i++) {
           // 如果当前选框被选中，那么个数+1
           if (inputs[i].checked == true) {
               num++;
           }
       }

       inputs[0].checked = (num === inputs.length - 1);
   }
   // 对【全选】input做监听  更改全选/非全选状态和文字
   inputs[0].onclick = function () {
       for (var i = 1; i < inputs.length; i++) {
           // 将全选框的状态赋给每一个选项框
           inputs[i].checked = this.checked;
       }
       // 改状态
       checkAll();
   };
   // 对每一个input做监听事件
   for (var i = 1; i < inputs.length; i++) {
       inputs[i].onclick = function () {
           // 改状态
           checkAll();
       }
   }

</script>
{/block}