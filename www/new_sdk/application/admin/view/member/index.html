{extend name="layout/content" /}

{block name="header"}
<!-- <link rel="stylesheet" href="https://cdn.bootcss.com/bootstrap/3.3.7/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.bootcss.com/bootstrap-table/1.12.1/bootstrap-table.css"> -->
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/bootstrap-table.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>(子账户)玩家注册归属(子账户)</title>
<style>
    label {
        padding-top: 10px;
        margin-left: 10px;
    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }

    .hide {
        display: none;
    }

    th, tr {
        text-align: center;
    }

    .detail-view td {
        text-align: left;
    }

    .margin-top-10 {
        margin-top: 10px;
    }

    .table > tbody > tr > td,
    .table > tbody > tr > th,
    .table > tfoot > tr > td,
    .table > tfoot > tr > th,
    .table > thead > tr > td,
    .table > thead > tr > th {
        padding: 8px;
        line-height: 1.********;
        vertical-align: middle;
        border-top: 1px solid #ddd;
    }

    .showPassword {
        cursor: pointer;
        text-decoration: underline;
    }

    :hover .noLine {
        text-decoration-line: none;
    }

    .layui-inline {
        margin-top: 5px;
    }

    .check_photo + img {
        display: none;
    }
</style>
{/block}

{block name="content"}
<body>
<div class="x-body">
    <blockquote class="layui-elem-quote" style="font-size: 13px;">查询数据为 nw_subaccount(子账户表)</blockquote>

    <form class="layui-form" id="submit-form" style="margin-bottom: 10px;">
        <div class="layui-inline">
            <label>商务：</label>
            <div class="layui-input-inline layui-form">
                <select name="tg_business" lay-filter="tg_business" id="tg_business" lay-search>
                    <option value="">请选择商务</option>
                </select>
            </div>

            <label>会长：</label>
            <div class="layui-input-inline">
                <select name="tg_president" lay-filter="tg_president" id="tg_president" lay-search>
                    <option value="">请选择会长</option>
                </select>
            </div>

            <label>子会长：</label>
            <div class="layui-input-inline">
                <select name="tg_president_son" lay-filter="tg_president_son" id="tg_president_son" lay-search>
                    <option value="">请选择子会长</option>
                </select>
            </div>

            <label>推广员：</label>
            <div class="layui-input-inline">
                <select name="tg_promoter" lay-filter="tg_promoter" id="tg_promoter" lay-search>
                    <option value="">请选择推广员</option>
                </select>
            </div>

            <div class="layui-inline">
                <label>开始时间：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="开始日" value="{$start_time}" name="start_time" id="start_time" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <label>结束时间：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="截止日" value="{$end_time}" name="end_time" id="end_time" autocomplete="off">
                </div>
            </div>
        </div>


        <div class="layui-inline">
            <div class="layui-inline">
                <label>关联游戏：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='gameid' name="sub_gameid" value="{$Request.get.sub_gameid}"/>
                    </div>
                </div>
            </div>

            <!-- <div class="layui-inline"> -->
            <!--     <label>子账号ID：</label> -->
            <!--     <div class="layui-input-inline"> -->
            <!--         <input class="layui-input" placeholder="请输入子账户ID" name="sub_id" value="{$Request.get.id}"> -->
            <!--     </div> -->
            <!-- </div> -->

            <!-- <div class="layui-inline"> -->
            <!--     <label>子账号名：</label> -->
            <!--     <div class="layui-input-inline"> -->
            <!--         <input class="layui-input" placeholder="请输入子账户名" name="sub_username" value="{$Request.get.id}"> -->
            <!--     </div> -->
            <!-- </div> -->

            <div class="layui-inline">
                <label>注册昵称：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="请输入注册昵称" name="m_nickname" value="{$Request.get.m_nickname}">
                </div>
            </div>

            <div class="layui-inline">
                <label>注册账号：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="请输入注册账号" name="m_username" value="{$Request.get.m_username}">
                </div>
            </div>

            <div class="layui-inline">
                <label>账号状态：</label>
                <div class="layui-input-inline">
                    <select name="sub_flag" style="">
                        <option value="" {eq name="$Request.get.flag" value="" } selected {/eq} >- 请选择状态 -</option>
                        <option value="0" {eq name="$Request.get.flag" value="0" } selected {/eq}>正常</option>
                        <option value="1" {eq name="$Request.get.flag" value="1" } selected {/eq}>冻结</option>
                    </select>
                </div>
            </div>

            <!-- 跳转用 -->
            <div class="layui-inline">
                <input type="hidden" name="page">
            </div>

            <div class="layui-inline">
                <button class="layui-btn mg_L" id="J_search_submit" lay-submit lay-filter="">查询</button>
                <a href="{:url()}" class="layui-btn layui-btn-primary">重置</a>
                <a class="layui-btn layui-btn-normal download" data-url="1" href="javascript:;">下载报表</a>
                <a class="layui-btn layui-btn-normal download" data-url="2" href="javascript:;">下载报表(高级)</a>
                <a href="{$jh_url}" class="layui-btn" target="_blank">查看聚合数据</a>
            </div>

            <!--<input type="text" name="complex_username" placeholder="请输入聚合账号" autocomplete="off" value="{$Request.get.complex_username}" class="layui-input mg_L">-->
        </div>

        <!--
        <p style="margin-top:10px;">
            <span style="color: red">*</span><a class="noLine" style="color: #666"> 单次查询日期的最长跨度为31天</a>
        </p>
        -->
    </form>

    <!-- <table class="layui-table"> data-toolbar="#toolbar"-->
    <table id="objTable" data-detail-view="true" data-detail-formatter="detailFormatter">
        <thead class="handleThead">
            <tr>
                <th><input type="checkbox" name="check_all" value="1"></th>
                <!-- <th>子账户ID</th> -->
                <!-- <th>子账户</th> -->
                <th>子账号ID</th>
                <th>主账号ID</th>
                <th>主账号名</th>
                <!--<th>聚合账号</th>-->
                <th>昵称</th>
                <!-- <th>头像</th> -->
                <th>游戏ID</th>
                <th>游戏名称</th>
                <th>累充金额</th>
                <th>账号状态</th>
                <th>是否实名</th>

                <th>推广员账号</th>
                <th>子会长</th>
                <th>公会账号</th>
                <th>商务账号</th>

                <th>注册时间</th>
                <th>最近登录时间</th>
                <th>操作</th>

                <th class="hide">imeil码</th>
                <th class="hide">累积充值次数</th>
                <th class="hide">角色名</th>
                <th class="hide">区服</th>
                <th class="hide">等级</th>
                <th class="hide">IP</th>
            </tr>
        </thead>

        <tbody id="viewer_wrap">
        {notempty name="list"} {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="check_item" value="{$vo.id}"></td>
                <!-- <td>{$vo.id}</td> -->
                <!-- <td>{$vo.sub_username}</td> -->
                <td>{$vo.id}</td>
                <td>{$vo.mid}</td>
                <td>{$vo.username}</td>
                <td>{$vo.nickname}</td>
                <!-- <td> -->
                <!--     {if condition="$vo['avatar']"} -->
                <!--     <a href="javascript:;" class="check_photo">点击查看</a> -->
                <!--     <img data-original="{$vo.avatar}" src="{$vo.avatar}" alt="{$vo.nickname}"> -->
                <!--     {/if} -->
                <!-- </td> -->
                <td>{$vo.gameid}</td>
                <td>{$game_list[$vo.gameid]??''}</td>
                <td>{$vo.total_pay_amount}</td>
                <td class="account_status">
                    {if condition="$vo['flag'] eq 0"}
                    <span class="layui-btn  layui-btn-radius layui-btn-normal">正常</span>
                    {else}
                    <span class="layui-btn  layui-btn-radius layui-btn-danger">冻结</span>
                    {/if}
                </td>
                <td class="account_status">
                    {if condition="$vo['real_name'] neq ''"}
                    <span class="layui-btn  layui-btn-radius layui-btn-normal">是</span>
                    {else}
                    <span class="layui-btn  layui-btn-radius layui-btn-danger">否</span>
                    {/if}
                </td>

                <td>{$vo.channel_three_name}</td>
                <td>{$vo.channel_two_name}</td>
                <td>{$vo.channel_one_name}</td>
                <td>{$vo.channel_zero_name}</td>

                <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                <td>{if condition="$vo['login_time'] gt 0"}{:date('Y-m-d H:i:s',$vo.login_time)}{else}--{/if}</td>
                <td class="td-manage" style="text-align: left;">
                    <!-- <a title="查看" href="{:url('Member/updatePassword',['id'=>$vo.mid])}"> -->
                    <!--     <i class="layui-icon">&#xe63c;</i>修改密码 -->
                    <!-- </a> -->
                    <a href="javascript:;" onclick="member_stop(this,'{:url(\'frozenSubaccount\',[\'id\'=>$vo.id,\'flag\'=>$vo.flag-1])}')" title="{if condition=" $vo['flag'] eq 0"}冻结{else}解冻{/if}" onclick="member_stop(this,'要冻结的id')">
                        {if condition="$vo['flag'] eq 0"}
                            <i class="layui-icon">&#xe640;</i><span>冻结账号</span>
                        {else}
                            <i class="layui-icon" style="color:#FF5722;">&#xe640;</i><span style="color:#FF5722;">解冻账号</span>
                        {/if}
                    </a>
                    <!-- {if condition="$vo['avatar']"} -->
                    <!--     <a href="javascript:;" onclick="del_avatar(this,'{:url(\'delAvatar\',[\'id\'=>$vo.mid])}')" title="删除头像"> -->
                    <!--         <i class="layui-icon">&#xe640;</i> 删除头像 -->
                    <!--     </a> -->
                    <!-- {/if} -->

                    <!-- {if condition="$vo['real_name'] neq ''"} -->
                    <!--     <a href="javascript:;" onclick="member_clear(this,'{:url(\'clearRealName\',[\'id\'=>$vo.mid])}')" -->
                    <!--        title="清空实名"> -->
                    <!--         <i class="layui-icon">&#xe640;</i> 清空实名 -->
                    <!--     </a> -->
                    <!-- {/if} -->
                </td>

                <td>{$vo.imeil}</td>
                <td>{$vo.total_pay_cnt}</td>
                <td>{$vo.rolename}</td>
                <td>{$vo.servername}</td>
                <td>{$vo.rolelevel}</td>
                <td>{$vo.ip}</td>

            </tr>
        {/volist}
        {else/}
        <tr>
            <td colspan="20" style="text-align: center;">暂无数据</td>
        </tr>
        {/notempty}
        </tbody>

    </table>

    <div class="demoTable margin-top-10">
        <button class="layui-btn layui-btn-danger" data-type="getCheckData" id="multi_frozen">批量冻结</button>
        <button class="layui-btn layui-btn-normal" data-type="getCheckData" id="multi_unfrozen">批量解冻</button>
    </div>

    <div class="pager-container margin-top-10">
            <span>
                {$total}条记录
            </span>
        {$page}
        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page_show"
                   onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
        </div>
    </div>


</div>

</body>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js"></script>
<!-- <script type="text/javascript" src="https://cdn.bootcss.com/bootstrap-table/1.12.1/bootstrap-table.js"></script> -->
<script type="text/javascript" src="__STATIC__/js/bootstrap-table.min.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script>

    $(document).ready(function() {
        $(".handleThead tr").find("th:eq(6)").hide()//隐藏表头
        $("#viewer_wrap tr").find("td:eq(6)").hide();//隐藏表头下面对应的子集
    })

    $("#gameid").FuzzySearch({
        inputID: 'gameid',
        title: '请输入游戏名称',
        data: {:json_encode($glist)},
        searchBtn:'J_search_submit'
    });

    var $table = $('#objTable');
    $table.bootstrapTable({});

    // $("#J_search_submit").on("mouseover",function () {
    //     var page=$("input[name=page_show]").val();
    //     if(page!=""){
    //         $("input[name=page]").val(page);
    //     }
    // })

    $("#J_search_submit").on("mouseover", function () {
        if ($("input[name=page_show]").val() !== "") {
            var page_show = $("input[name=page_show]").val();
            $("input[name=page]").val(page_show)
        }
    })

    $(document).keydown(function (event) {
        if (event.keyCode == 13) {
            if ($("input[name=page_show]").val() !== "") {
                var page_show = $("input[name=page_show]").val();
                $("input[name=page]").val(page_show)
            }
            $("#submit-form").submit()
        }
    });


    layui.use(['laydate', 'form'], function () {
        var laydate = layui.laydate;
        var form = layui.form;

        var starttime = laydate.render({
            elem: '#start_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

        // // 页面渲染后，在加载渠道，提升速度
        // window.onload = function () {
        //     $.getJSON("{:url('ajaxGetChannel')}", function (result) {
        //         if (result.code) {
        //             var html = '';
        //
        //             for (var i in result.data) {
        //                 var item = result.data[i];
        //                 html += '<option value="' + item.id + '">' + item.name + '</option>';
        //             }
        //
        //             if (html) {
        //                 $("select[name='channel_id']").append(html);
        //                 form.render('select', 'channel');
        //             }
        //         }
        //     });
        // };


        // ## 推广四级下拉 ##
        var businessType = getQueryVariable("tg_business"),
            president_type = getQueryVariable("tg_president"),
            president_type_son = getQueryVariable("tg_president_son"),
            promoter_type = getQueryVariable("tg_promoter")
        // 初始化下拉
        getChannelSelectData('sw', '', businessType);
        getChannelSelectData('hz', businessType, president_type);
        getChannelSelectData('zhz', president_type, president_type_son);
        getChannelSelectData('tgy', president_type_son, promoter_type);
        /**
         *  获取下拉数据
         * @param type sw=商务/hz=会长/zhz=子会长/tgy=推广员
         * @param pid 上级ID
         * @param select_id 选中ID
         */
        function getChannelSelectData(type = 'sw', pid = '', select_id = ''){
            $.ajax({
                type: "GET",
                url: '{:url("Channel/getSelectList")}',
                dataType: "json",
                async: false,
                data: {'type': type,'pid': pid},
                success: function (res) {
                    listData = res.data
                    // console.log("## getChannelSelectData: ", listData)
                    if (type === 'sw'){
                        $("#tg_business").empty();
                        $("#tg_business").append("<option value=''>请选择商务</option>")
                        $.each(listData, function (i, item) {
                            if (item.id == select_id){
                                $("#tg_business").append("<option value='" + item.id + "' selected>" + item.name + "</option>")
                            }else{
                                $("#tg_business").append("<option value='" + item.id + "'>" + item.name + "</option>")
                            }
                        })
                    }else if (type === 'hz'){
                        $("#tg_president").empty();
                        $("#tg_president").append("<option value=''>请选择会长</option>")
                        $.each(listData, function (i, item) {
                            if (item.id == select_id){
                                $("#tg_president").append("<option value='" + item.id + "' selected>" + item.name + "</option>")
                            }else{
                                $("#tg_president").append("<option value='" + item.id + "'>" + item.name + "</option>")
                            }
                        })
                    }else if (type === 'zhz'){
                        $("#tg_president_son").empty();
                        $("#tg_president_son").append("<option value=''>请选择子会长</option>")
                        $.each(listData, function (i, item) {
                            if (item.id == select_id){
                                $("#tg_president_son").append("<option value='" + item.id + "' selected>" + item.name + "</option>")
                            }else{
                                $("#tg_president_son").append("<option value='" + item.id + "'>" + item.name + "</option>")
                            }
                        })
                    }else if (type === 'tgy'){
                        $("#tg_promoter").empty();
                        $("#tg_promoter").append("<option value=''>请选择推广员</option>")
                        $.each(listData, function (i, item) {
                            if (item.id == select_id){
                                $("#tg_promoter").append("<option value='" + item.id + "' selected>" + item.name + "</option>")
                            }else{
                                $("#tg_promoter").append("<option value='" + item.id + "'>" + item.name + "</option>")
                            }
                        })
                    }
                    form.render('select')
                },
            });
        }
        // 获取指定URL的参数值
        function getQueryVariable(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }
        form.on('select(tg_business)', function (data) {
            if (data.value > 0){
                getChannelSelectData('hz', data.value, businessType);
            }else{
                getChannelSelectData('hz');
                getChannelSelectData('zhz');
                getChannelSelectData('tgy');
            }
        });
        form.on('select(tg_president)', function (data) {
            if (data.value > 0){
                getChannelSelectData('zhz', data.value, president_type);
            }else{
                getChannelSelectData('zhz');
            }
        });
        form.on('select(tg_president_son)', function (data) {
            if (data.value > 0){
                getChannelSelectData('tgy', data.value, promoter_type);
            }else{
                getChannelSelectData('tgy');
            }
        });
    });

    /*用户-停用*/
    function member_stop(obj, url) {
        layer.confirm('确认要冻结或者解冻吗？', {
            shade: 0.4
        }, function (index) {
            $.getJSON(url, function (res) {

                if (res.code) {
                    layer.msg(res.msg, {
                        icon: 1,
                        shade: 0.4,
                        time: 1000
                    }, function () {
                        location.href = res.url;
                    });
                } else {
                    layer.msg(res.msg, {
                        icon: 1,
                        shade: 0.4,
                        time: 1000
                    });
                }
            });

        });
    }

    /**删除头像*/
    function del_avatar(obj, url) {
        layer.confirm('确认要删除头像吗？', {
            shade: 0.4
        }, function (index) {
            $.getJSON(url, function (res) {

                if (res.code) {
                    layer.msg(res.msg, {
                        icon: 1,
                        shade: 0.4,
                        time: 1000
                    }, function () {
                        location.href = res.url;
                    });
                } else {
                    layer.msg(res.msg, {
                        icon: 1,
                        shade: 0.4,
                        time: 1000
                    });
                }
            });

        });
    }

    /*用户-停用*/
    function member_clear(obj, url) {
        layer.confirm('确认要清空实名吗？', {
            shade: 0.4
        }, function (index) {
            $.getJSON(url, function (res) {

                if (res.code) {
                    layer.msg(res.msg, {
                        icon: 1,
                        shade: 0.4,
                        time: 1000
                    }, function () {
                        location.href = res.url;
                    });
                } else {
                    layer.msg(res.msg, {
                        icon: 1,
                        shade: 0.4,
                        time: 1000
                    });
                }
            });

        });
    }

    /**
     * 下载
     */
    $('.download').on('click', function (event) {

        var start_time = $('#start_time').val();
        var end_time = $('#end_time').val();
        /*
        if (!end_time || !start_time) {
            layer.msg('请选择开始时间和结束时间');
            return;
        }*/
        /*
        if ((CompareDate(start_time, end_time)) > 7) {
            layer.msg('请选择7天以内的时间范围');
            return;
        }*/

        var url = $(this).data('url');
        var urlData = '';
        if (url == 1) {
            urlData = "{:url('download')}";
        } else if (url == 2) {
            urlData = "{:url('registDowmExcel')}";
        } else {
            layer.msg('参数错误！');
            return;
        }

        $.ajax({
            type: "post",
            url: urlData,
            async: false,
            data: $('.layui-form').serialize(),
            dataType: 'json',
            timeout: 5000,
            success: function (data) {

                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    });

    var inputs = document.querySelectorAll('input[type=checkbox]');

    // 【全选】
    function checkAll() {
        var num = 0;
        for (var i = 1; i < inputs.length; i++) {
            // 如果当前选框被选中，那么个数+1
            if (inputs[i].checked == true) {
                num++;
            }
        }

        inputs[0].checked = (checked === inputs.length - 1);
    }

    // 对【全选】input做监听  更改全选/非全选状态和文字
    inputs[0].onclick = function () {
        for (var i = 1; i < inputs.length; i++) {
            // 将全选框的状态赋给每一个选项框
            inputs[i].checked = this.checked;
        }
        // 改状态
        checkAll();
    };

    // 对每一个input做监听事件
    for (var i = 1; i < inputs.length; i++) {
        inputs[i].onclick = function () {
            // 改状态
            checkAll();
        }
    }

    /**
     * 获取选中id
     * @returns {string}
     */
    function getCheckedId() {
        var ids = '';
        for (var i = 0; i < inputs.length; i++) {
            if (inputs[i].checked) {
                ids ? ids += ',' : '';
                ids += inputs[i].value;
            }
        }

        return ids;
    }

    // 冻结、解冻 统一处理
    function handleFrozen(url, html) {
        var ids = getCheckedId();
        var _that = $(this);

        if (!ids) {
            layer.msg('请先选择账号');
            return false;
        }

        _that.addClass('layui-btn-disabled');

        $.getJSON(url, {
            ids: ids
        }, function (result) {
            layer.msg(result.msg);
            _that.removeClass('layui-btn-disabled');
            window.location.reload();
            return false;
            /*if (result.code) {
                for(var i=0; i< inputs.length; i++) {
                    if (inputs[i].checked) {
                        inputs[i].checked = false;
                        $(inputs[i]).parents('tr').find('.account_status').html(html);
                    }
                }
            }*/
        });
    }

    // 批量解冻
    $('#multi_unfrozen').on('click', function () {
        layer.confirm('确定批量解冻子用户？', function () {
            var url = "{:url('multiUnSubaccountFrozen')}";
            var html = '<span class="layui-btn  layui-btn-radius layui-btn-normal">正常</span>';
            handleFrozen(url, html);
        });

    });

    // 批量冻结
    $('#multi_frozen').on('click', function () {
        layer.confirm('确定批量冻结子用户？', function () {
            var url = "{:url('multiSubaccountFrozen')}";
            var html = '<span class="layui-btn  layui-btn-radius layui-btn-danger">冻结</span>';
            handleFrozen(url, html);
        });

    });

    function detailFormatter(index, row) {
        // console.log(row);
        var html = [];

        var obj = {
            0: "", //checkbox要算入
            1: "用户ID",
            2: "注册账号",
            3: "用户昵称",
            4: "头像",
            5: "游戏名称",
            6: "累充金额",
            7: "账号状态",
            8: "推广员账号",
            9: "公会账号",
            10: '商务账号',
            11: "注册时间",
            12: "最近登录时间",
            13: "操作",
            14: "imeil码",
            15: "累积充值次数",
            16: "角色名",
            17: "区服",
            18: "等级",
            19: "IP"
        }

        $.each(row, function (key, value) {

            if (key == 14 || key == 15 || key == 16 || key == 17 || key == 18 || key == 19) {
                html.push('<p><b>' + obj[key] + ':</b> ' + value + '</p>');
            } else {
                html.push('<p class="hide"><b>' + obj[key] + ':</b> ' + value + '</p>');
            }

        });
        console.log(html)
        return html.join('');
    }

    // 查看明文 -- 已不用
    $(".showPassword").click(function (event) {
        var id = $(this).data('id');
        var _that = $(this);
        $.ajax({
            type: "post",
            url: "{:url('indexPassword')}",
            /*async: false,*/
            data: {'id': id},
            dataType: 'json',
            /*timeout: 5000,*/
            success: function (data) {
                if (data['code']) {
                    _that.text(data['data']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    })


    var viewer = new Viewer(document.getElementById('viewer_wrap'), {
        url: 'data-original'
    });

    $(document).ready(function () {

        $(".check_photo").on("click", function () {
            $(this).siblings().click()
        })

    });
</script>
{/block}
