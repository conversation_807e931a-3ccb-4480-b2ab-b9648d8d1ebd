{extend name="layout/content" /} {block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>登录用户列表</title>
<style>
    label {

        padding-top: 10px;
        margin-left: 10px;

    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }

    .showPassword {
        cursor: pointer;
        text-decoration: underline;
        color: blue;
    }

    .layui-inline {
        margin-top: 10px;
    }
</style>
{/block} {block name="content"}

<div class="x-body">

    <form class="layui-form">

        <div>


            <div class="layui-inline">
                <label>开始时间：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="开始日" value="{$start_time}" name="start_time" id="start_time">
                </div>
            </div>

            <div class="layui-inline">
                <label>结束时间：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="截止日" value="{$end_time}" name="end_time" id="end_time">
                </div>
            </div>

            <!-- <div class="layui-inline"> -->
            <!--     <label>子账户：</label> -->
            <!--     <div class="layui-input-inline"> -->
            <!--         <input class="layui-input" placeholder="" name="sub_username" value="{$Request.get.sub_username}"> -->
            <!--     </div> -->
            <!-- </div> -->

            <div class="layui-inline">
                <label for="playerAcount">注册账号：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="" name="username" value="{$Request.get.username}">
                </div>
            </div>

            <div class="layui-inline">
                <label for="">IMEI搜索：</label>
                <div class="layui-input-inline">
                    <input type="text" name="imeil" placeholder="IMEI搜索" autocomplete="off" value="{$Request.get.imeil}"
                           class="layui-input mg_L">
                </div>
            </div>

            <div class="layui-inline">
                <label for="">关联游戏：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}"/>
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label for="">推广员账号：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='J_channelid' name="channel_id" value="{$Request.get.channel_id}"/>
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label for="">公会账号：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='J_parent_channel_id' name="parent_channel_id"
                               value="{$Request.get.parent_channel_id}"/>
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label for="">商务账号：</label>
                <div class="layui-input-inline">
                    <select name="bplus_channel_id" lay-filter="level" value="{:input('request.bplus_channel_id')}">
                        <option value="">选择商务</option>
                        {volist name="uniom" id="vo"}
                        <option value="{$vo.id}" {if condition="input('request.bplus_channel_id') eq $vo.id"
                                }selected="selected" {
                        /if} >{$vo.name}</option>
                        {/volist}
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <button class="layui-btn mg_L" id="J_search_submit" lay-submit lay-filter="sreach">查询</button>
                <a href="{:url()}" class="layui-btn layui-btn-primary">重置</a>
                <a class="layui-btn layui-btn-normal download" data-url="{:url('loginListDownload')}"
                   href="javascript:;">下载报表</a>
                <a class="layui-btn layui-btn-normal download" data-url="{:url('loginDowmExcel')}" href="javascript:;">下载报表（高级）</a>
            </div>


        </div>

        <!--
        <p style="margin-top:10px;">
            <span style="color: red">*</span><a style="color: #666">单次查询日期的最长跨度为31天</a>
        </p>
        -->
        <table class="layui-table">
            <thead>
            <tr>
                <!-- <th>子账户</th> -->
                <th>注册账号</th>
                <th>游戏名称</th>
                <th>IMEI</th>
                <th>ip</th>

                <th>账号状态</th>
                <th>推广员账号</th>
                <th>子会长</th>
                <th>公会账号</th>
                <th>商务账号</th>

                <th>登录时间</th>
                <th>注册时间</th>
                <!-- <th>操作</th> -->
            </tr>
            </thead>
            <tbody>

            {notempty name="list"} {volist name="list" id="vo"}
            <tr>
                <!-- <td>{$vo.sub_username??'-'}</td> -->
                <td>
                    <a class="showPassword" data-id="{$vo.id}">
                        {:escape($vo.username)}
                    </a>
                </td>
                <td>{$game_list[$vo.gameid]??'-'}</td>
                <td>{$vo.imeil}</td>
                <td>{$vo.ip}</td>

                <td>
                    {if condition="$vo['flag'] eq 0"}
                    <span class="badge badge-danger">正常</span>
                    {else}
                    <span class="badge badge-default">冻结</span>
                    {/if}
                </td>
                <td>{$vo.name}</td>
                <td>{$vo.bchild_name}</td>
                <td>{$vo.top_channel_name}</td>
                <td>{$vo.union_name}</td>

                <td>{:date('Y-m-d H:i:s',$vo.login_time)}</td>
                <td>{:date('Y-m-d H:i:s',$vo.reg_time)}</td>

                <!-- <td class="td-manage"> -->
                <!--     <a title="查看" href="{:url('Member/updatePassword',['id'=>$vo.id])}"> -->
                <!--     <i class="layui-icon">&#xe63c;</i>修改密码 -->
                <!-- </a> -->

                <!--{if condition="$vo['flag'] eq 0"}-->
                <!--<a href="javascript:;" onclick="member_stop_open(this,'{:url(\'frozen\',[\'id\'=>$vo.id,\'flag\'=>0])}')"-->
                <!--title="冻结账号">-->
                <!--<i class="layui-icon">&#xe640;</i>冻结账号-->
                <!--</a>-->
                <!--{else/}-->
                <!--<a href="javascript:;" onclick="member_stop_open(this,'{:url(\'frozen\',[\'id\'=>$vo.id,\'flag\'=>1])}')"-->
                <!--title="解冻账号">-->
                <!--<i class="layui-icon">&#xe640;</i>解冻账号-->
                <!--</a>-->
                <!--{/if}-->
                <!--</td>-->
            </tr>
            {/volist} {/notempty}
            </tbody>

        </table>

        <div class="pager-container margin-top-10">
                                <span>
                                    {$total}条记录
                                </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page"
                       onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>
    </form>

</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

    $("#gameid").FuzzySearch({
        inputID: 'gameid',
        title: '请输入游戏名称',
        data: {:json_encode($gList)},
        searchBtn    :'J_search_submit',
    });

    $("#J_channelid").FuzzySearch({
        inputID: 'J_channelid',
        title: '请输入推广员账号',
        searchBtn: 'J_search_submit',
        ajaxUrl: '{:url("Pay/ajaxGetNoComplexChannel")}',
    });

    $("#J_parent_channel_id").FuzzySearch({
        inputID: 'J_parent_channel_id',
        title: '选择公会账号',
        searchBtn: 'J_search_submit',
        ajaxUrl: '{:url("Pay/ajaxGetNoComplexChannel")}',
    });


    layui.use('laydate', function () {
        var laydate = layui.laydate;
        var starttime = laydate.render({
            elem: '#start_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

    });

    // 报表下载
    $('.download').on('click', function (event) {
        var url = $(this).data('url');
        $.ajax({
            type: "post",
            url: url,
            async: false,
            data: $('.layui-form').serialize(),
            dataType: 'json',
            timeout: 5000,
            success: function (data) {
                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    });

    // 查看明文
    $(".showPassword").click(function (event) {
        var id = $(this).data('id');
        var _that = $(this);
        $.ajax({
            type: "post",
            url: "{:url('loginPassword')}",
            /*async: false,*/
            data: {'id': id},
            dataType: 'json',
            /*timeout: 5000,*/
            success: function (data) {
                if (data['code']) {
                    _that.text(data['data']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    })
</script>
{/block}
