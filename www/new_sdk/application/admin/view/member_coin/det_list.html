{extend name="layout/content" /} {block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>用户账户明细</title>
<style>
    label {

        padding-top: 10px;
        margin-left: 10px;

    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }
    .showPassword{
        cursor: pointer;
        text-decoration: underline;
        color: blue;
    }

    .layui-inline{
        margin-top: 10px;
    }
</style>
{/block} {block name="content"}

<div class="x-body">
     
            <form class="layui-form">
                    <div>
                        <div class="layui-inline">
                            <label for="playerAcount">用户ID：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="" name="userid" value="{$Request.get.userid}">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label for="playerAcount">用户账号：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="" name="username" value="{$Request.get.username}">
                            </div>
                        </div>

						<div class="layui-inline">
							<label>游戏名：</label>
							<div class="layui-input-inline FuzzySearch_Container">
								<div>
									<input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" />
								</div>
							</div>
						</div>	

						<div class="layui-inline">
							<label class="layui-form-label" style="font-weight: bold;width: 95px;">交易类型：</label>
							<div class="layui-input-inline">
								<select name="type" >
									<option value="">- 请选择交易类型 -</option>
									<option value="1" {if condition="$Request.get.type eq '1'" }selected="selected" {/if}>转账</option>
									<!--
									<option value="2" {if condition="$Request.get.type eq '2'" }selected="selected" {/if}>回收币</option>
									-->
									<option value="3" {if condition="$Request.get.type eq '3'" }selected="selected" {/if}>消费</option>
									<option value="4" {if condition="$Request.get.type eq '4'" }selected="selected" {/if}>未消费释放</option>
								</select>
							</div>
						</div>

                        <div class="layui-inline">
                            <label>开始时间：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="开始日" value="{$start_time}" name="start_time" id="start_time">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label>结束时间：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="截止日" value="{$end_time}" name="end_time" id="end_time">
                            </div>
                        </div>

                            <div class="layui-inline">
                                <button class="layui-btn mg_L" id="J_search_submit" lay-submit lay-filter="sreach">查询</button>
								<a class="layui-btn" href="javascript:void(0);" onclick="reportDownload();">下载报表</a>
                                <a href="{:url()}" class="layui-btn layui-btn-primary">重置</a>
                            </div>

                        </div>

                        <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>用户账号</th>
                                        <th>游戏名</th>
                                        <th>变动前金额</th>
										<th>变动金额</th>
										<th>变动后金额</th>
                                        <th>交易类型</th>
										<th>关联订单号</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {notempty name="list"} {volist name="list" id="vo"}
                                    <tr>
                                        <td>{:escape($vo.userid)}</td>
                                        <td>{:escape($vo.username)}</td>
                                        <td>{$vo.game_name}</td>
                                        <td>{$vo.prev_amount}</td>
										<td>{$vo.change_amount}</td>
										<td>{$vo.after_amount}</td>
										<td>{if condition="$vo['type'] eq 1"} 转账 {elseif condition="$vo['type'] eq 2"} 回收币 {elseif condition="$vo['type'] eq 3"} 消费 {elseif condition="$vo['type'] eq 4"} 未消费释放 {else} {/if}</td>
                                        <td>{$vo.out_orderid}</td>
                                        <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                                    </tr>
                                    {/volist} {/notempty}
                                </tbody>
                            </table>

                                <div class="pager-container margin-top-10">
                                    <span>
                                        {$total}条记录
                                    </span>
                                    {$page}
                                    <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                                        <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                                    </div>
                                </div>
                        

            </form>


         
        



        
</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
     $("#gameid").FuzzySearch({
        inputID     : 'gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($glist)},
        searchBtn	:'J_search_submit',
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;
        var starttime = laydate.render({
            elem: '#start_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

    });

	/**
     * 下载
     */
    function reportDownload()
    {
        $.ajax({
            type: "post",
            url: "{:url('detList')}",
            async: false,
            data: $('.layui-form').serialize()+'&download=1',
            dataType: 'json',
            timeout: 5000,
            success: function (data) {

                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    }

    // 查看明文
    $(".showPassword").click(function (event) {
        var id = $(this).data('id');
        var _that=$(this);
        $.ajax({
            type: "post",
            url: "{:url('loginPassword')}",
            /*async: false,*/
            data: {'id':id},
            dataType: 'json',
            /*timeout: 5000,*/
            success: function (data) {
                if (data['code']) {
                    _that.text(data['data']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    })
</script>
{/block}