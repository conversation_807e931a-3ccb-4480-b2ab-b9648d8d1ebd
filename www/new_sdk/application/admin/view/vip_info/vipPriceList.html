{extend name="layout/content" /} {block name="header"}

<title>VIP价格表</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    /* 将li居中 */
    *{
        font-size: 14px !important;
    }

    ul.layui-tab-title {
        text-align: center;
        height: 45px;
    }

    .layui-tab-title li {
        line-height: 36px;
    }


    .layui-table td,
    .layui-table th {
        text-align: center;
    }

    .layui-tab-brief>.layui-tab-title .layui-this {
        color: #009688;
        border: 1px solid #009688;
    }

    .layui-tab-brief>.layui-tab-more li.layui-this:after,
    .layui-tab-brief>.layui-tab-title .layui-this:after {
        border-bottom: 0;
    }

    .layui-form-item .layui-input-inline {
        float: unset;
        width: 190px;
        margin-right: 10px;
    }

    ul.card_wrap li {
        border: 1px solid #ddd;
        padding: 25px 10px;
    }

    ul.card_wrap li:not(:nth-of-type(1)) {
        margin-top: 5px;
    }


    ul.card_wrap li h3,
    ul.card_wrap li h3 span {
        font-size: 20px;
    }

    .new {
        display: inline-block;
        border: 1px solid red;
        font-size: 12px !important;
        color: white;
        background: red;
        border-radius: 5px;
    }

    ul.card_wrap li p {
        font-size: 14px;
        color: #999;
        margin-top: 5px;
    }

    ul.card_wrap li h3:hover {
        cursor: pointer;
    }

    ul.card_wrap li article:hover {
        cursor: pointer;
    }

    ul.card_wrap li article {
        font-size: 18px;
        margin-top: 15px;
    }

    /* .active{
        width: 300px !important;
        top: 30% !important;
        left: calc(50% - 150px) !important;
    } */
</style>
{/block} {block name="content"}

<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
    <ul class="layui-tab-title allType">
        <li class="layui-btn layui-btn-radius layui-btn-primary" data-href="{:url('index')}">资讯汇总</li>
        <li class="layui-btn layui-btn-radius layui-btn-primary" data-href="{:url('serverNewList')}">开服表</li>
        <li class="layui-this layui-btn layui-btn-radius layui-btn-primary" data-href="{:url('vipPriceList')}">VIP价格表</li>
        <li class="layui-btn layui-btn-radius layui-btn-primary" id="three" data-href="{:url('newsInfo')}">新闻活动</li>
        <li class="layui-btn layui-btn-radius layui-btn-primary" data-href="{:url('serverTestList')}">开测表</li>
    </ul>

</div>

<div class="x-body">
   
    
           



        <form class="layui-form" method="get">

            <div style="float: left;">
                <span>当前位置：<a href="{:url('index')}">资讯汇总</a> -> VIP价格表</span>
            </div>


            <div style="float: right;">

                <div class="layui-inline">
                    <label>时间：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="开始时间" name="start" id="start" value="{$Think.get.start}">
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="结束时间" name="end" id="end" value="{$Think.get.end}">
                    </div>
                </div>


                <div class="layui-inline">
                    <label>游戏：</label>
                    <!-- <div class="layui-input-inline set_width">
                        <select name="gameid" lay-search>
                            <option value=""></option>
                            {volist name="gamelist" id="g"}
                            <option {if condition="$Request.get.gameid eq $g['id']"}selected="selected"{/if} value="{$g.id}">{$g.name}</option>
                            {/volist}
                        </select>
                    </div> -->
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" />
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
                </div>

            </div>

            <div style="clear:both"></div>



                <table class="layui-table">
                    <thead>
                    <tr>
                        <th>游戏名称</th>
                        <th>查看VIP价格</th>
                        <th>游戏上线时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    {notempty name="list"}
                    {volist name="list" id="vo"}
                    <tr>
                        <td>{$vo.game.name}</td>
    
                        <td>{if condition="$vo['image'] eq null"}无VIP表{else /}
                        <img class="preview" data-original="https://cdn.{$Think.QM_DOMAIN_URL}/{$vo.image}" alt="{$vo.game.name}" src="__STATIC__/images/preview.png"/>{/if}
                        </td>
    
                        <td>{:date("Y-m-d H:i:s",$vo.online_time)}</td>
                    </tr>
                    {/volist}
                    {else/}
                    <tr>
                        <td colspan="3">暂无数据</td>
                    </tr>
                    {/notempty}
                    </tbody>
                </table>
    
                <div class="pager-container">
                    {if condition="$list->total() gt '0'"}
                    <span>
                      {$list->total()}条记录
                    </span>
                    {/if}
                    {$page}
                    <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                        <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                    </div>
                </div>


            


        </form>
 


     
        
      
  


    <!-- <div style="display: none;" class="review-zone">
        <img style="width: 100%;" class="radius" src="#" />
    </div>
    <style type="text/css">
        a:hover {text-decoration: none;}
        a:active{text-decoration:none;}
        a{text-decoration:none;color:#000000;}
        .txt{
            display: block;
            width: 300px;
            height: auto;
            float: left;
            color: #333;
            font-weight: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .span6{
            height: 270px;
        }
    </style>
    <div class="clear"></div> -->

</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}" charset="utf-8"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $("#gameid").FuzzySearch({
            inputID     : 'gameid',
            title   	: '请输入游戏名称',
            data        :{:json_encode($gamelist)},
            searchBtn	:'J_search_submit',
        });

        var viewer = new Viewer(document.querySelector("tbody"), {
            url: 'data-original',
            navbar:false,
            
        });

        // 点击第三个新闻活动，如果存在历史记录就回退
        $('#three').on("click", function () {

        });

        $('.layui-tab-title > li').on('click', function () {
            window.location.href = $(this).data('href');
        });
    });

    // 预览图片
    // $('.preview').click( function(event) {
    //     var $this = $(event.currentTarget);
    //     var url = $this.data('original');
    //     $('.review-zone img').attr('src', url);
    //     var html = $('.review-zone').html();
    //     layer.open({
    //         //shift:6,
    //         type: 1,
    //         closeBtn: false,
    //         shadeClose: true,
    //         title: false, // 标题：无
    //         //skin: 'layui-layer-rim', //加上边框
    //         //area: ['500px', '500px'], //宽高
    //         content: html
    //     });

    //     $(".layui-layer.layui-layer-page").addClass("active");
    // });


    layui.use('laydate', function () {
        var laydate = layui.laydate;

        //执行一个laydate实例
        laydate.render({
            elem: '#start', //指定元素
            format: "yyyy-MM-dd"

        });

        //执行一个laydate实例
        laydate.render({
            elem: '#end', //指定元素
            format: "yyyy-MM-dd"
        });
    });

</script>
{/block}