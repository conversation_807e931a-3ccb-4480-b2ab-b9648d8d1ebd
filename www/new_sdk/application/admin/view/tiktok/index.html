{extend name="layout/content" /}
{block name="header"}

<title>游戏冻结</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        line-height: 1;

        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .edit {
        padding: 0 8.5px;
        height: 30px;
        line-height: 30px;
        border-radius: 5px;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">



    <form class="layui-form" method="get">

        <div >
            <div class="layui-inline">
                <label>游戏名称：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='J_gameid' name="game_id" value="{$Request.get.game_id}" />
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <a href="{:url('tiktok/add')}" class="layui-btn layui-btn-radius"><i class="layui-icon">&#xe654;</i>新增</a>
                <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
            </div>

        </div>

        <div style="clear:both"></div>





        <table class="layui-table">
            <thead>
            <tr>
                <th>游戏名称</th>
                <th>推广员</th>

                <th>操作</th>
            </tr>
            </thead>

            <tbody>
            {notempty name="list"}
            {volist name="list" id="vo"}

            <tr>
                <td>{$vo['gameGame']}</td>
                <td>{$vo['channelName']}</td>


                <td class="td-manage">
                    <a href="javascript:;" onclick="ajaxDeletePack('{:url(\'del\',[\'id\'=>$vo.id])}')" class="layui-btn  layui-btn-danger"><i class="layui-icon"></i>删除</a>

                </td>
            </tr>
            {/volist}
            {/notempty}
            </tbody>
        </table>



        <div class="pager-container">
                        <span>
                            {$total}条记录
                        </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>








    </form>



</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $("#J_gameid").FuzzySearch({

            inputID    : 'J_gameid',
            title   	: '请输入游戏名称',
            data       :{:json_encode($game_list)},
        searchBtn	:'J_search_submit',
    });

        $(function () {
            $("#J_gameid_show").attr('name','game_name');
            $("#J_gameid_show").val("{:input('get.game_name')}");
        })
    });

    function ajaxDeletePack(url) {

        //询问框
        layer.confirm('是否确定删除？', {
            btn: ['是','否'] //按钮
        }, function(){
            $.ajax({
                type: "post",
                url: url,
                async: false,
                data: '',
                dataType: 'json',
                timeout: 5000,
                success: function (data) {
                    if (data['code']) {
                        window.location.reload();
                    }else {
                        layer.alert(data['msg']);
                    }
                },
                error: function () {
                    layer.alert('网络错误，请重试');
                }
            });
        }, function(){});
    }
</script>
{/block}
