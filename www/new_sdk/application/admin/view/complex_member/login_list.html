{extend name="layout/content" /} {block name="header"}
<title>登录用户</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    label {
        padding-top: 10px;
        margin-left: 10px;
    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }

    .layui-form-label{
        width: 90px;
        margin: 0;
    }
</style>
{/block}

{block name="content"}
    <div class="x-body">
       
            <form class="layui-form">

                <div>

             
                    <div class="layui-inline">
                        <label class="layui-form-label">开始时间：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="开始日" value="{:input('request.start_time')}" name="start_time" id="start_time" style="width:130px;display:inline-block">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">结束时间：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="截止日" value="{:input('request.end_time')}" name="end_time" id="end_time" style="width:130px;display:inline-block">
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:66px;">账号：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" name="mg_username" value="{$Request.get.mg_username}" placeholder="精确匹配，请输入完整的账号">
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label class="layui-form-label">游戏名称：</label>
                        <div class="layui-input-inline FuzzySearch_Container">
                            <div>
                                <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" />
                            </div>
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label class="layui-form-label">渠道名称：</label>
                        <div class="layui-input-inline FuzzySearch_Container">
                            <input class="layui-input" placeholder="请输入渠道名称" name="channel_name" value="{$Request.get.channel_name}">
                        </div>
                    </div>
              
                
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="" id="J_search_btn">查询</button>
                        <a href="{:url()}" class="layui-btn layui-btn-primary">重置</a>
                        <a class="layui-btn layui-btn-normal download" href="javascript:;">下载报表</a>
                    </div>

                </div>




                <table class="layui-table">
                        <thead>
                            <tr>
                                <th>账号</th>
                                <th>游戏名称</th>
                                <th>IP</th>
                                <th>渠道名称</th>
                                <th>登录时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {notempty name="list"} {volist name="list" id="vo"}
                            <tr>
                                <td>{:escape($vo.mg_username)}</td>
                                <td>{:escape($vo.game_name)}</td>
                                <td>{$vo.ip}</td>
                                <td>{$vo.channel_name}</td>
                                <td>{:date('Y-m-d H:i:s',$vo.login_time)}</td>
                            </tr>
                            {/volist} {/notempty}
                        </tbody>
                    </table>
            
            
                    <div class="pager-container">
                        <span>
                            {$total}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>




            </form>
        


   



    </div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $("#gameid").FuzzySearch({
       inputID: 'gameid',
       title: '请输入游戏名称',
       data: {:json_encode($game_list)},
       searchBtn: 'J_search_btn',
   });

    layui.use('laydate', function () {
        var laydate = layui.laydate;

        var starttime = laydate.render({
            elem: '#start_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

    });

    // 报表下载
    $('.download').on('click', function () {

    	var start_time = $('#start_time').val();
        var end_time = $('#end_time').val();

		/*
        if (!end_time || !start_time) {
            layer.msg('请选择开始时间和结束时间');
            return;
        }
		*/

        $.ajax({
            type: "post",
            url: "{:url('loginListDownload')}",
            async: false,
            data: $('.layui-form').serialize(),
            dataType: 'json',
            timeout: 5000,
            success: function (data) {
                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    });
</script>
{/block}