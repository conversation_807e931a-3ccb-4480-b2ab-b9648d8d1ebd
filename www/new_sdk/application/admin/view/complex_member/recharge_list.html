{extend name="layout/content"/} {block name="header"}
<title>聚合用户充值列表</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">

<style>
    label {
        padding-top: 10px;
        margin-left: 10px;

    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }

    .layui-input {
        margin-right: 5px;
    }

    .set_width {
        margin-right: 5px;
    }

    .hide {
        display: none;
    }

    .glyphicon-plus:before {
        content: "\002b";
        font-size: 16px;
        font-weight: 900;
        width: 100%;
        display: inline-block;
    }

    .glyphicon-minus:before {
        content: "\2212";
        font-size: 16px;
        font-weight: 900;
        width: 100%;
        display: inline-block;
    }

    table td,
    table th {
        text-align: center;
    }

    .detail-view td,
    .detail-view th {
        text-align: left;
    }
    
    .layui-form-label{
        width: 90px;
        margin: 0;
    }
</style>

{/block} {block name="content"}


<div class="x-body">

        <form class="layui-form" method="GET" action="{:url('rechargeList')}">

                <div>

              

                    <div class="layui-inline">
                        <label class="layui-form-label">开始时间：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="开始时间" name="start" id="start" value="{:input('request.start')}" readonly='readonly'>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">开始时间：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="结束时间" name="end" id="end" value="{:input('request.end')}" readonly='readonly'>
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label class="layui-form-label">玩家账号：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="玩家账号" name="username" value="{$Request.get.username}">
                        </div>
                    </div>
             
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:66px;">游戏：</label>
                        <div class="layui-input-inline FuzzySearch_Container">
                            <div>
                                <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" />
                            </div>
                        </div>
                    </div>
                 
      
                
                    <div class="layui-inline">
                        <button class="layui-btn" type="submit" id="J_submit_btn">查询</button>
                    </div>
          
                </div>

                
                
                <table class="layui-table">
                        <thead>
                            <tr>
                                <th>充值账号</th>
                                <th>累计充值金额</th>
                                <th>累计充值次数</th>
                                <th>最近充值金额</th>
                            </tr>
                        </thead>
                
                        <tbody>
                            {empty name="list"}
                            <tr>
                                <td colspan="20" style="text-align: center;">暂无数据</td>
                            </tr>
                            {else/} {volist name="list" id="vo"}
                            <tr>
                                <td>{$vo.username}</td>
                                <td>{$vo.amount}</td>
                                <td>{$vo.frequency}</td>
                                <td>{$vo.ramount}</td>
                            </tr>
                            {/volist} {/empty}
                        </tbody>
                    </table>
                
                
                    <div class="pager-container">
                        <span>
                            {$total}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>



        </form>
 

  



</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $("#gameid").FuzzySearch({
        inputID     : 'gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
    searchBtn	:'J_submit_btn',
    });
    layui.use(['form', 'laydate'], function () {
        var laydate = layui.laydate;
        var starttime = laydate.render({
            elem: '#start',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });
    })
</script>
{/block}