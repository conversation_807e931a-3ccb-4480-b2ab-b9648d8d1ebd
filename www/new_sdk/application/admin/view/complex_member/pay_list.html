{extend name="layout/content"/} {block name="header"}
<title>充值记录管理</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">

<style>
    label {
        padding-top: 10px;
        margin-left: 10px;

    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }

    .layui-input {
        margin-right: 5px;
    }

    .set_width {
        margin-right: 5px;
    }

    .hide {
        display: none;
    }

    .glyphicon-plus:before {
        content: "\002b";
        font-size: 16px;
        font-weight: 900;
        width: 100%;
        display: inline-block;
    }

    .glyphicon-minus:before {
        content: "\2212";
        font-size: 16px;
        font-weight: 900;
        width: 100%;
        display: inline-block;
    }

    table td,
    table th {
        text-align: center;
    }

    .detail-view td,
    .detail-view th {
        text-align: left;
    }

    .layui-form-label{
       margin: 0;
    }

    .layui-inline{
        margin-top:10px;
    }
</style>

{/block} {block name="content"}


<div class="x-body">

        <form class="layui-form" method="GET" action="{:url('payList')}">

                <div>

                    <div class="layui-inline">
                        <label class="layui-form-label">时间：</label>
                        <input class="layui-input" placeholder="开始时间" name="start" id="start" value="{:input('request.start')}" readonly='readonly' style="width:130px;display:inline-block">
                        <span>-</span>
                        <input class="layui-input" placeholder="结束时间" name="end" id="end" value="{:input('request.end')}" readonly='readonly' style="width:130px;display:inline-block">
                    </div>


                    <div class="layui-inline">
                        <label class="layui-form-label">游戏：</label>
                        <div class="layui-input-inline FuzzySearch_Container">
                            <div>
                                <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:90px;">充值状态：</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">选择支付状态</option>
                                <option value="0" {if condition="$Request.get.status eq '0' " }selected="selected" {/if}>待支付</option>
                                <option value="1" {if condition="$Request.get.status eq 1 " }selected="selected" {/if}>支付成功</option>
                                <option value="2" {if condition="$Request.get.status eq 2 " }selected="selected" {/if}>支付失败</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label>发货状态：</label>
                        <div class="layui-input-inline">
                            <select name="cp_status">
                                <option value="">选择发货状态</option>
                                <option value="0" {if condition="$Request.get.cp_status eq '0' " }selected="selected" {/if}>发货失败</option>
                                <option value="1" {if condition="$Request.get.cp_status eq 1 " }selected="selected" {/if}>发货成功</option>

                            </select>
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label class="layui-form-label">订单号：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="订单号" id="orderid" name="orderid" value="{$Request.get.orderid}">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:120px;">子渠道订单号：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="子渠道订单号" id="sub_orderid" name="sub_orderid" value="{$Request.get.sub_orderid}">
                        </div>
                    </div>


                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:90px;">角色id：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="角色id" name="roleid" value="{$Request.get.roleid}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:90px;">角色名称：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="角色名称" name="rolename" value="{$Request.get.rolename}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:90px;">玩家账号：</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" placeholder="玩家账号" name="username" value="{$Request.get.username}">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">渠道：</label>
                        <div class="layui-input-inline FuzzySearch_Container">
                            <div>
                                <input type="hidden" id='channel_id' name="channel_id" value="{$Request.get.channel_id}" />
                            </div>
                        </div>
                    </div>


                    <div class="layui-inline">
                        <button class="layui-btn" type="submit" id="J_submit_btn">查询</button>
                        <a class="layui-btn download" href="javascript:;">下载报表</a>
                    </div>



                </div>




                <table class="layui-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>子渠道订单号</th>
                                <th>游戏名称</th>
                                <th>注册时间</th>
                                <th>账号</th>
                                <th>角色id</th>
                                <th>角色名</th>
                                <th>充值金额</th>
                                <th>支付状态</th>
                                <th>发货状态</th>
                                <th>渠道名称</th>
                                <th>时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>

                        <tbody>
                            {empty name="list"}
                            <tr>
                                <td colspan="20" style="text-align: center;">暂无数据</td>
                            </tr>
                            {else/} {volist name="list" id="vo"}
                            <tr>
                                <td>{$vo.orderid}</td>
                                <td>{$vo.sub_orderid}</td>
                                <td>{$vo.game_name}</td>
                                <td>{$vo.reg_time}</td>
                                <td>{$vo.username}</td>
                                <td>{$vo.roleid}</td>
                                <td>{$vo.rolename}</td>
                                <td><i class="fa fa-cny"></i>{$vo.amount}</td>
                                <td>{switch name="vo.status"} {case value="0"}
                                    <span class="badge badge-default">待支付</span>
                                    {/case} {case value="1"}
                                    <span class="badge badge-success">成功</span>
                                    {/case} {case value="2"}
                                    <span class="badge badge-danger">失败</span>
                                    {/case} {/switch}
                                </td>
                                <td>{if condition="$vo.cp_status eq 1"}
                                    <span class="badge badge-default">发货成功</span>
                                    {elseif condition="$vo.cp_status eq 0"}
                                    <span class="badge badge-danger">待发货</span>
                                    {elseif condition="$vo.cp_status eq 2"}
                                    <span class="badge badge-danger">发货失败</span>
                                    {/if}
                                </td>
                                <td>{$vo.channel_name}</td>
                                <td>{$vo.create_time}</td>
                                <td class="td-manage">
                                    <button type="button" title="查看" class="layui-btn layui-btn-normal J_show_param" data-orderid="{$vo.orderid}" data-attach="{$vo.attach}">查看附加信息</button>
                                    {if condition="$vo.status eq 1"}
                                    {empty name="vo.cp_status"}
                                    <button type="button" title="查看" class="layui-btn layui-btn-normal reCallback" data-orderid="{$vo.orderid}">一键发货</button>
                                    {/empty}
                                    {/if}
                                </td>
                            </tr>
                            {/volist} {/empty}
                        </tbody>
                    </table>




                    <div class="pager-container">
                        <span>
                            充值完成总金额：{$totalAmount}，{$total}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>





        </form>






</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

    $("#channel_id").FuzzySearch({
       inputID: 'channel_id',
       title: '请输入渠道名称',
       data: {:json_encode($channel_list)},
       searchBtn: 'J_submit_btn',
   });


    $("#gameid").FuzzySearch({
        inputID     : 'gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_submit_btn',
    });

    layui.use(['form', 'laydate'], function () {
        var laydate = layui.laydate;

        var starttime = laydate.render({
            elem: '#start',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });
    })

    /**
     * 下载
     */
    $('.download').on('click', function () {

        var start_time = $('#start').val();
        var end_time = $('#end').val();

		/*
        if (!end_time || !start_time) {
            layer.msg('请选择开始时间和结束时间');
            return;
        }
		*/

        $.ajax({
            type: "post",
            url: "{:url('payListDownload')}",
            async: false,
            data: $('.layui-form').serialize(),
            dataType: 'json',
            timeout: 5000,
            success: function (data) {

                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    });


    /**
     * 参数导出按钮点击事件
     */
    $('.J_show_param').on('click', function () {

        var orderid = $(this).data('orderid');
        $.ajax({
            type: "post",
            data: {'orderid':orderid},
            url: "{:url('showAttach')}",
            dataType: "json",
            timeOut: 10,
            success: function (msg) {
                if(msg.code) {
                    var attach = msg.msg;
                    var html = '<div>';
                    html += attach;
                    html += '</div>';
                    layer.open({
                        type: 1,
                        title: '合作方订单号',
                        skin: 'layui-layer-rim',
                        area: ['420px', '240px'], //宽高
                        content: html,
                    });
                    if (window.top !== window.self) {
                        window.parent.$('html, body').animate({
                            scrollTop: 0
                        }, 'slow');
                    }
                }else{
                    layer.alert(msg.msg, {icon: 5});
                }
            },
            error: function () {
                layer.alert('网络错误，请刷新页面重试！', {icon: 2});
            }
        });
        //var attach = $(this).data('attach');
    });
    /*
    一键发货
     */
    $('table tbody tr td').on('click', ".reCallback",function () {
        var orderid = $(this).data('orderid');
        $.ajax({
            type: "post",
            data: {'orderid':orderid},
            url: "{:url('reCallback')}",
            dataType: "json",
            timeOut: 10,
            success: function (msg) {
                if(msg.code) {
                    layer.alert(msg.msg);
                }else{
                    layer.alert(msg.msg, {icon: 5});
                }
            },
            error: function () {
                layer.alert('网络错误，请刷新页面重试！', {icon: 2});
            }
        });
    });
</script>
{/block}
