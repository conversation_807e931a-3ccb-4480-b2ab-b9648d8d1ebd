{{extend name="layout/content" /} {block name="header"}
<title>广告列表</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">

<style>
    .layui-table th,
    .layui-table td {
        text-align: center;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .check_photo + img {
        display: none;
    }

</style>
{/block}

{block name="content"}
<div class="x-body">

    <form class="layui-form" method='get' id="J_search_form">
        <div style="float:left">
            <a class="layui-btn layui-btn-radius" href="{:url('add')}">
                <i class="layui-icon">&#xe654;</i>新增
            </a>
        </div>

        <div style="float: right;">
        </div>

        <div style="clear:both"></div>

        <table class="layui-table">
            <thead>
            <tr>
                <th>礼包类型名称</th>
                <th>满足条件类型</th>
                <th>最低充值金额</th>
                <th>区间上限充值金额</th>
                <th>最低等级</th>
                <th>有效时间段</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
            </thead>

            <tbody id="viewer_wrap">
            {notempty name="list"}
            {volist name="list" id="vo"}
            <tr>
                <td>{:escape($vo.name)}</td>
                <td>
                    {switch name="$vo.type_id" }
                    {case value="1"}充值{/case}
                    {case value="2"}创角{/case}
                    {case value="3"}等级{/case}
                    {/switch}
                </td>
                <td>{$vo.recharge_amount}</td>
                <td>{if condition="$vo['recharge_type'] eq 2"}  {$vo.max_recharge_amount}{/if}</td>
                <td>{$vo.role_level}</td>
                <td>
                    {switch name="$vo.time_type" }
                    {case value="1"}全部{/case}
                    {case value="2"}每日{/case}
                    {case value="3"}周{/case}
                    {case value="4"}月{/case}
                    {case value="5"}季{/case}
                    {case value="6"}年{/case}
                    {case value="7"}
                        {$vo.start_time} 00:00:00
                        至
                        {$vo.end_time} 23:59:59{/case}
                    {/switch}
                </td>
                <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                <td class="td-manage">
                    <a href="{:url('edit',['id'=>$vo.id])}" class="layui-btn btn-info"><i
                            class="layui-icon">&#xe63c;</i>编辑</a>
                    <a href="javascript:;" onclick="del_info(this,'{:url('delete',['id'=>$vo.id])}')"
                       class="layui-btn layui-btn-danger"><i class="layui-icon"></i>删除</a>
                </td>
            </tr>
            {/volist}
            {/notempty}
            </tbody>
        </table>

        <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
            {$page}
            <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page"
                       onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
            </div>
        </div>
    </form>

</div>

{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
{/block}
