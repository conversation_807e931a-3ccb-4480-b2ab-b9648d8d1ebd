{extend name="layout/base" /} {block name="detail_css"}
<style>
    .layui-form-label {
        width: 125px;
    }

    header h1,
    header span {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 20px;
    }

    header span {
        color: red;
    }
</style>

{/block} 
{block name="content"}
<div class="layui-tab layui-tab-brief" style="padding-left:30px;margin-top:30px;">

    <div class="layui-tab-content">

        <div class="layui-tab-item layui-show">
            <header>
                <h1>新增支付方式</h1>
            </header>

            <form class="layui-form" method="post">

                <div class="layui-form-item">
                    <label for="" class="layui-form-label">支付方式：</label>

                    <div class="layui-input-inline">
                        <input type="text" autocomplete="off" name="paytype" lay-verify="required" class="layui-input" value="">
                    </div>
                    
                    <div class="layui-form-mid layui-word-aux">
                        <span class="x-red">*</span>（英文字母，必填，例：qm_zfb_h5 ）
                    </div>
                </div>


                <div class="layui-form-item">
                    <label for="" class="layui-form-label">名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" autocomplete="off" name="payname" lay-verify="required" class="layui-input" value="">
                    </div>
                    
                    <div class="layui-form-mid layui-word-aux">
                        <span class="x-red">*</span>（必填，例：祈盟-支付宝-H5 ）
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label for="" class="layui-form-label">状态：</label>
                    <div class="layui-input-inline">
                        <select name="status" lay-verify="required">
                            <option value="1">开启</option>
                            <option value="0">关闭</option>
                        </select>
                    </div>
                    
                    <div class="layui-form-mid layui-word-aux">
                        <span class="x-red">*</span>（必填）
                    </div>
                </div>

                <div class="layui-form-item">
                    <label for="" class="layui-form-label">备注：</label>
                    <div class="layui-input-inline">
                        <input type="text" autocomplete="off" name="ext" lay-verify="required" class="layui-input" value="">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label for="" class="layui-form-label"></label>
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-radius" lay-submit lay-filter="formDemo">提交</button>
                        <a class="layui-btn layui-btn-radius" onclick="javascript:history.go(-1);">返回</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

</div>
{/block}

{block name="detail_js"}
{/block}