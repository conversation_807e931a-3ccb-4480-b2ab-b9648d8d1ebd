{extend name="layout/content" /}
{block name="header"}
<title>账号申诉列表</title>
<style>

    .table{
        width: 100%;
        border:1px solid #e6e6e6;
        font-size: 14px;
    }

    .table th,.table td, .layui-table th,.layui-table td{
        padding:10px;
        text-align: center;
        border:1px solid #e6e6e6;
        font-size: 14px;
        color: #666;
    }

    .clearfix::after{
        content: "";
        clear:both;
        display: table;
    }

    .clearfix{
        zoom: 1;
    }

    .backWrap{
        float:left;
        margin-right: 10px;
    }

    .pull-left{
        float:left
    }

    .pull-right{
        float:right
    }

    .reloadPage{
        font-size: 14px;
        color: blue;
    }
    :hover .reloadPage{
        cursor: pointer;
    }

</style>
{/block}
{block name="content"}
<div class="x-body">
    
    <form class="layui-form clearfix" method="post">

        <div class="layui-inline backWrap">
            <div class="layui-input-inline">
                <a class="layui-btn layui-btn-primary layui-btn-radius" href="javascript:;" onclick="window.history.go(-1)" >返回</a>
            </div>
        </div>

        <div class="layui-inline pull-left">
            <div class="layui-input-inline">
                <a title="添加子权限" class="layui-btn layui-btn-radius" href="javascript:;" onclick="show_modal('历史密码校验','show','{$array.member.id}','','400');" ><i class="layui-icon">&#xe654;</i>密码人工判断</a>
            </div>
        </div>
        

        <div class="pull-right">
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input class="layui-input" name="username" placeholder="账号" value="{$Request.post.username}">
                </div>
             </div>
        
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input class="layui-input" name="code" placeholder="申诉编号" value="{$array.data.code}">
                </div>
            </div>
        
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <button class="layui-btn layui-btn-radius" lay-submit>查询</button>
                </div>
            </div>
        </div>

    </form>


    <table class="layui-table">
        <thead>
        <tr>
            <th>{$array.data.username}</th>
            <th>用户填写资料</th>
            <th>系统数据</th>
            <th>判断</th>
            <th>得分</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>注册时间</td>
            <td>{$array.data.regtime}</td>
            <td>{$array.member.reg_time}</td>
            {if condition="$array.result.regtime eq '1'"}
                <td><i class="fa fa-check" style="color: green;"></i></td>
                <td>{$array.config.MARK_REGISTRATION_TIME}</td>
            {else /}
                <td><i class="fa fa-close" style="color: red;"></i></td>
                <td>0</td>
            {/if}
        </tr>
        <tr>
            <td>注册IMEI码</td>
            <td>{$array.data.imei}</td>
            <td>{$array.member.imeil}</td>
            {if condition="$array.result.imei eq '1'"} <td><i class="fa fa-check" style="color: green;"></i></td><td>{$array.config.MARK_REGISTRATION_IEMI}</td>
            {else /}<td><i class="fa fa-close" style="color: red;"></i></td><td>0</td>
            {/if}

        </tr>
        <tr>
            <td>注册手机</td>
            <td>{$array.data.regmobile}</td>
            <td>{$array.reginfo.mobile}</td>
            <td>
                {if condition="$array.result.regmobile eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>
            <td>
                {if condition="$array.result.regmobile eq '1'"} {$array.config.MARK_REGISTERED_MOBILE_PHONE}
                {else /}0
                {/if}
            </td>
        </tr>
        <tr>
            <td>注册地点</td>
            <td>{$array.data.addr}</td>
            <td>
                {notempty name="$array.member.ip"}
                    {empty name="$array.member.addr"}<a class="reloadPage" onclick="window.location.reload();">点击刷新</a>{else /} {$array.member.addr} {/empty}
                {/notempty}
            </td>
            <td>
                {if condition="$array.result.addr eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>
            <td>
                {if condition="$array.result.addr eq '1'"} {$array.config.MARK_REGISTRATION_ADDRESS}
                {else /}0
                {/if}
            </td>
        </tr>
        <tr>
            <td>注册来源游戏</td>
            <td>{$array.data.reggamename}</td>
            <td>{$array.member.reggamename}</td>
            {if condition="$array.result.reggameid eq '1'"}
                <td><i class="fa fa-check" style="color: green;"></i></td>
                <td>{$array.config.MARK_SOURCE_OF_REGISTRATION}</td>
            {else /}
                <td><i class="fa fa-close" style="color: red;"></i></td>
                <td>0</td>
            {/if}
        </tr>
        <tr>
            <td>注册渠道</td>
            <td>{$array.data.channelname}</td>
            <td>{$array.member.channelname}</td>
            <td>
                {if condition="$array.result.regchannelid eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>
            <td>
                {if condition="$array.result.regchannelid eq '1'"} {$array.config.MARK_REGISTRATION_CHANNE}
                {else /}0
                {/if}
            </td>
        </tr>
        <tr>
            <td>首次充值游戏</td>
            <td>{$array.data.firstrechargegamename}</td>
            <td>{$array.gamename}</td>
            <td>
                {if condition="$array.result.firstrechargegameid eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>
            <td>
                {if condition="$array.result.firstrechargegameid eq '1'"} {$array.config.MARK_FIRST_CHARGE}
                {else /}0
                {/if}
            </td>
        </tr>
        <tr>
            <td>累计充值金额</td>
            <td>
                {$array.data.totalrecharge}
            </td>
            <td>{$array.totalpay}</td>
            <td>
                {if condition="$array.result.totalrecharge eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>
            <td>
                {if condition="$array.result.totalrecharge eq '1'"} {$array.config.MARK_ACCUMULATED_AMOUNT}
                {else /}0
                {/if}
            </td>
        </tr>

        {volist name="$array.oldmail" id="g" key="kk"}
            <tr>
                {eq name="kk" value="1"}
                <td rowspan="{$array.oldmail_1.num}" >历史绑定邮箱</td>
                {/eq}

                <td>{$g.oldmail}</td>
                <td>
                    <a href="javascript:content_show('历史绑定邮箱', '{volist name="array.infos.oldmail" id="oldmail"}{$oldmail}<br/>{/volist}');" class="layui-btn btn-info"><i class="icon-edit"></i>查看</a>
                </td>
                <td>
                    {if condition="$g.result eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                    {else /}<i class="fa fa-close" style="color: red;"></i>
                    {/if}
                </td>

                {eq name="kk" value="1"}
                <td rowspan="{$array.oldmail_1.num}" >
                    {if condition="$array.oldmail_1.isscore eq '1'"} {$array.config.MARK_HISTORY_MAILBOX}
                    {else /} 0
                    {/if}
                </td>
                {/eq}
            </tr>
        {/volist}

        {volist name="$array.oldmobile" id="g" key="kk"}
        <tr>
            {eq name="kk" value="1"}
            <td rowspan="{$array.oldmobile_1.num}" >历史绑定手机</td>
            {/eq}


            <td>{$g.oldmobile}</td>
            <td>
                <a href="javascript:content_show('历史绑定手机', '{volist name="array.infos.oldmobile" id="oldmobile"}{$oldmobile}<br/>{/volist}');" class="layui-btn btn-info"><i class="icon-edit"></i>查看</a>
            </td>
            <td>
                {if condition="$g.result eq '1'"}<i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>

            {eq name="kk" value="1"}
            <td rowspan="{$array.oldmobile_1.num}" >
                {if condition="$array.oldmobile_1.isscore eq '1'"} {$array.config.MARK_HISTORY_MOBILE_PHONE}
                {else /} 0
                {/if}
            </td>
            {/eq}
        </tr>
        {/volist}

        {volist name="$array.boundgame" id="g" key="kk"}
        <tr>
            {eq name="kk" value="1"}
            <td rowspan="{$array.boundgame_1.num}" >账号绑定游戏</td>
            {/eq}

            <td>{$g.name}</td>
            <td>
                 <a href="javascript:content_show('账号绑定游戏', '{volist name="array.infos.boundgameid" id="boundgameid"}{$boundgameid}<br/>{/volist}');" class="layui-btn btn-info"><i class="icon-edit"></i>查看</a>
            </td>
            <td>
                {if condition="$g.result eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>

            {eq name="kk" value="1"}
            <td rowspan="{$array.boundgame_1.num}" >
                {if condition="$array.boundgame_1.isscore eq '1'"} {$array.config.MARK_BINDING_GAME}
                {else /} 0
                {/if}
            </td>
            {/eq}
        </tr>
        {/volist}

        {volist name="$array.oldpwd" id="g" key="kk"}
        <tr>
            {eq name="kk" value="1"}
            <td rowspan="{$array.oldpwd_1.num}" >历史密码</td>
            {/eq}

            <td>{$g.oldpwd}</td>
            <td>
                 <a href="javascript:content_show('历史密码', '{volist name="array.infos.oldpwd" id="oldpwd"}{$oldpwd}<br/>{/volist}');" class="layui-btn btn-info"><i class="icon-edit"></i>查看</a>
            </td>
            <td>
                {if condition="$g.result eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>

            {eq name="kk" value="1"}
            <td rowspan="{$array.oldpwd_1.num}" >
                {if condition="$array.oldpwd_1.isscore eq '1'"} {$array.config.MARK_HISTORY_PASSWORD}
                {else /} 0
                {/if}
            </td>
            {/eq}
        </tr>
        {/volist}

        </tbody>
    </table>

    <table class="table">
        <thead>
        <tr>
            <th>充值记录核实</th>
            <th>充值时间</th>
            <th>充值金额</th>
            <th>历史充值记录</th>
            <th>判断</th>
            <th>得分</th>
        </tr>
        </thead>
        <tbody>

        {volist name="$array.recharge" id="k" key="kk"}
        <tr>
            <td>充值记录{$kk}</td>
            <td>{$k.rechargetime}</td>
            <td>{$k.rechargeamount}</td>
            <td>
                <a href="javascript:content_show('充值记录', '{volist name="array.infos.payhistory" id="g"}{$g}<br/>{/volist}');" class="layui-btn btn-info"><i class="icon-edit"></i>查看</a>
            </td>
            <td>
                {if condition="$k.result eq '1'"} <i class="fa fa-check" style="color: green;"></i>
                {else /}<i class="fa fa-close" style="color: red;"></i>
                {/if}
            </td>

            <td>
            {if condition="$k.result eq '1'"} {$array.config.MARK_RECHARGE_RECORD}
            {else /} 0
            {/if}
            </td>

        </tr>
        {/volist}

        </tbody>
    </table>
    <div style="font-size:30px;">总分：{$array.sum}</div>
</div>

{/block}

{block name="footer"}
<div class="modal fade" id="checkpassword" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" hidden>
        <div class="modal-dialog" style="width: 400px">
            <div class="modal-content">
                
                <div class="layui-form-item">
                    <label for="" class="layui-form-label">
                        <span class="x-red">*</span>密码：
                    </label>
                    <div class="layui-input-inline">
                        <input type="text" name="password" id='J_password_input' required="" lay-verify="required" autocomplete="off"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" onclick="save_submit();" lay-submit lay-filter="formDemo">验证</button>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal -->
    </div>
<script>
    function content_show(title,content) {
        layer.open({
            title: title,
            type: 1,
            area: ['420px', '240px'], //宽高
            scrollbar: false,
            content: '<div style="font-size:14px;line-height:40px;padding-left:20px;">'+content+'</div>'
        });
       
    }
    

    //展示模态窗
    function show_modal(title,url,id,w,h) {
        layer.open({
            title: title,
            type: 1,
            area: ['400px', '200px'],
            content: $('#checkpassword') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
        });
    }

    function save_submit() {
        var uid = {$array.member.id};
        var password = $('#J_password_input').val();

        $.ajax({
            type: 'post',
            url: "{:url('md5Password')}",
            async: false,
            data: {'uid':uid,'password':password},
            dataType: "json",
            success: function(msg){
                if(msg.code == 1) { // 密码验证正确
                    layer.tips(msg.msg, '#J_password_input', {
                        tips: [2, '#5FB878'],
                        time: 2000,
                    });
                }else{ // 密码验证错误
                    layer.tips(msg.msg, '#J_password_input', {
                        time: 2000,
                    });
                }
            },
            error: function(){
                layer.alert('网络错误，请刷新页面重试');
            },
        });
    }

   

</script>
{/block}
