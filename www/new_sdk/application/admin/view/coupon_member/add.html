{extend name="layout/content" /}
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap-table.css?v={$Think.STATIC_VERSION}">
{block name="header"}
<title>添加</title>
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('add')}" method="post">


        <div class="layui-form-item">
            <label class="layui-form-label">
                <span class="x-red">*</span>代金券：
            </label>
            <div class="layui-input-inline FuzzySearch_Container" style="width: 40%">
                <div >
                    <select lay-search name="coupon_id" id="coupon_id" class="layui-form-select">
                        {volist name="coupon" id="vo"}
                             <option value="{$vo.id}">{$vo.name}(面值:{$vo.money} 最低使用金额:{$vo.min_money})</option>
                        {/volist}
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red">*</span>账号：
            </label>
            <div class="layui-input-inline" style="width: 30%;">
                <textarea name="member_ids" rows="5" class="layui-textarea" required="" lay-verify="required"></textarea>

            </div>
        </div>


        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回
                </button>
            </div>
        </div>

    </form>
</div>

{/block}

{block name="footer"}

<script type="text/javascript" src="__STATIC__/js/admin/bootstrap-table.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charSet="utf-8" src="__STATIC__/lib/xm-select.js"></script>
<script>


</script>

{/block}

