{{extend name="layout/content" /} {block name="header"}
<title>广告列表</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">

<style>
    .layui-table th,
    .layui-table td {
        text-align: center;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .check_photo + img {
        display: none;
    }

</style>
{/block}

{block name="content"}
<div class="x-body">

    <form class="layui-form" id="J_search_form" action="{:url('confirm')}" method="post">
        <input type="hidden" name="ids" value="{$ids}">
        <input type="hidden" name="coupon_id" value="{$coupon.id}">
        <span> 代金券：{$coupon.name}(面值 {$coupon.money} 最低使用金额 {$coupon.min_money})</span>
        <table class="layui-table">
            <thead>
            <tr>

                <th>账号</th>
                <th>状态</th>
            </tr>
            </thead>

            <tbody id="viewer_wrap">
            {notempty name="members"}
            {volist name="members" id="vo"}
            <tr>
                <td>{$vo.username}</td>
                <td>{$vo.state}</td>
            </tr>
            {/volist}
            {/notempty}
            </tbody>
        </table>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">确认</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回
                </button>
            </div>
        </div>
    </form>

</div>

{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
{/block}
