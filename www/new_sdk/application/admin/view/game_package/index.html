{extend name="layout/content" /} {block name="header"}

<title>SDK游戏管理</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/lib/layui/css/layui.css">
<style>
</style>
{/block} {block name="content"}

<div class="x-body">

    <div class="layui-card" style="color: #ff5722;">
        <div class="layui-card-header">通知:</div>
        <div class="layui-card-body">
            新上传游戏母包需要联系客户端，更新客户端的版本号，否则强更无法正常使用。
        </div>
    </div>

    <form class="layui-form" method="get" action="{:url('index')}">
        <div style="float:left">
            <div class="layui-inline">
                <a class="layui-btn layui-btn-radius" href="{:url('add')}">
                    <i class="layui-icon">&#xe654;</i>新增游戏原包
                </a>
            </div>

        </div>
        <div style="float: right;">
            <div class="layui-inline">
                <div class="layui-input-inline">
                  <select name="game_id" lay-filter="required" lay-search="" id="game_list">
                  </select>
                </div>
            </div>

            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input type="text" name="game_name" class="layui-input" id="game_name" value="{$Request.get.game_name}" lay-filter="game_name" placeholder="请输入游戏名(模糊查询)" />
                </div>
            </div>

            <div class="layui-inline" style="margin-left:10px;">
                <button class="layui-btn" lay-submit>搜索</button>
                <a class="layui-btn" href="{:url('index')}">清空</a>
            </div>
        </div>
        <div style="clear:both"></div>
        <table class="layui-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>游戏名称</th>
<!--                             <th>原包名称</th>-->
                        <!-- <th>运营平台</th> -->
                        <th>原包大小</th>
                        <th>游戏平台</th>

                        <th>版本号</th>
                        <th>签名版本</th>
                        <th>原包名称</th>
                        <th>记录</th>
                        <th>操作人员</th>
                        <th>最新操作时间</th>

                        <th>操作</th>
                    </tr>
                </thead>

                <tbody >
                {notempty name="list"} {volist name="list" id="vo"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>{$vo.game_name}</td>
                    <!-- <td>{$vo.bag_name}</td> -->
<!--                         <td>
                        {switch name="$vo.platform_type"}
                        {case value="1" }
                        安卓
                        {/case}
                        {case value="2"}
                        IOS
                        {/case}
                        {default /}--
                        {/switch}
                    </td> -->
                    <td>{$vo.bag_size>=1 ? $vo.bag_size."M" : "-"}</td>
                    <td>
                        {if condition="$vo['type'] eq 1"}
                            IOS
                        {elseif condition="$vo['type'] eq 2"}
                            安卓
                        {elseif condition="$vo['type'] eq 3"}
                            H5
                        {else}
                            -
                        {/if}
                    </td>
                    <td>{$vo.channel_version}</td>
                    <td>{$vo.sign_ver==0 ? '-' : 'V'.$vo.sign_ver}</td>

                    <td>
                        {if condition="$vo['pinyin'] eq $vo['path_ext']"}
                            {php}echo substr($vo['path'],strrpos($vo['path'] ,"/")+1) ;{/php}
                        {else}
                            -
                        {/if}
                    </td>
                    <td>{$vo.control_name}</td>
                    <td><a href="#" packageId="{$vo.id}" style="color:#337ab7" class="check" game_id="{$vo.game_id}">查看记录</a></td>
                    <td>{$vo.update_time}</td>
                    <td>
                        <a title="编辑" href="{:url('add')}?game_id={$vo.game_id}&type={$vo.type}&package={php}echo substr($vo['path'],strrpos($vo['path'] ,"/")+1) ;{/php}" class="layui-btn layui-btn-normal">
                            <i class="layui-icon">&#xe63c;</i>编辑
                        </a>

                        <a title="编辑" href="#" class="layui-btn layui-btn-danger del" id="{$vo.id}" game_id="{$vo.game_id}">删除
                        </a>
                    </td>



                </tr>
                {/volist} {/notempty}

                </tbody>
            </table>

            <div class="pager-container">
                <span>
                    {$total}条记录
                </span>
                {$page}
                <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                    <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                </div>
            </div>
    </form>
</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" ></script>
<script type="text/javascript" src="__STATIC__/js/clipboard.min.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">
    function getQueryVariable(variable)
    {
           var query = window.location.search.substring(1);
           var vars = query.split("&");
           for (var i=0;i<vars.length;i++) {
                   var pair = vars[i].split("=");
                   if(pair[0] == variable){return pair[1];}
           }
           return(false);
    }
    $(document).ready(function() {
        layui.use('form', function(){
            var form = layui.form;
            var game_list = {:json_encode($game_list)};

            var game_id = getQueryVariable('game_id');
            if (game_list) {
                var str = '<option value="">直接选择或搜索选择游戏</option>';
                $.each(game_list, function(index, val) {
                    if (val.id == game_id) {
                        // str += `<option value=${val.id} selected="">${val.name}</option>`
                        str += '<option value='+val.id+' selected="">'+val.name+'</option>'
                    }else{
                        str += '<option value='+val.id+'>'+val.name+'</option>'
                    }
                    
                });
                $('#game_list').append(str)
                form.render();
            }
        });
    });
	$(".copy").click(function() {
        var giftcode = $(this).attr('url');
        var oInput = document.createElement('input');
        oInput.value = giftcode;
        document.body.appendChild(oInput);
        oInput.select(); // 选择对象
        document.execCommand("Copy"); // 执行浏览器复制命令
        oInput.className = 'oInput';
        oInput.style.display = 'none';
        layer.msg('复制链接成功');
    })
    $(document).on('click', '.toGameEnter', function(event) {
        layer.open({
          type: 2,
          area: ['400px', '370px'],
          shadeClose: false,
          fixed: false, //不固定
          maxmin: true,
          content: '{:url('game_upload/index')}'
        });
    });
    
    $(document).on('click', '.del', function(event) {
        var game_id     = $(this).attr('game_id');
        var id = $(this).attr('id'); 
        layer.confirm('是否删除该原包(删除后该记录也会删除)', {
          btn: ['确定','取消'] //按钮
        }, function(){
            $.ajax({
                type: "POST",
                url: "{:url('deleted')}",
                data: {"id":id,"game_id":game_id},
                dataType: "json",
                success: function(res) {
                    layer.closeAll()
                    layer.msg(res.msg)   
                    setTimeout(function(){
                            location.reload();
                        }, 1000);         
                },
            });
          
        }, function(){
          layer.msg('取消操作')
        });
    });

    $('.check').click(function(event) {
    var game_id     = $(this).attr('game_id');
    $.ajax({
        type: "POST",
        url: "{:url('checklog')}",
        data: {"game_id":game_id},
        dataType: "json",
        success: function(res) {
            var list ='';
            if (res.code == 1) {
                console.log(res.data)
                $.each(res.data, function(index, val) {
                    list += '<tr><td>'+val.id+'</td><td>'+val.control_type+'</td><td>'+val.control_name+'</td><td>'+val.create_time+'</td></tr>'
                });
            }else{
                list = '<tr><td colspan=4>暂无记录</td></tr>';
            }
            layer.open({
              title: '记录',
              type: 1,
              skin: 'layui-layer-rim', //加上边框
              area: ['840px', '480px'], //宽高
              content: '<table class="layui-table"><thead><tr><th>ID</th><th>操作类型</th><th>操作人员</th><th>操作时间</th></tr></thead><tbody>'+list+'</tbody></table>'
            });
        },
    });
});
</script>
{/block}