{extend name="layout/content" /}
{block name="header"}

<title>游戏防沉迷限制</title>
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        line-height: 1;
        
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .edit {
        padding: 0 8.5px;
        height: 30px;
        line-height: 30px;
        border-radius: 5px;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">

       <form class="layui-form" method="get">

            <div class="layui-inline" style="float:left;">
                <a href="{:url('GameRestrict/add')}" class="layui-btn layui-btn-radius"><i class="layui-icon">&#xe654;</i>新增</a>
            </div>

            <div style="float: right;">
                
                <div class="layui-inline">
                    <label>游戏名称：</label>
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_gameid' name="game_id" value="{$Request.get.game_id}" />
                        </div>
                    </div>
                </div>

                    <div class="layui-inline">
                        <label>限制类型：</label>
                        <div class="layui-input-inline">
							<select name="restrict_type" lay-filter="changeType-filter">
                                <option value="">- 请选择限制类型 -</option>
                                <option {if condition="input('restrict_type') eq 'realname'" }selected="selected" {/if} value="realname">实名弹窗</option>
                                <option {if condition="input('restrict_type') eq 'preventhook'" }selected="selected" {/if} value="preventhook">防沉迷限制</option>
                            </select>
                        </div>
                    </div>
     
                    <div class="layui-inline"  id="null_restrict_div" {if condition="input('restrict_type') neq ''" } style="display:none" {/if}>
                        <label>限制状态：</label>
                        <div class="layui-input-inline">
                            <select name="restrict_status">
                                <option value="">- 请选择限制状态 -</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline"  id="realname_restrict_div" {if condition="input('restrict_type') neq 'realname'" } style="display:none" {/if}>
                        <label>限制状态：</label>
                        <div class="layui-input-inline">
                            <select name="realname_restrict_status">
                                <option value="">- 请选择限制状态 -</option>
                                <option {if condition="input('realname_restrict_status') eq '1'" }selected="selected" {/if} value="1">关闭</option>
                                <option {if condition="input('realname_restrict_status') eq '2'" }selected="selected" {/if} value="2">非强制开启</option>
                                <option {if condition="input('realname_restrict_status') eq '3'" }selected="selected" {/if} value="3">强制开启</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline"  id="preventhook_restrict_div" {if condition="input('restrict_type') neq 'preventhook'" } style="display:none" {/if}>
                        <label>限制状态：</label>
                        <div class="layui-input-inline">
                            <select name="preventhook_restrict_status">
                                <option value="">- 请选择限制状态 -</option>
                                <option {if condition="input('preventhook_restrict_status') eq '1'" }selected="selected" {/if} value="1">关闭</option>
                                <option {if condition="input('preventhook_restrict_status') eq '2'" }selected="selected" {/if} value="2">开启</option>
                            </select>
                        </div>
                    </div>

                <div class="layui-inline">
                    <label>创建时间：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="开始时间" name="start" id="start" readonly='readonly' value="{:input('start')}" autocomplete="off">
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                        <input class="layui-input" placeholder="结束时间" name="end" id="end" readonly='readonly' value="{:input('end')}" autocomplete="off">
                    </div>
                </div>
      
            
                
				<div class="layui-inline">
                    <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
                </div>

			

            

        </div>

            <div style="clear:both"></div>



            <table class="layui-table">
                    <thead>
                    <tr>
                        <th>游戏名称</th>
                        <th>限制类型</th>
                        <th>限制状态</th>
                        <th>创建人</th>
                        <th>创建时间</th>
                        <th>修改人</th>
                        <th>修改时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
            
                    <tbody>
                    {notempty name="restrict"}
                    {volist name="restrict" id="vo"}
            
                    <tr>
                        <td>
                            {if condition="$vo.game_id eq 0"}
                            <font>所有游戏</font>
                            {else}
                            {:escape($vo.game_name)}
                            {/if}&nbsp;
                        </td>
                        <td>
                            {if condition="$vo.restrict_type eq 'realname'"}
                            <font>实名弹窗</font>
                            {/if}
                            {if condition="$vo.restrict_type eq 'preventhook'"}
                            <font>防沉迷限制</font>
                            {/if}
                        </td>
                        <td>
                            {if condition="$vo.restrict_type eq 'realname'"}
								{if ($vo.restrict_status==1)}
								<font>关闭</font>
								{elseif($vo.restrict_status==2)}
								<font>非强制开启</font>
								{elseif($vo.restrict_status==3)}
								<font>强制开启</font>
								{/if}
                            {/if}
                            {if condition="$vo.restrict_type eq 'preventhook'"}
								{if ($vo.restrict_status==1)}
								<font>关闭</font>
								{else if condition="$vo.restrict_status eq '2'"}
								<font>开启</font>
								{/if}
                            {/if}
                        </td>
                        <td>{:escape($vo.add_user_name)}</td>
                        <td>{:date('Y-m-d H:i:s',$vo['create_time'])}</td>
                        <td>{:escape($vo.update_user_name)}</td>
                        <td>{:date('Y-m-d H:i:s',$vo['update_time'])}</td>
                        <td class="td-manage">
                            <a href="{:url('edit',['id'=>$vo.id])}" class="layui-btn btn-info">
                                <i class="icon-edit"></i>编辑
                            </a>
                            <a href='javascript:void(0);' onclick="del_info(this,'{:url('delete',['id'=>$vo.id])}')" data-url="/CSC/reject" class="layui-btn layui-btn-danger">
                                删除
                            </a>
                        </td>
                    </tr>
                    {/volist}
                    {/notempty}
                    </tbody>
                </table>
            
                <!-- <div class="pager-container">
                        <span>
                          {$restrict->total()}条记录 
                        </span>
                    {$page}
                </div> -->


                    <div class="pager-container">
                        <span>
                            {$restrict->total()}条记录 
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>

        </form>
    
</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">
    $(document).ready(function () {
        var restrictType = $('select[name=restrict_type]').val();
	//	alert(restrictType);
        if (restrictType == 'realname') {
             $("#realname_restrict_div").show();
			 $("#preventhook_restrict_div").hide();
			 $("#null_restrict_div").hide();
        } else if(restrictType == 'preventhook') {
             $("#realname_restrict_div").hide();
			 $("#preventhook_restrict_div").show();
			 $("#null_restrict_div").hide();
        }
		else {
             $("#realname_restrict_div").hide();
			 $("#preventhook_restrict_div").hide();
			 $("#null_restrict_div").show();
        }
    });

 $(document).ready(function () { 
    $("#J_gameid").FuzzySearch({
    	 
    	 inputID    : 'J_gameid',
		 title   	: '请输入游戏名称',
		 data       :{:json_encode($gameList)},
		 searchBtn	:'J_search_submit',
	    });
     
    $(function () {
        $("#J_gameid_show").attr('name','game_name');
        $("#J_gameid_show").val("{:input('get.game_name')}");
    })

    layui.use(['form', 'laydate'], function () {
        var laydate = layui.laydate;
        var form 	= layui.form;

        var starttime = laydate.render({
            elem: '#start',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });
        
        // 监听平台选择--是否有版号
        form.on('select(changeType-filter)', function (data) {
		//	alert(data.value);
			if (data.value == 'realname') {
				 $("#realname_restrict_div").show();
				 $("#preventhook_restrict_div").hide();
				 $("#null_restrict_div").hide();
			} else if(data.value == 'preventhook') {
				 $("#realname_restrict_div").hide();
				 $("#preventhook_restrict_div").show();
				 $("#null_restrict_div").hide();
			}
			else {
				 $("#realname_restrict_div").hide();
				 $("#preventhook_restrict_div").hide();
				 $("#null_restrict_div").show();
			}
        });
    });
});
</script>
{/block}