{extend name="layout/base" /}

{block name="detail_css"}
<style>

.layui-form-item .layui-input-inline {
    float: left;
    width: 290px;
    margin-right: 10px;
}
</style>
{/block}

{block name="content"}
<div class="x-body">
    <fieldset class="layui-elem-field layui-field-title" >
        <legend>防沉迷规则说明</legend>
    </fieldset>
    <form method="post" class="layui-form" action="{:url('restrictExplain')}">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                消息内容：
            </label>
            <div class="layui-input-inline">
                <div class="layui-input-inline" style="width:50%" >
                    <textarea name="content" id="J_content" autocomplete="off" rows="30" cols="100" lay-verify="required">{$info|default=''}</textarea>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary toRest">重置</button>
            </div>
        </div>

    </form>
</div>
{/block}
{block name="detail_js"}
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.config.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.all.min.js"></script>
<!--<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/lang/zh-cn/zh-cn.js"></script>-->
<!--<script src="http://apps.bdimg.com/libs/layer/2.1/layer.js"></script>-->
<script>
    layui.use(['form', 'layer', 'laydate', 'layedit'], function () {
        var ue = UE.getEditor('J_content');

        $(".toRest").click(function () {
            ue.setContent('')
        })
    });



</script>
{/block}