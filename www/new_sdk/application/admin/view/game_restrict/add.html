{extend name="layout/content" /}
{block name="header"}
<title>新增</title>
<link href="__STATIC__/lib/layui_formSelect/formSelects-v4.css" rel="stylesheet" type="text/css">
<link href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}" rel="stylesheet" type="text/css">
<style>
    .layui-form-label {
        width: 180px;
    }

    .select2 + .layui-form-select {
        display: none;
    }
    .layui-form-select dl{
        z-index: 1000;
    }
    .xm-select-parent .xm-form-select dl{
        z-index: 999999;
    }
    .layui-form-item .layui-input-inline {
        width: 210px;
    }
    .layui-form-select dl {
        z-index: 8999999;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">

    <form class="layui-form" action="{:url('add')}" method="post" enctype="multipart/form-data">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">游戏名称*：</label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <input type="hidden" id='J_gameid' name="game_id" value="{$Request.get.game_id}" />
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">限制类型*：</label>
            <div class="layui-input-inline">
                <select name="restrict_type" lay-filter="changeType-filter" lay-verify="required">
				    <option value="">请选择限制类型</option>
                    <option value="realname">实名弹窗</option>
                    <option value="preventhook">防沉迷限制</option>
                </select>
            </div>
        </div>


        <div class="layui-form-item" id="null_restrict_div">
            <label for="" class="layui-form-label">限制状态*：</label>
            <div class="layui-input-inline">
                <select name="null_restrict_status" id="null_restrict_status" lay-verify="required">
                    <option value="">请选择限制状态</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item" id="realname_restrict_div">
            <label for="" class="layui-form-label">限制状态*：</label>
            <div class="layui-input-inline">
                <select name="realname_restrict_status" id="realname_restrict_status" lay-verify="required">
                    <option value="">请选择限制状态</option>
                    <option value="1">关闭</option>
                    <option value="2">非强制开启</option>
                    <option value="3">强制开启</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item"  id="preventhook_restrict_div" style="display:none">
            <label for="" class="layui-form-label">限制状态*：</label>
            <div class="layui-input-inline">
                <select name="preventhook_restrict_status" id="preventhook_restrict_status"  lay-verify="required">
                    <option value="">请选择限制状态</option>
                    <option value="1">关闭</option>
                    <option value="2">开启</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">操作：</label>
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit type="submit" lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn layui-btn-primary">返回
                </button>
            </div>
        </div>


    </form>
</div>

{/block}

{block name="footer"}

<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.config.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/lib/layui_formSelect/formSelects-v4.min.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script src="__STATIC__/lib/layui/layui.all.js?v={$Think.STATIC_VERSION}"></script>

<script>
    $(document).ready(function () {
        var restrictType = $('select[name=restrict_type]').val();
	//	alert(restrictType);
        if (restrictType == 'realname') {
             $("#realname_restrict_div").show();
			 $("#realname_restrict_status").attr('lay-verify', 'required');
			 $("#preventhook_restrict_div").hide();
			 $("#preventhook_restrict_status").removeAttr('lay-verify');
			 $("#null_restrict_div").hide();
			 $("#null_restrict_status").removeAttr('lay-verify');
        } else if(restrictType == 'preventhook'){
             $("#realname_restrict_div").hide();
			 $("#realname_restrict_status").removeAttr('lay-verify');
			 $("#preventhook_restrict_div").show();
			 $("#preventhook_restrict_status").attr('lay-verify', 'required');
			 $("#null_restrict_div").hide();
			 $("#null_restrict_status").removeAttr('lay-verify');
        }
		else{
             $("#realname_restrict_div").hide();
			 $("#realname_restrict_status").removeAttr('lay-verify');
			 $("#preventhook_restrict_div").hide();
			 $("#preventhook_restrict_status").removeAttr('lay-verify');
			 $("#null_restrict_div").show();
			 $("#null_restrict_status").attr('lay-verify', 'required');
		}
    });

    $("#J_gameid").FuzzySearch({
        inputID     : 'J_gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_submit_btn',
    });

    layui.use(['form', 'layer'], function () {
        var ue = UE.getEditor('J_content');
     
        var html, selected, option = '';

        var form = layui.form,
            layer = layui.layer;

        // 监听平台选择--是否有版号
        form.on('select(changeType-filter)', function (data) {
		//	alert(data.value);
			if (data.value == 'realname') {
				 $("#realname_restrict_div").show();
				 $("#realname_restrict_status").attr('lay-verify', 'required');
				 $("#preventhook_restrict_div").hide();
				 $("#preventhook_restrict_status").removeAttr('lay-verify');
				 $("#null_restrict_div").hide();
				 $("#null_restrict_status").removeAttr('lay-verify');
			} else if(data.value == 'preventhook'){
				 $("#realname_restrict_div").hide();
				 $("#realname_restrict_status").removeAttr('lay-verify');
				 $("#preventhook_restrict_div").show();
				 $("#preventhook_restrict_status").attr('lay-verify', 'required');
				 $("#null_restrict_div").hide();
				 $("#null_restrict_status").removeAttr('lay-verify');
			}
			else{
				 $("#realname_restrict_div").hide();
				 $("#realname_restrict_status").removeAttr('lay-verify');
				 $("#preventhook_restrict_div").hide();
				 $("#preventhook_restrict_status").removeAttr('lay-verify');
				 $("#null_restrict_div").show();
				 $("#null_restrict_status").attr('lay-verify', 'required');
			}
        });
    });
</script>
{/block}