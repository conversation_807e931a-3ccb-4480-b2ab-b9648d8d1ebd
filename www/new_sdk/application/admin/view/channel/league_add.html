{extend name="layout/content" /}
{block name="header"}
<title>添加菜单</title>
{/block}
{block name="content"}
<body>
<div class="x-body">
		<form method="post" class="layui-form" action="{:url('add_league_post')}">
			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>商务账号：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>登录密码：
				</label>
				<div class="layui-input-inline">
					<input type="password" name="password" required="" lay-verify="required" autocomplete="off"
						   class="layui-input">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>确认登录密码：
				</label>
				<div class="layui-input-inline">
					<input type="password" name="repassword" required="" lay-verify="required" autocomplete="off"
						   class="layui-input">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					备注：
				</label>
				<div class="layui-input-inline">
					<textarea placeholder="请输入内容" name="beizhu" class="layui-textarea"></textarea>
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>登录验证：
				</label>
				<div class="layui-input-inline">
					<select class="form-control" name="login_check">
						<option value="1">开启</option>
						<option value="0">关闭</option>
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>手机号码：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="mobile" required="" lay-verify="required" autocomplete="off"
						   class="layui-input">
				</div>
			</div>
			<!--
			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>支付密码：
				</label>
				<div class="layui-input-inline">
					<input type="password" name="pay_password" required="" lay-verify="required" autocomplete="off"
						   class="layui-input">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					<span class="x-red">*</span>确认支付密码：
				</label>
				<div class="layui-input-inline">
					<input type="password" name="pay_repassword" required="" lay-verify="required" autocomplete="off"
						   class="layui-input">
				</div>
			</div>
			-->

			<!--
			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					用户账号：
				</label>
				<div class="layui-input-inline">
					<select class="form-control" name="show_full_account">
						<option value="0">密文</option>
						<option value="1">明文</option>
					</select>
				</div>
			</div>
			-->
			<input type="hidden" name="show_full_account" value="1"/>
			
			<div class="layui-form-item">
				<label class="layui-form-label">状态：</label>
				<div class="layui-input-block">
					<input type="radio" name="status" value="1" title="正常" checked>
					<input type="radio" name="status" value="0" title="冻结">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">推广方式：</label>
				<div class="layui-input-block">
					<input type="radio" name="channel_tg_type" value="0" title="普通" checked="">
					<input type="radio" name="channel_tg_type" value="1" title="外放(商务代结算)" >
					<input type="radio" name="channel_tg_type" value="2" title="外放(公会结算)">
				</div>
			</div>
			
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit lay-filter="formDemo">提交</button>
					<a href="{:url('leaguesmanage')}" type="button" class="layui-btn layui-btn-primary">返回</a>
				</div>
			</div>
		</form>
	</div>
</body>
{/block}


{block name="footer"}
{/block}