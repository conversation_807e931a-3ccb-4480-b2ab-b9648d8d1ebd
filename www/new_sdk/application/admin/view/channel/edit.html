{extend name="layout/content" /}
{block name="header"}
<title>编辑渠道</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block}
{block name="content"}
<body>
<div class="x-body">
		<form method="post" class="layui-form" action="{:url('editPost')}?type={$type}">
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">
					<span class="x-red">*</span>账号名称：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" autocomplete="off" class="layui-input" value="{$channel.username}" disabled>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">登录密码：</label>
				<div class="layui-input-inline">
					<input type="password" name="password" autocomplete="off" class="layui-input">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">确认登录密码：</label>
				<div class="layui-input-inline">
					<input type="password" name="repassword"  autocomplete="off" class="layui-input">
				</div>
			</div>

			{if condition="$channel['channel_level'] gt 0"}
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">
					<span class="x-red"></span>平台币余额：
				</label>
				<label class="layui-form-label">
					{$channel.ptb_amt}
				</label>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">
					<span class="x-red"></span>结算币余额：
				</label>
				<label class="layui-form-label">
					{$channel.js_amount}
				</label>
			</div>
			{/if}

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">备注：</label>
				<div class="layui-input-inline">
					<textarea placeholder="请输入内容" name="beizhu" class="layui-textarea">{$channel.beizhu}</textarea>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">
					<span class="x-red">*</span>登录验证：
				</label>
				<div class="layui-input-inline">
					<select class="form-control" name="login_check" lay-filter="login-ver-filter">
						<option value="1" {if condition="$channel['login_check'] eq 1"} selected="selected" {/if}>开启</option>
						<option value="0" {if condition="$channel['login_check'] eq 0"} selected="selected" {/if}>关闭</option>
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">
					<span class="x-red">{if condition="$channel['login_check'] eq 1"}*{else /} {/if}</span>手机号码：
				</label>
				<div class="layui-input-inline">
					<!-- <input type="text" name="mobile" lay-verify="" autocomplete="off" class="layui-input" value="{$channel.mobile}"> -->

					<input type="text" name="mobile" {if condition="$channel['login_check'] eq 1"} lay-verify="required" {else /} lay-verify="" {/if}  autocomplete="off" class="layui-input" value="{$channel.mobile}">
					
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">支付密码：</label>
				<div class="layui-input-inline">
					<input type="password" name="pay_password"  autocomplete="off"  class="layui-input">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">确认支付密码：</label>
				<div class="layui-input-inline">
					<input type="password" name="pay_repassword" autocomplete="off" class="layui-input">
				</div>
			</div>
			
			<!--
			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">用户账号：</label>
				<div class="layui-input-inline">
					<select class="form-control" name="show_full_account">
						<option value="0" {if condition="$channel['show_full_account'] eq 0"} selected="selected" {/if}>密文</option>
						<option value="1" {if condition="$channel['show_full_account'] eq 1"} selected="selected" {/if}>明文</option>
					</select>
				</div>
			</div>
			-->
			<input type="hidden" name="show_full_account" value="{$channel.show_full_account}"/>


			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;">状态：</label>
				<div class="layui-input-block">
					<input type="radio" name="status" value="1" title="正常" {if condition="$channel['status'] eq 1"} checked {/if} >
					<input type="radio" name="status" value="0" title="冻结" {if condition="$channel['status'] eq 0"} checked {/if}>
				</div>
			</div>
			{switch name="$channel.channel_level"} 
			    {case value="1"}
			    	<div class="layui-form-item">
						<label class="layui-form-label" style="width:160px;">专服结算方式：</label>
						<div class="layui-input-block">
							<input type="radio" name="cps_settle_period" value="1" title="周结" {if condition="$channel['cps_settle_period'] eq 1"} checked {/if} >
							<input type="radio" name="cps_settle_period" value="2" title="日结" {if condition="$channel['cps_settle_period'] eq 2"} checked {/if}>
						</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label" style="width:160px;">混服结算方式：</label>
						<div class="layui-input-block">
							<input type="radio" name="mcps_settle_period" value="1" title="周结" {if condition="$channel['mcps_settle_period'] eq 1"} checked {/if} >
							<input type="radio" name="mcps_settle_period" value="2" title="日结" {if condition="$channel['mcps_settle_period'] eq 2"} checked {/if}>
						</div>
					</div>
			    {/case} 
			    {case value="0"}
			    	<div class="layui-form-item">
						<label class="layui-form-label" style="width:160px;">推广方式：</label>
						<div class="layui-input-block">
							<input type="radio" name="" value="0" title="普通" {if condition="$channel['channel_tg_type'] eq 0"} checked {/if} disabled="">
							<input type="radio" name="" value="1" title="外放(商务代结算)" {if condition="$channel['channel_tg_type'] eq 1"} checked {/if} disabled="">
							<input type="radio" name="" value="2" title="外放(公会结算)" {if condition="$channel['channel_tg_type'] eq 2"} checked {/if} disabled="">
						</div>
					</div>
			    {/case} 
			    
				{default /} 

			{/switch}
			

			<div class="layui-form-item">
				<label class="layui-form-label" style="width:160px;"></label>
				<div class="layui-input-inline">
					<input type="hidden" name="id" value="{$channel.adminid}">
					<input type="hidden" name="channel_id" value="{$channel.channelid}">
					<button class="layui-btn" id="J_submit_btn" lay-submit lay-filter="*">提交</button>
					{switch name="$type"} 
					    {case value="1"}
					    	<a href="{:url('leaguesmanage')}" type="button" class="layui-btn layui-btn-primary">返回</a>
					    {/case} 
						{default /} 
							<a href="{:url('index')}" type="button" class="layui-btn layui-btn-primary">返回</a>
					{/switch}
				</div>
			</div>
		</form>
	</div>
</body>
{/block}

{block name="footer"}

<script type="text/javascript" src="__STATIC__/js/jquery_v3.3.1.js"></script>
<script type="text/javascript" src="__STATIC__/lib/layui/layui.js?v={$Think.STATIC_VERSION}" charset="utf-8"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript">

    layui.use(['form', 'layer'], function () {
        var form = layui.form,
            layer = layui.layer;


		form.on('select(login-ver-filter)', function (data) {
		
			//开启
			if(data.value==1){
				$("input[name=mobile]").attr("lay-verify","required");
				$("input[name=mobile]").parent().siblings().children("span").text("*");
			}else{
				//关闭
				$("input[name=mobile]").removeAttr("lay-verify")
				$("input[name=mobile]").parent().siblings().children("span").text("");
			}
		})


        form.on('submit(*)', function (data) {
            console.log(data.elem) //被执行事件的元素DOM对象，一般为button对象
            console.log(data.form) //被执行提交的form对象，一般在存在form标签时才会返回
            console.log(data.field) //当前容器的全部表单字段，名值对形式：{name: value}

        //    return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });





		







    })

</script>
{/block}