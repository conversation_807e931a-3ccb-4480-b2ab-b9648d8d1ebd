{extend name="layout/content" /}
{block name="header"}
<title>添加菜单</title>
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">
{/block}
{block name="content"}
<style type="text/css">
	.layui-form-label{
		width:130px;
	}
	.img{
		width: 88px;
		height: 88px;
		cursor: pointer;
	}
</style>
<body>

<div class="x-body" id="viewer_wrap">
		<form class="layui-form">
			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					姓名/法人：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['name']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					身份证号：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['person_id']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					支付宝账号：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['zhifubao_number']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					支付宝/银行绑定手机：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['mobile']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					银行卡号：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['bankNum']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					开户银行省市：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['provinceCity']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					银行名称：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['bankName']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					开户支行：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['openBank']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					开户支行行号：
				</label>
				<div class="layui-input-inline">
					<input type="text" name="username" required="" lay-verify="required" autocomplete="off"
						   class="layui-input" value="{$list['bankId']}" disabled="">
				</div>
			</div>

			<div class="layui-form-item">
				<label for="" class="layui-form-label">
					身份证/营业执照图片
				</label>
				<div class="layui-input-inline">

					<img src="https://cdn.{$Think.QM_DOMAIN_URL}/{$list['back_image_url']}" class="img check_photo"
						 id="terst" data-original="{$Think.STATIC_DOMAIN}{$list['back_image_url']}">

					<img src="https://cdn.{$Think.QM_DOMAIN_URL}/{$list['front_image_url']}" class="img check_photo"
						 data-original="{$Think.STATIC_DOMAIN}{$list['front_image_url']}">
				</div>
			</div>
			<div style="text-align: center;">
				<a type="button" class="layui-btn layui-btn-primary" id="cancell">关闭</a>
			</div>
			
		</form>
	</div>
</body>
{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script type="text/javascript">
	// $(document).on('click', '.img', function(event) {
	// 	var src = $(this).attr('src');
	// 	console.log(src)
	// 	var img = '<img src="'+src+'" style="max-width:450px;">'
    //     layer.open({
	// 	  type: 1,
	// 	  title: false,
	// 	  closeBtn: 0,
	// 	  area: ['auto'],
	// 	  skin: 'layui-layer-nobg', //没有背景色
	// 	  shadeClose: true,
	// 	  content: img
	// 	});
	// });
	//取消按钮
	$(document).on('click', '#cancell', function(event) {
		var index=parent.layer.getFrameIndex(window.name);
		parent.layer.close(index);
	});

	$(".check_photo").on("click", function () {
            $(this).siblings().click()
        })

	var viewer = new Viewer(document.getElementById('viewer_wrap'), {
        url: 'data-original'
    });
</script>
{/block}