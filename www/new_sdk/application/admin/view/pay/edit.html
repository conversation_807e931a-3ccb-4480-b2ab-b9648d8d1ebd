{extend name="layout/base" /}

{block name="content"}
<body class="page-container-bg-solid page-boxed">
	<!-- BEGIN CONTAINER -->
	<div class="page-container">
		<!-- BEGIN CONTENT -->
		<div class="page-content-wrapper">
			<!-- BEGIN PAGE CONTENT BODY -->
			<div class="page-content" style="min-height: 938px;">
				<!-- div class="container" -->
				<div style="margin-left: 10px;">
					<ul class="page-breadcrumb breadcrumb">
						<li>
							<a href="{:url('Index/index')}" target="_top">Home</a>
							<i class="fa fa-circle"></i>
						</li>
					</ul>

					<!-- BEGIN PAGE CONTENT INNER -->
					<div class="page-content-inner">
						<div class="portlet light portlet-fit ">
							<div class="portlet-title">
							</div>

							<div class="portlet-body">
							    <div class="note note-success">
							        <h4 class="block">编辑</h4>
							    </div>

							    <form action="{:url('Member/update',['id'=>input('id')])}" method="POST" class="form-horizontal">
							        <div class="form-group">
							            <label class="col-md-3 control-label" for="title">游戏名称:</label>
							            <div class="col-md-5">
							                <input name="name" class="form-control" type="text" value="{:escape($gameInfo.name)}" style="width: 50%;">
							            </div>
							        </div>
							
							        <div class="form-group">
							          <label class="col-md-3 control-label" for="order_recheck">订单二次验证:</label>
							          <div class="col-md-5">
							          	<select name="order_recheck" class="form-control input-small input-inline select" style="width: 200px;">
						                    <option {if condition="$gameInfo.order_recheck eq '1'"}selected="selected"{/if} value="1">是</option>
						                    <option {if condition="$gameInfo.order_recheck eq '0'"}selected="selected"{/if} value="0">否</option>
						                </select>
							          </div>
							        </div>
							        
							        <div class="form-group">
							          <label class="col-md-3 control-label" for="cooperation_status">对接状态:</label>
							          <div class="col-md-5">
							          	<select name="cooperation_status" class="form-control input-small input-inline select" style="width: 50%;">
              								<option {if condition="$gameInfo.cooperation_status eq '0'"}selected="selected"{/if} value="0">对接中</option>
              								<option {if condition="$gameInfo.cooperation_status eq '1'"}selected="selected"{/if} value="1">上线</option>
              								<option {if condition="$gameInfo.cooperation_status eq '2'"}selected="selected"{/if} value="2">白名单限制</option>            
              							</select>
							          </div>
							        </div>
							
							        <div class="form-group">
							            <label class="col-md-3 control-label" for="channel_version">强更版本:</label>
							            <div class="col-md-5">
							                <input name="channel_version" class="form-control" type="text" value="{$gameInfo.channel_version}" style="width: 50%;">
							            </div>
							        </div>
							        
							        <div class="form-group">
										<label class="col-md-3 control-label" for="platform">平台选择:</label>
								       	<div class="col-md-5">
								          	<select name="platform" class="form-control input-small input-inline select" style="width: 200px;">
								                <option {if condition="$gameInfo.platform eq '0'"}selected="selected"{/if} value="0">安卓</option>
								                <option {if condition="$gameInfo.platform eq '1'"}selected="selected"{/if} value="1">IOS</option>
								            </select>
								        </div>
							        </div>
							        
							        <div class="form-group">
										<label class="col-md-3 control-label" for="remarks">备注:</label>
								       	<div class="col-md-5">
								       		<textarea name="remarks" class="form-control" style="width: 50%;">{$gameInfo.remarks}</textarea>
								        </div>
							        </div>
							        
							        <div class="form-group">
							            <label class="col-md-3 control-label" for="title">操作:</label>
							            <div class="col-md-5">
							                <input type="submit" class="btn btn-outline btn-circle purple-sharp blockui uppercase" name="Submit" value="确定修改">
							                <input type="reset" name="Reset" value="返回" class="btn btn-danger btn-circle" data-title="您确定要放弃修改吗？" data-confirmation-by="cancel-edit" data-placement="right" data-btn-ok-label="确定放弃" data-btn-ok-icon="icon-like" data-btn-ok-class="btn-success" data-btn-cancel-label="继续编辑" data-btn-cancel-icon="icon-close" data-btn-cancel-class="btn-danger" data-original-title="" title="">
							            </div>
							        </div>
							    </form>
							</div>
					</div>
				</div>
						<!-- END PAGE CONTENT INNER -->
			</div>
		</div>
				<!-- END PAGE CONTENT BODY -->
				<!-- END CONTENT BODY -->
		</div>
			<!-- END CONTENT -->
	</div>
{/block}

{block name="detail_js"}
		<!-- BEGIN PAGE LEVEL PLUGINS -->
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-confirmation/bootstrap-confirmation.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/moment.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-editable/bootstrap-editable/js/bootstrap-editable.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-editable/inputs-ext/address/address.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-editable/inputs-ext/wysihtml5/wysihtml5.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-typeahead/bootstrap3-typeahead.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/bootstrap-suggest/bootstrap-suggest.min.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/global/plugins/jquery.pulsate.min.js" type="text/javascript"></script>

		<!-- END PAGE LEVEL PLUGINS -->
		<!-- BEGIN THEME GLOBAL SCRIPTS -->
		<script src="__STATIC__/metronic4.5.2/global/scripts/app.js" type="text/javascript"></script>
		<!-- END THEME GLOBAL SCRIPTS -->
		<!-- BEGIN THEME LAYOUT SCRIPTS -->
		<script src="__STATIC__/metronic4.5.2/layouts/layout3/scripts/layout.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/layouts/layout3/scripts/demo.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/layouts/global/scripts/quick-sidebar.min.js" type="text/javascript"></script>

		<link href="__STATIC__/footable/footable.core.css" rel="stylesheet">
		<script type="text/javascript" src="__STATIC__/footable/footable.js"></script>

		<link href="__STATIC__/toastr/build/toastr.min.css" rel="stylesheet">
		<script type="text/javascript" src="__STATIC__/toastr/build/toastr.min.js"></script>
		<!-- END THEME LAYOUT SCRIPTS -->

		<script src="__STATIC__/js/admin/function.js" type="text/javascript"></script>
		<script src="__STATIC__/metronic4.5.2/common.js?v={$Think.STATIC_VERSION}" type="text/javascript"></script>
{/block}