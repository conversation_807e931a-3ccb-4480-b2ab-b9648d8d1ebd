{extend name="layout/content" /}
{block name="header"}
<title>充值禁止列表-编辑规则</title>

<style>
    .layui-form-label {
        width: 100px;
    }

    header {
        text-align: right;
        padding: 10px;
        background: #EFEFEF;
        font-weight: 700;
        font-size: 15px;
    }

    .layui-btn:nth-of-type(1) {
        margin-left: 130px;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">
    <div class="layui-row">
        <form class="layui-form layui-col-md12" method="post" action="{:url('ruleEdit',['id'=>$data.id])}">

            <div class="layui-form-item">
                <label for="" class="layui-form-label">游戏名称：</label>
                <div class="layui-input-inline">
                    <select name="game_id" lay-search>
                        <option value="">请选择游戏名称</option>
                        {volist name="game_list" id="g"}
                        <option value="{$g.id}"
                                {if condition="$data.game_id eq $g.id" }selected="selected" {/if}> {$g.name}</option>
                        {/volist}
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label">拒绝的充值方式：</label>
                <div class="layui-input-inline">
                    <select name="deny_paytype">
                        <option value="">请选择</option>
                        {notempty name="paytype_list"}
	                    {volist name="paytype_list" id="pvo"}
	                    <option value="{$pvo.paytype}" {if condition="$data.deny_paytype eq $pvo.paytype" }selected="selected"{/if}>{$pvo.payname}</option>
	                    {/volist}
	                    {/notempty}
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <button class="layui-btn" lay-submit="" lay-filter="">确定</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn layui-btn-primary">返回
                </button>
            </div>

        </form>
    </div>
</div>
{/block}

{block name="footer"}
{/block}
