{extend name="layout/content" /} {block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>禁止充值列表</title>
<style>
    label {
        padding-top: 10px;
        margin-left: 10px;
    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }
    .set_width{
        margin-right: 5px;
    }
</style>
{/block} {block name="content"}
<div class="x-body">
    
        <form class="layui-form">
                <div style="float:left">
                    <a class="layui-btn" href="{:url('ruleadd')}">
                        <i class="layui-icon">&#xe61e;</i>新增记录
                    </a>
                </div>

                
                <div style="float:right">
                    <div class="layui-inline">
                        <label>游戏：</label>
                        <div class="layui-input-inline FuzzySearch_Container">
                            <div>
                                <input type="hidden" id='J_gameid' name="gameid" value="{$Request.get.gameid}" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="" id="J_search_submit">查询</button>
                    </div>
                </div>

                <div style="clear:both"></div>


                <table class="layui-table">
                        <thead>
                            <tr>
                                <th>游戏ID</th>
                                <th>游戏名称</th>
                                <th>禁止充值方式</th>
                                <th>发布时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {volist name="list" id="vo"}
                            <tr>
                                <td>{$vo.game_id}</td>
                                <td>{$game_name[$vo.game_id] ?? ''}</td>
                                <td>{$paytype_list[$vo.deny_paytype]}</td>
                                <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                                <td>
                                    <a href="{:url('ruleEdit',['id'=>$vo.id])}" class="layui-btn">
                                        <i class="layui-icon icon-edit">&#xe642;</i>编辑
                                    </a>
                                    <a href="javascript:;" onclick="del_info(this,'{:url(\'ruleDelete\',[\'id\'=>$vo.id])}')" class="layui-btn layui-btn-danger">
                                        <i class="layui-icon">&#xe640;</i>删除
                                    </a>
                                </td>
                            </tr>
                            {/volist}
                        </tbody>
                    </table>
                    
                
                  

                    <div class="pager-container" style="margin-top:10px;">
                        <span>
                            {$list->total()}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>

        </form>
    

</div>
{/block} 
{block name="footer"} 

<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
	$("#J_gameid").FuzzySearch({
		 inputID    : 'J_gameid',
		 title   	: '请输入游戏名称',
		 data       :{:json_encode($game_list)},
		 searchBtn	:'J_search_submit',
   }); 
</script>
{/block}