{extend name="layout/base" /} {block name="detail_css"}
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap-table.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<link rel="stylesheet" href="__STATIC__/css/admin/paginator.css?v={$Think.STATIC_VERSION}">
<title>渠道审核表</title>
<style>
	label {
		padding-top: 10px;
		margin-left: 10px;
	}

	

	
	.hide{
        display: none;
    }

	.table>tbody>tr>td, 
    .table>tbody>tr>th, 
    .table>tfoot>tr>td, 
    .table>tfoot>tr>th, 
    .table>thead>tr>td, 
    .table>thead>tr>th {
		padding: 8px;
		line-height: 1.42857143;
		vertical-align: middle;
		border-top: 1px solid #ddd;
	}


	table td,
    table th {
        text-align: center;
    }

    .detail-view td,
    .detail-view th {
        text-align: left;
	}
	
	.layui-form-label{
		width: 90px;
		margin: 0;
	}

	.layui-inline{
		margin-top: 10px;
	}
</style>


{/block} {block name="content"}
<div class="x-body">
	

		<form class="layui-form" id="J_search_form">


			<div>

		
				<div class="layui-inline">
					<label class="layui-form-label">渠道名：</label>
					<div class="layui-input-inline">
						<input class='layui-input' type="text" id='username' name="username" value="{:input('username')}" placeholder="请输入渠道名"/>
					</div>
				</div>


				<div class="layui-inline">
					<label class="layui-form-label">手机号：</label>
					<div class="layui-input-inline">
						<div>
							<input class='layui-input' type="text" id='linkman_mobile' name="linkman_mobile" value="{:input('linkman_mobile')}"  placeholder="请输入手机号"/>
						</div>
					</div>
				</div>



				<div class="layui-inline">
					<label class="layui-form-label">审核状态：</label>
					<div class="layui-input-inline">
						<select name="status">
							<option value="">- 请选择审核状态 -</option>
							<option value="0" {if condition="input('status') eq '0'" }selected="selected" {/if}>待审核</option>
							<option value="1" {if condition="input('status') eq '1'" }selected="selected" {/if}>通过</option>
							<option value="2" {if condition="input('status') eq '2'" }selected="selected" {/if}>未通过</option>
						</select>
					</div>
				</div>



				<div class="layui-inline">
					<label class="layui-form-label">申请时间：</label>
					<!-- <div class="layui-input-inline"> -->
						<input class="layui-input" placeholder="开始日" name="start_time" id="start" value="{:input('request.start_time',date('Y-m-d'))}" autocomplete="off" style="width:130px;display:inline-block">
						<span>-</span>
						<input class="layui-input" placeholder="截止日" name="end_time" id="end" value="{:input('request.end_time',date('Y-m-d'))}" autocomplete="off" style="width:130px;display:inline-block">
					<!-- </div> -->
				</div>
				
				<div class="layui-inline" style="margin-left:10px;">
					<button class="layui-btn" lay-submit lay-filter="search" id="J_search_btn">查询</button>
				</div>

				
			</div>
		




			<table id="objTable" data-toolbar="#toolbar" data-detail-view="true" data-detail-formatter="detailFormatter">
					<thead>
						<tr>
							<th>渠道名</th>
							<th>姓名</th>
							<th>手机号</th>
							<th>QQ号</th>
							<th>微信号</th>
							<th>主要经营模式</th>
							<th>规模(人数)</th>
							<th>申请时间</th>
							<th>审核状态</th>
							<th>操作</th>
							<th class="hide">账户类型</th>
							<th class="hide">支付宝账号</th>
							<th class="hide">支付宝姓名</th>
							<th class="hide">银行账号</th>
							<th class="hide">公司全称</th>
							<th class="hide">开户银行</th>
							<th class="hide">联系人邮箱</th>
							<th class="hide">做过的游戏</th>
							<th class="hide">做过的平台</th>
							<th class="hide">是否有交易平台店铺</th>
							<th class="hide">交易平台</th>
							<th class="hide">店铺名称</th>
							<th class="hide">主营店铺地址</th>
							<th class="hide">收货地址</th>
							<th class="hide">对接人</th>
							<th class="hide">审核人</th>
							<th class="hide">审核时间</th>
							<th class="hide">备注</th>
						</tr>
					</thead>
					<tbody>
						{notempty name="list"} {volist name="list" id="vo"}
						<tr>
							<td>{:escape($vo.username)}</td>
							<td>{:escape($vo.real_name)}</td>
							<td>{$vo.linkman_mobile}</td>
							<td>{$vo.account_qq}</td>
							<td>{$vo.account_wechat}</td>
							<td>
								{if condition="$vo.account_type == 'private'"}
									{switch name=$vo.business_mode_private} {case value="0"}折扣代充{/case} {case value="1"}折扣公会{/case} {case value="2"}纯公会{/case} {case value="3"}萌新{/case}
									{case value="4"}其他{/case}
									{default /} {/switch}
								{else /}
									{switch name=$vo.business_mode_public} {case value="0"}cps外放{/case} {case value="1"}内会推广{/case} {case value="2"}其他{/case} 
									{default /} {/switch}
								{/if}
							</td>
							<td>{$vo.company_scale}</td>
							<td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
							<td>
								{if($vo.status==0)} 待审核 {elseif($vo.status==1)} 通过 {elseif($vo.status==2)}未通过{else}  {/if}
							</td>
							<td class="td-manage">
								{if condition="$vo.status == '0'"}
								<a class="layui-btn layui-btn-xs layui-btn-danger toGameEnter" data-id="{$vo.id}""><i class="icon-edit"></i>审核</a>
								{else /}
								-
								{/if}
							</td>
							<td>
								{if($vo.account_type=='private')} 对私 {elseif($vo.account_type=='public')} 对公 {else}  {/if}
							</td>
							<td>{if condition="$vo.account_type=='private'"}{$vo.alipay_account}{else /}{/if}</td>
							<td>{if condition="$vo.account_type=='private'"}{$vo.alipay_realname}{else /}{/if}</td>
							<td>{if condition="$vo.account_type=='public'"}{$vo.bank_account}{else /}{/if}</td>
							<td>{if condition="$vo.account_type=='public'"}{$vo.company_name}{else /}{/if}</td>
							<td>
								{if condition="$vo.account_type=='public'"}
								{switch name=$vo.bank_name} {case value="1"}工商银行{/case} {case value="2"}建设银行{/case} {case value="3"}农业银行{/case} {case value="4"}中国银行{/case}
								{case value="5"}招商银行{/case} {case value="6"}民生银行{/case} {case value="7"}光大银行{/case} {case value="8"}交通银行{/case} {case value="9"}中信银行{/case}
								{case value="10"}平安银行{/case} {case value="11"}兴业银行{/case} {case value="12"}华夏银行{/case} {case value="13"}广发银行{/case} {case value="14"}浦发银行{/case}
								{case value="15"}日照银行{/case}
								{default /} {/switch}
								{else /}{/if}
							</td>
							<td>{if condition="$vo.account_type=='public'"}{$vo.linkman_email}{else /}{/if}</td>
							<td>{$vo.promote_games}</td>
							<td>{$vo.promote_platforms}</td>
							<td>
								{if($vo.company_has_shop=='1')} 是 {elseif($vo.company_has_shop=='0')} 否 {else}  {/if}
							</td>
							<td>
								{if condition="$vo.company_has_shop=='1'"}
								{switch name=$vo.shop_platform} {case value="0"}淘宝{/case} {case value="1"}交易猫{/case} {case value="2"}淘手游{/case} {case value="3"}7881{/case}
								{case value="4"}其他{/case}
								{default /} {/switch}
								{else /}{/if}
							</td>
							<td>{if condition="$vo.company_has_shop=='1'"}{$vo.shop_name}{else /}{/if}</td>
							<td>{if condition="$vo.company_has_shop=='1'"}{$vo.shop_address}{else /}{/if}</td>
							<td>{$vo.linkman_address}</td>
							<td>
								{if($vo.status==1 && $vo.contact_admin_id>0)}
								{php}
									echo \Think\Db::name('cy_admin')->where(['id' => $vo['contact_admin_id']])->value('username');
								{/php}
								{/if}
							</td>
							<td>
								{if($vo.status<>0 && $vo.audit_admin_id>0)}
								{php}
									echo \Think\Db::name('cy_admin')->where(['id' => $vo['audit_admin_id']])->value('username');
								{/php}
								{/if}
							</td>
							<td>{if(!empty($vo.audit_time))}{:date('Y-m-d H:i:s',$vo.audit_time)}{/if}</td>
							<td>{$vo.beizhu}</td>
						</tr>
						{/volist} {/notempty}
					</tbody>
				</table>
			
				<div class="pager-container">
					<span>
						{$list->total()}条记录
					</span>
					{$page}
					<div class="layui-inline" style="margin-left:10px;width:80px;margin-top:0px">
							<input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
					</div>
				</div>






	
		</form>


	

</div>

<!--  模态框  -->
<div class="modal fade" id="J_show_pointHistory_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" style="font-size: 25px;">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">审核信息</h4>
            </div>

            <div class="modal-body x-body">
                <form class="layui-form" action="auditApply" method="post">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:100px"><span class="x-red">*</span>审核状态：</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" id="status" value="1" title="通过">
                            <input type="radio" name="status" id="status" value="2" title="不通过">
                        </div>
                    </div>
					
					<div class="layui-form-item">
						<label for="contact_admin_id" class="layui-form-label" style="width:100px">对接人：</label>
						<div class="layui-input-inline FuzzySearch_Container">
							<div>
								<input type="hidden" id='contact_admin_id' name="contact_admin_id" value=""/>
							</div>
						</div>
					</div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注：</label>
                        <div class="layui-input-block">
                            <textarea name="beizhu" id="beizhu" placeholder="请输入备注" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <input id="enter_id" name="id" style="display: none" type="text" value="" />
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-btn-primary" data-dismiss="modal">取消</button>
	                        <button type='button' class="layui-btn" lay-submit lay-filter="formDemo" id="J_search_btn" onclick="auditApply();">确定</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{/block} 
{block name="detail_js"}
<script src="__STATIC__/js/admin/modal.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap-table.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
	var $table = $('#objTable');
	$table.bootstrapTable({});

	layui.use(['laydate', 'layer'], function () {
		var laydate = layui.laydate;

		//执行一个laydate实例
		laydate.render({
			elem: '#start', //指定元素
			type: 'date'
		});

		//执行一个laydate实例
		laydate.render({
			elem: '#end', //指定元素
			type: 'date'
		});
	});
	
	//表格的
	function detailFormatter(index, row) {
		var html = [];

		var obj = {
			0: "渠道名",
			1: "姓名",
			2: "手机号",
			3: "QQ号",
			4: "微信号",
			5: "主要经营模式",
			6: "规模(人数)",
			7: "申请时间",
			8: "审核状态",
			9: "操作",
			10: "账户类型",
			11: "支付宝账号",
			12: "支付宝姓名",
			13: "银行账号",
			14: "公司全称",
			15: "开户银行",
			16: "联系人邮箱",
			17: "做过的游戏",
			18: "做过的平台",
			19: "是否有交易平台店铺",
			20: "交易平台",
			21: "店铺名称",
			22: "主营店铺地址",
			23: "收货地址",
			24: "对接人",
			25: "审核人",
			26: "审核时间",
			27: "备注",
		}
		$.each(row, function (key, value) {

			if (key >= 10 && key<=27) {
				html.push('<p><b>' + obj[key] + ':</b> ' + value + '</p>');
			} else {
				html.push('<p class="hide"><b>' + obj[key] + ':</b> ' + value + '</p>');
			}
		});
		return html.join('');
	}
	
    $(function () {
		$("#contact_admin_id").FuzzySearch({
			 inputID    : 'contact_admin_id',
			 title   	: '请选择对接人',
			 data       :{:json_encode($admin_list)},
			 searchBtn	:'J_search_btn',
		});
        $(".toGameEnter").click(function () {
            // 重置
            $('.toReset').click();
            // id、channel_id赋值
            var id = $(this).data('id');
            $("#enter_id").val(id);

            $('#J_show_pointHistory_modal').modal();
        })

    })
	//通过审核
	function auditApply(){
		var status,contact_admin_id,beizhu;
		var enter_id = $("#enter_id").val();
		status = $("input[name='status']:checked").val();
		contact_admin_id = $("#contact_admin_id").val();
		beizhu = $("#beizhu").val();
		if(status!=1 && status!=2){
			layer.alert('请选择审核状态');
			return false;	
		}
		if(status==1){
			if(!contact_admin_id){
				layer.alert('请选择对接人');
				return false;
			}
		}
		
		$.ajax({
			type: "post",
			url: "{:url('ChannelApply/audit')}",
			async: false,
			data: {
				"id": enter_id,
				"status": status,
				"contact_admin_id": contact_admin_id,
				"beizhu": beizhu,
			},
			timeOut: 5000,
			dataType: "json",
			success: function (msg) {		
				if (msg['code']) {
					// alert后面新增加回调，点击确定后才会执行重新加载
					layer.alert('审核成功', function (index) {
						location.reload();
					});
				} else {
					layer.alert(msg['msg']);
				}
			},
			error: function () {
				layer.alert("网络错误，请刷新页面重试");
			},
		});
	}
</script>
{/block}