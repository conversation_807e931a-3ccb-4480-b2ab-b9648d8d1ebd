<?php

namespace app\admin\controller;


use think\Loader;
use think\Db;

class Business extends Admin
{
    protected $gameList;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

    }
    public function index(){
        $where = [];
        $order = 'id desc';

        $adminList = model('common/Admin')->alias('a')->join('nw_admin_role_user b','a.id=b.user_id')->field('a.id,a.username')->where(['b.role_id'=>['in',[12,17,18]]])->order('a.id desc')->column('a.username','a.id');
        $channelList = model('common/Channel')->where(['level'=>0])->order('id desc')->column('name','id');
        $business = model('common/Business');
        $list = $business
            ->order($order)
            ->paginate(10, false, ['query' => input('get.')])->each(function($item,$key) use ($channelList){

                $channel = explode(',',$item['channel_ids']);
                $tmp = [];
                foreach ($channel as $k=>$v){
                    $tmp[] = $channelList[$v];
                }
                $item['channel'] = implode(',',$tmp);
                return $item;
        });

        $this->assign('adminList', $adminList);
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    public function add(){
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if(!$data['select']){
                $this->error('请选择游戏');
            }
            if(model('common/Business')->where(['admin_id'=>$data['admin_id']])->find()){
                $this->error('用户已存在');
            }
            $data['channel_ids'] = $data['select'];
            unset($data['select']);
            $data['update_time'] = $data['create_time'] = time();
            $m = model('common/Business')->insertGetId($data);

            if (!empty($m)) {
                $this->success('添加成功', url('index'));
            }
            $this->error('添加失败');
        }

        $adminList = model('common/Admin')->alias('a')->join('nw_admin_role_user b','a.id=b.user_id')->where(['b.role_id'=>['in',[12,17,18]],'a.status'=>1])->field('a.id,a.username')->order('a.id desc')->select();

        $channelList = model('common/Channel')->where(['level'=>0,'status'=>1])->field('id,name')->order('id desc')->select();
        $this->assign('admin_list', $adminList);
        $this->assign('channel_list', $channelList);
        return $this->fetch();
    }

    public function edit(){
        $id = $this->request->param('id', '', 'intval');
        if (!$data = model('common/Business')->find($id)) {
            $this->error('参数错误，不存在该数据');
        }

        if ($this->request->isPost()) {

            if(model('common/Business')->where('admin_id','=',$data['admin_id'])->where('admin_id','<>',$data['admin_id'])->find()){
                $this->error('用户已存在');
            }

            $data = $this->request->post();
            if(!$data['select']){
                $this->error('请选择游戏');
            }
            $data['channel_ids'] = $data['select'];
            unset($data['select']);
            $data['update_time'] = $data['create_time'] = time();


            if (model('common/Business')->where(['id' => $id])->update($data)) {
                $this->success('修改成功', url('index'));
            } else {
                $this->error(model('common/Business')->getError() ?: '修改失败');
            }
            $this->error('修改失败');
        }

        $this->assign('data', $data);
        $adminList = model('common/Admin')->alias('a')->join('nw_admin_role_user b','a.id=b.user_id')->where(['b.role_id'=>['in',[12,17,18]],'a.status'=>1])->field('a.id,a.username')->order('a.id desc')->select();

        $channelList = model('common/Channel')->where(['level'=>0,'status'=>1])->field('id,name')->order('id desc')->select();
        $this->assign('admin_list', $adminList);
        $this->assign('channel_list', $channelList);
        return $this->fetch();
    }

    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel =model('common/Business');
        if (!$appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }
}
