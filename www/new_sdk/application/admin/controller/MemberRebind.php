<?php

/**
 * 用户换绑控制器
 *
 * Created by PhpStorm.
 * User: Admin
 * Date: 2018/5/10
 * Time: 10:27
 */

namespace app\admin\controller;

use think\Db;
use app\common\model\Channel;
use app\common\model\Game as GameModel;
use app\common\model\GameInfo;
use app\common\model\Logininfo;
use app\common\logic\SubPackage as SubChannel;
use app\common\model\AndroidSdk;
use app\common\model\AutoPackage;
use app\common\library\ValidateExtend;


class MemberRebind extends Admin
{
    protected $departmentModel;
    protected $gameModel;
    protected $subChannel;
    protected $channelModel;
    protected $sdkgamelistModel;
    protected $gameList;
    protected $channel;
    protected $gameIOS;  // 特殊处理的 IOS游戏id 数组

    protected function _initialize()
    {
        parent::_initialize();
        $this->subChannel       = new SubChannel;
        $this->channelModel     = new Channel;
        $this->gameModel        = new GameModel;
        $this->sdkgamelistModel = $this->cyModel('sdkgamelist');

        $this->gameList = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$selfGameList = array();
		foreach ($tmpSelfGameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
		$this->selfGameList  = $selfGameList;

        $this->channel  = $channelList = $this->nwModel('channel')->where(['flag' => 3])->field('id,name')->select();
        $this->childChannel = $this->nwModel('channel')->where(['flag' => 3,'level' => 3])->field('id,name')->select();
        // 特殊处理的 IOS游戏id 数组
        $this->gameIOS = config('gameIOS');
    }

    /**
     * 换绑记录列表
     * @return mixed
     */
    public function index()
    {
        $game_id				= input('game_id',0,'intval');
        $account				= input('account','','trim');
        $cusername				= input('cusername','','trim');
        $start_time				= input('start_time','','trim');
        $end_time				= input('end_time','','trim');
        $admin_type				= input('admin_type',0,'intval');

        $condition				 = [];
		if(input('account')){
			$condition['r.username']	= $account;
		}
		if(input('game_id') <> ''){
			$condition['r.game_id']	= $game_id;
		}
		if(input('admin_type') <> ''){
			$condition['r.admin_type']  = $admin_type;
		}
	    //申请开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $condition['r.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $condition['r.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $condition['r.create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }

		$whereRaw = '';
		if(input('cusername')<>''){
			$whereRaw .= ' r.origin_cusername="'.$cusername.'" or r.cusername="'.$cusername.'" ';
		}

		if($whereRaw){
			$rebindList = model('PlayerRebind')->alias('r')
							->join('cy_game g', 'r.game_id = g.id', 'left')
							->where($condition)
							->whereRaw($whereRaw)
							->field("r.id,r.userid,r.username,r.game_id,g.name as game_name,r.origin_cuserid,r.origin_cusername,r.cuserid,r.cusername,r.create_time,r.cmmt,r.admin_type,r.admin_id,r.admin_name")
							->order("r.id desc")
							->paginate(10, false, ['query' => $condition]);
		}
		else{
			$rebindList = model('PlayerRebind')->alias('r')
							->join('cy_game g', 'r.game_id = g.id', 'left')
							->where($condition)
							->field("r.id,r.userid,r.username,r.game_id,g.name as game_name,r.origin_cuserid,r.origin_cusername,r.cuserid,r.cusername,r.create_time,r.cmmt,r.admin_type,r.admin_id,r.admin_name")
							->order("r.id desc")
							->paginate(10, false, ['query' => $condition]);
		}

		foreach ($rebindList as $key => $value) {
			$channel = get_top_second_channel_name($value['origin_cuserid']);
			$rebindList[$key]['origin_top_channel']    = $channel['top_name'];
			$cuserid_channel = get_top_second_channel_name($value['cuserid']);
			$rebindList[$key]['cuser_top_channel']    = $cuserid_channel['top_name'];
		}
        // 获取分页显示
        $page = $rebindList->render();

        $this->assign('game_list', $this->selfGameList);
        $this->assign('page', $page);
        $this->assign('rebindList', $rebindList);

        return $this->fetch();
    }
    /**
     * 换绑申请列表
     * @return mixed
     */
    public function applyList()
    {
        $game_id				= input('game_id',0,'intval');
        $account				= input('account','','trim');
        $cusername				= input('cusername','','trim');
        $start_time				= input('start_time','','trim');
        $end_time				= input('end_time','','trim');
        $status					= input('status',0,'intval');

        $condition				 = [];
		if(input('account')){
			$condition['r.username']	= $account;
		}
		if(input('game_id') <> ''){
			$condition['r.game_id']	= $game_id;
		}
		if(input('status') <> ''){
			$condition['r.status']  = $status;
		}
	    //申请开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $condition['r.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $condition['r.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $condition['r.create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }

        $whereRaw = '';
        if (input('cusername') <> '') {
            $whereRaw .= ' r.origin_cusername="' . $cusername . '" or r.cusername="' . $cusername . '" ';
        }

        if ($whereRaw) {
            $rebindList = model('PlayerRebindApply')->alias('r')
                ->join('cy_game g', 'r.game_id = g.id', 'left')
                ->join('nw_subaccount s', 's.id=r.sub_id', 'left')
                ->where($condition)
                ->whereRaw($whereRaw)
                ->field("r.id,r.userid,r.username,r.game_id,g.name as game_name,r.origin_cuserid,r.origin_cusername,r.cuserid,r.cusername,r.create_time,r.cmmt,r.status,r.admin_id,r.admin_name,r.audit_remark,r.audit_admin_id,r.audit_admin_name,r.audit_time,r.picture1,r.picture2,r.picture3,r.oldPoint,r.deductionAmount,r.newPoint,r.getAmount,r.remark,r.playerAmount, s.sub_username")
                ->order("r.id desc")
                ->paginate(25, false, ['query' => $condition]);
        } else {
            $rebindList = model('PlayerRebindApply')->alias('r')
                ->join('cy_game g', 'r.game_id = g.id', 'left')
                ->join('nw_subaccount s', 's.id=r.sub_id', 'left')
                ->where($condition)
                ->field("r.id,r.userid,r.username,r.game_id,g.name as game_name,r.origin_cuserid,r.origin_cusername,r.cuserid,r.cusername,r.create_time,r.cmmt,r.status,r.admin_id,r.admin_name,r.audit_remark,r.audit_admin_id,r.audit_admin_name,r.audit_time,r.picture1,r.picture2,r.picture3,r.oldPoint,r.deductionAmount,r.newPoint,r.getAmount,r.remark,r.playerAmount, s.sub_username")
                ->order("r.id desc")
                ->paginate(25, false, ['query' => $condition]);
        }
        foreach ($rebindList as $key => $value) {
            $channel = get_top_second_channel_name($value['origin_cuserid']);
            $rebindList[$key]['origin_top_channel'] = $channel['top_name'];
            $cuserid_channel = get_top_second_channel_name($value['cuserid']);
            $rebindList[$key]['cuser_top_channel'] = $cuserid_channel['top_name'];
        }
        // 获取分页显示
        $page = $rebindList->render();

        $this->assign('game_list', $this->selfGameList);
        $this->assign('page', $page);
        $this->assign('rebindList', $rebindList);

        return $this->fetch();
    }

	//审核
    public function audit()
    {
        if (request()->isAjax()) {
            $id = input('id',0,'intval');
            $beizhu = input('beizhu','','trim');
            $status = input('status','0','intval');

            if(empty($id)){
                $this->error('ID不能为空');
            }
	        if($status<>1 && $status<>2){
                $this->error('非法审核状态');
            }

            $rebindApplyInfo = model('PlayerRebindApply')->where(['id' => $id])->find();
            if (empty($rebindApplyInfo)) {
                $this->error('玩家换绑申请不存在');
            } else if ($rebindApplyInfo['status'] <> 0) {
                $this->error('玩家换绑申请已审核过，不能再进行审核');
            }

            if ($status == $rebindApplyInfo['status']) {
                $this->error('您未更改订单状态,请选择要更改的状态');
            }

            $auditData = array();
            $auditData['status'] = $status;
            $auditData['audit_time'] = NOW_TIMESTAMP;
            $auditData['audit_remark'] = $beizhu;
            $auditData['audit_admin_id'] = session('ADMIN_ID');
            $auditData['audit_admin_name'] = session('USERNAME');

            if ($status == 1) {
                if (!$rebindApplyInfo['cuserid']) {
                    $this->error('换绑申请异常:要换绑的推广账号不存在');
                }
                $new_channel_info = model('Channel')->where(['id' => $rebindApplyInfo['cuserid']])->field('name,level')->find();
                if (empty($new_channel_info)) {
                    $this->error('玩家换绑申请异常:要换绑的推广员账号不存在');
                } else if ($new_channel_info['level'] <> 3) {
                    $this->error('玩家换绑申请异常:要换绑的推广员账号渠道层级有误,非推广员账号');
                }

                $old_channel_info = model('Channel')->where(['id' => $rebindApplyInfo['origin_cuserid']])->field('id,name,level')->find();
                if (!$old_channel_info) {
                    $this->error('玩家所属原推广员账号不存在');
                }

                $channelInfo = (new Channel())->where(['id' => $rebindApplyInfo['cuserid']])->field('id,yql_id')->find();
                if (!$channelInfo) {
                    $this->error('玩家切换的新渠道不存在');
                }

                $MemberChannelGameInfo = model('MemberChannelGame')->where(['member_id' => $rebindApplyInfo['userid'], 'game_id' => $rebindApplyInfo['game_id']])->find();
                if ($MemberChannelGameInfo) {
                    if ($MemberChannelGameInfo['channel_id'] <> $rebindApplyInfo['origin_cuserid']) {
                        $this->error('换绑申请异常:换绑申请中的原C账号与玩家当前所属推广员账号不一致！');
                    }
                } else {
                    $this->error('玩家尚未进入该游戏，不属于任何推广员');
                }
            }

            Db::startTrans();
            try {
                if ($status == 2) {  //审核不通过
                    $result = model('PlayerRebindApply')->where(['id' => $id, 'status' => array('in', [0])])->update($auditData);
                    if ($result === false) {
                        throw new Exception("更改玩家换绑申请状态失败");
                    }
                    $message = "玩家换绑审核,换绑ID:" . $rebindApplyInfo['id'] . "，审核状态：审核不通过";
                    $this->insertLog($this->current_node, "玩家换绑审核,换绑ID:" . $rebindApplyInfo['id'] . "，审核状态：审核不通过", 24);
                } else if ($status == 1) {  //审核通过
                    if ($result = model('MemberChannelGame')->where(['member_id' => $rebindApplyInfo['userid'], 'game_id' => $rebindApplyInfo['game_id']])->update(['channel_id' => $rebindApplyInfo['cuserid']])) {
                        $gamename = $rebindApplyInfo['game_name'];
                        $message = '玩家' . $rebindApplyInfo['username'] . ',在游戏:' . $gamename . '的归属渠道由：' . $rebindApplyInfo['origin_cusername'] . ',变更为:' . $rebindApplyInfo['cusername'] . '的信息;';
                        $this->insertLog($this->current_node, $message, 24);
                        // 插入玩家历史记录
                        Db::table('cy_member_history')->insert([
                            'userid' => $rebindApplyInfo['userid'],
                            'channel' => $gamename . "，" . $new_channel_info['name'],
                            'ip' => request()->ip(),
                            'create_time' => time(),
                            'admin_id' => session('ADMIN_ID')
                        ]);

                        $updGameServerData = array();
                        $updGameServerData['channel_id'] = $rebindApplyInfo['cuserid'];
                        $updGameServerResult = model('MemberGameServer')->where(['member_id' => $rebindApplyInfo['userid'], 'game_id' => $rebindApplyInfo['game_id']])->update($updGameServerData);

                        Db::table("cy_members")->where("id", $rebindApplyInfo['userid'])->update(
                            [
                                "channel_id" => $rebindApplyInfo['cuserid'],
                                "update_time" => NOW_TIMESTAMP
                            ]
                        );
                        Db::table("nw_subaccount")->where("id", $rebindApplyInfo['sub_id'])->update(
                            [
                                "channel_id" => $rebindApplyInfo['cuserid'],
                                "update_time" => NOW_TIMESTAMP
                            ]
                        );
                        /*
                        $updGameServerImeilData = array();
                        $updGameServerImeilData['channel_id'] = $rebindApplyInfo['cuserid'];
                        $updGameServerResult = model('GameServerImeil')->where(['member_id' => $rebindApplyInfo['userid'], 'game_id' => $rebindApplyInfo['game_id']])->update($updGameServerImeilData);

                        $updGameServerIpData = array();
                        $updGameServerIpData['channel_id'] = $rebindApplyInfo['cuserid'];
                        $updGameServerResult = model('GameServerIp')->where(['member_id' => $rebindApplyInfo['userid'], 'game_id' => $rebindApplyInfo['game_id']])->update($updGameServerIpData);
                        */

                        $rebindData = [
                            'userid' => $rebindApplyInfo['userid'],
                            'username' => $rebindApplyInfo['username'],
                            'game_id' => $rebindApplyInfo['game_id'],
                            'game_name' => $rebindApplyInfo['game_name'],
                            'origin_cuserid' => $rebindApplyInfo['origin_cuserid'],
                            'origin_cusername' => $rebindApplyInfo['origin_cusername'],
                            'cuserid' => $rebindApplyInfo['cuserid'],
                            'cusername' => $rebindApplyInfo['cusername'],
                            'create_time' => time(),
                            'cmmt' => '管理后台换绑申请审核通过',
                            'admin_type' => 1,
                            'admin_id' => session('ADMIN_ID'),
                            'admin_name' => session('USERNAME'),
                        ];
                        $insertRebindId = model('PlayerRebind')->insert($rebindData);

                        // if($insertRebindId){
                        // 	$rebindPayFromTime = strtotime(date('Y-m-d',(time()-((date('w',time())==0?7:date('w',time()))-1)*24*3600)));
                        // 	$updPayData = array();
                        // 	$updPayData['channel_id']	=	$rebindData['cuserid'];
                        // 	$updPayResult = model('Pay')->where(['userid' => $rebindData['userid'],'gameid' => $rebindData['game_id'],'channel_id' => $rebindData['origin_cuserid'],'create_time'=> ['egt', $rebindPayFromTime]])->update($updPayData);
                        // 	if ($updPayResult) {
                        // 		$message .= '该玩家本周后消费订单也已做换绑。';
                        // 	}
                        // }


                        // 处理对应 安卓/IOS 端的游戏
                        $gameType = model('Common/Game')->where(['id' => $rebindApplyInfo['game_id']])->value('type');
                        $bandWhere = [];
                        if ($gameType == 1) {
                            $bandWhere = ['ios_game_id' => $rebindApplyInfo['game_id']];
                            $bandField = 'android_game_id';
                        } else if ($gameType == 2) {
                            $bandWhere = ['android_game_id' => $rebindApplyInfo['game_id']];
                            $bandField = 'ios_game_id';
                        }
                        $bandGameId = Db::table('nw_game_band')->where($bandWhere)->value($bandField);
                        if ($bandGameId) {
                            $gameInfo = Db::table('cy_game')->where(['id' => $bandGameId])->field('id,name')->find();
                        }
                        if ($gameInfo) {
                            Db::table('cy_member_channel_game_rel')->where(['member_id' => $rebindApplyInfo['userid'], 'game_id' => $gameInfo['id']])->update(['channel_id' => $rebindApplyInfo['cuserid']]);

                            $message = '玩家' . $rebindApplyInfo['username'] . ', 在游戏:' . $gameInfo['name'] . '的归属渠道由：' . $rebindApplyInfo['origin_cusername'] . ',变更为:' . $rebindApplyInfo['cusername'] . '的信息;';
                            $this->insertLog($this->current_node, $message, 24);
                            // 插入玩家历史记录
                            Db::table('cy_member_history')->insert([
                                'userid' => $rebindApplyInfo['userid'],
                                'channel' => $gameInfo['name'] . "，" . $new_channel_info['name'],
                                'ip' => request()->ip(),
                                'create_time' => time(),
                                'admin_id' => session('ADMIN_ID')
                            ]);

                            $updGameServerResult = Db::table("nw_member_game_server")->where(['member_id' => $rebindApplyInfo['userid'], 'game_id' => $gameInfo['id']])->update(['channel_id' => $rebindApplyInfo['cuserid']]);
                            Db::table("nw_subaccount")->where(['member_id' => $rebindApplyInfo['userid'], 'game_id' => $gameInfo['id']])->update(["channel_id" => $rebindApplyInfo['cuserid'], "update_time" => NOW_TIMESTAMP]);
                            $rebindData = [
                                'userid' => $rebindApplyInfo['userid'],
                                'username' => $rebindApplyInfo['username'],
                                'game_id' => $gameInfo['id'],
                                'game_name' => $gameInfo['name'],
                                'origin_cuserid' => $rebindApplyInfo['origin_cuserid'],
                                'origin_cusername' => $rebindApplyInfo['origin_cusername'],
                                'cuserid' => $rebindApplyInfo['cuserid'],
                                'cusername' => $rebindApplyInfo['cusername'],
                                'create_time' => time(),
                                'cmmt' => '管理后台换绑申请审核通过',
                                'admin_type' => 1,
                                'admin_id' => session('ADMIN_ID'),
                                'admin_name' => session('USERNAME'),
                            ];
                            $insertRebindId = model('PlayerRebind')->insert($rebindData);
                        }

                        Db::commit();
                    } else {
                        throw new Exception("换绑申请异常:您未做任何绑定修改");
                    }

					$result = model('PlayerRebindApply')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
					if ($result === false) {
						throw new Exception("更改玩家换绑申请状态失败");
					}
					$this->insertLog($this->current_node, "玩家换绑审核,换绑ID:".$rebindApplyInfo['id']."，审核状态：审核通过",24);
				}
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("添加失败： " . $e->getMessage());
            }
			$this->success('审核成功; '.$message);
        }
        else{
            $this->error('非法请求');
        }
    }
}
