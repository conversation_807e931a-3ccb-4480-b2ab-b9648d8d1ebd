<?php

namespace app\admin\controller;

class Bind extends Admin
{
    public function index()
    {
        $where = [];


        $orderid = $this->request->get('orderid', '', 'trim');

        if (isset($orderid) && !empty($orderid)) {
            $where['orderid'] = $orderid;
        }
        $order = 'id desc';

        $couponModel = model('Bind');
        $list = $couponModel
            ->where($where)
            ->order($order)
            ->paginate(10, false, ['query' => input('get.')]);

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    public function add()
    {

        if ($this->request->isPost()) {
            $data = $this->request->post();

            $ids = explode("\r\n", $data['ids']);
            $ids = array_map('filterAndTrimInput', $ids);
            $ids = array_unique(array_filter($ids));
            $pay = model('Pay')->alias('a')->whereIn('a.orderid', $ids)
                ->join('nw_game_channel_divide_settle b', 'a.settle_id=b.id', 'left')
                ->where('a.channel_id', $data['old_channel_id'])
                ->where('a.status', 1)->column('a.id,a.channel_id,a.settle_id,a.orderid,b.first_audit_status,b.second_audit_status', 'a.orderid');

            if (!$channel = model('Channel')->where('id', $data['new_channel_id'])->where('level', 3)->where('status', 1)->find()) {
                $this->error('记录不存在');
            }


            $arr = [];
            $orderids = [];
            foreach ($ids as $k => $v) {
                if (isset($pay[$v])) {
                    if ($pay[$v]['settle_id'] > 0) {
                        if ($pay[$v]['first_audit_status'] == 1 && $pay[$v]['second_audit_status'] == 1) {
                            $arr[] = [
                                'orderid' => $v,
                                'name' => '',
                                'state' => '<span style="color: red">订单已结算无法换绑</span>'
                            ];
                        } else {
                            $arr[] = [
                                'orderid' => $v,
                                'name' => '',
                                'state' => '<span style="color: red">订单已申请结算，请先拒绝结算后再换绑</span>'
                            ];
                        }
                    } else {
                        $orderids[] = $v;
                        $arr[] = [
                            'orderid' => $v,
                            'name' => $channel['name'],
                            'state' => '<span style="color: #0bb20c">可换绑</span>'
                        ];
                    }

                } else {
                    $arr[] = [
                        'orderid' => $v,
                        'name' => '',
                        'state' => '<span style="color: red">订单不存在</span>'
                    ];
                }
            }

            $this->assign('orderids', implode(',', $orderids));
            $this->assign('channel', $channel);
            $this->assign('list', $arr);
            $this->assign('old_channel_id', $data['old_channel_id']);
            return $this->fetch('confirm');
        }

        $channel = model('Channel')->where('level', 3)->where('status', 1)
            ->field('id,name')
            ->order('id desc')->select();
        $this->assign('channel', $channel);
        return $this->fetch();
    }


    public function confirm()
    {
        $data = $this->request->post();

        if (!$data['orderids']) {
            $this->error('订单不存在');
        }
        if (!$data['new_channel_id']) {
            $this->error('新推广关系不存在');
        }

        $channel = model('Channel')->where('id', $data['new_channel_id'])->where('level', 3)->where('status', 1)->find();
        if (!$channel) {
            $this->error('推广关系不存在');
        }
        $old_channel = model('Channel')->where('id', $data['old_channel_id'])->where('level', 3)->where('status', 1)->find();
        if (!$old_channel) {
            $this->error('推广关系不存在');
        }

        $orderids = array_map('filterAndTrimInput', explode(',', $data['orderids']));
        $orderids = array_unique(array_filter($orderids));
        $pay = model('Pay')->alias('a')->whereIn('a.orderid', $orderids)
            ->where('a.channel_id', $data['old_channel_id'])
            ->where('a.status', 1)->column('a.id,a.channel_id,a.settle_id,a.orderid', 'a.orderid');

        if (!$pay) {
            $this->error('订单不存在');
        }
        $bind = [];
        $bind_order = [];
        foreach ($pay as $k => $v) {
            if ($v['settle_id'] == 0) {
                $bind_order[] = $v['orderid'];
                $bind[] = [
                    'orderid' => $v['orderid'],
                    'old_channel_id' => $v['channel_id'],
                    'new_channel_id' => $data['new_channel_id'],
                    'old_channel_name' => $old_channel['name'],
                    'new_channel_name' => $channel['name'],
                    'create_time' => time(),
                    'admin_name' => session('USERNAME')
                ];
            }
        }
        if(!$bind_order){
            $this->error('订单不存在');
        }

        model('Common/Pay')->startTrans();
        if (!model('Common/Pay')->whereIn('orderid', $bind_order)
            ->where('channel_id', $data['old_channel_id'])->update(['channel_id' => $data['new_channel_id']])) {
            model('Common/Pay')->rollback();
            $this->error('绑定失败');
        }
        if (model('common/Bind')->insertAll($bind)) {
            model('Common/Pay')->commit();
            $this->success('绑定成功', url('index'));
        } else {
            model('Common/Pay')->rollback();
            $this->error('绑定失败');
        }
    }
}
