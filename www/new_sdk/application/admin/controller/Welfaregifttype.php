<?php

namespace app\admin\controller;


use think\Loader;
use think\Db;

class Welfaregifttype extends Admin
{
    protected $gameList;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

    }

    /**
     *玩家福利类型列表
     */
    public function index()
    {
        $where = [];
        $order = 'create_time desc,id desc';

        $welfareModel = Db::name('cy_welfare_gift_type');
        $list = $welfareModel
            ->order($order)
            ->paginate(10, false, ['query' => input('get.')]);

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }


    /**
     *增
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            if ($data['type_id'] == 1 && $data['recharge_amount'] < 0) {
                $this->error('充值金额不能小于0');
            }
            if ($data['type_id'] == 3 && $data['role_level'] < 0) {
                $this->error('充值金额不能小于0');
            }
            if ($data['recharge_type'] == 2 && $data['max_recharge_amount'] < 0) {
                $this->error('区间上限充值金额不能小于0');
            }

            if ($data['recharge_type'] == 2 && $data['max_recharge_amount'] <= $data['recharge_amount']) {
                $this->error('区间上限充值金额不能小于最低充值金额');
            }

            if ($data['type_id'] == 1) {
                $data['role_level'] = 0;
                if ($data['time_type'] == 7 && !($data['start_time'] && $data['end_time'])) {
                    $this->error('自定义日期必填');
                } else if (in_array($data['time_type'], [1, 2, 3, 4, 5, 6])) {
                    $data['start_time'] = $data['end_time'] = null;
                }
                if ($data['recharge_type'] == 1){
                    $data['max_recharge_amount'] = 0;
                }
            } else {
                $data['max_recharge_amount'] = 0;
                $data['recharge_amount'] = 0;
                $data['time_type'] = 1;
                $data['start_time'] = $data['end_time'] = null;
            }

            $data['update_time'] = $data['create_time'] = time();
            if (Db::name('cy_welfare_gift_type')->insertGetId($data)) {
                $this->success('添加成功', url('index'));
            }
            $this->error('添加失败');
        }

        return $this->fetch();
    }


    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel = Db::name('cy_welfare_gift_type');
        if (!$appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }


    /**
     *编辑SDK密钥
     */
    public function edit()
    {
        $id = $this->request->param('id', '', 'intval');
        $appModel = Db::name('cy_welfare_gift_type');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data = $this->request->post();

            if ($data['type_id'] == 1 && $data['recharge_amount'] < 0) {
                $this->error('充值金额不能小于0');
            }
            if ($data['type_id'] == 3 && $data['role_level'] < 0) {
                $this->error('充值金额不能小于0');
            }
            if ($data['recharge_type'] == 2 && $data['max_recharge_amount'] < 0) {
                $this->error('区间上限充值金额不能小于0');
            }

            if ($data['recharge_type'] == 2 && $data['max_recharge_amount'] <= $data['recharge_amount']) {
                $this->error('区间上限充值金额不能小于最低充值金额');
            }
            if ($data['type_id'] == 1) {
                $data['role_level'] = 0;
                if ($data['time_type'] == 7 && !($data['start_time'] && $data['end_time'])) {
                    $this->error('自定义日期必填');
                } else if (in_array($data['time_type'], [1, 2, 3, 4, 5, 6])) {
                    $data['start_time'] = $data['end_time'] = null;
                }
                if ($data['recharge_type'] == 1){
                    $data['max_recharge_amount'] = 0;
                }
            } else {
                $data['recharge_amount'] = 0;
                $data['max_recharge_amount'] = 0;
                $data['time_type'] = 1;
                $data['start_time'] = $data['end_time'] = null;
            }

            $data['update_time'] = time();
            if (Db::name('cy_welfare_gift_type')->where(['id' => $id])->update($data)) {
                $this->success('修改成功', url('index'));
            }
            $this->error($appModel->getError() ?: '修改失败');
        }
        $this->assign('data', $data);
        return $this->fetch();
    }


}
