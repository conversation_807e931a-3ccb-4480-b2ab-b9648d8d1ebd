<?php
/**
 * 游戏管理控制器
 */

namespace app\admin\controller;

use app\common\library\FileUpload;
use app\common\model\GameBand;
use app\common\model\Game as GameModel;
use app\common\model\SdkGameList;
use app\common\model\GameInfo;
use Overtrue\Pinyin\Pinyin;
use app\common\model\PromotionShortLink;
use think\Db;
use think\Exception;

class ChannelRatio extends Admin
{

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $tmpChannelList = Db::table('nw_channel')->field('id,name')->where(['level'=>1])->select();
		$channeList = array();
		foreach ($tmpChannelList as $game) {
              $channeList[ $game['id']] = $game;
        }
        $this->channeList  = $channeList;
    }

    public function index()
    {	
    	$channelId = input('channel_id');
    	$where = ['level'=>1];
    	if ($channelId) {
            $where['c.id'] = $channelId;
        }
        $list = Db::name('nw_channel_recharge_ratio')->alias('r')
						->join('nw_channel c', 'r.channel_id = c.id','inner')
						->field('r.id,r.channel_name,FROM_UNIXTIME(r.create_time) as create_time,FROM_UNIXTIME(r.begin_time,"%Y-%m-%d") as begin_time,FROM_UNIXTIME(r.end_time,"%Y-%m-%d") as end_time,r.ratio,r.channel_id')
						->where($where)
						->paginate(10, false, ['query' => $where]); 
		$page = $list->render();
		$this->assign('channel_id', $channelId);
		$this->assign('channel_list', $this->channeList);
        $this->assign("page", $page);
        $this->assign("list", $list);
    	return $this->fetch();
    }
    /**
     * 删除成功
     * @return [type] [description]
     */
    public function delete()
    {
    	$id = $this->request->param('id', '', 'intval');
    	$channel_id = $this->request->param('channel_id', '', 'intval');
        if (empty($id) || empty($channel_id)) {
            $this->error('参数错误!');
        }
        if (!Db::name('nw_channel_recharge_ratio')->find($id)) {
            $this->error('参数错误，不存在该数据');
        }

        Db::startTrans();
		try{
			//添加充值比例记录
			$res = Db::name('nw_channel_recharge_ratio')->where('id', '=', $id)->delete();
			if ( !$res) {
				throw new Exception("删除充值比例失败");
			}
			$logData['ratio_id']		= $id;
			$logData['channel_id']      = $channel_id;
			$logData['type']			= 3;
			$logData['create_time']		= NOW_TIMESTAMP;
			$logData['admin_type']		= 2;
			$logData['admin_name']		= session('USERNAME');
			$insertLogId = model("ChannelRechargeRatioLog")->insert($logData);
			if(!$insertLogId){
				throw new Exception("创建充值比例日志失败! ");
			}

			// 提交事务
			Db::commit();
			
		} catch (\Exception $e) {
			// 回滚事务
			Db::rollback();
			
			$this->error('', 0, '充值比例删除失败'.$e->getMessage());
		}
		$this->success('删除比例成功',url('index'));
    }
    /**
     * 添加分成比例
     * @param string $value [description]
     */
    public function add()
    {
    	$this->assign('channel_list', $this->channeList);
        return $this->fetch();
    }
    /**
     * 增加接口
     * @param string $value [description]
     */
    public function addPost()
    {
    	$postParam = $this->request->param();
    	
    	if (!empty($postParam['ratio'])) {
    		$ratio = intval($postParam['ratio']);
    	}
        if($ratio < 1 || $ratio > 100){
          $this->error("分成比例范围必须在1-100内");
        }
        if (!$postParam['channel_id'] || !$postParam['date']) {
        	$this->error("参数错误!");
        }
        if (Db::name('nw_channel_recharge_ratio')->where(['channel_id'=>$postParam['channel_id']])->value('id')) {
            $this->error('参数错误，该公会已经设置过充值比例');
        }
		$begin_time           = explode('/', $postParam['date'])[0];
		$end_time             = explode('/', $postParam['date'])[1];
		$cont                 = array();
		$cont['channel_id']   = $postParam['channel_id'];
		$cont['channel_name'] = db::name('nw_channel')->where('id','=',$postParam['channel_id'])->value('name');
		$cont['ratio']        = $postParam['ratio'];
		$cont['admin_type']   = 1;
		$cont['create_time']  = NOW_TIMESTAMP;
		$cont['update_time']  = NOW_TIMESTAMP;
		$cont['begin_time']   = strtotime(date($begin_time));
		$cont['end_time']     = strtotime(date($end_time));
		$cont['admin_name']		= session('USERNAME');
		Db::startTrans();
		try{
			$channelInfo = model('Channel')->where(['id'=>$postParam['channel_id']])->find();
			if(empty($channelInfo) || $channelInfo['level']<>1){
				throw new Exception("存在账号异常,请核对账号名称后再进行操作");
			}
			//添加充值比例记录
			$insertRatioId = model('ChannelRechargeRatio')->insertGetId($cont);
			if ( !$insertRatioId) {
				throw new Exception("添加充值比例失败");
			}
			$logData['ratio_id']		= $insertRatioId;
			$logData['channel_id']      = $postParam['channel_id'];
			$logData['ratio']			= $postParam['ratio'];
			$logData['begin_time']		= strtotime(date($begin_time));
			$logData['end_time']		= strtotime(date($end_time));
			$logData['type']			= 1;
			$logData['create_time']		= NOW_TIMESTAMP;
			$logData['admin_type']		= 2;
			$logData['admin_name']		= session('USERNAME');
			$insertLogId = model("ChannelRechargeRatioLog")->insert($logData);
			if(!$insertLogId){
				throw new Exception("创建充值比例日志失败! ");
			}

			// 提交事务
			Db::commit();
			
		} catch (\Exception $e) {
			// 回滚事务
			Db::rollback();
			
			$this->error('', 0, '充值比例添加失败'.$e->getMessage());
		}
		$this->success('添加比例成功',url('index'));
    }
    /**
     * 编辑
     * @return [type] [description]
     */
    public function update()
    {
    	if (request()->isPost()) {
	        $postParam = $this->request->param();
	    	
	    	if (!empty($postParam['ratio'])) {
	    		$ratio = intval($postParam['ratio']);
	    	}
	        if($ratio < 1 || $ratio > 100){
	          $this->error("分成比例范围必须在1-100内");
	        }

	        if (!$postParam['channel_id'] || !$postParam['date'] || !$postParam['id']) {
	        	$this->error("参数错误!");
	        }

	        if (!Db::name('nw_channel_recharge_ratio')->where(['channel_id'=>$postParam['channel_id'],'id'=>$postParam['id']])->value('id')) {
	            $this->error('参数错误，该公会还未设置过充值比例');
	        }
	        $begin_time           = explode('/', $postParam['date'])[0];
			$end_time             = explode('/', $postParam['date'])[1];
			$cont                 = array();
			$cont['ratio']        = $postParam['ratio'];
			$cont['admin_type']   = 1;
			$cont['update_time']  = NOW_TIMESTAMP;
			$cont['begin_time']   = strtotime(date($begin_time));
			$cont['end_time']     = strtotime(date($end_time));
			$cont['admin_name']	  = session('USERNAME');

			Db::startTrans();
			try{
				$channelInfo = model('Channel')->where(['id'=>$postParam['channel_id']])->find();
				if(empty($channelInfo) || $channelInfo['level']<>1){
					throw new Exception("存在账号异常,请核对账号名称后再进行操作");
				}
				//添加充值比例记录
				$res = Db::name('nw_channel_recharge_ratio')->where('id',$postParam['id'])->update($cont);
	            if (!$res) {
	                $this->error("更新失败");
	            }

				$logData['ratio_id']		= $postParam['id'];
				$logData['channel_id']      = $postParam['channel_id'];
				$logData['ratio']			= $postParam['ratio'];
				$logData['begin_time']		= strtotime(date($begin_time));
				$logData['end_time']		= strtotime(date($end_time));
				$logData['type']			= 2;
				$logData['create_time']		= NOW_TIMESTAMP;
				$logData['admin_type']		= 2;
				$logData['admin_name']		= session('USERNAME');
				$insertLogId = model("ChannelRechargeRatioLog")->insert($logData);
				if(!$insertLogId){
					throw new Exception("创建充值比例日志失败! ");
				}

				// // 提交事务
				Db::commit();
				
			} catch (\Exception $e) {
				// 回滚事务
				Db::rollback();
				
				$this->error('', 0, '充值比例添加失败'.$e->getMessage());
			}
			$this->success('修改比例成功',url('index'));
        }else{
        	$id = $this->request->param('id', '', 'intval');
	        if (empty($id)) {
	            $this->error('参数错误!');
	        }

	        if (!Db::name('nw_channel_recharge_ratio')->find($id)) {
	            $this->error('参数错误，不存在该数据');
	        }
	        $data = model('ChannelRechargeRatio')->where(['id'=>$id])->find();
	        $this->assign('data', $data);
	        return $this->fetch();
        }
    	
    }
    /**
     * 查看记录
     * @return [type] [description]
     */
    public function checklog()
    {  
        $postParam = $this->request->param();
        if (empty($postParam['id']) || empty($postParam['channelid'])) {
            $this->error("参数错误");
        }
        $res = model('ChannelRechargeRatioLog')->where(['channel_id'=>$postParam['channelid']])->order('id desc')->select();
        foreach ($res as $key => $value) {
            $res[$key]['begin_time'] = date('Y-m-d',$value['begin_time']);
            $res[$key]['end_time'] = date('Y-m-d',$value['end_time']);
            switch ($value['type']) {
            	case '1':
            		$res[$key]['type'] = '增加';
            		break;
            	case '2':
            		$res[$key]['type'] = '修改';
            		break;
            	case '3':
            		$res[$key]['type'] = '删除';
            		$res[$key]['begin_time'] = '--';
            		$res[$key]['ratio'] = '--';
            		$res[$key]['end_time'] = '--';
            		break;
            }
        }
        if ($res) {
            $this->success('获取数据成功',null,$res);
        }else{
            $this->error("暂无数据");
        }
    }

    /**
     * 审核列表
     * @return [type] [description]
     */
    public function applyCheck()
    {

    	$channelId = input('channel_id');
    	$orderid = input('orderid');
    	$recharge_type = input('recharge_type');
    	$status = input('status');
    	$where = ['c.level'=>1];
    	if ($channelId) {
            $where['c.id'] = $channelId;
        }
        if ($orderid) {
            $where['r.orderid'] = $orderid;
        }
        if ($recharge_type) {
            $where['r.recharge_type'] = $recharge_type;
        }
        if ($status || $status == "0") {
            $where['r.status'] = $status;
        }
        $list = model('ChannelRecharge')->alias('r')
        	->join('nw_channel c', 'r.channel_id = c.id','inner')
        	->field('r.*')
			->order("r.id desc")
			->where($where)
			->paginate(10, false, ['query' => input('get.')]); 
		if (!empty($list)) {
			foreach ($list as $key => $value) {
				$list[$key]['ratio'] = @round($value['real_amount']/$value['amount']*100,2);
				switch ($value['status']) {
					case '0':
						$list[$key]['status'] = '未审核';
						break;
					case '1':
						$list[$key]['status'] = '审核成功';
						break;
					case '2':
						$list[$key]['status'] = '审核失败';
						break;
				}
			}

		}
		
		$page = $list->render();
		$this->assign('channel_id', $channelId);
		$this->assign('channel_list', $this->channeList);
        $this->assign("page", $page);
        $this->assign("list", $list);
    	return $this->fetch();
    }
    /**
     * 审核页面
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function examine()
    {
    	$postParam = $this->request->param();
        if (empty($postParam['id'])) {
            $this->error("参数错误");
        }
        $list = db::name('nw_channel_recharge')
			->where(['id'=>$postParam['id']])->find();
		if (!empty($list)) {
			$list['ratio'] = @round($list['real_amount']/$list['amount']*100,2);
			$list['create_time'] = date('Y-m-d H:i:s',$list['create_time']);
			switch ($list['status']) {
				case '0':
					$list['status_type'] = '未审核';
					break;
				case '1':
					$list['status_type'] = '审核成功';
					break;
				case '2':
					$list['status_type'] = '审核失败';
					break;
			}

		}	
		$this->assign("list", $list);
    	return $this->fetch();
    }
    /**
     * 审核
     * @return [type] [description]
     */
    public function doAudit()
    {
		$postParam = $this->request->param();
		$id        = $postParam['id'];	//渠道ID
		$status    = $postParam['status'];	//审核状态:1(审核通过),2(审核不通过)
		$remark    = $postParam['check_remark'];	//审核备注
		if(!$id){
			$this->error('请选择要审核的记录!');
		}
	    if($status<>1 && $status<>2){
			$this->error('非法审核状态');
        }

        $auditData = array();
		$auditData['id'] = $id;
		$auditData['status'] = $status;
		$auditData['finish_time'] = NOW_TIMESTAMP;
		$auditData['check_remark'] = $remark;
		$auditData['check_admin_type'] = 1;
		$auditData['check_admin_name'] = SESSION('USERNAME');
    	Db::startTrans();
        try {
			//审核通过
			if($status==1){
				$list = db::name('nw_channel_recharge')->where(['id'=>$postParam['id'],'status'=>array('in',[0])])->find();
				if (!$list) {
					throw new Exception("获取系统数据异常");
				}

				$detData = array();
                $detData['channel_id']      = $list['channel_id'];
                $detData['channel_name']    = $list['channel_name'];
                $detData['change_amount']	= +$list['amount'];
                $detData['account_type']	= 1;   //通用账户
                $detData['type']			= 4;   //充值收入
                $detData['out_orderid']		= $list['orderid'];
                $detData['create_time']     = NOW_TIMESTAMP;
                $insertDetId = model('ChannelAccountDet')->insertGetId($detData);   				
				if ( !$insertDetId) {
					throw new Exception("添加账户变动明细失败");
				}
				$updData = array();
				$updData['amount'] = Db::raw("amount+".$list['amount']);
				$updData['update_time'] = time();
				$updFromResult = model('Channel')->where(['id'=>$list['channel_id']])->update($updData);
				
				if (!$updFromResult) {
					throw new Exception("账户金额变动失败");
				}
				$auditData['finish_status'] = 1;
				$result = model('ChannelRecharge')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
				if (!$result) {
					throw new Exception("充值审核失败");
				}
			}
			else if($status==2){  //审核不通过
				$result = model('ChannelRecharge')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
				if (!$result) {
					throw new Exception("充值审核失败");
				}
			}
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('', 0, '充值审核失败'.$e->getMessage());

        }
        $this->success('审核成功',url('applyCheck'));
    }
}
