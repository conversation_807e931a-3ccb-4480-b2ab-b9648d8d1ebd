<?php
/**
 * 礼包管理
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/1/23
 * Time: 15:27
 */

namespace app\admin\controller;


// use app\common\controller\Base;

use think\Db;


class Gift extends Admin
{
    private $_type = [
        '0' => '普通礼包',
        '1' => '充值礼包'
    ];

    private $_condis = [
        'TOPLEVEL'     => '最高档位可领',
        'MULLEVEL'     => '多档位可领',
        'REPEATABLE'   => '可重复领取',
        'UNREPEATABLE' => '不可重复领取',
    ];

    private $_condisType = [
        'TOPLEVEL'     => '累计充值',
        'MULLEVEL'     => '累计充值',
        'REPEATABLE'   => '单笔充值',
        'UNREPEATABLE' => '单笔充值',
    ];

    private $status_list = [
        0 => '待审核',
        1 => '通过',
        2 => '未通过'
    ];

    protected function _initialize()
    {
        parent::_initialize();
    }


    /**
     * 礼包列表
     * @return mixed
     */
    public function index()
    {
        $condition['cy_libaoinfo.isdelete'] = 0;
        $title     = $this->request->get('title', '', 'trim');
        $gameid  = $this->request->get('gameid', '', 'trim');
        !empty($title) && $condition['title'] = $title;
        $condition['gameid'] = $gameid;

        $start_time = input('request.cr_start');
        //开始时间和结束时间不为空时
        if ($start_time != '' && input('request.cr_end') != '') {
            $condition['cy_libaoinfo.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime(input('request.cr_end').' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time!= '') {
            $condition['cy_libaoinfo.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif (input('request.cr_end') != '') {
            $condition['cy_libaoinfo.create_time'] = ['<=', strtotime(input('request.cr_end').' 23:59:59')];
        }
        $tmpGameList = model('Common/Game')->getAllByCondition('id,name');

        $GiftModel = model('Common/Gift');
        $giftList  = $GiftModel->getList($condition);
        $this->assign('game_list', $tmpGameList);
        $this->assign('list', $giftList);
        $this->assign('title', $title);
        $this->assign('page', $giftList->render());
        return $this->fetch();
    }


    /**
     * 新增礼包
     * @return mixed
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->getParam();
            $data['gameid'] = $data['select'];
            unset($data['select']);
            if (true !== ($res = $this->validate($data, 'Gift'))) {
                $this->error($res);
            }
            $GiftModel = model('Common/Gift');
            if ($GiftModel->add($data)) {
                $this->success('添加成功',url('index'));
            }
            $this->error($GiftModel->getError() ?: '添加失败');
        }

        $tmpGameList = model('Common/Game')->getAllByCondition('id,name',['cooperation_status'=>['in',[0,1,2]]]);
        $this->assign('game_list', $tmpGameList);
        return $this->fetch('add');
    }

    /**
     * 编辑礼包
     */
    public function edit()
    {
        $data = $this->checkData();
        if ($this->request->isPost()) {
            $data = $this->getParam();
            if (true !== ($res = $this->validate($data, 'Gift.edit'))) {
                $this->error($res);
            }
            $GiftModel = model('Common/Gift');
            model('Common/Gift')->startTrans();
            if ($GiftModel->allowField(true)->save($data, ['id' => $data['id']]) !== false ) {

                model('Common/Giftcode')->where('infoid',$data['id'])->where('status',0)->delete();

                foreach ($data['code'] as $v) {
                    $codes[] = ['code' => $v,'infoid'=>$data['id']];
                }
                model('Common/Giftcode')->insertAll($codes);

                model('Common/Gift')->commit();
                $this->success('编辑成功',url('index'));
            }
            model('Common/Gift')->rollback();
            $this->error($GiftModel->getError() ?: '编辑失败');
        }
        $code = model('Common/Giftcode')->where('infoid',$data['id'])->where('status',0)->column('code', 'id');
//        $code = $data->giftCode()->column('code', 'id');
        $this->assign('code', implode("\r\n", $code));
        $this->assign('data', $data);
        $tmpGameList = model('Common/Game')->getAllByCondition();

		foreach ($tmpGameList as $game) {
              $gameList[ $game['id']] = $game;
        }
        $game = explode(',', $data['gameid']);
        $tmpGameList = model('Common/Game')->getAllByCondition('name', ['id' => ['in', $game]]);

        $this->assign('game_name', implode(',', array_column($tmpGameList, 'name')));


        return $this->fetch();
    }

    /**
     * 设置是否最新
     */
    public function setNew()
    {
            $id     = input('id',0,'intval');
            $is_new = input('is_new',0,'intval');

            if(empty($id)){
                $this->error('礼包ID不能为空');
            }

            $GiftModel = model('Common/Gift');

            if($is_new)
                $data['is_new'] = 1;    //设为最新
            else
                $data['is_new'] = 0;    //取消最新

            $data['update_time'] = NOW_TIMESTAMP;

            if ($GiftModel->allowField(true)->save($data, ['id' => $id]) !== false ) {

                if($is_new)
                    $this->success('设为最新成功',url('index'));
                else
                    $this->success('取消成功');
            }
            $this->error($GiftModel->getError() ?: '设为最新失败');
    }

    /**
     * 删除礼包
     */
    public function delete()
    {
        $data = $this->checkData();
        if ($data->save(['isdelete'=>1],['id'=>$this->request->param('id', 0, 'intval')])) {
            $this->success('删除成功!');
        }
        $this->error('删除失败');
    }


    /**
     * 最新礼包列表
     * @return mixed
     */
    public function newList()
    {
        $condition['cy_libaoinfo.isdelete'] = 0;
        $condition['cy_libaoinfo.is_new']   = 1;

        $title     = $this->request->get('title', '', 'trim');
        $gameName  = $this->request->get('game_name', '', 'trim');

        !empty($title) && $condition['title'] = $title;
        !empty($gameName) && $condition['gameName'] = $gameName;


        $GiftModel = model('Common/Gift');

        $giftList  = $GiftModel->getList($condition,'cy_libaoinfo.update_time desc');
        $this->assign('list', $giftList);
        $this->assign('title', $title);
        $this->assign('game_name', $gameName);
        $this->assign('page', $giftList->render());

        return $this->fetch('new_list');
    }


    /**
     * 获取参数新增（复用）
     * @return mixed
     */
    protected function getParam()
    {
        $data              = $this->request->param();
        $data['starttime'] = $this->request->param('starttime', 0, 'strtotime');
        $data['endtime']   = $this->request->param('endtime', 0, 'strtotime');
//        if ($data['is_top']) {
//            $data['t_starttime'] = $this->request->param('t_starttime', 0, 'strtotime');
//            $data['t_endtime']   = $this->request->param('t_endtime', 0, 'strtotime');
//        }
        if (isset($data['code'])) {
            $code          = $data['code'];
            $codeArray     = explode("\r\n", $code);
            $codeArray     = array_map('filterAndTrimInput', $codeArray);
            $codeArray     = array_filter($codeArray);
            $data['code']  = $codeArray;
            $data['total'] = count($codeArray);
        }
        return $data;
    }

    /**
     * 检查礼包信息
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    protected function checkData()
    {
        $id        = $this->request->param('id', 0, 'intval');
        $GiftModel = model('Common/Gift');
        $data      = [];
        if (empty($id) || !($data = $GiftModel->find($id))) {
            $this->error('参数错误,不存该礼包');
        }
        return $data;
    }

    /**
     * 公会礼包管理页面
     */
    public function guildGift(){

        $keyword      = input('keyword' , '' , 'filterAndTrimInput');
        $gameId       = input('gameid' , '' , 'filterAndTrimInput');
        $lanmu        = input('lanmu' , '' , 'filterAndTrimInput');
        $limitFirst   = input('limitFirst' , '' , 'filterAndTrimInput');  // 当 lanmu 为 2 是 1 累计充值 2 单笔充值
        $condisFirst  = input('condisFirst' , '' , 'filterAndTrimInput'); // 当 limitFirst 为 1 是累计充值    TOPLEVEL 最高档位可领 MULLEVEL 多档位可领
        $condisSecend = input('condisSecend' , '' , 'filterAndTrimInput');// 当 limitFirst 为 2 是单笔充值    REPEATABLE 可重复领取 UNREPEATABLE 不可重复领取
        $limit_channel= input('limit_channel', '' , 'filterAndTrimInput'); // 条件筛选，是否开启渠道限制
        $condition    = [];

        if ( ! empty($keyword) ) {
            $condition['l.title'] = array('LIKE', '%'.$keyword.'%');
        }

        if ( ! empty($lanmu) ) {
            $condition['l.type'] = $lanmu - 1;
        }

        // 礼包类型为充值礼包
        if ( ! empty($lanmu) && $lanmu == 2) {
            if ( ! empty($limitFirst) ) {
                // 累计充值
                if ($limitFirst == 1) {
                    if (empty($condisSecend) && ! empty($condisFirst) && in_array($condisFirst, ['TOPLEVEL', 'MULLEVEL'])) {
                        $condition['l.get_condis'] = $condisFirst;
                    }
                    if (empty($condisFirst)) {
                        $condition['l.get_condis'] = array(['eq','TOPLEVEL'],['eq','MULLEVEL'],'or');
                    }
                }
                // 单笔充值
                if ($limitFirst == 2) {
                    if (empty($condisFirst) && ! empty($condisSecend) && in_array($condisSecend, ['UNREPEATABLE', 'REPEATABLE'])) {
                        $condition['l.get_condis'] = $condisSecend;
                    }
                    if (empty($condisFirst)) {
                        $condition['l.get_condis'] = array(['eq','UNREPEATABLE'],['eq','REPEATABLE'],'or');
                    }
                }
            }
        }else{ // 普通礼包
            if ($limit_channel == '0'){
                $condition['b.status'] = "0";
            }elseif ($limit_channel == 1){
                $condition['b.status'] = [['eq',1],['exp',Db::raw('is null')],'or'];
            }
        }

        if ( ! empty($gameId) ) {
            $condition['g.id'] = $gameId;
        }

        $condition['l.isdelete'] = 0;

        // 判断：因为字段的特殊性， 普通礼包是没有领取限制，故要做条件过滤
        // 没选礼包类型，有选领取限制，则默认礼包类型为充值礼包
        if (!array_key_exists('l.type', $condition) && array_key_exists('l.get_condis', $condition)){
            $condition['l.type'] = 1;
        }
        // 既有选礼包类型，又选领取限制
        if(array_key_exists('l.type', $condition) && array_key_exists('l.get_condis', $condition)){
            if ($condition['l.type'] != 1){
                $condition['l.get_condis'] = rand(1234,5678);
            }
        }
        $guildGiftInfoM = model('GuildGiftinfo');

        $list  = $guildGiftInfoM
            ->alias('l')
            ->field('l.id, l.title, l.gameid, l.type, l.starttime, l.limit_pens, l.limit_amount,l.endtime, l.is_top, l.t_starttime, l.t_endtime, l.update_time, l.create_time, g.name as gamename,l.get_condis, b.status as is_channel_limit')
            ->join('cy_game g',' l.gameid = g.id')
            ->join('cy_guild_limit b','l.id = b.infoid','left')
            ->where($condition)
            ->order('l.create_time desc')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function($item, $key){
                $type        = $this->_type;
                $libaoCondis = $this->_condis;
                $libaoCondisType = $this->_condisType;
                $item['type_1']      = $item['type'];

                // 累计充值
                if (in_array($item['get_condis'], ['TOPLEVEL', 'MULLEVEL'])) {
                    $item['libaoCondis']    = $libaoCondis[$item['get_condis']];
                    $item['libaoCondisType']= $libaoCondisType[$item['get_condis']];
                }

                // 单笔充值
                if (in_array($item['get_condis'], ['UNREPEATABLE', 'REPEATABLE'])) {
                    $item['libaoCondis']    = $libaoCondis[$item['get_condis']];
                    $item['libaoCondisType']    = $libaoCondisType[$item['get_condis']].'（'.$item['limit_pens'].'）笔';
                }

                $item['type']        = $type[$item['type']];
            });
        /*$sql = $guildGiftInfoM->getLastSql();
        dump($sql);*/
        $this->assign('list', $list);
        $this->assign('page', $list->render());

        $game_list = model('Game')->field('id,name')->order('name asc')->select();
        $this->assign('game_list', $game_list);

        $this->assign('lanmu_list', $this->_type);

        return $this->fetch('guildlist');
    }

    /**
     * 删除公会礼包
     */
    public function delGuildGift() {
        $id = $this->request->param('id', 0, 'intval');

        if ( ! $id ) {
            $this->error('数据不存在');
        }

        $delete = model('GuildGiftinfo')->save(['isdelete'  => 1],['id' => $id]);

        if ( $delete !== false ) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 添加公会礼包
     */
    public function addGuildGift()
    {
        if ($this->request->isGet()) {
            $game_list = model('Game')->field('id,name')->order('name asc')->select();
            $this->assign('game_list', $game_list);
            return $this->fetch('guildadd');
        }

        if($this->request->isPost()){
            $libao_data['gameid']       = input('post.gameid', 0, 'intval');
            $libao_data['title']        = input('post.title', '', 'filterAndTrimInput');
            $libao_data['content']      = input('post.content', '', 'filterAndTrimInput');
            $libao_data['is_top']       = input('post.is_top', 0, 'intval');
            $libao_data['limit_amount'] = input('post.limit_amount');
            $libao_data['type']         = input('post.type', 0, 'intval');    // 0 普通礼包 1 充值礼包
            $libao_data['starttime']    = input('post.starttime', 0, 'strtotime');
            $libao_data['endtime']      = input('post.endtime', 0, 'strtotime');
            $get_condis1                = input('post.get_condis1', 0, 'intval');  // 领取限制1 当type 为1 充值礼包时才用到  1 累计充值 2 单笔充值
            $get_condis2                = input('post.get_condis2', '', 'filterAndTrimInput');  //领取限制2 根据 get_condis1 不同的值 变换
            $limit_num                  = input('post.limit_num',  0, 'intval');  // 充值笔数 当type 为1 get_condis1 为 2 单笔充值 才用到

            if ( $libao_data['is_top'] ) {
                $libao_data['t_starttime'] = input('post.t_starttime', 0, 'strtotime');
                $libao_data['t_endtime']   = input('post.t_endtime', 0, 'strtotime');
            }

            if ($libao_data['type'] == 1) {
                if ( empty($libao_data['limit_amount'])) {
                    $this->error('充值礼包，领取条件不能为空' );
                }

                if(!preg_match("/^[1-9][0-9]*$/",$libao_data['limit_amount'])) {
                    $this->error('充值礼包，领取条件只能输入大于0的正整数' );
                }

                // 累计充值
                if (isset($get_condis1) && $get_condis1 == 1) {
                    if (!in_array($get_condis2, ['TOPLEVEL', 'MULLEVEL'])) {
                        $this->error('抱歉，领取限制2参数有误！' );
                    }
                    $libao_data['get_condis'] = $get_condis2;
                }
                // 单笔充值
                if (isset($get_condis1) && $get_condis1 == 2) {
                    if (!in_array($get_condis2, ['REPEATABLE', 'UNREPEATABLE'])) {
                        $this->error('抱歉，领取限制2参数有误！' );
                    }

                    $libao_data['get_condis'] = $get_condis2;
                    if(!preg_match("/^[1-9][0-9]*$/", $limit_num)) {
                        $this->error('充值礼包，充值笔数只能输入大于0的正整数' );
                    }
                    $libao_data['limit_pens'] = $limit_num;
                }
            }

            $libao_data['create_time'] = NOW_TIMESTAMP;
            $libao_data['update_time'] = NOW_TIMESTAMP;

            if ( empty($libao_data['gameid']) || empty($libao_data['title']) || empty($libao_data['content'])
                || empty($libao_data['starttime']) || empty($libao_data['endtime']) ) {
                $this->error('参数不完整', url('Gift/addGuildGift') );
            }

            $code                = input('post.code');
            $codeToArray         = explode("\r\n", $code);
            $codeToArray         = array_map('filterAndTrimInput', $codeToArray);
            $codeToArray         = array_filter($codeToArray);
            $total               = count($codeToArray);
            $libao_data['total'] = $total;

            $GuildGiftinfoM = model('GuildGiftinfo');
            $GuildGiftinfoM->data($libao_data);
            $GuildGiftinfoM->save();
			$lastInsId = $GuildGiftinfoM->id;
            if ( $lastInsId ) {

                $mLibao = model('GuildGift');
                $list = [];
                foreach ( $codeToArray as $eachCode ) {
                    $libaoInfo = array(
                        'infoid' => $lastInsId,
                        'code'   => $eachCode,
                        'update_time' => NOW_TIMESTAMP,
                    );

                    $list[] = $libaoInfo;
                }
                $mLibao->saveAll($list);

                $this->success('添加成功', url('Gift/guildGift') );
            } else {
                $this->error('添加失败', url('Gift/addGuildGift') );
            }
        }
    }

    /**
     * 编辑公会礼包
     */
    public function editGuildGift(){
        if ( $this->request->isGet() ) {
            $id = input('id', 0, 'intval');

            if ( $id > 0 ) {

                $data = model('GuildGiftinfo')->where( array('id'=>$id) )->find();
                $code = model('GuildGift')->where( array('infoid'=>$id) )->column('code');
                if ( ! empty($code) ) {
                    $code_str = '';
                    foreach ( $code as $key => $value ) {
                        $code_str .= $value."\r\n";
                    }
                }

                $gameInfo = model('Game')->where(['id' => $data['gameid']])->find();
                $data['gameName'] = $gameInfo['name'];
                $data['code'] = $code_str;

                $this->assign('vo', $data);
            }

            $game_list = model('Game')->field('id,name')->order('name asc')->select();
            $this->assign('game_list', $game_list);
            $this->assign('noedit', 1);

            return $this->fetch('guildedit');
        }

        if ( $this->request->isPost() ) {
            $id = input('post.id', 0, 'intval');

            if ( ! $id ) {
                $this->error('数据不存在', url('Gift/index') );
            }

            $libao_data['title']        = input('post.title' , '' , 'filterAndTrimInput');
            $libao_data['content']      = input('post.content' , '' , 'filterAndTrimInput');
            $libao_data['is_top']       = input('post.is_top' , 0 , 'intval');
            $libao_data['limit_amount'] = input('post.limit_amount' , 0 , 'intval');
            $libao_data['type']         = input('post.type' , 0 , 'intval');
            $libao_data['starttime']    = input('post.starttime' , 0 , 'strtotime');
            $libao_data['endtime']      = input('post.endtime' , 0 , 'strtotime');

            if ( $libao_data['is_top'] ) {
                $libao_data['t_starttime'] = input('post.t_starttime', 0, 'strtotime');
                $libao_data['t_endtime']   = input('post.t_endtime', 0, 'strtotime');
            }

            if ($libao_data['type'] == 1) {
                if ( empty($libao_data['limit_amount'])) {
                    $this->error('充值礼包，领取条件不能为空' );
                }

                if(!preg_match("/^[1-9][0-9]*$/",$libao_data['limit_amount'])) {
                    $this->error('充值礼包，领取条件只能输入大于0的正整数' );
                }
            }

            $update = model('GuildGiftinfo')->save($libao_data,array('id'=>$id));

            if ( $update !== false ) {
                $this->success('修改成功', url('Gift/guildGift') );
            } else {
                $this->error('修改失败', url('Gift/editGuildGift', array('id'=>$id) ) );
            }

        }

    }

    /**
     * 公会礼包渠道限制
     */
    public function giftChannelLimit(){
        if ($this->request->isGet()){
            $id = input('id', 0, 'intval');
            if (empty($id)){
                $this->error('参数错误' );
            }
            $data = model('GuildLimit')->where(['infoid'=>$id])->find();
            $this->assign('info',$data);
            $this->assign('infoid',$id);
            $this->assign('limitArr',$data['part_channel']);
            $this->assign('channel_list',model('Channel')->where(['status'=>1])->field('id,name')->select());
            return $this->fetch('giftChannelLimit');
        }

        if ($this->request->isPost()){
			$Data     = $this->request->param();
            $formData = [];
            parse_str($Data['formData'] , $formData);
            $infoid       = isset($formData['infoid']) ? $formData['infoid'] : null;
            $all_limit    = isset($formData['all_limit']) ? $formData['all_num'] : null;
            $all_num      = isset($formData['all_num']) ? $formData['all_num'] : null;
            $all_day      = isset($formData['all_day']) ? $formData['all_day'] : null;
            $all_day_num  = isset($formData['all_day_num']) ? $formData['all_day_num'] : null;
            $part_limit   = isset($formData['part_limit']) ? $formData['part_limit'] : null;
            $part_num     = isset($formData['part_num']) ? $formData['part_num'] : null;
            $part_day     = isset($formData['part_day']) ? $formData['part_day'] : null;
            $part_day_num = isset($formData['part_day_num']) ? $formData['part_day_num'] : null;
            if (isset($Data['limitArr'])) {
                $denied_channel_1 = implode(',' , $Data['limitArr']) . ',';
            } else {
                $denied_channel_1 = '';
            }
            $status = isset($formData['status']) ? $formData['status'] : null;
            $remark = isset($formData['remark']) ? $formData['remark'] : null;


            $condition = [];
            /*---------------所有渠道配置---------------*/
            if (!empty($all_limit)){
                $condition['all_limit'] = $all_limit-1;
            }else{
                $all_limit = 2;
                $condition['all_limit'] = 1;
            }
            // 开启，则必须配置数量；不开启，但是填写了数量，则必须判断
            if (($all_limit == 1) || ($all_limit == 2 && $all_num != '')){
                if (preg_match("/^\d+$/",$all_num) ){
                    $condition['all_num'] = $all_num;
                }else{
                    $this->result('',0,'所有渠道配置：限制总额数量必须为>=0的整数！数量不能为空');
                }
            }

            if (!empty($all_day)){
                $condition['all_day'] = $all_day-1;
            }else{
                $all_day = 2;
                $condition['all_day'] = 1;
            }
            // 开启，则必须配置数量；不开启，但是填写了数量，则必须判断
            if ( ($all_day == 1) || ($all_day == 2 && $all_day_num != '' )){
                if (preg_match("/^\d+$/",$all_day_num) ){
                    $condition['all_day_num'] = $all_day_num;
                }else{
                    $this->result('',0,'所有渠道配置：每日限额数量必须为>=0的整数！数量不能为空');
                }

            }

            // 限制总额 和 每日限额都开启
            if($all_limit == 1 && $all_day == 1){
                if ($all_day_num > $all_num){
                    $this->result('',0,'所有渠道配置：每日限额不能大于限制总额');
                }
            }

            /*---------------部分渠道配置---------------*/
            if (!empty($part_limit)){
                $condition['part_limit'] = $part_limit-1;
            }else{
                $part_limit = 2;
                $condition['part_limit'] = 1;
            }
            // 开启，则必须配置数量；不开启，但是填写了数量，则必须判断
            if ( ($part_limit == 1) || ($part_limit == 2 && $part_num != '')){
                if (preg_match("/^\d+$/",$part_num)){
                    $condition['part_num'] = $part_num;
                }else{
                    $this->result('',0,'部分渠道配置：限制总额数量必须为>=0的整数！数量不能为空');
                }

            }

            if (!empty($part_day)){
                $condition['part_day'] = $part_day-1;
            }else{
                $part_day = 2;
                $condition['part_day'] = 1;
            }
            // 开启，则必须配置数量；不开启，但是填写了数量，则必须判断
            if ( ($part_day == 1) || ($part_day == 2 && $part_day_num != '')){
                if (preg_match("/^\d+$/",$part_day_num)){
                    $condition['part_day_num'] = $part_day_num;
                }else{
                    $this->result('',0,'部分渠道配置：每日限额数量必须为>=0的整数！数量不能为空');
                }

            }

            // 限制总额 和 每日限额都开启
            if($part_limit == 1 && $part_day == 1){
                if ($part_day_num > $part_num){
                    $this->result('',0,'部分渠道配置：每日限额不能大于限制总额');
                }
            }
            // 若部分渠道：开启限制总额 或 每日限额，渠道不能为空
            if (($part_limit == 1 || $part_day == 1) && ($denied_channel_1 == ',' || $denied_channel_1=='')){
                $this->result('',0,'部分渠道配置：开启限制总额或每日限额后，渠道不能为空');
            }
            if ($denied_channel_1 != ','){
                $condition['part_channel'] = $denied_channel_1;
            }
            /*---------------总开关---------------*/
            if (empty($status)){
                $this->result('',0,'请选择是否开启领取限制');
            }
            $condition['status'] = $status-1;

            // 若总开关打开，则4个开关必须至少有一个是打开的
            if($status == 1 && !($all_limit==1 || $all_day==1 || $part_limit==1|| $part_day==1 )){
                $this->result('',0,'领取限制开启，则所有渠道或部分渠道至少有一个限制开启');
            }

            if (!empty($remark)){
                $condition['remark'] = $remark;
            }

            $res = model('GuildLimit')->setGiftLimit($infoid,$condition);

            if ($res !== false){
                $this->result(url('Gift/guildGift'),1,'设置成功' );
            }
            $this->result('',0,'设置失败');
        }
    }

    /**
     * ajax获取所有渠道列表
     */
    public function getChannelAll()
    {
        $channel_list = model('Common/Channel')->getChannleList();
        // 去除超级管理员渠道
        /*unset($channel_list[11]);*/
        echo json_encode($channel_list);
    }

    /**
     * 公会礼包领取记录
     */
    public function guildGiftReceive(){

        $giftId       = input('giftId' , '' , 'filterAndTrimInput');
        $type         = input('type' , '' , 'filterAndTrimInput'); // 1 普通礼包 2充值礼包  入库 需要减1
        $limitFirst   = input('limitFirst' , '' , 'filterAndTrimInput');  // 当 lanmu 为 2 是 1 累计充值 2 单笔充值
        $condisFirst  = input('condisFirst' , '' , 'filterAndTrimInput'); // 当 limitFirst 为 1 是累计充值    TOPLEVEL 最高档位可领 MULLEVEL 多档位可领
        $condisSecend = input('condisSecend' , '' , 'filterAndTrimInput');// 当 limitFirst 为 2 是单笔充值    REPEATABLE 可重复领取 UNREPEATABLE 不可重复领取

        $channel_id  = input('channel_id' , '' , 'filterAndTrimInput');
        $channel_top = input('.channel_top' , '' , 'filterAndTrimInput');
        $condition   = [];

        if ( ! empty($giftId) ) {
            $condition['i.id'] = $giftId;
        }

        if ( ! empty($type) ) {
            $condition['i.type'] = $type - 1;
        }

        // 礼包类型为充值礼包
        if ( ! empty($type) && $type == 2) {
            if ( ! empty($limitFirst) ) {
                // 累计充值
                if ($limitFirst == 1) {
                    if (empty($condisSecend) && ! empty($condisFirst) && in_array($condisFirst, ['TOPLEVEL', 'MULLEVEL'])) {
                        $condition['i.get_condis'] = $condisFirst;
                    }
                    if (empty($condisFirst)) {
                        $condition['i.get_condis'] = array(['eq','TOPLEVEL'],['eq','MULLEVEL'],'or');
                    }
                }
                // 单笔充值
                if ($limitFirst == 2) {
                    if (empty($condisFirst) && ! empty($condisSecend) && in_array($condisSecend, ['UNREPEATABLE', 'REPEATABLE'])) {
                        $condition['i.get_condis'] = $condisSecend;
                    }
                    if (empty($condisFirst)) {
                        $condition['i.get_condis'] = array(['eq','UNREPEATABLE'],['eq','REPEATABLE'],'or');
                    }
                }
            }
        }

        if ( ! empty($channel_id) ) {
            $condition['l.channel_id'] = $channel_id;
        }

        // 判断：因为字段的特殊性， 普通礼包是没有领取限制，故要做条件过滤
        // 没选礼包类型，有选领取限制，则默认礼包类型为充值礼包
        if (!array_key_exists('i.type', $condition) && array_key_exists('i.get_condis', $condition)){
            $condition['i.type'] = 1;
        }
        // 既有选礼包类型，又选领取限制
        if(array_key_exists('i.type', $condition) && array_key_exists('i.get_condis', $condition)){
            if ($condition['i.type'] != 1){
                $condition['i.get_condis'] = rand(1234,5678);
            }
        }

        $giftLog = model('GuildGiftlog');

        $list = $giftLog ->alias('l')
            ->join('cy_guild_giftinfo i ',' i.id = l.infoid','left')
            ->join('cy_game g',' l.game_id = g.id')
            ->field('i.id,l.channel_id,i.title,i.type,i.limit_pens,l.game_id,i.limit_amount,l.create_time,count(l.id) as total,i.get_condis, g.name as game_name')
            ->where($condition)
            ->order('l.id DESC')
            ->group('l.infoid, l.channel_id')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function($game, $key){
                $type                 = $this->_type;
                $libaoCondis = $this->_condis;
                $libaoCondisType = $this->_condisType;
                // 累计充值
                if (in_array($game['get_condis'], ['TOPLEVEL', 'MULLEVEL'])) {
                    $game['type2']    = $libaoCondis[$game['get_condis']];
                    $game['type1']= $libaoCondisType[$game['get_condis']];
                }

                // 单笔充值
                if (in_array($game['get_condis'], ['UNREPEATABLE', 'REPEATABLE'])) {
                    $game['type2']    = $libaoCondis[$game['get_condis']];
                    $game['type1']    = $libaoCondisType[$game['get_condis']].'（'.$game['limit_pens'].'）笔';
                }

                $game['type_1']    = $game['type'];

                $game['channel_name'] = get_current_channel_name($game['channel_id']);
                $game['top_channel']  = setDefault(get_top_second_channel_name($game['channel_id']));
//                $game['top_channel']  = 111111;

                $giftLog_1 = model('GuildGiftlog');
                $game['used']         = $giftLog_1->where(['create_time' => $game['create_time']])->count();
                $game['first_time']   = $giftLog_1->where(['infoid' => $game['id']])->field('create_time')->order('create_time ASC')->find();

                $game['type']         = $type[$game['type']];
                $game['get_condis']   = $this->_condis[$game['get_condis']];
            });


        /*dump(model('Channel')->where(['status'=>1])->field('id,name')->select());
        exit;*/
        $this->assign('list', $list);
        $this->assign('page', $list->render());

        $gameList = model('Common/Game')->getAllByCondition('id,name');
        $this->assign('game_list',$gameList);
        $this->assign('channel_list',model('Channel')->where(['status'=>1])->field('id,name')->select());

        $this->assign('giftList', $this->_getGiftNameList());
        $this->assign('channelTopList', getChannelByTop());
        $this->assign('lanmu_list', $this->_type);

        return $this->fetch('guildReceivelist');
    }

    /**
     * 获取礼包名称列表
     */
    private function _getGiftNameList() {
        $gift_list = [];
        //$giftInfo = model('GuildGiftinfo');
//        $gift_list = $giftInfo->field('id, title as name')->where(['isdelete' => 0])->order('id desc')->select();
        $gift_list = Db::table('cy_guild_giftinfo')->field('id, title as name')->where(['isdelete' => 0])->order('id desc')->select();
        return $gift_list;
    }

    /**
     * 礼包详情
     *
     * @param int giftId
     * @return string
     */
    public function detaileGuildGift() {
        $giftId    = input('giftId', '', 'filterAndTrimInput');
        $channelId = input('channelId', '', 'filterAndTrimInput');
        $giftModel = model('GuildGiftinfo');
        $giftInfo = $giftModel->where(['id' => $giftId])->find();

        if (!$giftInfo) {
            $this->error('无此礼包！', url('guildGiftReceive') );
        }
        $giftLog = model('GuildGiftlog');
        $result = $giftLog->where(['infoid' => $giftId, 'channel_id' => $channelId])->select();

        if ( $result ) {
            $group = array();
            $list = array();
            foreach ($result as $k => $v) {
                $group[$v['create_time']][] = $v;
            }

            foreach ($group as $time => $item) {
                foreach ($item as $key => $val) {
                    $list[$time]['id']             = $val['user_id'];
                    $list[$time]['startTime']      = $time;
                    $list[$time]['giftNum']        = count($item);
                    $list[$time]['giftCode'][$key] = $val['code']."\r\n";
                    $list[$time]['giftType']       = $val['type'];
                    if ($val['type'] == 1) {
                        $member =  model('Members')->where(['id'=>$val['user_id']])->field('username')->find()['username'];
                        $list[$time]['username'] = isset($member) ? $member : '0';
                    } else {
                        $list[$time]['username'] = 0;
                    }
                }
            }
            $list = array_values($list);
        }
        $this->assign('list', $list);

        /*dump(model('Members')->where(['id'=>27550904])->field('username')->find()['username']);
        dump($list);*/
        return $this->fetch('detaileGuildGift');

    }

    /**
     * 公会领取礼包审核页
     */
    public function getGiftCheckList(){
        $gift_name  = input('get.gift_name', '', 'filterAndTrimInput');
        $condis     = input('get.condis', '', 'filterAndTrimInput');
        $channel_id = input('get.channel_id', 0, 'intval');
        $start_time = input('get.start_time', '', 'filterAndTrimInput');
        $end_time   = input('get.end_time', '', 'filterAndTrimInput');

        $condition = [];
        if ( ! empty($gift_name) ) {
            $condition['a.infoid'] = $gift_name;
        }

        if ( ! empty($condis) ) {
            $condition['b.get_condis'] = $condis;
        }

        if ( ! empty($channel_id) ) {
            $condition['a.channel_id'] = $channel_id;
        }

		/*$time_condition = makeTimeCondition($start_time, $end_time);
        if(is_array($time_condition) && count($time_condition)>0) {
            $condition['a.create_time'] = $time_condition;
        }

        // 未设置任何条件时，默认显示当天数据
        if(empty($condition) || count($condition) == 0) {
            $start = $_GET['start_time'] = date('Y-m-d');
            $condition['a.create_time'] = makeTimeCondition($start);
        }*/

        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $condition['a.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $condition['a.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $condition['a.create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {
            $start_time = date('Y-m-d', time());
            $end_time   = date('Y-m-d', time());

            $condition['a.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        }


        $list = model('GuildGiftapply')
            ->alias('a')
            ->join("cy_guild_giftinfo b "," a.infoid = b.id ")
            ->join("cy_game c "," c.id = a.game_id")
            ->join("cy_department d "," d.id = a.channel_id")
            ->field("a.*,b.title,b.get_condis,b.type as gift_type, c.name as game_name ,d.name as channel_name,d.id_path ")
            ->where($condition)
            ->order('a.audit_status asc, a.create_time desc')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function($item, $key){
                $item['id_path'] = mb_substr($item['id_path'],1,2);
                $departArr = model('Channel')->where("id = '{$item['id_path']}'")->field('name')->find();
                $item['dj_departName'] = $departArr['name'];
                return $item;
            });

        // 获取 礼包 数组
        $this->assign('page', $list->render());
        $this->assign('list',$list);
        $this->assign('gift_List', $this->_getGiftNameList());
        $this->assign('channel_list',model('Channel')->where(['status'=>1])->field('id,name')->select());
        $this->assign('status_list', $this->status_list);
        $this->assign('condis_list', $this->_condis);
        $this->assign('giftType_list', $this->_type);

        $this->assign('start_time', $start_time);
        $this->assign('end_time', $end_time);

        return $this->fetch('getGiftCheckList');
    }

    /**
     * 公会礼包审核
     */
    function getGiftCheck(){
        $id           = input('post.id', 0, 'intval');
        $channel_id   = input('post.channel_id', 0, 'intval');
        $audit_status = input('post.audit_status', 0, 'intval');
        $apply_num    = input('post.apply_num', 0, 'intval');
        $audit_num    = input('post.audit_num', 0, 'filterAndTrimInput');
        $audit_cmmt   = input('post.audit_cmmt', '', 'filterAndTrimInput');

        if (empty($id)){
            $this->result('',0,'参数错误');
        }

        if (empty($audit_status)){
            $this->result('',0,'请选择是否审核通过');
        }
        if ($audit_status ==1 && (empty($audit_num) || !preg_match("/^[1-9][0-9]*$/",$audit_num))){
            $this->result('',0,'发放数量必须为正整数');
        }
        // 如果审核通过，确定要发放礼包，对礼包数目进行核查
        if ($audit_status == 1){
            $giftApplyArr = model("GuildGiftapply")->where("id = '{$id}'")->find();
            if ($giftApplyArr['apply_num'] != $apply_num){
                $this->result('',0,'领取数量和记录的不一致');
            }
        }

        if ($audit_status ==1 && ((int)$audit_num > (int)$apply_num)){
            $this->result('',0,'发放数量不能超过领取数量');
        }

        // 获取礼包剩余数量，验证发放数量是否会超过 实际 剩余数量
        if ($audit_status ==1){
            $giftInfoArr = model("GuildGiftinfo")->where("id = '{$giftApplyArr['infoid']}'")->find();
            $giftHasNum = (int)$giftInfoArr['total'] - (int)$giftInfoArr['used'];
            if($giftHasNum < $audit_num){
                $this->result('',0,'发放数量不能超过礼包剩余数量');
            }
        }

        // 不同意时 发放数量为0
        if ($audit_status ==2){
            $audit_num = 0;
        }

        // 开启事物
        Db::startTrans();

        // 更新公会礼包申请表
        $data = [
            'audit_status' => $audit_status,
            'audit_num' => $audit_num,
            'audit_cmmt' => $audit_cmmt,
            'audit_time' => time(),
            'read_yn' => 0,
        ];
        if ($audit_status == 2){
            $guildStatus = '不通过';
        }else {
            $guildStatus = '通过';
        }
        $res1 = model('GuildGiftapply')->save($data,['id'=>$id]);
        // 消息通知表 数据
        $noticeArr = [
            'title'=> '“'.$giftInfoArr['title'].'”领取申请'.$guildStatus,
            'content'=> '“'.$giftInfoArr['title'].'礼包领取”审核不通过，原因为：'.$audit_cmmt.'。',
            'create_time'=> time(),
            'channel_id'=> $channel_id,
        ];
        $res2 = true;
        $res4 = true;
        $res5 = true;

        if ($audit_status == 1){
            $data2 = ['used'=> (int)$giftInfoArr['used'] + $audit_num ,'update_time'=>time()];
            $res2 = model("GuildGiftinfo")->save($data2,['id'=>$giftApplyArr['infoid']]);
            $noticeArr['content'] = '“'.$giftInfoArr['title'].'礼包领取”审核通过，请及时查收。';
            // 如果备注不为空，则要加上备注
            if(!empty($audit_cmmt)){
                $noticeArr['content'] = '“'.$giftInfoArr['title'].'礼包领取”审核通过，请及时查收，注意事项：'.$audit_cmmt;
            }
        }

        $resApplyInfo = model('GuildGiftapply')->alias('a')
            ->field('a.channel_id,a.id as appli_id,a.game_id,b.title,b.type,b.id as lbinfo_id')
            ->join('cy_guild_giftinfo b ',' a.infoid = b.id')
            ->where(['a.id'=>$id, 'b.isdelete'=>0])
            ->find();
        if($audit_status == 1 && $resApplyInfo['type'] == 0){
            $resApplyCode = model('GuildGiftapply')->alias('a')
                ->field('b.code,b.id')
                ->join("cy_guild_gift b "," a.infoid = b.infoid",'left')
                ->where(['a.id'=>$id, 'b.status'=>0])
                ->limit($audit_num)
                ->select();

            if (empty($resApplyCode) || count($resApplyCode) < $audit_num){
                $msg = "礼包剩余数量不足";
                $res4 = false;
            }

            if ($res4){
                // 普通礼包写入cy_guild_giftlog 礼包领取记录表
                $insertLogArr = [];
                $GuildGift_idArr = [];
                foreach ($resApplyCode as $v){
                    $insertLogArr[] = [
                        'user_id' => 0,
                        'channel_id' => $resApplyInfo['channel_id'],
                        'game_id' => $resApplyInfo['game_id'],
                        'title' => $resApplyInfo['title'],
                        'type' => $resApplyInfo['type'],
                        'infoid' => $resApplyInfo['lbinfo_id'],
                        'giftid' => $v['id'],
                        'code' => $v['code'],
                        'create_time' => time(),
                    ];

                    $GuildGift_idArr[] = ['id'=>$v['id'],'status'=>1,'update_time'=>time()];
                }
                $GuildGiftlogModel = model('GuildGiftlog');
                $res4 = $GuildGiftlogModel->saveAll($insertLogArr);
                $res5 = model('GuildGift')->saveAll($GuildGift_idArr);
                if (!$res4 || !$res5){
                    $res4 = false;
                }
            }
        }

        $GuildMessageModel = model('GuildMessage');
        $GuildMessageModel->data($noticeArr);
        $res3 = $GuildMessageModel->save();

        // 提交事务
        if($res1 && $res2 && $res3 && $res4 && $res5){
            Db::commit();
            $this->result('',1,'更新成功');
        }else{
            Db::rollback();
            $this->result('',0,'更新失败：'.$msg);
        }
    }

    /**
     * 礼包领取记录 列表
     */
    public function giftReceiveList(){
        $condition = [];
        $username  = input('username' , '' , 'filterAndTrimInput');
        $gameid    = input('gameid' , '' , 'filterAndTrimInput');
        $title     = input('title' , '' , 'filterAndTrimInput');

        if (!empty($username)){
            $condition['a.username'] = $username;
        }
        if (!empty($gameid)){
            $condition['l.gameid'] = $gameid;
        }
        if (!empty($title)){
            $condition['l.title'] = $title;
        }


        $start_time = input('request.cr_start');
        //开始时间和结束时间不为空时
        if ($start_time != '' && input('request.cr_end') != '') {
            $condition['a.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime(input('request.cr_end').' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time!= '') {
            $condition['a.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif (input('request.cr_end') != '') {
            $condition['a.create_time'] = ['<=', strtotime(input('request.cr_end').' 23:59:59')];
        }else {
            $start_time            = $end_time = date('Y-m-d', time());
            $condition['a.create_time'] = ['>=', strtotime($start_time)];
        }

        $list = model('Libaolog')->alias('a')
            ->join('cy_libaoinfo l','l.id=a.libaoid')
            ->join('cy_game g','g.id=l.gameid','left')
            ->field('a.id,l.title,a.username,a.create_time,g.name,a.code')
            ->where($condition)
            ->order('a.create_time desc')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function($item, $key){
                $item['username'] = stringObfuscation($item['username'],3);
                return $item;
            });

        $this->assign('list', $list);
        $this->assign('page', $list->render());

        $tmpGameList = model('Common/Game')->getAllByCondition('id,name');
      	$gameList = array();
		foreach ($tmpGameList as $game) {
              $gameList[ $game['id']] = $game;
        }

        $this->assign('game_list',$gameList);
        return $this->fetch('giftReceiveList');
    }

    /**
     * 获取用户名（礼包领取记录）
     */
    public function checkReceiveUser(){
        $id = input('id' , '' , 'filterAndTrimInput');
        if (empty($id)) $this->error('参数错误！');

        $info = model('Libaolog')->alias('a')
            ->join('cy_libaoinfo l','l.id=a.libaoid')
            ->field('a.username,l.title')
            ->where('a.id',$id)
            ->find();

        $this->insertLog($this->current_node,"查看账号：".$info['username']."，关联礼包名：".$info['title'],151);


        $this->success($info['username']);
    }

}
