<?php
/**
 * 前台注册用户管理控制器
 */

namespace app\admin\controller;

use app\common\model\Members as MembersModel;
use app\common\model\MembersTwo;
use think\Db;
use app\common\logic\Member as MemberService;
use app\common\library\MakeReport;
use app\common\library\FileUpload;
use think\Exception;

class ChannelAccountDet extends Admin
{
    protected $membersModel;
    protected $where;
    protected $start_time;
    protected $end_time;

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->membersModel = new MembersModel;
        $this->gameList     = $gameList = model('Common/Game')->getAllByCondition('id,name');
		$this->selfGameList  = $selfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
        $this->where        = [];
    }

    /**
     * 渠道账户明细列表
     */
    public function detList()
    {
        $where = $this->_getDetListCondition();
		//var_dump($where);

        $list = model("ChannelAccountDet")->alias('d')
			->join('nw_channel channel', 'd.channel_id = channel.id','left')
            ->field("d.id,d.channel_id,d.channel_name,d.change_amount,d.change_amount,d.account_type,d.type,d.out_orderid,d.create_time,channel.level,channel.status,channel.amount,channel.js_amount")
            ->where($where)
            ->order('d.create_time desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];

        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);

        return $this->fetch('det_list');
    }

    /**
     * 账户变动明细  条件查询
     * @return array
     */
    protected function _getDetListCondition()
    {
        $start_time       = input('request.start_time', '', 'trim');
        $end_time         = input('request.end_time', '', 'trim');
        $channel_name     = input('request.channel_name', '', 'trim');
		$channel_id		  = input('request.channel_id', 0, 'intval');
		$type			  = input('request.type', '', 'trim');
		$account_type	  = input('request.account_type', '', 'trim');
		$out_orderid	  = input('request.out_orderid', '', 'trim');

		$where = array();
        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)){
            $where['d.create_time'] = $this->getTimeCondition($start_time,$end_time,false);
        }
        //渠道ID
        if ($channel_id) {
            $where['d.channel_id'] = $channel_id;
        }
        //渠道名称
        if ($channel_name != '') {
            $where['d.channel_name'] = $channel_name;
        }
        //账户类型
        if ($account_type != '') {
            $where['d.account_type'] =  $account_type;
        }
        //类型
        if ($type != '') {
            $where['d.type'] =  $type;
        }
	    //游戏ID
        if ($out_orderid != '') {
            $where['d.out_orderid'] = $out_orderid;
        }
        return $where;
    }

    /**
     * 获取日期查询条件
     * @param  int  $start_time  开始时间
     * @param  int  $end_time    结束时间
     * @param  bool  $isdefault    是否默认今天日期
     * @return
     */
    private function getTimeCondition($start_time,$end_time,$isdefault = true)
    {
        $this->start_time = $start_time;
        $this->end_time   = $end_time;
        $time = [];
        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $time = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $time = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $time = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {

            if($isdefault){
                $this->start_time = $this->end_time = date('Y-m-d', time());

                $time = [
                    ['>=', strtotime($this->start_time)],
                    ['<=', strtotime($this->end_time . ' 23:59:59')],
                ];
            }
        }

        return $time;
    }
}
