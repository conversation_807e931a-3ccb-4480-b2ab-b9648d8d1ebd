<?php
/**
 * 前台注册用户管理控制器
 */

namespace app\admin\controller;

use app\common\library\MakeReportGo;
use app\common\model\Members;
use app\common\model\Members as MembersModel;
use app\common\model\MembersTwo;
use app\common\model\Channel;
use app\common\model\Game;
use app\common\model\Pay;
use app\common\model\Subaccount;
use think\Cache;
use think\Db;
use app\common\logic\Member as MemberService;
use app\common\library\MakeReport;
use app\common\library\FileUpload;
use think\Env;

class Member extends Admin
{
    protected $membersModel;
    protected $where;
    protected $start_time;
    protected $end_time;

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->membersModel = new MembersModel;

        $this->where = [];
        $this->gameList = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [], '', 'self');
        $selfGameList = array();
        foreach ($tmpSelfGameList as $game) {
            $selfGameList[$game['id']] = $game;
        }
        $this->selfGameList = $selfGameList;

    }

    public function getWhere()
    {
        $where = [];

        // 推广四联查询
        $business_type = input('tg_business', '');           // 商务
        $president_type = input('tg_president', '');         // 会长
        $president_type_son = input('tg_president_son', ''); // 子会长
        $promoter_type = input('tg_promoter', '');           // 推广员
        if ($business_type <> '') {
            $where['s.channel_id'] = ['in', get_child_channel_arr($business_type)];
        }
        if ($president_type <> '') {
            $where['s.channel_id'] = ['in', get_child_channel_arr($president_type)];
        }
        if ($president_type_son <> '') {
            $where['s.channel_id'] = ['in', get_child_channel_arr($president_type_son)];
        }
        if ($promoter_type <> '') {
            $where['s.channel_id'] = $promoter_type;
        }

        !empty($flag = input('sub_flag')) && $where['s.flag'] = $flag;
        !empty($sub_gameid = input('sub_gameid')) && $where['s.game_id'] = $sub_gameid;
        !empty($sub_id = input('sub_id')) && $where['s.id'] = $sub_id;
        !empty($sub_username = input('sub_username')) && $where['s.sub_username'] = ['like', '%' . $sub_username . '%'];
        !empty($m_nickname = input('m_nickname')) && $where['m.nickname'] = ['like', '%' . $m_nickname . '%'];
        !empty($m_username = input('m_username')) && $where['m.username'] = ['like', '%' . $m_username.'%'];

        // 获取查询日期
        $start_time = input('start_time');
        $end_time = input('end_time');
        // 获取查询日期
        if ($start_time && $end_time) {
            $where['s.create_time'] = ['between time', [strtotime($start_time), strtotime($end_time . ' 23:59:59')]];
        }

        // if ($username=='' && $id == '' && $nickname == ''){
        //     if(strtotime($this->end_time.' 23:59:59')-strtotime($this->start_time)>31*24*3600)
        //     {
        //         $this->error('单次查询日期的最长跨度为31天');
        //     }
        // }

        if (!empty(input('gh_status'))) { // 是否聚合用户：0=否、1=是
            $where['channel.gh_status'] = 1;
        }

        $this->where = $where;
    }

    /**
     * 注册用户列表
     */
    public function index()
    {
        $this->getWhere();
        $param = input('get.');
        $page_size = input('page_size', 15);

        $list = Db::table('nw_subaccount')->alias('s')
            ->join('cy_members m', 's.member_id=m.id', 'left')
            ->join('nw_channel channel', 'channel.id=s.channel_id', 'left')
            ->join('nw_channel bchild', 'bchild.id=channel.parent_id AND bchild.level = 2', 'left')
            ->field('s.id,s.sub_username,s.real_name,m.id as mid,m.username,m.nickname,m.avatar,s.channel_id,s.game_id as gameid,s.create_time,m.ip,m.imeil,m.total_pay_amount,m.login_time,bchild.name as bchild_name,bchild.parent_id as bcparent_id, s.flag')
            ->where($this->where)
            // ->order('reg_time desc')
            ->order('s.id desc')
            ->paginate($page_size, false, array('query' => $param))
            ->each(function ($item, $key) {
                $channel_list = get_channel_top_by_aggregate($item['channel_id']);
                $item['channel_zero_name'] = !empty($channel_list['0']['name']) ? $channel_list['0']['name'] : '';
                $item['channel_one_name'] = !empty($channel_list['1']['name']) ? $channel_list['1']['name'] : '';
                $item['channel_two_name'] = !empty($channel_list['2']['name']) ? $channel_list['2']['name'] : '';
                $item['channel_three_name'] = !empty($channel_list['3']['name']) ? $channel_list['3']['name'] : '';
                return $item;
            });;

        // $list = Db::table('cy_members')->alias('m')
        //     ->join('nw_channel channel', 'channel.id=m.channel_id', 'left')
        //     ->join('nw_channel bchild', 'bchild.id=channel.parent_id AND bchild.level = 2', 'left')
        //     ->field('m.id,m.username,m.nickname,m.avatar,m.channel_id,m.flag,m.gameid,m.reg_time,m.ip,m.imeil,m.total_pay_amount,m.login_time,bchild.name as bchild_name,bchild.parent_id as bcparent_id')
        //     ->where($this->where)->order('reg_time desc')
        //     ->paginate(10, false, array('query' => $param));

        /*        $list  = $this->membersModel
                    ->join('nw_channel channel', 'channel.id=l.channel_id', 'left')
                    ->join('nw_channel bchild', 'bchild.id=channel.parent_id AND bchild.level = 2', 'left')
                    ->field('id,username,nickname,avatar,channel_id,flag,gameid,reg_time,ip,imeil,total_pay_amount,login_time')
                    ->where($this->where)->order('reg_time desc')
                    ->paginate(10, false, array('query' => $param));*/

        $gameList = $this->selfGameList;
        $gList = $gameList;
        $gameList = array_column($gameList, 'name', 'id');
        $data     = $list->toArray()['data'];
        $memberIdArr = array();
        foreach ($data as &$v) {
            $memberIdArr[] = $v['id'];

            // $v['top_channel_name'] = get_top_channel($v['channel_id'])['name'];
            // $v['dep_name'] = get_channel_name($v['channel_id']);
            // $v['union_name'] = get_union_channel($v['channel_id'])['name'];

            $condition = array();
            $condition['userid'] = $v['id'];
            $condition['gameid'] = $v['gameid'];
            $roleinfo = (new MemberService())->queryUserRole($condition); // 账号查询
            $v['rolename'] = $roleinfo['rolename'];
            $v['servername'] = $roleinfo['servername'];
            $v['rolelevel'] = $roleinfo['rolelevel'];
            $userPayInfo = model('Pay')->where(['status' => 1, 'userid' => $v['mid']])->group('userid')->field(['userid', 'sum(amount) as money', 'count(id) as paycount'])->find();
            $v['total_pay_amount'] = isset($userPayInfo->money) ? $userPayInfo->money : 0;
            $v['total_pay_cnt'] = isset($userPayInfo->paycount) ? $userPayInfo->paycount : 0;
            $v['avatar'] = $v['avatar'] ? STATIC_DOMAIN . $v['avatar'] : '';

            // 账号加密
            // $v['username'] = stringObfuscation($v['username'],3);
        }

        $channel = [];
        $channelId = input('request.channel_id');
        if ($channelId) {
            $channel = model('Common/Channel')->field(['id', 'name'])->find($channelId);
        }

        //$channel_list = model('Common/Channel')->getAllByCondition('id,name',['status' => 1,'flag'=>['in','3,4']]);

        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('page', $list->render());
        $this->assign('game_list', $gameList);
        $this->assign('glist', $gList);

        $this->assign('channel', $channel);
        $this->assign('channel_list', []);
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);

        $uniom = model('Channel')->where(['level' => 0])->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表
        $this->assign('jh_url', url('Member/index', ['gh_status' => 1, 'page_size' => '100'], 'html'));

        return $this->fetch();
    }

    public function index1(){
        $this->getWhere();
        $param = input('get.');
        $page_size = input('page_size', 15);
        $list = Db::table('nw_subaccount')->alias('s')
            ->join('cy_members m', 's.member_id=m.id', 'left')
            ->join('nw_channel channel', 'channel.id=s.channel_id', 'left')
            ->join('nw_channel bchild', 'bchild.id=channel.parent_id AND bchild.level = 2', 'left')
            ->field('s.id,s.sub_username,s.real_name,m.id as mid,m.username,m.nickname,m.avatar,s.channel_id,s.game_id as gameid,m.reg_time,m.ip,m.imeil,m.total_pay_amount,m.login_time,bchild.name as bchild_name,bchild.parent_id as bcparent_id, s.flag')
            ->where($this->where)
            ->order('s.id desc')
            ->paginate($page_size, false, array('query' => $param));
        $gameList = $this->selfGameList;
        $gList = $gameList;
        $gameList = array_column($gameList, 'name', 'id');
        $data     = $list->toArray()['data'];
        if(!empty($data)){
            $roleInfo = Db::table('cy_role_info')
                ->field(['servername', 'rolename', 'rolelevel','userid','gameid'])
                ->order('id', 'asc')
                ->select()->toArray();
            foreach ($data as &$v){
                foreach ($roleInfo as $val){
                    if($v['id']==$val['userid'] && $v['gameid']==$val['gameid']){
                        $v['rolename'] = $val['rolename'];
                        $v['servername'] = $val['servername'];
                        $v['rolelevel'] = $val['rolelevel'];
                    }
                }
                $userPayInfo = model('Pay')->where(['status' => 1, 'userid' => $v['id']])->group('userid')->field(['userid', 'sum(amount) as money', 'count(id) as paycount'])->find();
                $v['total_pay_amount'] = isset($userPayInfo->money) ? $userPayInfo->money : 0;
                $v['total_pay_cnt'] = isset($userPayInfo->paycount) ? $userPayInfo->paycount : 0;
                $v['avatar'] = $v['avatar'] ? STATIC_DOMAIN . $v['avatar'] : '';
                $channel_list = get_channel_top_by_aggregate($v['channel_id']);
                $v['channel_zero_name'] = !empty($channel_list['0']['name']) ? $channel_list['0']['name'] : '';
                $v['channel_one_name'] = !empty($channel_list['1']['name']) ? $channel_list['1']['name'] : '';
                $v['channel_two_name'] = !empty($channel_list['2']['name']) ? $channel_list['2']['name'] : '';
                $v['channel_three_name'] = !empty($channel_list['3']['name']) ? $channel_list['3']['name'] : '';
            }
        }
        $channel = [];
        $channelId = input('request.channel_id');
        if ($channelId) {
            $channel = model('Common/Channel')->field(['id', 'name'])->find($channelId);
        }
        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('page', $list->render());
        $this->assign('game_list', $gameList);
        $this->assign('glist', $gList);

        $this->assign('channel', $channel);
        $this->assign('channel_list', []);
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);

        $uniom = model('Channel')->where(['level' => 0])->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表
        $this->assign('jh_url', url('Member/index', ['gh_status' => 1, 'page_size' => '100'], 'html'));
        return $this->fetch();
    }

    /**
     * 报表下载
     *
     */
    public function download($is_show = ['is_show'=>false])
    {
        if (request()->isAjax()) {


            $this->getWhere(false);

            if ($is_show['is_show']) {
                $start_time = input('request.start_time', '', 'trim');
                $end_time = input('request.end_time', '', 'trim');
                $username = input('request.username', '', 'trim');
                $gameid = input('request.gameid', '', 'intval');
                if (!$start_time) {
                    $start_time = date("Y-m-d");
                }
                if (!$end_time) {
                    $end_time = date("Y-m-d");
                }
                $LogStr = "时间：" . $start_time . "~" . $end_time;
                if ($gameid) {
                    $game_name = get_game_nickname($gameid);
                } else {
                    $game_name = "全部游戏";
                }
                $LogStr .= "，游戏：" . $game_name;
                $LogStr .= "，玩家账号：" . $username;
                $this->insertLog($this->current_node, $LogStr, 122);
            }

            $sql = $this->membersModel
                ->field('id,username,nickname,channel_id,flag,gameid,reg_time,total_pay_amount,login_time,imeil')
                ->where($this->where)
                ->order('reg_time desc')->fetchSql(true)->select();

            if ((new MakeReportGo())->addTask('memberList', $sql, session_id(), $is_show)) {
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');

        }
        else{
            $this->error('非法请求');
        }

    }

    /**
     * 冻结账户
     */
    public function frozen()
    {
        $id = input('id', 0, 'intval');
        $flag = input('flag', 0, 'abs');
        if (empty($id)) {
            $this->error('用户ID不能为空');
        }

        $members = new MembersModel();
        if (!$info = $members->get(['id' => $id])) {
            $this->error('非法参数');
        }

        if ($flag == 1) $msg = '冻结'; else
            $msg = '解冻';

        if ($members->update(['flag' => $flag, 'update_time' => time(),], ['id' => $id])) {
            // 插入冻结记录
            model('FrozenHistory')->addData($id, $flag);

            $info = $msg . "玩家：{$info['username']}";
            $this->insertLog($this->current_node, $info, 22);

            $this->success($msg . '账户成功');

        } else {
            $this->error($msg . '账户失败');
        }
    }

    /**
     * 子账户 - 冻结/解冻
     */
    public function frozenSubaccount()
    {
        $id = input('id', 0, 'intval');
        $flag = input('flag', 0, 'abs');
        if (empty($id)) {
            $this->error('用户ID不能为空');
        }

        $nw_subaccount = new Subaccount();
        if (!$info = $nw_subaccount->get(['id' => $id])) {
            $this->error('非法参数');
        }

        if ($flag == 1) $msg = '冻结'; else
            $msg = '解冻';

        if ($nw_subaccount->update(['flag' => $flag, 'update_time' => time(),], ['id' => $id])) {
            // 插入冻结记录
            model('FrozenHistory')->addData($id, $flag);

            $info = $msg . "玩家：{$info['sub_username']}";
            $this->insertLog($this->current_node, $info, 22);

            $this->success($msg . '账户成功');

        } else {
            $this->error($msg . '账户失败');
        }
    }

    /**
     * 登录用户列表
     */
    public function loginList()
    {
        $where = $this->_getLoginListCondition(true);

        if (isset($where['m.username'])) {
            $username = $where['m.username'];
            $userid = $this->membersModel->where(array('username' => $username))->value('id');
            if (!intval($userid)) {
                $userid = 0;
            }
            $where['l.userid'] = $userid;
            unset($where['m.username']);
        }
        // 剔除聚合渠道数据
        //   $where['c.flag'] = ['<>',4];

        // dump($where);
        $list = Db::table('cy_logininfo')->alias('l')
            ->join('nw_subaccount s', 's.id=l.sub_id')
            ->join('nw_channel channel', 'channel.id=l.channel_id','left')
            ->join('nw_channel bchild', 'bchild.id=channel.parent_id','left')
            //  ->join('cy_members m', 'l.userid = m.id')
            //  ->join('nw_channel c', 'l.channel_id = c.id','left')
            //  ->field("l.id,l.ip,l.channel_id,l.userid,l.gameid,l.imeil,l.login_time,m.username,m.total_pay_amount,m.flag,c.name,m.reg_time")
            ->field("l.id,l.ip,l.channel_id,l.userid,l.gameid,l.imeil,l.login_time,bchild.name as bchild_name,bchild.parent_id as bcparent_id,s.id as sub_id,s.sub_username")
            ->where($where)
            ->order('l.login_time desc')
            ->paginate(25, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];
        foreach ($data as $key => $value) {
            $data[$key]['top_channel_name'] = get_top_channel($value['channel_id'])['name'];
            $data[$key]['union_name'] = get_union_channel($value['channel_id'])['name'];
            $data[$key]['name'] = get_channel_name($value['channel_id']);
            $memberInfo = $this->membersModel->field('id,username,total_pay_amount,flag,reg_time')->find($value['userid']);
            // 账号加密
            $data[$key]['username'] = stringObfuscation($memberInfo['username'], 3);
            $data[$key]['total_pay_amount'] = $memberInfo['total_pay_amount'];
            $data[$key]['flag'] = $memberInfo['flag'];
            $data[$key]['reg_time'] = $memberInfo['reg_time'];
        }

        $gameList = $this->selfGameList;
        $gList = array_column($gameList, 'name', 'id');
        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        $this->assign('game_list', $gList);
        $this->assign('gList', $gameList);
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $uniom = model('Channel')->where(['level' => 0])->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表

        return $this->fetch('login_list');
    }

    /**
     * 登录用户 报表下载
     */
    public function loginListDownload($is_show = ['is_show'=>false])
    {
        if (request()->isAjax()) {
            $where = $this->_getLoginListCondition(true);
            if ($is_show['is_show']) {
                $start_time = input('request.start_time', '', 'trim');
                $end_time = input('request.end_time', '', 'trim');
                $username = input('request.username', '', 'trim');
                $gameid = input('request.gameid', '', 'intval');
                if (!$start_time) {
                    $start_time = date("Y-m-d");
                }
                if (!$end_time) {
                    $end_time = date("Y-m-d");
                }
                $LogStr = "时间：" . $start_time . "~" . $end_time;
                if ($gameid) {
                    $game_name = get_game_nickname($gameid);
                } else {
                    $game_name = "全部游戏";
                }
                $LogStr .= "，游戏：" . $game_name;
                $LogStr .= "，玩家账号：" . $username;
                $this->insertLog($this->current_node, $LogStr, 121);
            }
            // 剔除聚合渠道数据
            //   $where['c.flag'] = ['<>',4];
            $sql = Db::table('cy_logininfo')->alias('l')
                ->join('cy_members m', 'l.userid = m.id')
                ->join('nw_channel c', 'l.channel_id = c.id', 'left')
                ->field("l.id,l.imeil as imeil,l.ip as ip,l.channel_id as channel_id,l.userid,l.gameid as gameid,l.login_time,m.username,m.total_pay_amount,m.flag,c.name,m.reg_time")
                ->where($where)
                ->order('l.login_time desc')
                ->fetchSql(true)
                ->select();

            if((new MakeReportGo())->addTask('memberLoginList', $sql, session_id(), $is_show)){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');

        } else {

            $this->error('非法请求');

        }
    }

    /**
     * 登录用户  条件查询
     * @return array
     */
    protected function _getLoginListCondition($isdefault)
    {
        //   $where['m.is_local'] = 1;

        $start_time       = input('request.start_time', '', 'trim');
        $end_time         = input('request.end_time', '', 'trim');
        $username         = input('request.username', '', 'trim');
        $sub_username         = input('request.sub_username', '', 'trim');
        $imeil            = input('request.imeil', '', 'trim');
        $gameid           = input('request.gameid', '', 'intval');
        //        $channel_name     = input('request.channel_name', '', 'trim');
        //        $top_channel_name = input('request.top_channel_name', '', 'trim');

        $channel_id     = input('channel_id', 0, 'intval');
        $top_channel_id = input('parent_channel_id', 0, 'intval');
        $bplus_channel_id = $this->request->param('bplus_channel_id', 0, 'intval');

        // 获取查询日期
        $where['l.login_time'] =$this->getTimeCondition($start_time,$end_time,$isdefault);

        /*
        if ($username==''){
            if(strtotime($this->end_time.' 23:59:59')-strtotime($this->start_time)>31*24*3600)
            {
                $this->error('单次查询日期的最长跨度为31天');
            }
        }
        */


        //用户名
        if ($username != '') {
            //  $where['m.username'] = ['like', $username . '%'];
            $where['m.username'] = $username;
        }

        // 子账户
        if ($sub_username != '') {
            $where['s.sub_username'] = $sub_username;
        }

        //imeil
        if ($imeil != '') {
            $where['l.imeil'] = ['=', $imeil];
        }
        //游戏名称
        if (!empty($gameid)) {
            $where['l.gameid'] = ['=', $gameid];
        }

        //渠道名称
        if ( !empty($channel_id) || !empty($top_channel_id) || !empty($bplus_channel_id)) {
            $where['l.channel_id'] = $this->getChannelCondition($channel_id,$top_channel_id,$bplus_channel_id);
        }

        return $where;
    }

    /**
     * 冻结用户列表
     */
    public function frozenList()
    {

        $where           = [];
        $memberIdArr     = [];
        $where['m.flag'] = 1;
        //查询参数

        $start_time = input('request.start_time');
        $end_time   = input('request.end_time');
        $username   = input('request.username');
        $gameid     = input('request.gameid');
        $imeil      = input('request.imeil');

        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $where['m.reg_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $where['m.reg_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $where['m.reg_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {
            /*
            $start_time          = $end_time = date('Y-m-d', time());
            $where['m.reg_time'] = ['>=', strtotime($start_time)];
            */
        }

        //用户名
        if ($username != '') {
            $where['m.username'] = ['like', '%' . $username . '%'];
        }
        //imeil
        if ($imeil != '') {
            $where['m.imeil'] = ['=', $imeil];
        }

        //游戏名称
        if ($gameid != '') {
            $where['m.gameid'] = ['=', $gameid];
        }

        $list = Db::table('cy_members')->alias('m')
            ->join('nw_channel c', 'm.channel_id = c.id')
            ->field("m.id,m.imeil,m.reg_time,m.login_time,m.gameid,m.username,m.total_pay_amount,m.flag,m.channel_id,c.name")
            ->order('m.id desc')
            ->where($where)->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];


        foreach ($data as $key => $value) {
            $memberIdArr[]                    = $value['id'];
            $data[ $key ]['top_channel_name'] = get_top_channel($value['channel_id'])['name'];
            $data[ $key ]['bchild_name'] = get_top_second_channel_name($value['channel_id'])['second_name'];
        }

        $payArr   = model('pay')->where(['status' => 1, 'userid' => ['IN', $memberIdArr]])->group('userid')
            ->column('userid,sum(amount) as money,count(id) as paycount');
        $gameList = $this->selfGameList;
        $gList  = array_column($gameList, 'name', 'id');
        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('payArr', $payArr);
        $this->assign('start_time', $start_time);
        $this->assign('end_time', $end_time);
        $this->assign('page', $list->render());
        $this->assign('game_list', $gameList);
        $this->assign('gList', $gList);

        return $this->fetch('frozen_list');
    }

    /**
     * 冻结子用户列表
     */
    public function frozenSubaccountList()
    {

        $where = [];
        $memberIdArr = [];
        $where['s.flag'] = 1;
        //查询参数

        $start_time = input('request.start_time');
        $end_time = input('request.end_time');
        $username = input('request.username');
        $sub_username = input('request.sub_username');
        $gameid = input('request.gameid');
        $imeil = input('request.imeil');

        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $where['s.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $where['s.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $where['s.create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {
            /*
            $start_time          = $end_time = date('Y-m-d', time());
            $where['m.create_time'] = ['>=', strtotime($start_time)];
            */
        }

        // 子账号用户名
        if ($username != '') {
            $where['s.sub_username'] = ['like', '%' . $sub_username . '%'];
        }
        // 用户名
        if ($username != '') {
            $where['m.username'] = ['like', '%' . $username . '%'];
        }
        //imeil
        if ($imeil != '') {
            $where['m.imeil'] = ['=', $imeil];
        }

        //游戏名称
        if ($gameid != '') {
            $where['s.game_id'] = ['=', $gameid];
        }

        $list = Db::table('nw_subaccount')->alias('s')
            ->join('cy_members m', 's.member_id = m.id')
            ->join('nw_channel c', 'm.channel_id = c.id')
            ->field("s.id,s.sub_username,m.id as mid,m.imeil,m.reg_time,m.login_time,s.game_id as gameid,m.username,m.total_pay_amount,s.flag,s.channel_id,c.name")
            ->order('m.id desc')
            ->where($where)->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];


        foreach ($data as $key => $value) {
            $memberIdArr[] = $value['id'];
            $data[$key]['top_channel_name'] = get_top_channel($value['channel_id'])['name'];
            $data[$key]['bchild_name'] = get_top_second_channel_name($value['channel_id'])['second_name'];
        }

        $payArr = model('pay')->where(['status' => 1, 'userid' => ['IN', $memberIdArr]])->group('userid')
            ->column('userid,sum(amount) as money,count(id) as paycount');
        $gameList = $this->selfGameList;
        $gList = array_column($gameList, 'name', 'id');
        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('payArr', $payArr);
        $this->assign('start_time', $start_time);
        $this->assign('end_time', $end_time);
        $this->assign('page', $list->render());
        $this->assign('game_list', $gameList);
        $this->assign('gList', $gList);

        return $this->fetch('frozen_subaccount_list');
    }

    /**
     * 实名认证列表
     */
    public function identityList()
    {
        $MembersTwoModel = new MembersTwo;

        $where = [];

        //用户名
        if (trim(input('request.username')) != '') {
            //   $where['m.username'] = ['like', '%' . input('request.username') . '%'];
            $where['m2.username'] = trim(input('request.username'));
        }

        //渠道名称
        if (trim(input('request.realname')) != '') {
            ///   $where['m2.realname'] = ['like', '%' . input('request.realname') . '%'];
            $where['m2.realname'] = trim(input('request.realname'));
        }

        //游戏名称
        if (trim(input('request.idcard')) != '') {
            //   $where['m2.idcard'] = ['like', '%' . input('request.idcard') . '%'];
            $where['m2.idcard'] = trim(input('request.idcard'));
        }

        //查询参数
        $param = input('get.');

        $list = $MembersTwoModel->table('cy_memberstwo m2')->field('m2.username,m2.realname,m2.idcard,m2.userid as uid, cg.name as cg_name')
            // ->where('m2.userid = m.id and (realname != "") AND (realname != -1) AND (realname IS NOT NULL) AND (idcard != "") AND (idcard != -1) AND (idcard IS NOT NULL)')
            // ->where('m2.userid = m.id and (m2.realname>"-1" AND m2.idcard>"-1")')
            ->join('cy_members cm', 'm2.userid = cm.id','left')
            ->join('cy_game cg', 'cm.gameid = cg.id','left')
            ->where('m2.realname>"-1" AND m2.idcard>"-1"')
            ->where($where)->order('m2.id desc')
            ->paginate(20, false, array('query' => $param));

        $this->assign('list', $list);
        $this->assign('page', $list->render());

        return $this->fetch('identity_list');
    }

    /**
     * 绑定信息管理
     */
    public function bindList()
    {
        $where = $this->_getBindCondition();

        if(!empty($where)){

            //查询参数
            $param = input('get.');

            /*
             $list = $this->membersModel->field('SQL_CALC_FOUND_ROWS id,username,mobile,email,total_pay_amount,gameid')
             ->where('(mobile != "" AND mobile IS NOT NULL) OR (email != "" AND email IS NOT NULL)')
             ->where($where)
             ->order('id desc')->paginate(10, true, array('query' => $param));*/

            $curpage = input('page') ? input('page') : 1;   //当前页码

            $list_data = $this->membersModel->field('SQL_CALC_FOUND_ROWS id,username,mobile,email,total_pay_amount,gameid')
                //  ->where('(mobile != "" AND mobile IS NOT NULL) OR (email != "" AND email IS NOT NULL)')
                ->where('(mobile>"" OR email>"")')
                ->where($where)
                ->order('id desc')->page($curpage,10)->select();

            //总记录数
            $total = Db::query("SELECT FOUND_ROWS() as total;");

            $list = \think\paginator\driver\Bootstrap::make($list_data,10,$curpage,$total[0]['total'],false,[
                'var_page' => 'page',
                'path'     => url('bindList'),//这里根据需要修改url
                'query'    => $param,
                'fragment' => '',
            ]);

            $data      = $list->toArray();
            $gameid    = array_column($data['data'], 'gameid');
            $gameid    = array_unique($gameid);
            $gameList  = model('game')->whereIn('id', $gameid)->column('id,name');

            $this->assign('list', $list);
            $this->assign('gameList', $gameList);
            $this->assign('page', $list->render());
        }

        $game_list = $this->selfGameList;
        $this->assign('game_list', $game_list);

        return $this->fetch('bind_list');
    }

    private function _getParams()
    {
        $where = [];
        //开始时间和结束时间不为空时
        if (input('request.start_time') != '' && input('request.end_time') != '') {
            $where['m.reg_time'] = [
                ['>=', strtotime(input('request.start_time'))],
                ['<=', strtotime(input('request.end_time') . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif (input('request.start_time') != '') {
            $where['m.reg_time'] = ['>=', strtotime(input('request.start_time'))];
        } //结束时间不为空时
        elseif (input('request.end_time') != '') {
            $where['m.reg_time'] = ['<=', strtotime(input('request.end_time') . ' 23:59:59')];
        }


        //渠道名称
        if (input('request.channel_name') != '') {
            $where['d.name'] = ['like', '%' . input('request.channel_name') . '%'];
        }

        //用户名
        if (input('request.username') != '') {
            $where['m.username'] = ['like', '%' . input('request.username') . '%'];
        }

        //imeil
        if (input('request.imeil') != '') {
            $where['m.imeil'] = ['=', input('request.imeil')];
        }

        //游戏名称
        if (input('request.gameid') != '') {
            $where['g.id'] = ['=', input('request.gameid')];
        }
        //手机号
        if (input('request.mobile') != '') {
            $where['mobile'] = ['like', '%' . input('request.mobile') . '%'];
        }

        //邮箱
        if (input('request.email') != '') {
            $where['email'] = ['like', '%' . input('request.email') . '%'];
        }
        return $where;
    }

    /**
     * 绑定信息 查询条件
     * @return array
     */
    protected function _getBindCondition()
    {
        $where = [];

        $mobile           = input('request.mobile');
        $email            = input('request.email');
        $username         = input('request.username');
        $gameid           = input('request.gameid');
        $type             = input('request.type');
        $total_pay_amount = input('request.total_pay_amount');


        if ($username != '') {
            $where['username'] = $username;
        }

        if ($email != '') {
            $where['email'] = $email;
        }

        if ($mobile != '') {
            $where['mobile'] = $mobile;
        }

        //游戏名称
        if ($gameid != '') {
            $where['gameid'] = ['=', $gameid];
        }

        //游戏名称
        if ($total_pay_amount != '') {
            $where['total_pay_amount'] = [$type, $total_pay_amount];
        }

        return $where;
    }

    /**
     * 绑定信息 报表下载
     */
    public function bindListDownload($is_show = ['is_show'=>false])
    {
        if (request()->isAjax()) {
            $where = $this->_getBindCondition();

            if($is_show['is_show']){
                $type             = input('request.type');
                $total_pay_amount = input('request.total_pay_amount');
                $username         = input('request.username', '', 'trim');
                $gameid           = input('request.gameid', '', 'intval');
                if($total_pay_amount){
                    $LogStr = "充值金额：".$type.$total_pay_amount;
                }
                else{
                    $LogStr = "充值金额：";
                }
                if($gameid){
                    $game_name = get_game_nickname($gameid);
                }
                else{
                    $game_name = "全部游戏";
                }
                $LogStr .= "，游戏：".$game_name;
                $LogStr .= "，玩家账号：".$username;
                $this->insertLog($this->current_node,$LogStr,123);
            }

            $sql = $this->membersModel->field('id,username,mobile,email,total_pay_amount,gameid')
                //   ->where('(mobile != "" AND mobile IS NOT NULL) OR (email != "" AND email IS NOT NULL)')
                ->where('(mobile>"" OR email>"" )')
                ->where($where)
                ->order('id desc')
                ->fetchSql(true)
                ->select();

            if ((new MakeReportGo())->addTask('memberBindList', $sql, session_id(),$is_show)){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');

        } else {

            $this->error('非法请求');

        }
    }
    /**
     * 修改绑定信息
     */
    public function updateBind($id)
    {
        $id = (int)$id;

        if (empty($id)) {
            $this->error('用户ID不能为空');
        }

        $memberInfo = $this->membersModel->where(['id' => $id])->find();

        if (empty($memberInfo)) $this->error('用户不存在');

        if (request()->isPost()) {

            $data = [
                'mobile' => input('post.mobile'),
                'email'  => input('post.email'),
            ];

            $result = $this->validate($data, [
                ['mobile', 'mobile', '手机号码格式不正确'],
                ['email', 'email', '邮箱格式不正确'],
            ]);


            if (true !== $result) {

                $this->error($result);
            } else {
                $now_time = time();

                $data['update_time'] = $now_time;

                if ($this->membersModel->update($data, ['id' => $id])) {

                    if ( $data['mobile'] != $memberInfo['mobile']) {
                        // 插入玩家历史记录
                        Db::table('cy_member_history')->insert([
                            'userid'      => $id,
                            'mobile'      => $data['mobile'],
                            'ip'          => request()->ip(),
                            'create_time' => $now_time,
                            'admin_id'    => session('ADMIN_ID')
                        ]);
                    }

                    if ( $data['email'] != $memberInfo['email']) {
                        // 插入玩家历史记录
                        Db::table('cy_member_history')->insert([
                            'userid'      => $id,
                            'email'       => $data['email'],
                            'ip'          => request()->ip(),
                            'create_time' => $now_time,
                            'admin_id'    => session('ADMIN_ID')
                        ]);
                    }


                    $msg = "修改玩家：{$memberInfo['username']}的手机号：{$data['mobile']}，邮箱:{$data['email']}";
                    $this->insertLog($this->current_node, $msg, 23);
                    $this->success('绑定信息修改成功', 'Member/bindList');
                } else
                    $this->error('绑定信息修改失败');
            }
        }

        $this->assign('memberInfo', $memberInfo);

        return $this->fetch('update_bind');
    }

    /**
     * 修改密码
     */
    public function updatePassword($id,$type = 1)
    {
        $id = (int)$id;

        $type = (int)$type;

        if (empty($id)) {
            $this->error('用户ID不能为空');
        }

        $memberInfo = $this->membersModel->where(['id' => $id])->find();

        if (empty($memberInfo)) $this->error('用户不存在');

        if (request()->isPost()) {

            $password         = input('post.password');
            $confirm_password = input('post.confirm_password');

            $data = [
                'password'         => $password,
                'confirm_password' => $confirm_password,
            ];

            $result = $this->validate($data, [
                ['password', 'require|length:6,15', '请输入密码|密码长度为 6 - 15'],
                ['confirm_password', 'require|length:6,15', '请输入确认密码|密码长度为 6 - 15'],
            ]);


            if (true !== $result) {

                $this->error($result);
            } elseif ($password != $confirm_password) {
                $this->error('密码不一致');
            } else {
                $now_time = time();

                unset($data['confirm_password']);

                $data['password']    = auth_code($password, "ENCODE", Env::get('auth_key'));
                $data['update_time'] = $now_time;

                if ($this->membersModel->update($data, ['id' => $id])) {
                    // 插入玩家历史记录
                    Db::table('cy_member_history')->insert([
                        'userid'      => $id,
                        'password'    => $data['password'],
                        'ip'          => request()->ip(),
                        'create_time' => $now_time,
                        'admin_id'    => session('ADMIN_ID')
                    ]);

                    $msg = "修改玩家：{$memberInfo['username']}的密码";
                    $this->insertLog($this->current_node, $msg, 21);
                    //                    $memberLogic = new MemberLogic;
                    //
                    //                    $memberLogic->syncMember($memberInfo['username'], $password, $memberInfo['mobile'], $now_time);
                    if ($type == 1) {
                        $this->success('密码修改成功', 'Member/index');
                    }else{
                        $this->success('密码修改成功', 'Member/information');
                    }
                } else
                    $this->error('密码修改失败');
            }
        }

        $this->assign('memberInfo', $memberInfo);

        return $this->fetch('update_password');
    }

    /**
     * 游戏-账号-角色 查询
     */
    public function query()
    {
        $condition = [];
        if (!empty(input('gh_status'))) { // 是否聚合用户：0=否、1=是
            $condition['channel.gh_status'] = 1;

            // TODO: 设置抓取频率为一分钟10次。
            $redis = Cache::init()->handler();
            $cache_keys = 'member_query:' . getUserIp();
            $num = $redis->get($cache_keys);
            if (!$num) {
                $num = 1;
                $redis->setex($cache_keys, 60, 1);
            } else {
                $num = $num + 1;
                $redis->Incrby($cache_keys, 1);
            }
            if ($num > 10) {
                return "访问次数受限，一分钟内请求为十次：" . $num;
            }
        }

        $gameId = input('game_id', 0, 'intval');
        $username = input('username', '', 'trim');
        $rolename = input('rolename', '', 'trim');
        $start_time = input('start_time', '', 'trim');
        $end_time = input('end_time', '', 'trim');
        $page_size = input('page_size', 15);

        !empty($gameId) && $condition['info.gameid'] = $gameId;
        // !empty($username) && $condition['user.username'] = ['LIKE', '%' . $username . '%'];
        if (!empty($username)) {
            $userIds = Db::table('cy_members')->where('username', $username)->column('id');
            $condition['info.userid'] = ['in', $userIds];
        }
        !empty($rolename) && $condition['rolename'] = $rolename;

        // 推广数据
        $business_type = input('business_type', '');
        $president_type = input('president_type', '');
        $president_type_son = input('president_type_son', '');
        $promoter_type = input('promoter_type', '');
        if ($business_type <> '') {
            $condition['info.channel_id'] = ['in', get_child_channel_arr($business_type)];
        }
        if ($president_type <> '') {
            $condition['info.channel_id'] = ['in', get_child_channel_arr($president_type)];
        }
        if ($president_type_son <> '') {
            $condition['info.channel_id'] = ['in', get_child_channel_arr($president_type_son)];
        }
        if ($promoter_type <> '') {
            $condition['info.channel_id'] = $promoter_type;
        }

        // 获取查询日期
        if ($start_time && $end_time) {
            $condition['info.create_time'] = ['between time', [strtotime($start_time), strtotime($end_time . ' 23:59:59')]];
        }

        $infoList = Db::table('cy_role_info')->alias('info')
            ->join('nw_channel channel', 'channel.id = info.channel_id')
            ->join('cy_members user', 'user.id = info.userid')
            ->join('cy_game game', 'game.id = info.gameid')
            ->join('nw_subaccount sub', 'info.userid = sub.member_id AND info.gameid = sub.game_id')
            ->field(['info.id', 'game.name as game_name', 'user.username', 'info.serverid', 'info.servername', 'info.roleid', 'info.rolename', 'info.create_time', 'info.update_time', 'sub.channel_id', 'info.userid', 'info.rolelevel'])
            ->where($condition)
            ->order('update_time desc')
            ->group('info.roleid')
            ->paginate($page_size, 5000, ['query' => input('get.')])
            ->each(function ($item, $key) {
                $channel_list = get_channel_top_by_aggregate($item['channel_id']);
                $item['channel_zero_name'] = !empty($channel_list['0']['name']) ? $channel_list['0']['name'] : '';
                $item['channel_one_name'] = !empty($channel_list['1']['name']) ? $channel_list['1']['name'] : '';
                $item['channel_two_name'] = !empty($channel_list['2']['name']) ? $channel_list['2']['name'] : '';
                $item['channel_three_name'] = !empty($channel_list['3']['name']) ? $channel_list['3']['name'] : '';
                return $item;
            });

        $this->assign('list', $infoList);
        $this->assign('page', $infoList ? $infoList->render() : '');
        $this->assign('game_list', $this->selfGameList);
        $this->assign('list_count', count($infoList));

        $this->assign('jh_url', url('Member/query', ['gh_status' => 1, 'page_size' => '100'], 'html'));
        return $this->fetch();
    }

    /**
     * 游戏-账号-角色 列表
     */
    public function roleList()
    {
        $infoList = [];
        $action = $this->request->param('action');

        if (0 == strnatcasecmp($action, 'search')) {
            $gameId = $this->request->param('game_id', 0, 'intval');
            $username = $this->request->param('username', '', 'trim');
            $rolename = $this->request->param('rolename', '', 'trim');
            $condition = [];

            if (empty($gameId)) {
                $this->error('请选择游戏');
            } else {
                $condition['game.id'] = $gameId;
            }
            /*
            if (empty($username) && empty($rolename)) {
                $this->error('用户名和角色名必填一个');
            }
            */

            //            !empty($username) && $condition['user.username'] = ['LIKE', '%' . $username . '%'];
            !empty($username) && $condition['user.username'] = $username;
            !empty($rolename) && $condition['info.rolename'] = $rolename;

        } else {
            $condition['info.create_time'] = ['between time', [strtotime(date('Y-m-d')), strtotime(date('Y-m-d 23:59:59'))]];
        }
        $infoList = (new MemberService())->accountQuery($condition); // 账号查询

        $this->assign('list', $infoList);
        $this->assign('page', $infoList ? $infoList->render() : '');
        $this->assign('game_list', $this->selfGameList);
        $this->assign('list_count', count($infoList));

        return $this->fetch();
    }

    // 批量冻结
    public function multiFrozen()
    {
        $ids = $this->request->param('ids', '', 'trim');

        if (empty($ids)) {
            $this->error('请先选择账号');
        }

        $idArr = explode(',', $ids);

        $result = $this->membersModel->update(['flag' => 1], ['id' => ['IN', $idArr]]);

        if ($result) {
            $this->success('冻结成功');
        } else {
            $this->error('冻结失败');
        }
    }
    // 批量解冻
    public function multiUnFrozen()
    {
        $ids = $this->request->param('ids', '', 'trim');

        if (empty($ids)) {
            $this->error('请先选择账号');
        }

        $idArr = explode(',', $ids);

        $result = $this->membersModel->update(['flag' => 0], ['id' => ['IN', $idArr]]);

        if ($result) {
            $this->success('解冻成功');
        } else {
            $this->error('解冻失败');
        }
    }

    // 子账户-批量冻结
    public function multiSubaccountFrozen()
    {
        $ids = $this->request->param('ids', '', 'trim');

        if (empty($ids)) {
            $this->error('请先选择账号');
        }

        $idArr = explode(',', $ids);
        $nw_subaccount = new Subaccount();
        $result = $nw_subaccount->update(['flag' => 1, 'update_time' => time()], ['id' => ['IN', $idArr]]);

        if ($result) {
            $this->success('冻结成功');
        } else {
            $this->error('冻结失败');
        }
    }

    // 子账户-批量解冻
    public function multiUnSubaccountFrozen()
    {
        $ids = $this->request->param('ids', '', 'trim');

        if (empty($ids)) {
            $this->error('请先选择账号');
        }

        $idArr = explode(',', $ids);
        $nw_subaccount = new Subaccount();
        $result = $nw_subaccount->update(['flag' => 0, 'update_time' => time()], ['id' => ['IN', $idArr]]);

        if ($result) {
            $this->success('解冻成功');
        } else {
            $this->error('解冻失败');
        }
    }

    // 注册单用户
    public function register()
    {
        if ($this->request->isPost()) {
            $username = strtolower($this->request->param('username', '', 'trim'));
            $password = $this->request->param('password', '', 'trim');
            $channelId = $this->request->param('channel_id', 0, 'intval');
            $gameId = $this->request->param('game_id', 0, 'intval');

            $data = [
                'username' => $username,
                'password' => $password,
                'channel_id' => $channelId,
                'gameid' => $gameId,
            ];

            $validResult = $this->validate($data, 'admin/Member');

            if (true !== $validResult) {
                $this->error($validResult);
            }

            $channelInfo = model('Channel')->where(["id"=>$channelId])->find();
            if($channelInfo['level'] <> 3){
                $this->error('非推广员渠道,不能进行绑定');
            }

            if($gameId && $channelId){
                //是否禁止注册
                if (isFrozenOption($channelId,$gameId,'register') || isFrozenOption($channelId,$gameId)) {
                    $this->error('您所在渠道已被禁止注册此游戏');
                }
            }

            if(!$gameId){
                // 没有选择绑定游戏
                $this->error('请选择绑定游戏');
            }
            if(!$channelId){
                // 没有选择绑定渠道
                $this->error('请选择绑定渠道');
            }

            // 判断该游戏是否处于白名单状态，若是，则判断是否有分包记录，没有的话，禁止注册关联
            if(!empty($gameId)) {
                $game_info  = model('game')->where(['id' => $gameId])->find();
                // 处于白名单状态
                if(2 == $game_info['cooperation_status']) {
                    $conditions = array();
                    $conditions['gameid'] = $gameId;
                    $conditions['channel_id'] = $channelId;
                    $subPackageCnt = model('SdkGameList')->where($conditions)->count();
                    if(!$subPackageCnt){
                        $this->error('该游戏处于白名单状态，无法新增');
                    }
                }
            }

            $result = (new MemberService())->registerUser($data);

            if (true === $result) {
                $this->success('注册成功');
            } else {
                $this->error($result);
            }
        }

        $channelList = (new Channel)->getAllByCondition('id,name', ['status' => 1, 'flag' => 3, 'level' => 3]);
        // 只能筛选上架和白名单的游戏
        $gameList    = (new Game())->getAllByCondition('id,name', ['isdelete' => 0, 'cooperation_status' =>['in','1,2']],'','self');
        $selfGameList = array();
        foreach ($gameList as $game) {
            $selfGameList[ $game['id']] = $game;
        }
        $this->assign('game_list', $selfGameList);
        $this->assign('channel_list', $channelList);
        return $this->fetch();
    }

    // 批量注册
    public function multiRegister()
    {
        $channelId = $this->request->param('channel_id', 0, 'intval');
        $gameId    = $this->request->param('game_id', 0, 'intval');
        $filename   = $this->request->param('tmpname');

        if (empty($filename)) {
            $this->error('请上传txt文件');
        }

        if (empty($channelId)) {
            $this->error('请选择绑定渠道');
        }

        if (empty($gameId)) {
            $this->error('请选择绑定游戏');
        }

        try{
            $users = file(STATIC_DOMAIN.$filename);
        }
        catch (\Exception $e){

            $this->error('批量注册失败，'.$e->getMessage());
        }

        $channelInfo = model('Channel')->where(["id"=>$channelId])->find();
        if($channelInfo['level'] <> 3){
            $this->error('非推广员渠道,不能进行绑定');
        }

        if($gameId && $channelId){
            //是否禁止注册
            if (isFrozenOption($channelId,$gameId,'register') || isFrozenOption($channelId,$gameId)) {
                $this->error('您所在渠道已被禁止注册此游戏');
            }
        }

        // 判断该游戏是否处于白名单状态，若是，则判断是否有分包记录，没有的话，禁止注册关联
        if(!empty($gameId)) {
            $game_info  = model('game')->where(['id' => $gameId])->find();
            // 处于白名单状态
            if(2 == $game_info['cooperation_status']) {
                $conditions = array();
                $conditions['gameid'] = $gameId;
                $conditions['channel_id'] = $channelId;
                $subPackageCnt = model('SdkGameList')->where($conditions)->count();
                if(!$subPackageCnt){
                    $this->error('该游戏处于白名单状态，无法新增');
                }
            }
        }

        $memberService = new MemberService();
        $checkResult = $memberService->checkContent($users); // 重新检测内容

        if (true !== $checkResult) {
            $this->error($checkResult);
        }

        $result = [];

        foreach ($users as $user) {
            list($username, $password) = explode(':', trim($user));
            $data = [
                'username'   => strtolower(trim($username)),
                'password'   => trim($password),
                'channel_id' => $channelId,
                'gameid'     => $gameId
            ];

            $validResult = $this->validate($data, 'admin/Member');

            // 注册失败的
            if (true !== $validResult) {
                $result[] = ['username' => $username, 'reason' => $validResult, 'status' => 0];
            } else {

                // 注册用户
                $flag = $memberService->registerUser($data);

                if (true === $flag) {
                    $result[] = ['username' => $username, 'reason' => '', 'status' => 1];
                } else {
                    $result[] = ['username' => $username, 'reason' => $flag, 'status' => 0];
                }
            }
        }

        (new FileUpload())->deleteFile($filename); // 删除远程txt

        $this->success('批量注册提交成功,具体注册结果可查看返回信息', null, $result);
    }

    /**
     * 上传txt文件
     */
    public function uploadFile()
    {
        $file = request()->file('file');

        $result = (new MemberService())->uploadFile($file);

        if (is_array($result)) {
            $this->success('文件上传成功', null, $result);
        } else {
            $this->error($result);
        }
    }

    // 首页 ajax加载渠道，避免渠道太多，页面渲染太慢
    public function ajaxGetChannel()
    {
        $channel_list = model('Common/Channel')->getAllByCondition('id,name',['status' => 1,'flag'=>['in','3,4']]);

        $this->success('', '', $channel_list);
    }

    /**
     * 注册用户管理--账号明文查看操作
     */
    public function indexPassword(){
        $channelId = $this->request->param('id', 0, 'intval');
        if (empty($channelId)){
            $this->result('',0,'参数错误！');
        }
        $userName = $this->membersModel->where(['id'=>$channelId])->value('username');
        // 记录操作日志
        $this->insertLog($this->current_node,'查看账号：'.$userName,25);

        $this->result($userName,1,$userName);
    }

    /**
     * 登录用户列表--账号明文查看操作
     */
    public function loginPassword(){
        $logininfoId = $this->request->param('id', 0, 'intval');
        if (empty($logininfoId)){
            $this->result('',0,'参数错误！');
        }
        $info = Db::table('cy_logininfo')->alias('l')
            ->join('cy_members m', 'l.userid = m.id')
            ->join('cy_game c', 'l.gameid = c.id','left')
            ->field("m.username,c.name")
            ->where(['l.id'=>$logininfoId])
            ->find();

        // 记录操作日志
        $this->insertLog($this->current_node,'查看账号：'.$info['username'].'，关联游戏：'.$info['name'],26);
        $this->result($info['username'],1,$info['username']);

    }

    /**
     * 绑定信息管理--明文查看操作
     */
    public function bindPassword(){
        $Id = $this->request->param('id' , 0 , 'intval');
        $file = $this->request->param('file' , '' , 'trim');

        if (empty($Id) || empty($file)){
            $this->result('',0,'参数错误！');
        }
        $file = trim($file);
        if ($file == 'username'){
            $action = '账号';
        }elseif ($file == 'mobile'){
            $action = '手机号';
        }elseif($file == 'email'){
            $action = '邮箱';
        }else{
            $action = '密文';
        }

        if ( $file == 'username') {
            $field = ["a.$file","c.name"];
        }else{
            $field = ["a.$file","c.name","a.username"];
        }

        /*dump($file);*/
        $info = $this->membersModel->alias('a')->join('cy_game c', 'a.gameid = c.id','left')->where(['a.id'=>$Id])->field($field)->find();
        $logStr = '';
        if (empty($info['name'])){
            $logStr = '查看'.$action.'：'.$info[$file];
        }else{
            $logStr = '查看'.$action.'：'.$info[$file].'，关联游戏：'.$info['name'];
        }

        if( $file != 'username') $logStr .= '，关联账号：' .$info['username'];

        // 记录操作日志
        $this->insertLog($this->current_node,$logStr,27);
        $this->result($info[$file],1,$info[$file]);
    }

    /**
     * 实名认证--账号明文查看操作
     */
    public function identityPassword(){
        $Id = $this->request->param('id', 0, 'intval');
        $file = $this->request->param('file' , '' , 'trim');
        if (empty($Id) || empty($file)){
            $this->result('',0,'参数错误！');
        }
        $file = trim($file);
        if ($file == 'username'){
            $action = '账号';
            $info = $this->membersModel->where(['id'=>$Id])->value('username');
        }elseif ($file == 'idcard'){
            $action = '身份证';
            $info = model('MembersTwo')->where(['userid'=>$Id])->value('idcard');
        }else{
            $this->result('',0,'参数错误！');
        }

        // 记录操作日志
        $this->insertLog($this->current_node,'查看'.$action.'：'.$info,28);
        $this->result($info,1,$info);

    }


    /**
     * 冻结用户列表--账号明文查看操作
     */
    public function frozenUsername(){
        $id = $this->request->param('userid', 0, 'intval');
        if (empty($id)){
            $this->result('',0,'玩家ID参数错误！');
        }
        $userName = $this->membersModel->where(['id'=>$id])->value('username');
        // 记录操作日志
        $this->insertLog($this->current_node,'查看账号：'.$userName,29);

        $this->result($userName,1);
    }

    /**
     * 用户汇总信息--明文查看操作
     */
    public function infomationPassword(){
        $Id = $this->request->param('id' , 0 , 'intval');
        $file = $this->request->param('file' , '' , 'trim');
        if (empty($Id) || empty($file)){
            $this->result('',0,'参数错误！');
        }
        $file = trim($file);
        if ($file == 'username'){
            $action = '账号';
            $info = $this->membersModel->where(['id'=>$Id])->value('username');
        }elseif ($file == 'idcard'){
            $action = '身份证';
            $info = model('MembersTwo')->where(['userid'=>$Id])->value('idcard');
        }elseif ($file == 'email'){
            $action = '邮箱';
            $info = $this->membersModel->where(['id'=>$Id])->value('email');
        }elseif ($file == 'mobile'){
            $action = '手机号';
            $info = $this->membersModel->where(['id'=>$Id])->value('mobile');
        }else{
            $this->result('',0,'参数错误！');
        }

        // 记录操作日志
        $this->insertLog($this->current_node,'查看'.$action.'：'.$info,124);
        $this->result($info,1,$info);
    }

    /**
     * 登录列表下载--无加密
     */
    public function loginDowmExcel(){
        $this->loginlistdownload(['is_show'=>true]);
    }

    /**
     * 注册管理页下载--无加密
     */
    public function registDowmExcel(){
        $this->download(['is_show'=>true]);
    }

    /**
     * 绑定信息页下载--无加密
     */
    public function bindDowmExcel(){
        $this->bindListDownload(['is_show'=>true]);
    }

    /**
     * 用户信息汇总
     */
    public function information()
    {
        $this->informationWhere(2);
        $param = input('get.');

        $list  = Db::table('cy_members')->alias('m')
            ->join('nw_channel channel', 'channel.id=m.channel_id', 'left')
            ->join('nw_channel bchild', 'bchild.id=channel.parent_id AND bchild.level = 2', 'left')
            ->field('m.id,m.username,m.nickname,m.channel_id,m.flag,m.gameid,m.reg_time,m.ip,m.imeil,m.total_pay_amount,m.login_time,bchild.name as bchild_name,bchild.parent_id as bcparent_id')
            ->where($this->where)->order('reg_time desc')
            ->paginate(10, false, array('query' => $param));

        /*        $list  = $this->membersModel
                    ->field('id,username,nickname,channel_id,flag,gameid,reg_time,ip,imeil,total_pay_amount,login_time')
                    ->where($this->where)->order('reg_time desc')
                    ->paginate(10, false, array('query' => $param));*/

        $gameList = $this->selfGameList;
        $gList = $gameList;
        $gameList = array_column($gameList, 'name', 'id');
        $data     = $list->toArray()['data'];
        $memberIdArr = array();
        foreach ($data as &$v) {
            $memberIdArr[] = $v['id'];

            $v['top_channel_name'] = get_top_channel($v['channel_id'])['name'];
            $v['dep_name'] = get_channel_name($v['channel_id']);
            $v['union_name'] = get_union_channel($v['channel_id'])['name'];
            // 账号加密
            $v['mdusername'] = stringObfuscation($v['username'],3);
        }

        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('page', $list->render());
        $this->assign('game_list', $gameList);
        $this->assign('glist', $gList);

        $uniom = model('Channel')->where(['level'=>0])->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表

        return $this->fetch();
    }

    // 用户信息汇总查询条件
    public function informationWhere($isdefault)
    {
        $where = [];
        $id               = input('request.id');
        $mobile           = input('request.mobile');
        $email            = input('request.email');
        $nickname         = input('request.nickname');
        $ip               = input('request.ip');
        $flag             = input('request.flag');
        $start_time       = input('request.start_time');
        $end_time         = input('request.end_time');
        $channel_id       = input('request.channel_id');
        $username         = input('request.username');
        $gameid           = input('request.gameid');
        $top_channel_id   = input('request.parent_channel_id');
        $bplus_channel_id = input('bplus_channel_id', 0, 'intval');

        // 获取查询日期
        $where['reg_time'] =$this->getTimeCondition($start_time,$end_time,$isdefault);

        /*
        if ($username == '' && $nickname == '' && $id == ''){
            if(strtotime($this->end_time.' 23:59:59')-strtotime($this->start_time)>31*24*3600)
            {
                $this->error('单次查询日期的最长跨度为31天');
            }
        }
        */

        //渠道名称
        if ( !empty($channel_id) || !empty($top_channel_id) || !empty($bplus_channel_id)) {
            $where['channel_id'] = $this->getChannelCondition($channel_id,$top_channel_id,$bplus_channel_id);
        }

        //用户ID
        if ($id != '') {
            $where['m.id'] = $id;
        }
        //用户昵称
        if ($nickname != '') {
            $where['nickname'] = $nickname;
        }

        //用户名
        if ($username != '') {
            $where['username'] = $username;
        }
        //游戏名称
        if ($gameid != '') {
            $where['gameid'] = ['=', $gameid];
        }

        //手机号
        if ($mobile != '') {
            $where['mobile'] = $mobile;
        }
        //邮箱
        if ($email != '') {
            $where['email'] = $email;
        }

        //注册IP
        if ($ip != '') {
            $where['ip'] = $ip;
        }
        //账号状态
        if ($flag != '') {
            $where['flag'] = $flag;
        }

        $this->where      = $where;

    }
    // 账户-批量冻结
    public function batchMemberFreeze()
    {
        $ids = $this->request->param('ids', '', 'trim');
        
        if (empty($ids)) {
            $this->error('请先选择账号');
        }
        
        $idArr = explode(',', $ids);
        $nw_subaccount = new MembersModel();
        $result = $nw_subaccount->update(['flag' => 1, 'update_time' => time()], ['id' => ['IN', $idArr]]);
        
        if ($result) {
            $this->success('冻结成功');
        } else {
            $this->error('冻结失败');
        }
    }
    // 账户-批量解冻
    public function batchMemberThaw()
    {
        $ids = $this->request->param('ids', '', 'trim');
        
        if (empty($ids)) {
            $this->error('请先选择账号');
        }
        
        $idArr = explode(',', $ids);
        $nw_subaccount = new MembersModel();
        $result = $nw_subaccount->update(['flag' => 0, 'update_time' => time()], ['id' => ['IN', $idArr]]);
        
        if ($result) {
            $this->success('解冻成功');
        } else {
            $this->error('解冻失败');
        }
    }
    
    

    /**
     * 获取渠道查询条件
     * @param  int  $channel_id     渠道ID
     * @param  int  $top_channel_id 顶级渠道ID
     * @return
     */
    private function getChannelCondition($channel_id,$top_channel_id,$bplus_channel_id=0)
    {

        //联盟/公会/渠道有选择时判断
        if(!empty($channel_id) || !empty($top_channel_id) || !empty($bplus_channel_id)){
            if(!empty($channel_id) && !empty($top_channel_id) && !empty($bplus_channel_id)){
                unset($condition['p.channel_id']);

                $bChannelIds = get_child_channel_arr($top_channel_id);
                array_push($bChannelIds,$top_channel_id);

                $bplusChannelIds = get_child_channel_arr($bplus_channel_id);
                array_push($bplusChannelIds,$bplus_channel_id);

                $channelIds = array_intersect($bChannelIds,$bplusChannelIds,array($channel_id));

                if($channelIds){
                    $channel = ['in',$channelIds];
                }
                else{
                    $channel = -1;
                }
            } else if(!empty($top_channel_id) && !empty($bplus_channel_id)){

                $bChannelIds = get_child_channel_arr($top_channel_id);
                array_push($bChannelIds,$top_channel_id);

                $bplusChannelIds = get_child_channel_arr($bplus_channel_id);
                array_push($bplusChannelIds,$bplus_channel_id);

                $channelIds = array_intersect($bChannelIds,$bplusChannelIds);

                if($channelIds){
                    $channel = ['in',$channelIds];
                }
                else{
                    $channel = -1;
                }
            } elseif (!empty($channel_id) && !empty($bplus_channel_id)) {

                $bplusChannelIds = get_child_channel_arr($bplus_channel_id);
                array_push($bplusChannelIds,$bplus_channel_id);

                $channelIds = array_intersect($bplusChannelIds,array($channel_id));

                if($channelIds){
                    $channel = ['in',$channelIds];
                }
                else{
                    $channel = -1;
                }
            } elseif (!empty($channel_id) && !empty($top_channel_id)) {

                $bChannelIds = get_child_channel_arr($top_channel_id);
                array_push($bChannelIds,$top_channel_id);

                $channelIds = array_intersect($bChannelIds,array($channel_id));

                if($channelIds){
                    $channel = ['in',$channelIds];
                }
                else{
                    $channel = -1;
                }
            } elseif (!empty($top_channel_id)) {

                $channelIds = get_child_channel_arr($top_channel_id);
                array_push($channelIds,$top_channel_id);

                $channel = ['in',$channelIds];
            } elseif (!empty($bplus_channel_id)) {

                $channelIds = get_child_channel_arr($bplus_channel_id);
                array_push($channelIds,$bplus_channel_id);

                $channel = ['in',$channelIds];
            } elseif (!empty($channel_id)) {
                $channel = (int)$channel_id;
            }
        }

        /*
        if(!empty($channel_id) && !empty($top_channel_id)){

            $channelIds = get_child_channel_arr($top_channel_id);
            array_push($channelIds,$top_channel_id);

            $channel = [
                ['=', (int)$channel_id],
                ['in', $channelIds],
            ];
        }
        elseif (!empty($channel_id)) {
            $top_channel_id          = 0; // 如果同时输入渠道名称和顶级渠道，出于性能考虑，只查询渠道名称这个条件
            $channel = (int)$channel_id;
        }
        //顶级渠道存到pay表中 存在渠道改变时候 需要定期刷表的情况后续开发
        elseif (!empty($top_channel_id)) {
            unset($this->where['channel_id']); // 如果同时输入渠道名称和顶级渠道，出于性能考虑，只查询渠道名称这个条件

            $channelIds = get_child_channel_arr($top_channel_id);
            array_push($channelIds,$top_channel_id);

            $channel = ['in',$channelIds];
        }
        */

        return $channel;
    }

    /**
     * 获取日期查询条件
     * @param  int  $start_time  开始时间
     * @param  int  $end_time    结束时间
     * @param  bool  $isdefault    是否默认今天日期
     * @return
     */
    private function getTimeCondition($start_time,$end_time,$isdefault = true)
    {
        $this->start_time = $start_time;
        $this->end_time   = $end_time;
        $time = [];
        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $time = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $time = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $time = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {

            if($isdefault === 2){
                $this->start_time = $this->end_time = '';
                $time = [
                    ['>=', strtotime($this->start_time)],
                    ['<=', strtotime($this->end_time . ' 23:59:59')],
                ];
            }else if($isdefault){
                $this->start_time = $this->end_time = date('Y-m-d', time());
                $time = [
                    ['>=', strtotime($this->start_time)],
                    ['<=', strtotime($this->end_time . ' 23:59:59')],
                ];
            }

        }

        return $time;
    }

    // 获取用户账号详情
    public function userInfo()
    {
        $id = $this->request->param('id', '', 'intval');

        $code = 1;
        // 获取用户信息
        $info = $this->membersModel->where('id',$id)
            ->field('id,username,avatar,mobile,email,nickname,channel_id,flag,gameid,reg_time,ip,imeil,total_pay_amount,login_time')
            ->find();

        if (empty($info)) {
            $code = 0;
        }else{
            $info['username'] = stringObfuscation($info['username'],3);
            $info['top_channel_name'] = get_top_channel($info['channel_id'])['name'] ?: '';
            $info['dep_name'] = get_channel_name($info['channel_id']) ?: '';
            $userPayInfo = model('Pay')->where(['status' => 1, 'userid' => $id])->group('userid')->field(['userid', 'sum(amount) as money','count(id) as paycount'])->find();
            $info['total_pay_amount'] = isset($userPayInfo->money) ? $userPayInfo->money : '';
            $info['flag'] = $info['flag'] ? '冻结':'正常';
            $info['reg_time'] = $info['reg_time'] ? date('Y-m-d H:i:s',$info['reg_time']) : '';
            $info['imeil'] = $info['imeil'] ?: '';
            $info['nickname'] = $info['nickname'] ?: '';
            $info['gamename'] = $info['gameid'] ? model('Game')->getName($info['gameid']) : '';
            $info['mobile'] = $info['mobile'] ? mobileObfuscation($info['mobile']) : '';
            $info['email'] = $info['email'] ? mailObfuscation($info['email']) : '';
            // 获取用户实名认证信息
            $userInfo = (new MembersTwo())->where('userid',$id)->field('realname,idcard,sex,qq,birthday,address,zipcode')->find();
            $info['realname'] = $userInfo['realname'] ?: '';
            $info['idcard'] = $userInfo['idcard'] ? idcardObfuscation($userInfo['idcard']) : '';
            $info['sex'] = $userInfo['sex'] == 1 ? '女': $userInfo['sex'] == 2 ? '男' : '保密';
            $info['birthday'] = $userInfo['birthday'] ?: '';
            $info['address'] = $userInfo['address'] ?: '';
            $info['zipcode'] = $userInfo['zipcode'] ?: '';
            $info['qq'] = $userInfo['qq'] ?: '';
            $info['avatar'] = $info['avatar'] ? STATIC_DOMAIN . $info['avatar'] : '';
            // 获取最后一次登录信息
            $loginInfo = model('Logininfo')->where('userid',$id)->field('imeil,login_time,ip')->order('login_time desc')->find();
            $info['last_imeil'] = $loginInfo['imeil'] ? : '';
            $info['last_ip'] = $loginInfo['ip'] ? : '';
            $info['last_login_time'] = $loginInfo['login_time'] ? date('Y-m-d H:i:s',$loginInfo['login_time']) : '';

        }


        echo json_encode(['code'=>$code,'data'=>$info]);

    }

    /**
     * 用户冻结详情
     */
    public function frozenInfo($ids)
    {
        $param = input('get.');

        $where['userid'] = (int)$ids;

        $admin_id         = input('request.admin_id');
        $flag             = input('request.flag');
        $start_time       = input('request.start_time');
        $end_time         = input('request.end_time');

        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)){
            $where['create_time'] =$this->getTimeCondition($start_time,$end_time,false);
        }

        //操作人
        if ($admin_id != '') {
            $where['admin_id'] = $admin_id;
        }
        //冻结状态
        if ($flag != '') {
            $where['status'] = $flag;
        }

        $list  = model('FrozenHistory')
            ->where($where)->order('id desc')
            ->paginate(10, false, array('query' => $param))->each(function($item, $key){
                if ( !$item['admin_id']) {
                    $item['admin_name'] = '申诉冻结';
                }else{
                    $item['admin_name'] =model('admin')->where('id',$item['admin_id'])->value('username');
                }
                return $item;
            });

        $aList = model('Common/Admin')->field('id,username as name')->where(['type'=> 1 ,'status'=> 1])->order('id')->select();

        $adminList = array_merge([['id'=>0,'name'=>'申诉冻结']] ,$aList);


        $this->assign('list', $list);
        $this->assign('total', $list->total());     //总条数
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('page', $list->render());
        $this->assign('adminList',$adminList);

        return $this->fetch();
    }

    public function membersHistory($ids)
    {
        $param = input('get.');

        $where['userid'] = (int)$ids;

        $type             = input('request.type');
        $start_time       = input('request.start_time');
        $end_time         = input('request.end_time');

        // 获取查询日期
        $resWhere = $this->getTimeCondition($start_time,$end_time,false);
        if (!empty($resWhere)){
            $where['h.create_time'] = $resWhere;
        }


        //冻结状态
        if ($type != '') {
            if ( $type == 0 ) $where['h.channel'] = ['neq',''];
            if ( $type == 1 ) $where['h.mobile'] = ['neq',''];
            if ( $type == 2 ) $where['h.email'] = ['neq',''];
            if ( $type == 3 ) $where['h.password'] = ['neq',''];
        }

        $list  = model('MemberHistory')->alias('h')
            ->join('cy_members m','m.id = h.userid')
            ->field('h.*,m.username')
            ->where($where)->order('id desc')
            ->paginate(10, false, array('query' => $param))->each(function($item, $key){
                if ( !$item['admin_id']) {
                    $item['admin_name'] = $item['username'];
                }else{
                    $item['admin_name'] =model('admin')->where('id',$item['admin_id'])->value('username');
                }

                if ( $item['password']) {
                    $item['type'] = '密码';
                    $item['text'] = passwordObfuscation($item['password']);
                }elseif( $item['email']){
                    $item['type'] = '邮箱';
                    $item['text'] = $item['email'];
                }elseif( $item['mobile']){
                    $item['type'] = '手机';
                    $item['text'] = $item['mobile'];
                }else{
                    $item['type'] = '渠道';
                    $item['text'] = $item['channel'];
                }

                return $item;
            });


        $this->assign('list', $list);
        $this->assign('total', $list->total());     //总条数
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('page', $list->render());

        return $this->fetch();
    }

    /**
     * 用户信息汇总修改密码
     */
    public function updateInformationPassword($id)
    {
        $id = (int)$id;


        if (empty($id)) {
            $this->error('用户ID不能为空');
        }

        $memberInfo = $this->membersModel->where(['id' => $id])->find();

        if (empty($memberInfo)) $this->error('用户不存在');

        if (request()->isPost()) {

            $password         = input('post.password');
            $confirm_password = input('post.confirm_password');

            $data = [
                'password'         => $password,
                'confirm_password' => $confirm_password,
            ];

            $result = $this->validate($data, [
                ['password', 'require|length:6,15', '请输入密码|密码长度为 6 - 15'],
                ['confirm_password', 'require|length:6,15', '请输入确认密码|密码长度为 6 - 15'],
            ]);


            if (true !== $result) {

                $this->error($result);
            } elseif ($password != $confirm_password) {
                $this->error('密码不一致');
            } else {
                $now_time = time();

                unset($data['confirm_password']);

                $data['password']    = auth_code($password, "ENCODE", Env::get('auth_key'));
                $data['update_time'] = $now_time;

                if ($this->membersModel->update($data, ['id' => $id])) {
                    // 插入玩家历史记录
                    Db::table('cy_member_history')->insert([
                        'userid'      => $id,
                        'password'    => $data['password'],
                        'ip'          => request()->ip(),
                        'create_time' => $now_time,
                        'admin_id'    => session('ADMIN_ID')
                    ]);

                    $msg = "修改玩家：{$memberInfo['username']}的密码";
                    $this->insertLog($this->current_node, $msg, 125);
                    //                    $memberLogic = new MemberLogic;
                    //
                    //                    $memberLogic->syncMember($memberInfo['username'], $password, $memberInfo['mobile'], $now_time);
                    $this->success('密码修改成功', 'Member/information');

                } else
                    $this->error('密码修改失败');
            }
        }

        $this->assign('memberInfo', $memberInfo);

        return $this->fetch('update_password');
    }

    /**
     * 用户信息汇总冻结账户
     */
    public function informationFrozen()
    {
        $id   = input('id', 0, 'intval');
        $flag = input('flag', 0, 'abs');
        if (empty($id)) {
            $this->error('用户ID不能为空');
        }

        if (!$info = $this->membersModel->field('id,username')->find($id)) {
            $this->error('非法参数');
        }

        if ($flag == 1) $msg = '冻结'; else
            $msg = '解冻';

        if ($this->membersModel->update([
            'flag'        => $flag,
            'update_time' => time(),
        ], ['id' => $id])) {
            // 插入冻结记录
            $res = model('FrozenHistory')->addData($id,$flag);

            $info = $msg . "玩家：{$info['username']}";
            $this->insertLog($this->current_node, $info, 126);

            $this->success($msg . '账户成功');

        } else {
            $this->error($msg . '账户失败');
        }
    }

    /**
     * 删除用户头像
     */
    public function delAvatar()
    {
        $id   = input('id', 0, 'intval');
        if (empty($id)) {
            $this->error('用户ID不能为空');
        }

        if (!$info = $this->membersModel->field('id,username')->find($id)) {
            $this->error('非法参数');
        }

        if ($this->membersModel->update([
            'avatar'      => '',
            'update_time' => time(),
        ], ['id' => $id])) {

            $info = "删除玩家：{$info['username']}头像";

            $this->insertLog($this->current_node, $info, 127);

            $this->success('删除成功');

        } else {
            $this->error('删除失败');
        }
    }

    public function clearRealName(){
        $id   = input('id', 0, 'intval');
        if (empty($id)) {
            $this->error('用户ID不能为空');
        }
        $memberInfo = $this->membersModel->where(['id' => $id])->find();

        if (empty($memberInfo)) $this->error('用户不存在');


        if (model('common/MembersTwo')->where(['userid' => $id])->update(['realname' => '', 'idcard' => ''])) {

            $msg = "清空玩家：{$memberInfo['username']}的实名认证";
            $this->insertLog($this->current_node, $msg, 21);
            $this->success('清空成功', 'Member/index');
        } else {
            $this->error('清空失败');
        }

    }

    // 角色管理
    public function getRole()
    {
        $param = input('get.');

        $where = [];
        $gameId = input('gameid', 0, 'intval');
        $username = input('username', '', 'trim');
        $rolename = input('role_name', '', 'trim');
        $roleid = input('roleid', '', 'trim');
        $start_time = input('start_time', '', 'trim');
        $end_time = input('end_time', '', 'trim');
        $page_size = input('page_size', 20);

        !empty($gameId) && $where['mgs.game_id'] = $gameId;
        // !empty($username) && $where['user.username'] = ['LIKE', '%' . $username . '%'];
        // !empty($username) && $where['user.username'] = $username;
        if (!empty($username)) {
            $userIds = Db::table('cy_members')->where('username', $username)->column('id');
            $where['mgs.member_id'] = ['in', $userIds];
        }
        !empty($rolename) && $where['mgs.rolename'] = $rolename;
        !empty($roleid) && $where['mgs.roleid'] = $roleid;

        // 推广数据
        $business_type = input('business_type', '');
        $president_type = input('president_type', '');
        $president_type_son = input('president_type_son', '');
        $promoter_type = input('promoter_type', '');
        if ($business_type <> '') {
            $where['mgs.channel_id'] = ['in', get_child_channel_arr($business_type)];
        }
        if ($president_type <> '') {
            $where['mgs.channel_id'] = ['in', get_child_channel_arr($president_type)];
        }
        if ($president_type_son <> '') {
            $where['mgs.channel_id'] = ['in', get_child_channel_arr($president_type_son)];
        }
        if ($promoter_type <> '') {
            $where['mgs.channel_id'] = $promoter_type;
        }

        // 获取查询日期
        if ($start_time && $end_time) {
            $where['mgs.create_time'] = ['between time', [strtotime($start_time), strtotime($end_time . ' 23:59:59')]];
        }

        $list = Db::table('nw_member_game_server')->alias('mgs')
            ->field('mgs.*, m.username')
            ->join('cy_members m', 'mgs.member_id=m.id')
            ->where($where)->order('mgs.update_time desc')
            ->paginate($page_size, false, array('query' => $param))
            ->each(function ($item, $key) {
                $channel_list = get_channel_top_by_aggregate($item['channel_id']);
                $item['channel_zero_name'] = !empty($channel_list['0']['name']) ? $channel_list['0']['name'] : '';
                $item['channel_one_name'] = !empty($channel_list['1']['name']) ? $channel_list['1']['name'] : '';
                $item['channel_two_name'] = !empty($channel_list['2']['name']) ? $channel_list['2']['name'] : '';
                $item['channel_three_name'] = !empty($channel_list['3']['name']) ? $channel_list['3']['name'] : '';
                return $item;
            });

        $this->assign('list', $list);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());

        // 所属游戏
        $gameList = $this->selfGameList;
        $gList = json_encode($gameList);
        $gameList = array_column($gameList, 'name', 'id');
        $this->assign('game_list', $gameList);
        $this->assign('g_list', $gList);

        // 推广信息
        $uniom = model('Channel')->where(['level' => 0])->field('id,name')->select();
        $this->assign('uniom', $uniom);
        return $this->fetch();
    }
}
