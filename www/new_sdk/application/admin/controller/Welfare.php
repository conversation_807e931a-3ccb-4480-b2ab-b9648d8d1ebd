<?php

namespace app\admin\controller;


use app\common\library\OneFileUpload;
use app\common\logic\Member as MemberService;
use think\Loader;
use think\Db;

class Welfare extends Admin
{
    protected $gameList;


    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

    }

    /**
     *玩家福利列表
     */
    public function index()
    {
        $where = [];
        $order = 'create_time desc,id desc';

        //查询参数
        $gameid = $this->request->get('gameid', '', 'trim');
        $name = $this->request->get('name', '', 'trim');

        if (isset($name) && !empty($name)) {
            $where['a.name'] = ['like', "%$name%"];
        }

        $tmpGameList = model('Common/Game')->getAllByCondition('id,name',['is_welfare'=>2]);
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }
        $welfareGiftType = model('welfareGiftType')->select();
        foreach ($welfareGiftType as $ty) {
            $welfareGiftType[$ty['id']] = $ty;
        }

        $resourcesModel = Db::name('cy_welfare_resources');
        $resources = $resourcesModel->field('id,name')->select();
        foreach ($resources as $v) {
            $resources[$v['id']] = $v;
        }
        
        $welfareModel = model('welfare');
        $list = $welfareModel->alias('a')
            ->field('a.*')
            ->where($where)
            ->where(function ($query) use ($gameid) {
                if (isset($gameid) && !empty($gameid)) {
                    $query->whereRaw(sprintf('FIND_IN_SET(%s,game_id)', $gameid));
                }
            })
            ->order($order)
            ->paginate(10, false, ['query' => input('get.')])->each(function ($item, $key) use ($gameList, $welfareGiftType, $resources) {
                $game_ids = explode(',', $item['game_id']);
                $tmp = [];
                foreach ($game_ids as $k => $v) {
                    $tmp[] = $gameList[$v]['name'];
                }
                $item['game_name'] = implode('<br>', $tmp);
                if ($welfareGiftType[$item['welfare_gift_type_id']]['type_id'] == 1) {
                    $dayStr = '';
                    switch ($welfareGiftType[$item['welfare_gift_type_id']]['recharge_type']){
                        case 1:
                            $dayStr = '全部';
                            break;
                        case 2:
                            $dayStr = '日';
                            break;
                        case 3:
                            $dayStr = '周';
                            break;
                        case 4:
                            $dayStr = '月';
                            break;
                        case 5:
                            $dayStr = '季';
                            break;
                        case 6:
                            $dayStr = '年';
                            break;
                        case 7:
                            $dayStr = '自定义';
                            break;
                    }
                    if($welfareGiftType[$item['welfare_gift_type_id']]['recharge_type'] == 1){
                        $note = '（使用条件：最低充值金额' . $welfareGiftType[$item['welfare_gift_type_id']]['recharge_amount'] . '）时间段：'.$dayStr;
                    }else{
                        $note = '（使用条件：区间金额' . $welfareGiftType[$item['welfare_gift_type_id']]['recharge_amount'].'-'.$welfareGiftType[$item['welfare_gift_type_id']]['max_recharge_amount'] . '）时间段：'.$dayStr;
                    }

                } elseif ($welfareGiftType[$item['welfare_gift_type_id']]['type_id'] == 2) {
//                    $note = '（使用条件： 创角）';
                    $note = '';
                } else {
                    $note = '（使用条件： 最低等级' . $welfareGiftType[$item['welfare_gift_type_id']]['role_level'] . '）';
                }
                $item['welfare_gift_type_name'] = $welfareGiftType[$item['welfare_gift_type_id']]['name'] . $note;

                $resources_data = json_decode($item['resources_data'], true);
                $tmp = [];
                foreach ($resources_data as $k => $v) {
                    $tmp[] = '礼包名称：' . $resources[$v['welfare_resources_id']]['name'] . ' 数量：' . $v['number'];
                }
                if ($tmp) {
                    $item['welfare_resources_name'] = implode('<br>', $tmp);
                } else {
                    $item['welfare_resources_name'] = '';
                }

                return $item;
            });


        $this->assign('game_list', $tmpGameList);
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }


    /**
     *增
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            if (!in_array($data['grant_type_id'], [3, 8, 9])) {
                $data['frequency'] = 1;
            }
            if (!in_array($data['grant_type_id'], [7])) {
                $data['first_type'] = 1;
            }
            if (!in_array($data['grant_type_id'], [8,9])) {
                $data['month_type'] = 1;
            }
            if (!$data['select']) {
                $this->error('请选择游戏');
            }
            $data['game_id'] = $data['select'];

            if(!$data['welfare_gift_type_id']){
                $this->error('福利礼包类型');
            }
            if(!isset($data['welfare_resources_id']) && !$data['welfare_resources_id']){
                $this->error('请选择福利!');
            }
            $resources_data = [];
            foreach ($data['welfare_resources_id'] as $k => $v) {
                if(!$v){
                    $this->error('请选择福利!');
                }
                $resources_data[] = [
                    'welfare_resources_id' => $v,
                    'number' => $data['number'][$k],
                ];
            }
            $data['resources_data'] = json_encode($resources_data);
            unset($data['select'], $data['welfare_resources_id'], $data['number']);
            $data['update_time'] = $data['create_time'] = time();
            $m = Db::name('cy_welfare')->insertGetId($data);


            if (!empty($m)) {
                $this->success('添加成功', url('index'));
            }
            $this->error('添加失败');
        }

        $tmpGameList = model('Common/Game')->getAllByCondition('id,name',['is_welfare'=>2]);

        $this->assign('game_list', $tmpGameList);
        $welfare_gift_type = Db::name('cy_welfare_gift_type')->select();
        $this->assign('welfare_gift_type', $welfare_gift_type);
        $this->assign('grant_type_ids', model('Welfare')->grant_type_id);
        return $this->fetch();
    }


    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel = model('Common/Welfare');
        if (!$appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }


    /**
     *编辑SDK密钥
     */
    public function edit()
    {
        $id = $this->request->param('id', '', 'intval');
        $appModel = Db::name('cy_welfare');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!in_array($data['grant_type_id'], [3, 8, 9])) {
                $data['frequency'] = 1;
            }
            $resources_data = [];

            if(!isset($data['welfare_resources_id']) && !$data['welfare_resources_id']){
                $this->error('请选择福利!');
            }
            foreach ($data['welfare_resources_id'] as $k => $v) {
                if(!$v){
                    $this->error('请选择福利!');
                }
                $resources_data[] = [
                    'welfare_resources_id' => $v,
                    'number' => $data['number'][$k],
                ];
            }
            $data['resources_data'] = json_encode($resources_data);
            unset($data['welfare_resources_id'], $data['number']);
            $data['update_time'] = time();
            Db::name('cy_welfare')->where(['id' => $id])->update($data);
            $this->success('修改成功', url('index'));


            $this->error($appModel->getError() ?: '修改失败');
        }
        $game = explode(',', $data['game_id']);
        $tmpGameList = model('Common/Game')->getAllByCondition('name', ['id' => ['in', $game]]);

        $resourcesModel = Db::name('cy_welfare_resources');
        $resources = $resourcesModel->where(function ($query) use ($game) {
            foreach ($game as $k => $v) {
                $query->whereRaw(sprintf('FIND_IN_SET(%s,game_id)', $v));
            }
        })->field('id,name')->select();
        $this->assign('resources', $resources);

        $this->assign('game_name', implode(',', array_column($tmpGameList, 'name')));

        $welfare_gift_type = Db::name('cy_welfare_gift_type')->select();
        $this->assign('welfare_gift_type', $welfare_gift_type);
        $this->assign('grant_type_ids', model('Welfare')->grant_type_id);


        $this->assign('resources_data', json_decode($data['resources_data'], true));
        $this->assign('data', $data);
        return $this->fetch();
    }

    public function getWelfareResources()
    {
        $resourcesModel = Db::name('cy_welfare_resources');

        $game = input('game');
        if (!$game) {
            $this->error('游戏不能为空');
        }
        $gameArr = explode(',', $game);
        $result = $resourcesModel->where(function ($query) use ($gameArr) {
            foreach ($gameArr as $k => $v) {
                $query->whereRaw(sprintf('FIND_IN_SET(%s,game_id)', $v));
            }
        })->field('id,name')->select();
        $result = array_merge([['id'=>'','name'=>'']],json_decode(json_encode($result),true));
        $this->jsonResult($result, 1, '成功');
    }


}
