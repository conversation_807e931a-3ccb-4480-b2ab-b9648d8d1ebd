<?php


namespace app\admin\controller;


use think\Env;

class Tasks
{
    public function index()
    {
        $cronjobs = array();

        $cronjobs['minutes5'] = '00,05,10,15,20,25,30,35,40,45,50,55,58 * * * *';  //每五分钟执行一次

        $cronjobs['minutes5_s'] = '02,07,12,17,22,27,32,37,42,47,52,57 * * * *';  //每五分钟执行一次

        $cronjobs['minutes6'] = '01,07,13,19,25,31,37,43,49,55 * * * *';  //每六分钟执行一次
        $cronjobs['minutes10'] = '01,11,21,31,41,51 * * * *';  //每十分钟执行一次
        $cronjobs['minutes30'] = '00,30 * * * *';  //每小时的00分 30分执行一次
        $cronjobs['hours_15'] = '15 * * * *';  //每小时的第十五分钟执行一次
        $cronjobs['day_hours_1_10'] = '10 00 * * *';  //每天0点10分执行一次
        $cronjobs['day_hours3'] = '00 0,2,4,6,8,10,12,14,16,18,20,22 * * *';  //每3小时分执行一次


        $cronjobs['day_hours_1'] = '01 01 * * *';  //每天1点执行一次

        $cronjobs['day_hours_3'] = '01 03 * * *';  //每天3点执行一次
        $cronjobs['day_hours_4'] = '01 04 * * *';  //每天4点执行一次
        $cronjobs['day_hours_5'] = '01 05 * * *';  //每天5点执行一次
        $cronjobs['day_hours_10'] = '01 10 * * *';  //每天10点执行一次
        $cronjobs['monday_hours_5'] = '01 05 * * 1';  //每周星期一5点执行一次

        $cronjobs['month_hours_5'] = '01 05 01 * ?';  //每月1号5点执行一次

        $cronjobs['day_hours_2_10'] = '11 02 * * *';  //每天2点10分执行一次
        $cronjobs['day_hours_2_30'] = '31 02 * * *';  //每天2点30分执行一次
        $cronjobs['day_hours_2_40'] = '41 02 * * *';  //每天2点40分执行一次
        $cronjobs['day_hours_2_50'] = '51 02 * * *';  //每天2点50分执行一次

        $cronjobs['day_hours_3_10'] = '12 03 * * *';  //每天3点10分执行一次
        $cronjobs['day_hours_3_30'] = '32 03 * * *';  //每天3点30分执行一次
        $cronjobs['day_hours_3_40'] = '42 03 * * *';  //每天3点40分执行一次
        $cronjobs['day_hours_3_50'] = '52 03 * * *';  //每天3点50分执行一次

        $cronjobs['day_hours_4_10'] = '13 04 * * *';  //每天4点10分执行一次
        $cronjobs['day_hours_4_20'] = '23 04 * * *';  //每天4点20分执行一次
        $cronjobs['day_hours_4_30'] = '33 04 * * *';  //每天4点30分执行一次
        $cronjobs['day_hours_4_40'] = '43 04 * * *';  //每天4点40分执行一次
        $cronjobs['day_hours_4_50'] = '53 04 * * *';  //每天4点50分执行一次

        $cronjobs['day_hours_5_10'] = '14 05 * * *';  //每天5点10分执行一次
        $cronjobs['day_hours_5_20'] = '24 05 * * *';  //每天5点20分执行一次
        $cronjobs['day_hours_5_30'] = '34 05 * * *';  //每天5点30分执行一次
        $cronjobs['day_hours_5_40'] = '44 05 * * *';  //每天5点40分执行一次
        $cronjobs['day_hours_5_50'] = '54 05 * * *';  //每天5点50分执行一次

        $cronjobs['day_hours_6_10'] = '15 06 * * *';  //每天4点10分执行一次
        $cronjobs['day_hours_6_30'] = '35 06 * * *';  //每天4点30分执行一次
        $cronjobs['day_hours_6_40'] = '45 06 * * *';  //每天4点40分执行一次
        $cronjobs['day_hours_6_50'] = '55 06 * * *';  //每天4点50分执行一次

        $time = time();
        if ($this->is_time_cron($time, $cronjobs['minutes5'])) {

            $this->minutes5();
        }
        if ($this->is_time_cron($time, $cronjobs['minutes5_s'])) {

            $this->minutes5_s();
        }

        if ($this->is_time_cron($time, $cronjobs['minutes6'])) {

            $this->minutes6();
        }

        if ($this->is_time_cron($time, $cronjobs['minutes10'])) {
            // addLog('运行每十分钟执行一次' . (time() - $time));
            $this->minutes10();
        }

        $this->url = Env::get('dingtalk.warning_url');
        if ($this->is_time_cron($time, $cronjobs['day_hours3'])) {
            $this->day_hours3();
        }
        if ($this->is_time_cron($time, $cronjobs['hours_15'])) {
            //addLog('运行每小时的第十五分钟执行一次');
            $this->hours15();
            $errmsg = '【风谷】定时任务每小时的第十五分钟执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }
        if ($this->is_time_cron($time, $cronjobs['day_hours_1_10'])) {
            $errmsg = '【风谷】定时任务运行每天0点10分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
            $this->day_hours_1_10();
        }

        if ($this->is_time_cron($time, $cronjobs['minutes30'])) {

            $this->minutes30();
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_1'])) {
            //addLog('运行每天1点执行一次');
            $this->dayHours1();
            $errmsg = '【风谷】定时任务运行每天1点执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }


        if ($this->is_time_cron($time, $cronjobs['day_hours_3'])) {
            //addLog('运行每天3点执行一次');
            $this->dayHours3();
            $errmsg = '【风谷】定时任务运行每天3点执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_4'])) {
            //addLog('运行每天4点执行一次');
            $this->dayHours4();
            $errmsg = '【风谷】定时任务运行每天4点执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_5'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours5();
            $errmsg = '定时任务运行每天5点执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_10'])) {
            //addLog('运行每天5点执行一次');
            $this->day_hours_10();
            $errmsg = '定时任务运行每天10点执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['monday_hours_5'])) {
            //addLog('运行每周星期一5点执行一次');
            $this->mondayHours5();
            $errmsg = '【风谷】运行每周星期一5点执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }
        if ($this->is_time_cron($time, $cronjobs['month_hours_5'])) {
            //addLog('每月1号5点执行一次');
            $this->monthHours5();
            $errmsg = '【风谷】每月1号5点执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_5_10'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours5_10();
            $errmsg = '【风谷】每天5点10分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_5_20'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours5_20();
            $errmsg = '【风谷】每天5点20分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_5_30'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours5_30();
            $errmsg = '每天5点30分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_5_40'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours5_40();
            $errmsg = '每天5点40分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_5_50'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours5_50();
            $errmsg = '【风谷】每天5点50分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }


        if ($this->is_time_cron($time, $cronjobs['day_hours_4_10'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours4_10();
            $errmsg = '【风谷】每天4点10分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_4_20'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours4_20();
            $errmsg = '【风谷】每天4点20分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_4_30'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours4_30();
            $errmsg = '每天4点30分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_4_40'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours4_40();
            $errmsg = '每天4点40分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_4_50'])) {
            //addLog('运行每天5点执行一次');
            $this->dayHours4_50();
            $errmsg = '每天4点50分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_3_10'])) {

            $this->dayHours3_10();
            $errmsg = '【风谷】每天3点10分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_3_30'])) {

            $this->dayHours3_30();
            $errmsg = '【风谷】每天3点30分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_3_40'])) {

            $this->dayHours3_40();
            $errmsg = '【风谷】每天3点40分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        if ($this->is_time_cron($time, $cronjobs['day_hours_3_50'])) {

            $this->dayHours3_50();
            $errmsg = '【风谷】每天3点50分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }
        if ($this->is_time_cron($time, $cronjobs['day_hours_2_50'])) {

            $this->dayHours2_50();
            $errmsg = '每天2点50分执行一次！' . (time() - $time);
            $this->curlDD($errmsg, $this->url);
        }

        //addLog('运行每分钟执行一次'.time());
        $this->minutes1();

        // $errmsg = '【风谷】执行一次！' . (time() - $time);
        // $this->curlDD($errmsg, $this->url);
    }

    public function day_hours_10()
    {

        $url = [
            'http://devadmin.'.QM_DOMAIN_URL.'/job/signature'
        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours2_10()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours2_30()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours2_40()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours2_50()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours3_10()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours3_30()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours3_40()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours3_50()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours4_10()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours4_20()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours4_30()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours4_40()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours4_50()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours5_10()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours5_20()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours5_30()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours5_40()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours5_50()
    {
        $date = date("Y-m-d", strtotime("-1 day"));
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function monthHours5()
    {
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function minutes5()
    {

        $url = [

        ];
        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }


    public function minutes5_s()
    {

        $url = [

        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function minutes6()
    {
        $url = [

        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function minutes10()
    {
        $url = [
            'http://devadmin.'.QM_DOMAIN_URL.'/fixed/recharge'
        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function day_hours3()
    {
        $url = [

        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function hours15()
    {
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function day_hours_1_10()
    {

        $url = [

        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function minutes30()
    {
        $url = [


            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/reg_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/role_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/act_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/recharge_num?day=' . date('Y-m-d'),

            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/reg_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/role_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/act_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/recharge_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/one_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/three_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/four_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/five_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/six_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/seven_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/fifteen_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/thirty_stay?day=' . date('Y-m-d'),


            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/reg_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/role_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/act_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/recharge_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/one_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/three_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/four_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/five_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/six_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/seven_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/fifteen_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/thirty_stay?day=' . date('Y-m-d'),


            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/reg_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/role_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/act_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/recharge_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/one_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/three_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/four_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/five_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/six_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/seven_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/fifteen_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/thirty_stay?day=' . date('Y-m-d'),


            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/reg_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/role_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/act_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/recharge_num?day=' . date('Y-m-d'),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/one_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/three_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/four_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/five_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/six_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/seven_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/fifteen_stay?day=' . date('Y-m-d'),
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/thirty_stay?day=' . date('Y-m-d'),
        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours1()
    {
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours3()
    {
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours4()
    {
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function dayHours5()
    {
        $url = [
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/reg_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/role_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/act_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/recharge_num?day=' . date('Y-m-d', strtotime('-1 day')),

            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/reg_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/role_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/act_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/recharge_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/one_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/three_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/four_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/five_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/six_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/seven_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/fifteen_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/thirty_stay?day=' . date('Y-m-d', strtotime('-1 day')),


            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/reg_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/role_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/act_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/recharge_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/one_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/three_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/four_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/five_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/six_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/seven_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/fifteen_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/thirty_stay?day=' . date('Y-m-d', strtotime('-1 day')),


            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/reg_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/role_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/act_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/recharge_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/one_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/three_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/four_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/five_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/six_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/seven_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/fifteen_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/thirty_stay?day=' . date('Y-m-d', strtotime('-1 day')),


            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/reg_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/role_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/act_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/recharge_num?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/one_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/three_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/four_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/five_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/six_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/seven_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/fifteen_stay?day=' . date('Y-m-d', strtotime('-1 day')),
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/thirty_stay?day=' . date('Y-m-d', strtotime('-1 day')),

        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }

    public function mondayHours5()
    {
        $url = [];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }


    public function minutes1()
    {

        $url = [

        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }


    function is_time_cron($time, $cron)
    {
        $cron_parts = explode(' ', $cron);
        if (count($cron_parts) != 5) {
            return false;
        }

        list($min, $hour, $day, $mon, $week) = explode(' ', $cron);

        $to_check = array('min' => 'i', 'hour' => 'G', 'day' => 'j', 'mon' => 'n', 'week' => 'w');

        $ranges = array(
            'min' => '0-59',
            'hour' => '0-23',
            'day' => '1-31',
            'mon' => '1-12',
            'week' => '0-6',
        );

        foreach ($to_check as $part => $c) {
            $val = $$part;
            $values = array();

            /*
             For patters like 0-23/2
            */
            if (strpos($val, '/') !== false) {
                //Get the range and step
                list($range, $steps) = explode('/', $val);

                //Now get the start and stop
                if ($range == '*') {
                    $range = $ranges[$part];
                }
                list($start, $stop) = explode('-', $range);

                for ($i = $start; $i <= $stop; $i = $i + $steps) {
                    $values[] = $i;
                }
            } /*
             For patters like :
            2
            2,5,8
            2-23
            */ else {
                $k = explode(',', $val);

                foreach ($k as $v) {
                    if (strpos($v, '-') !== false) {
                        list($start, $stop) = explode('-', $v);

                        for ($i = $start; $i <= $stop; $i++) {
                            $values[] = $i;
                        }
                    } else {
                        $values[] = $v;
                    }
                }
            }

            if (!in_array(date($c, $time), $values) and (strval($val) != '*')) {
                return false;
            }
        }

        return true;
    }

    function curlDD($msg, $url)
    {
        $postData = [
            'msgtype' => 'text',
            'text' => [
                'content' => mb_convert_encoding($msg, "UTF-8", "auto"),
            ]
        ];

        $postString = json_encode($postData);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 对认证证书来源的检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postString);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json"
        ));

        $result = curl_exec($ch);

        $HTTP_CODE = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);
    }

    public function manual()
    {

        $day = input('day');
        if (!date('Y-m-d', strtotime($day))) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $url = [

//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/reg_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/role_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/act_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/recharge_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/one_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/three_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/four_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/five_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/six_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/seven_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/fifteen_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game/thirty_stay?day=' . $day,
//
//
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/reg_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/role_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/act_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/recharge_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/one_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/three_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/four_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/five_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/six_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/seven_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/fifteen_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server/thirty_stay?day=' . $day,


//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/reg_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/role_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/act_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/recharge_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/one_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/three_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/four_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/five_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/six_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/seven_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/fifteen_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_game_channel/thirty_stay?day=' . $day,
//
//
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/reg_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/role_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/act_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/recharge_num?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/one_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/three_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/four_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/five_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/six_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/seven_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/fifteen_stay?day=' . $day,
//            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_server_channel/thirty_stay?day=' . $day,
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/reg_num?day=' . $day,
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/role_num?day=' . $day,
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/act_num?day=' . $day,
            'http://devadmin.'.QM_DOMAIN_URL.'/retaine_platform/recharge_num?day=' . $day,
        ];

        foreach ($url as $k => $v) {
            timingCurl($v);
        }
    }
}
