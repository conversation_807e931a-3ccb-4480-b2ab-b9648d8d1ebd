<?php
/**
 *关于我们管理
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/11/19
 * Time: 11:50
 */
namespace app\admin\controller;

class Aboutus extends Admin
{
    protected function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->aboutusModel = model('Aboutus');
    }

    /**
     *关于管理列表
     */
    public function index()
    {
        $order = $this->request->param('order');
        $orderStr = null;
        $orderUrl = url('index',['order'=>'desc']);

        if ($order == 'desc'){
            $orderUrl = url('index',['order'=>'asc']);
            $orderStr = 'order desc';
        }elseif ($order == 'asc'){
            $orderStr = 'order asc';
            $orderUrl = url('index',['order'=>'desc']);
        }

        $list = $this->aboutusModel->getList($orderStr);
        $this->assign('list',$list);
        $this->assign('page',$list);
        $this->assign('orderUrl', $orderUrl);
        return $this->fetch();
    }
    /*
     * 关于管理添加
     */
    public function addAbout()
    {
        if ($this->request->isPost()) {
            $data = $this->getAboutusParam();

            $result = $this->validate($data, [
                ['title', 'require', '标题不能为空'],
                ['name', 'require', '路径参数不能为空'],
                ['content', 'require', '内容不能为空'],
                ['order', 'require|integer|gt:0', '序列号不能为空|序列号必须为正整数'],
            ]);

            if (true !== $result) {
                $this->error($result);
            }

            if ($this->isShowName($data['name'])) {
                $this->error('路径参数已经存在');
            }

            $data['create_time'] = time();

            if ($this->aboutusModel->allowField(true)->save($data)) {
                $this->aboutusModel->delCache(); // 删除缓存
                $this->success('添加成功',url('index'));
            }
            $this->error($this->aboutusModel->getError() ?: '添加失败');
        }
        return $this->fetch('add_about');
    }
    /**
     *关于管理编辑
     */
    public function editAbout()
    {
        $id = $this->request->param('id', 0, 'intval');
        if ($this->request->isPost()) {
            $data = $this->getAboutusParam();

            $result = $this->validate($data, [
                ['title', 'require', '标题不能为空'],
                ['name', 'require', '路径参数不能为空'],
                ['content', 'require', '内容不能为空'],
                ['order', 'require|integer|gt:0', '序列号不能为空|序列号必须为正整数'],
            ]);
            if (true !== $result) {
                $this->error($result);
            }

            if ($this->isShowName($data['name'],$id)) {
                $this->error('路径参数已经存在');
            }

            if ($this->aboutusModel->allowField(true)->save($data, ['id' => $id]) !== false ) {
                $this->aboutusModel->delCache(); // 删除缓存
                $this->success('编辑成功',url('index'));
            }
            $this->error($this->aboutusModel->getError() ?: '编辑失败');
        }

        $data = $this->aboutusModel->where('id',$id)->find();
        $this->assign('data', $data);
        return $this->fetch('edit_about');
    }

    public function getAboutusParam()
    {
        $data = [
            'title'=> input('post.title'),
            'name'=> input('post.name'),
            'content'=> input('post.content'),
            'order'=> input('post.order'),
        ];
        return $data;
    }
    /*
     * 删除
     */
    public function delAboutus()
    {
        $id = $this->request->param('id', 0, 'intval');
        if (empty($id) || !($data = $this->aboutusModel->find($id))) {
            $this->error('参数错误,不存在你要删除的内容');
        }
        if ($data->delete()) {
            $this->aboutusModel->delCache(); // 删除缓存
            $this->success('删除成功!');
        }
        $this->error('删除失败');
    }

    // 判断路径参数是否存在
    private function isShowName($name,$id = 0)
    {
        $condition = [
            'name'  => $name,
            'id'    => ['<>',$id],
        ];

        $count = $this->aboutusModel->where($condition)->count();

        return $count ? true : false;
    }
}