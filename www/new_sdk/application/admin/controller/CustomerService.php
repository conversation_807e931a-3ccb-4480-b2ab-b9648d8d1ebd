<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/11/1
 * Time: 14:02
 */
namespace app\admin\controller;
use app\common\controller\Base;
use app\common\model\Kefu;
use think\Cache;
use think\Db;

class CustomerService extends Admin
{
    private $_status = [
      '0' => '未审核',
      '1' => '成功',
      '2' => '失败',
      '3' => '成功改完密码',
      '4' => '已驳回'
    ];

    protected function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->settingModel = model('CscSetting');
        $this->appealModel = model('Common/CscAppeal');
        $this->kefuModel = model('Common/Kefu');
    }

    /**
     *客服系统配置列表
     */
    public function customerDeploy()
    {
        $cscList = $this->settingModel->getList();
        $this->assign('list',$cscList);
        $this->assign('page',$cscList);
        return $this->fetch('customer_deploy');
    }

    /**
     * 新增客服系统配置列表
     * @return mixed
     */
    public function addCustomer()
    {
        if ($this->request->isPost()) {
            $data = $this->getCustomerParam();

            $result = $this->validate($data, [
                ['name', 'require', '字段名不能为空'],
                ['title', 'require', '备注不能为空'],
            ]);

            if (true !== $result) {
                $this->error($result);
            }
            if ($this->settingModel->where(['name'=>$data['name']])->find()) {
                $this->error('字段名不能重复');
            }

            if ($this->settingModel->allowField(true)->save($data)) {
                $this->success('添加成功',url('customerdeploy'));
            }
            $this->error($this->settingModel->getError() ?: '添加失败');
        }
        return $this->fetch('add_customer');
    }

    /*
     *客服系统配置列表修改
     */
    public function editCustomer()
    {
        $id = $this->request->param('id', 0, 'intval');
        if ($this->request->isPost()) {
            $data = $this->getCustomerParam();

            $result = $this->validate($data, [
                ['name', 'require', '字段名不能为空'],
                ['title', 'require', '备注不能为空'],
            ]);
            if (true !== $result) {
                $this->error($result);
            }

            if ($this->settingModel->allowField(true)->save($data, ['id' => $id]) !== false ) {
                // 重新计算未发放的申诉单
                $this->setAppealScore();
                $this->success('编辑成功',url('customerdeploy'));
            }
            $this->error($this->settingModel->getError() ?: '编辑失败');
        }

        $data = $this->settingModel->where('id',$id)->find();
        $this->assign('data', $data);
        return $this->fetch('edit_customer');
    }
    /*
     *客服配置编辑、新增字段
     */
    public function getCustomerParam()
    {
        $data = [
            'name'  => input('post.name'),
            'value' => input('post.value'),
            'title' => input('post.title'),
        ];

        return $data;
    }

    protected function getAppealParam(){
        $condition = [];
        $username = $this->request->get('username','','trim');
        $code = $this->request->get('code','','trim');
        $status = $this->request->get('status','','trim');
        $reason = $this->request->get('reason',0,'intval');
        !empty($username) && $condition['username'] = $username;
        !empty($code) && $condition['code'] = $code;

        if ($status != '') $condition['status'] = $status;
        !empty($reason) && $condition['reason'] = $reason;

        $start_time = input('request.cr_start');
        //开始时间和结束时间不为空时
        if ($start_time != '' && input('request.cr_end') != '') {
            $condition['create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime(input('request.cr_end').' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time!= '') {
            $condition['create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif (input('request.cr_end') != '') {
            $condition['create_time'] = ['<=', strtotime(input('request.cr_end').' 23:59:59')];
        }

        return $condition;
    }
    /**
     *客服管理申诉列表
     */
    public function appeal()
    {
        $condition = $this->getAppealParam();
        //获取后台通过申诉分数的值
        $score = $this->settingModel->where('name','MARK_APPEAL')->value('value');

        $AppealList = $this->appealModel
            ->where($condition)
            ->field('id,username,mobile,mail,code,status,reason,score,create_time,notice_time,remark,operate_time,operate_id')
            ->order('status asc,create_time desc')
            ->paginate(15, false, array('query' => input('get.')));
        foreach ($AppealList as $key => &$v) {
            $v['username'] = stringObfuscation($v['username'],3);

            $v['operator'] = '';
            if (!empty($v['operate_id'])){
                $v['operator'] = model('Admin')->where('id',$v['operate_id'])->value('username');
            }
        }
       
        $this->assign('score',$score);
        $this->assign('list',$AppealList);
        $this->assign('page',$AppealList);

        return $this->fetch();
    }

    

    /**
     *被盗嫌疑列表
     */
    public function stolenList()
    {
        $username = $this->request->get('username','','trim');
        $code = $this->request->get('code','','trim');
        !empty($username) && $Where['username'] = $username;
        !empty($code) && $Where['code'] = $code;
        //获取配置分数
        $score = $this->settingModel->where('name','MARK_APPEAL')->value('value');
        $Where['reason'] = 2;
        $Where['score'] = ['egt',$score];
        $AppealList = $this->appealModel
            ->where($Where)
            ->field('id,username,mobile,mail,code,status,reason,score,create_time,notice_time')
            ->order('status asc,create_time desc')
            ->paginate(15, false, array('query' => input('get.')));
        foreach ($AppealList as $key => $v) {
            $v['username'] = stringObfuscation($v['username'],3);
        }

        $this->assign('list', $AppealList);
        $this->assign('page', $AppealList->render());
        return $this->fetch('stolen_list');
    }


    /**
     *客服QQ列表
     */
    public function customerQq()
    {
        $qqList = $this->kefuModel->getList();
        $gameLit = model('Common/Game')->getAllByCondition('id,name', [],'','self');
        $newGameList = array_column($gameLit, 'name', 'id');

        $this->assign('list',$qqList);
        $this->assign('page',$qqList);
        $this->assign('game_list',$newGameList);
        return $this->fetch('customer_qq');
    }

    /*
     * 新增客服QQ
     */
    public function addQq()
    {
        if ($this->request->isPost()) {
            $data = $this->getQqParam();

            $result = $this->validate($data, [
                ['qq', 'require|number|min:5|max:11', 'qq不能为空|qq要数字哦|qq不能小于五位数|qq不能大于11位'],
            ]);

            if (true !== $result) {
                $this->error($result);
            }

            if ($this->kefuModel->allowField(true)->save($data)) {
                $this->success('添加成功',url('customerqq'));
            }
            $this->error($this->kefuModel->getError() ?: '添加失败');
        }

        $gameLit = model('Common/Game')->getAllByCondition('id,name', [],'','self');
        $this->assign('game_list',$gameLit);
        return $this->fetch('add_qq');
    }

    /*
     * 编辑客服qq
     */
    public function editQq()
    {
        $id = $this->request->param('id', 0, 'intval');
        if ($this->request->isPost()) {
            $data = $this->getQqParam();

            $result = $this->validate($data, [
                ['qq', 'require|min:5|max:11|number', 'qq不能为空|qq不能小于五位数|qq不能大于11位|qq要数字哦'],
            ]);
            if (true !== $result) {
                $this->error($result);
            }

            if ($res = model('common/Kefu')->allowField(true)->save($data, ['id' => $id]) !== false ) {
                if(!empty($data['game_id'])){
                    Cache::rm(Kefu::CACHE_KEY . ':' . $data['game_id']);
                }else{
                    Cache::rm(Kefu::CACHE_KEY);
                }
                $this->success('编辑成功',url('customerqq'));
            }
            $this->error($this->kefuModel->getError() ?: '编辑失败');
        }

        $data = model('common/Kefu')->where('id',$id)->find();
        $this->assign('data', $data);

        $gameLit = model('Common/Game')->getAllByCondition('id,name', [],'','self');
        $this->assign('game_list',$gameLit);
        return $this->fetch('edit_qq');
    }

    /*
     * 删除客服qq
     */
    public function delQq()
    {
        $id = $this->request->param('id', 0, 'intval');
        if (empty($id) || !($data = $this->kefuModel->find($id))) {
            $this->error('参数错误,不存在改客服qq的');
        }
        if ($data->delete()) {
            $this->success('删除成功!');
        }
        $this->error('删除失败');
    }

    /*
     * 客服qq编辑、新增字段
     */
    public function getQqParam()
    {
        $data = [
            'qq'        => input('post.qq'),
            'nickname' => input('post.nickname'),
            'game_id' => input('post.game_id'),
        ];
        return $data;
    }

    /**
     * 用户申诉信息页面
     */
    public function appealInfo()
    {
        if ($this->request->isPost()) {
            $username = $this->request->post('username','','trim');
            $code = $this->request->post('code','','trim');
            !empty($username) && $condition['username'] = $username;
            !empty($code) && $condition['code'] = $code;
        }else{
            $id = $this->request->param('id', 0, 'intval');
            !empty($id) && $condition['id'] = $id;
        }
        if (!isset($condition) || empty($condition)) {
            $this->error('查询条件不能为空');
        }
        $array = $this->appealModel->getAppealResult($condition);
        if (empty($array)) {
            $this->error('暂无数据');
        }
        $data = $array['data'];
        $result = $array['result'];
        $oldpwd  = $this->getOldPwd($data,$result);
        $oldmail = $this->getOldMail($data,$result);
        $oldmobile = $this->getOldMobile($data,$result);
        $boundgame = $this->getBoundgame($data,$result);
        foreach ($array['recharge'] as $key => &$value) {
            $value['result'] = isset($array['result']['recharge'][$key])? $array['result']['recharge'][$key] : 0;
        }
        $array['oldpwd'] = $oldpwd;
        $array['oldmail'] = $oldmail;
        $array['oldmobile'] = $oldmobile;
        $array['boundgame'] = $boundgame;

        // 多选分数合并显示操作
        /*$recharge_1 = array_column($array['recharge'],'result');*/
        $oldpwd_1 = array_column($array['oldpwd'],'result');
        $oldmail_1 = array_column($array['oldmail'],'result');
        $oldmobile_1 = array_column($array['oldmobile'],'result');
        $boundgame_1 = array_column($array['boundgame'],'result');
        $array['recharge_1'] = ['num'=>count($array['recharge']),'isscore'=>0];
        $array['oldpwd_1'] = ['num'=>count($array['oldpwd']),'isscore'=>0];
        $array['oldmail_1'] = ['num'=>count($array['oldmail']),'isscore'=>0];
        $array['oldmobile_1'] = ['num'=>count($array['oldmobile']),'isscore'=>0];
        $array['boundgame_1'] = ['num'=>count($array['boundgame']),'isscore'=>0];

        /*if (in_array(1,$recharge_1)){
            $array['recharge_1']['isscore'] = 1;
        }*/
        if (in_array(1,$oldpwd_1)){
            $array['oldpwd_1']['isscore'] = 1;
        }
        if (in_array(1,$oldmail_1)){
            $array['oldmail_1']['isscore'] = 1;
        }
        if (in_array(1,$oldmobile_1)){
            $array['oldmobile_1']['isscore'] = 1;
        }
        if (in_array(1,$boundgame_1)){
            $array['boundgame_1']['isscore'] = 1;
        }

        $this->assign('array',$array);
        return $this->fetch('appeal_info');

    }

    //获取历史密码数据结果
    public function getOldPwd($data,$result)
    {
        $oldpwd = [];
        $data['oldpwd'] = array_values($data['oldpwd']);
        foreach ($data['oldpwd'] as $key => $pwd){
            $oldpwd[$key] = array(
                'oldpwd' => $pwd,
                'result' => isset($result['oldpwd'][$key]) ? $result['oldpwd'][$key] : 0,
            );
        }
        return $oldpwd;
    }
    
    //获取历史邮箱数据结果
    public function getOldMail($data,$result)
    {
        $oldmail = [];
        $data['oldmail'] = array_values($data['oldmail']);
        foreach ($data['oldmail'] as $key => $mail){
            $oldmail[$key] = array(
                'oldmail' => $mail,
                'result' => isset($result['oldmail'][$key]) ? $result['oldmail'][$key] : 0,
            );
        }
        return $oldmail;
    }
    //获取历史手机数据结果
    public function getOldMobile($data,$result)
    {
        $oldmobile = [];
        $data['oldmobile'] = array_values($data['oldmobile']);
        foreach ($data['oldmobile'] as $key => $mobile){
            $oldmobile[$key] = array(
                'oldmobile' => $mobile,
                'result' => isset($result['oldmobile'][$key]) ? $result['oldmobile'][$key] : 0,
            );
        }
        return $oldmobile;
    }

    //获取绑定游戏数据结果
    public function getBoundgame($data, $result)
    {
        $boundgame = [];
        $data['boundgameid'] = array_values($data['boundgameid']);
        foreach ($data['boundgameid'] as $key => $id){
            $boundgame[$key] = array(
                'boundgameid' => $id,
                'result' => isset($result['boundgameid'][$key]) ? $result['boundgameid'][$key] : 0,
                'name' => $this->appealModel->getGameName($id)
            );
        }
        return $boundgame;
    }

    /**
     * 手动校验用户提供给客服的密码是否正确
     */
    public function md5Password(){

        if($this->request->isAjax()){
            $status = 0;
            $uid = (int)input('post.uid');
            $password = input('post.password');
            $password = auth_code($password, 'ENCODE');
            $historyinfo = model('Common/MemberHistory')->where('userid',$uid)->field('password')->select(); //历史信息
            foreach ($historyinfo as $history){
                if($password == $history['password']){
                    $status = 1;
                }
            }
            if ($status == 1) {
                $this->success('密码正确');
            }else{
                $this->error('密码错误');
            }

        }
        
    }
    
    //发送申诉结果信息
    public function send(){

        $id = (int)$this->request->param('id', 0, 'intval');
        if ( ! $id ) {
            $this->error('数据不存在');
        }

        $status = (int)$this->request->param('status', 0, 'intval');
        
        $systemScore = $this->settingModel->where('name','MARK_APPEAL')->value('value');
        
        $info = $this->appealModel->where(['id' => $id])->find();
        
        if (!$info) {
            $this->error('未查询到该申诉信息');
        }
    
        $certificate = '';
        $code = $info['code'];
        $mail = $info['mail'];
        $mobile = $info['mobile'];
        $username = $info['username'];
        $score = $info['score'];
        
        
        $condition['code'] = $code;
        $time = $info['notice_time'];
        $is_finish = $info['is_finish'];
        if(!empty($is_finish)){
            $this->error('通知已发送');
        }
        if(NOW_TIMESTAMP < $time){
            $this->error('未到可通知时间');
        }

        if($status == 0 || $status == 4){
            $data['operate_time'] = NOW_TIMESTAMP;
            $data['operate_id'] = session('ADMIN_ID');
        }
        
        if(empty($status)){
            if($score >= $systemScore){
                $certificate = rand(111111, 999999);
                $status = 1;
            }else{
                $status = 2;
            }
        }

        if($status == 2 || $status == 4){
            $data['is_finish'] = 1;
        }
        if ($status == 4){
            $remark = $this->request->param('remark', '', 'trim');
            if (!empty($remark)) $data['remark'] = $remark;
        }

        $data['operate_id'] = mg_get_current_admin_id();
        $data['status'] = $status;
        
        if (isset($certificate)) {
            $data['certificate'] = $certificate;
        }
        
        if (model('Common/CscAppeal')->updateAppeal($data, $info['id']) === false ) {
            $this->error('操作失败');
        }

        if($mail){
            $reg = '/^\w+([\.-]?\w+)*@\w+([\.-]?w+)*(\.\w{2,3})+$/';
            if(0 == preg_match($reg, $mail)) {
                $this->error('邮箱格式不正确');
            }else{
                $result = (new \app\common\library\Mail)->sendAppealCodeMail($username, $code, $mail, '', $certificate, $status);
                
            }
        }
        if($mobile){
            $preg = '/^1[345789]{1}\d{9}$/';
            if (0 == preg_match($preg, $mobile)) {
                $this->error('手机格式错误');
            }else{
                $result = (new \app\common\library\Sms)->sendAppealCode($username, $code, $mobile, '', $certificate, $status);
            }
        }
        
        if(isset($result) && $result['status']){
            $this->success('操作成功');
            $message = '玩家'.$username.'账号申诉成功';
        }else{
            $this->error($result['msg'], url('appeal'));
            $message = '玩家'.$username.'账号申诉失败';
        }
    }

    // 驳回
    public function reject(){
        $this->send();
    }

    // 重新计算未发送状态下的申诉总得分
    public function setAppealScore()
    {
        $data = $this->appealModel->where(['status'=>0,'score'=>['gt',0],'reason'=>['in','1,2']])->select();
        foreach ($data as $key => $value) {
            $score = $this->appealModel->getAppealScore($value);
            $result = $this->appealModel->where('id',$value['id'])->setField('score', $score);
        }
    }

    //查看用户名
    public function checkUsername($id,$code)
    {
        /*$id = $this->request->param('id', 0, 'intval');
        $code = $this->request->param('code', 0, 'intval');*/
        if (empty($id) || empty($code)){
            $this->result('',0,'参数错误！');
        }
        $info = $this->appealModel->field('username,code')->where(['id'=>$id])->find();

        // 记录操作日志
        $this->insertLog($this->current_node,'查看账号：'.$info['username'].'，申诉编号：'.$info['code'],$code);

        $this->result($info['username'],1,$info['username']);
    }

    // 申诉列表 - 查看用户名
    public function checkUsername_1($id){
        $this->checkUsername($id,111);
    }

    // 被盗嫌疑列表 - 查看用户名
    public function checkUsername_2($id){
        $this->checkUsername($id,112);
    }

    /**
     * 修改申诉驳回备注
     */
    public function changeAppealRemark(){
        $remark = $this->request->param('remark', '', 'trim');
        $id = (int)$this->request->param('id', '0', 'intval');
        if (!$id) $this->error('参数错误！');

        if (empty($remark)) $this->success('');

        if (model('Common/CscAppeal')->updateAppeal(['remark'=>$remark], $id) === false ) {
            $this->error('操作失败');
        }
        $this->success('操作成功');
    }
}