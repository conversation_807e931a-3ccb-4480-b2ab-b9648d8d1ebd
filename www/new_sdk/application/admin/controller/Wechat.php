<?php
/**
 * 公众号管理控制器
 * 
 */
namespace app\admin\controller;

use think\Config;
use think\Db;
use tree\Tree;
use think\Env;

class Wechat extends Admin
{
    public function _initialize() {
        parent::_initialize();
    	$this->appid = Env::get('appid');
        $this->appsecret = Env::get('appsecret');

        $this->wxMenuModel = model('WxMenu');
    }

    public function getAccessToken() {
		$redis = \think\Cache::store('default');
        $redisKey = "weixinaccesstoken".$this->appid;
        $WeixinAccessTokenCache = trim($redis->get($redisKey));
        if($WeixinAccessTokenCache){
            return $WeixinAccessTokenCache;
        } else{
            $url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid='.$this->appid.'&secret='.$this->appsecret;
            $resjson = file_get_contents($url);
            $obj = json_decode($resjson);
            if($obj->access_token){
                $redis->set($redisKey, $obj->access_token, 30);
                return $obj->access_token;
            }
            if($obj->errcode){
                return $obj->errmsg;
            }
        }
    }

    public function menu() {
        // 排序
        $this->listOrders($this->wxMenuModel);
        $result     = model('WxMenu')->order(["list_order" => "ASC"])->select();
        $tree       = new Tree();
        $tree->icon = ['&nbsp;&nbsp;&nbsp;│ ', '&nbsp;&nbsp;&nbsp;├─ ', '&nbsp;&nbsp;&nbsp;└─ '];
        $tree->nbsp = '&nbsp;&nbsp;&nbsp;';

        $newMenus = [];
        foreach ($result as $m) {
            $newMenus[$m['id']] = $m;
        }
		$result2 = array();
        foreach ($result as $key => $value) {
            $result2[$key]['parent_id_node'] = ($value['parent_id']) ? ' class="child-of-node-' . $value['parent_id'] . '"' : '';
            $result2[$key]['style']          = empty($value['parent_id']) ? '' : 'display:none;';

            $str_manage = '';
			if($value['parent_id']==0){
				if (authUI('admin/Wechat/menuEdit')) {
					$str_manage .= '<a href="' . url("Wechat/menuEdit", ["parent_id" => $value['id'], "menu_id" => $this->request->param("menu_id")])
					. '">添加子菜单</a> ';
				}
				else{
					$str_manage .= '<a></a>';
				}
			}
			else{
				$str_manage .= '<a></a>';
			}

            if (authUI('admin/Wechat/menuEdit')) {
                $str_manage .=  '<a href="' . url("Wechat/menuEdit", ["id" => $value['id'], "menu_id" => $this->request->param("menu_id")])
                    . '">编辑</a>  ';
            }

            if (authUI('admin/Wechat/delete')) {
                $str_manage .=  '<a href="javascript:;" onclick="del_info(this,\'' . url("Wechat/menuDel", ["id" => $value['id'], "menu_id" => $this->request->param("menu_id")]) . '\')"> 删除 </a> ';
            }

			if($value['type']=='view'){
				$typeName = '跳转网页';
			}
			else if($value['type']=='click'){
				$typeName = '发送消息';
			}
			else{
				$typeName = '';
			}

            $result2[$key]['status']       = $typeName;
            $result2[$key]['str_manage']   = $str_manage;
			$result2[$key]['parent_id']    = $value['parent_id'];
            $result2[$key]['id']		   = $value['id'];
            $result2[$key]['list_order']   = $value['list_order'];
            $result2[$key]['name']   = $value['name'];
        }
        $tree->init($result2);

        $str      = "<tr id='node-\$id' \$parent_id_node style='\$style'>
                        <td style='padding-left:20px;'><input name='list_orders[\$id]' type='text' size='3' value='\$list_order'  class=\'layui-input\'></td>
                        <td>\$spacer\$name</td>
                        <td>\$status</td>
                        <td>\$str_manage</td>
                    </tr>";
        $category = $tree->getTree(0, $str);
        $this->assign("category", $category);
        return $this->fetch();


    }

    public function menuLoad() {
        if($this->appid) {
            $menu = $this->getMenus();
            $menus = [];
            if($menu) {
                $menu = json_decode($menu, true);
                $menus = $menu['menu']['button'];
            }
		//	var_dump($menus);
            if($menus) {
                model('WxMenu')->where('1=1')->delete();
                $datetime = time();
                $s1 = 0;
                foreach($menus as $menu) {
                    $data = [
                        'parent_id' => 0,
                        'name' => $menu['name'],
                        'list_order' => $s1,
                        'updated' => $datetime,
                    ];
                    $data = $this->_menuType($data, $menu);
                    $menuId = model('WxMenu')->insertGetId($data);

                    if(!empty($menu['sub_button']) && $menuId) {
                        $s2 = 0;
                        foreach ($menu['sub_button'] as $subMenu) {
                            $d = [
                                'parent_id' => $menuId,
                                'name' => $subMenu['name'],
                                'list_order' => $s2,
                                'updated' => $datetime,
                            ];
                            $d = $this->_menuType($d, $subMenu);
                            model('WxMenu')->insertGetId($d);
                            ++$s2;
                        }
                    }
                    ++$s1;
                }
				$this->success("操作成功");
            }
			$this->error("操作失败");
        }
        $this->error('请选择公众号');
    }

    private function _menuType($data, $menu) {
        if(isset($menu['type'])) {
            $t = $data['type'] = $menu['type'];
            if($t == 'click') {
                $data['key'] = $menu['key'];
            } else if($t == 'view') {
                $data['url'] = $menu['url'];
            } else if($t == 'media_id') {
                $data['media_id'] = $menu['media_id'];
            }
        }
        return $data;
    }

    private function getMenus(){
        $accessToken = $this->getAccessToken();
        $url =  'https://api.weixin.qq.com/cgi-bin/menu/get?access_token='.$accessToken;
        return file_get_contents($url);
    }

    public function menuEdit() {
        $id = $this->request->param("id", 0, 'intval');
        $appId = $this->appid;
        $parentId = $this->request->param("parent_id", 0, 'intval');

        if(!$appId)
            return false;
        if($id) {
            $data = model('WxMenu')->find($id);
            if ($data['key']) {
                $menuEvent = model('WxMenuEvent')->getEventByKey($data['key']);
                $data['content'] = $menuEvent['event_content'];
            }
			else{
				$data['content'] = '';
			}
        }
		else{
			if(!model('WxMenu')->checkNum($parentId)) {
				if($parentId){
					$this->error('该菜单已添加5个二级菜单，无法继续添加');
				}
				else{
					$this->error('已添加3个一级菜单，无法继续添加');
				}
			}

			$data = array();
			$data['name'] = '';
			$data['parent_id'] = 0;
			$data['id'] = 0;
			if($parentId){
				$data['url'] = '';
				$data['content'] = '';
				$data['type'] = 'click';
			}
		}
        $this->assign(compact('id', 'data', 'parentId'));
        return $this->fetch();
    }

    public function menuSave() {
        $id = $this->request->param("id", 0, 'intval');
        $data = $_POST;
        $data['name'] = $data['name'];
        $data['updated'] = date('Y-m-d H:i:s');
        if($id) {
			if(isset($data['type']) && $data['type']){
				$Content = $data['content'];
				unset($data['content']);
			}
			unset($data['parent_id']);
            if(model('WxMenu')->update($data) !== false) {
                if (isset($data['type']) && $data['type']=='click') {
                    $params = array();
                    $params['id'] = $id;
                    $params['key'] = 'click_event_key_' . $id;
                    model('WxMenu')->update($params);

                    $menuEvent = model('WxMenuEvent')->where(array('event_key' => 'click_event_key_' . $id))->find();
                    if ($menuEvent) {
                        $params = array();
                        $params['event_content'] = $Content;
                        
                        model('WxMenuEvent')->where(array('id' => $menuEvent['id']))->update($params);

                    } else {

                        $params = array();
                        $params['event'] = 'click';
                        $params['event_key'] = 'click_event_key_' . $id;
                        $params['event_content'] = $Content;
                        model('WxMenuEvent')->insertGetId($params);
                    }
                }
				$this->success('修改成功', url('Wechat/menu'));
            }
        } else {
            if(!model('WxMenu')->checkNum($data['parent_id'])) {
				if($data['parent_id']){
				$this->error('该菜单已添加5个二级菜单，无法继续添加');
				}
				else{
					$this->error('已添加3个一级菜单，无法继续添加');
				}
            }
			if(isset($data['type']) && $data['type']){
				$Content = $data['content'];
				unset($data['content']);
			}
            if($id = model('WxMenu')->insertGetId($data)){
                if (isset($data['type']) && $data['type']=='click') {
                    $params = array();
                    $params['id'] = $id;
                    $params['key'] = 'click_event_key_' . $id;
                    model('WxMenu')->update($params);

                    $params = array();
                    $params['event'] = 'click';
                    $params['event_key'] = 'click_event_key_' . $id;
                    $params['event_content'] = $Content;
                    model('WxMenuEvent')->insertGetId($params);
                }
				$this->success('添加成功', url('Wechat/menu'));
            }
        }
		$this->error('操作失败');
    }

    public function menuDel() {
	    $id = $this->request->param("id", 0, 'intval');
        $count = model('WxMenu')->where(["parent_id" => $id])->count();
        if ($count > 0) {
            $this->error("该菜单下还有子菜单，无法删除！");
        }
        if($id && $menu = model('WxMenu')->find($id)) {
            if(model('WxMenu')->where(['id'=>$id])->delete()) {
				$this->success('删除成功');
            }
        }
		$this->error('操作失败');
    }

    public function menuSend() {
        $appId = $this->appid;
        if($appId) {

			$TopMenus = model('WxMenu')->where(['parent_id'=>0])->order('list_order asc,id asc')->select();
            foreach($TopMenus as $TopMenu) {
				$num = model('WxMenu')->where(['parent_id'=>$TopMenu['id']])->count();
				if(!$num) {
					$this->error('存在一级菜单未配置子菜单,不能发布!');
				}
			}

            $data = model('WxMenu')->getAppMenu();
            $menus = [];
            foreach($data as $d) {
                $menus[$d['parent_id']][] = $d;
            }

            $buttons = [];
            foreach($menus[0] as $m) {
                $menu = [
                    'name' => urldecode($m['name']),
                ];
                $menu = $this->_menuType($menu, $m);
                if(!$m['type']) {
                    foreach($menus[$m['id']] as $mm) {
                        $menu2 = [
                            'name' => urldecode($mm['name']),
                        ];
                        $menu2 = $this->_menuType($menu2, $mm);
                        $menu['sub_button'][] = $menu2;
                    }
                }
                $buttons['button'][] = $menu;
            }

            if(empty($buttons)) {
                $url = 'https://api.weixin.qq.com/cgi-bin/menu/delete?access_token='.$this->getAccessToken($appId);
                $resobj = apipost($url,[],false,true);
            } else {
                $url = 'https://api.weixin.qq.com/cgi-bin/menu/create?access_token='.$this->getAccessToken($appId);
                $params = json_encode($buttons, JSON_UNESCAPED_UNICODE);
                //echo $params; exit;
                $resobj = apipost($url,$params,false,true);
            }
            $obj = json_decode($resobj, true);
            if($obj['errcode'] != 0) {
                $this->error($obj['errmsg']);
            }
			$this->success('发送到公众号成功');
        }
		$this->error('请选择公众号');
    }
}