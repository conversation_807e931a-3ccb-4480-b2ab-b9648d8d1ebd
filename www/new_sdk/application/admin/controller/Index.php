<?php

namespace app\admin\controller;

use app\admin\model\MenuModel;

/**
 * 后台index控制器
 * <AUTHOR>
 *
 */
class Index extends Admin
{
    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 首页显示处理
     * @return mixed|string
     */
    public function index()
    {
        $menuModel = new MenuModel();
        $menus     = $menuModel->menuTree();
        $wk_isShow =  cmf_auth_check(session('ADMIN_ID'),'admin/workstation/index');//判断是否有工作台权限
        $this->assign("menus", $menus);
        $this->assign("wk_isShow", $wk_isShow);
        $this->assign('admin_info', $this->admin_info);

        return $this->fetch();
    }
}
