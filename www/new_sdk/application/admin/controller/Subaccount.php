<?php

/**
 * 玩家游戏账号控制器
 *
 * Created by PhpStorm.
 * User: Admin
 * Date: 2018/5/10
 * Time: 10:27
 */

namespace app\admin\controller;

use think\Db;
use app\common\model\Game as GameModel;
use app\common\model\GameInfo;
use app\common\model\Logininfo;
use app\common\model\AndroidSdk;
use app\common\model\AutoPackage;
use app\common\library\ValidateExtend;


class Subaccount extends Admin
{
    protected $gameModel;
    protected $sdkgamelistModel;
    protected $gameList;

    protected function _initialize()
    {
        parent::_initialize();
        $this->gameModel        = new GameModel;
        $this->sdkgamelistModel = $this->cyModel('sdkgamelist');

        $this->gameList = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$selfGameList = array();
		foreach ($tmpSelfGameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
		$this->selfGameList  = $selfGameList;
    }

    /**
     * 换绑记录列表
     * @return mixed
     */
    public function index()
    {
        $game_id				= input('game_id',0,'intval');
        $account				= input('account','','trim');
        $start_time				= input('start_time','','trim');
        $end_time				= input('end_time','','trim');
        $sub_username			= input('sub_username','','trim');

        $condition				 = [];
		if(input('account')){
			$condition['m.username']	= $account;
		}
		if(input('sub_username')){
			$condition['s.sub_username']  = $sub_username;
		}
		if(input('game_id') <> ''){
			$condition['s.game_id']	= $game_id;
		}
	    //申请开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $condition['s.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $condition['s.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $condition['s.create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }

		//查询参数
        $param = input('get.');

		$subaccountList = model('Subaccount')->alias('s')
							->join('cy_members m', 's.member_id = m.id')
							->join('cy_game g', 's.game_id = g.id')
							->where($condition)
							->field("s.id,s.member_id as userid,m.username,s.sub_username,s.game_id,g.name as game_name,s.create_time,s.auth_type,s.real_name,s.idcard_num,s.last_auth_time,s.auth_pi,s.auth_status")
							->order("id desc")
							->paginate(10, false, ['query' => $param]);

        // 获取分页显示
        $page = $subaccountList->render();
        // dump($subaccountList);

        $this->assign('game_list', $this->selfGameList);
        $this->assign('page', $page);
        $this->assign('subaccountList', $subaccountList);

        return $this->fetch();
    }
}