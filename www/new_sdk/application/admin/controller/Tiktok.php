<?php
/**
 * 前台注册用户管理控制器
 */

namespace app\admin\controller;

use app\common\model\Members as MembersModel;
use app\common\model\MembersTwo;
use think\Db;
use app\common\logic\Member as MemberService;
use app\common\library\MakeReport;
use app\common\library\FileUpload;
use think\Exception;

class Tiktok extends Admin
{
    public function __construct()
    {
        parent::__construct();
        $tmpSelfGameList = model('Common/Game')->alias('g')->field('g.id,g.name')
            ->join('cy_gameinfo cgi', 'g.id=cgi.game_id')
            ->where(['platform' => 0])->order('id desc')->select();

        $selfGameList = array();
        foreach ($tmpSelfGameList as $game) {
            $selfGameList[$game['id']] = $game;
        }
        $game_list = array(array('id' => 0, 'name' => ''));
        $game_list = array_merge($game_list, $selfGameList);
        $this->assign('game_list', $game_list);
    }

    public function index()
    {
        $game_id = input('request.game_id', 0, 'intval');


        $where = [];
        //用户ID
        if ($game_id) {
            $where['game_id'] = $game_id;
        }

        $list = model("TiktokPush")->alias('t')
            ->field("t.*,g.name as gameGame,c.name as channelName")
            ->join('cy_game g', 'g.id=t.game_id', 'left')
            ->join('nw_channel c', 'c.id=t.channel_id', 'left')
            ->where($where)
            ->order('t.id desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];
        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    public function add()
    {
        $channel = model('channel')->where(['level' => 3, 'status' => 1])->field('id,name')->select();
        $this->assign('channel', $channel);
        return $this->fetch();
    }

    public function save()
    {
        $game_id = input('game_id', 0, 'intval');
        $select = input('select', '');

        $ids = explode(',', $select);
        if (!$ids) {
            $this->error('推广id不存在');
        }
        $channel_ids = model('TiktokPush')->where('game_id', $game_id)->whereIn('channel_id', $ids)->column('channel_id');

        if ($channel_ids) {
            $ids = array_diff($ids, $channel_ids);
        }

        if(!$ids){
            $this->error('推广id已存在');
        }

        $data = [];
        foreach ($ids as $k => $v) {

            $data[] =[
                'game_id'=>$game_id,
                'channel_id'=>$v,
            ];
        }

        if(model('TiktokPush')->insertAll($data)){
            $this->success('保存成功', 'tiktok/index');
        }else{
            $this->error('保存成功');
        }

    }

    // 删除
    public function del()
    {
        $id = $this->request->param('id',0,'intval');
        if (!$id){
            $this->error('ID不能为空');
        }

        $res = model('TiktokPush')->where('id',$id)->delete();

        if ($res) {
            $this->success('删除成功', url('index'));

        }

        $this->error('删除失败');

    }

}
