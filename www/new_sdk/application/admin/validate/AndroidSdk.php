<?php
/**
 * Created by PhpStorm.
 * User: cqingt
 * Date: 2018/6/27
 * Time: 10:19
 */
namespace app\admin\validate;

use app\common\library\ValidateExtend;
use think\Db;

class AndroidSdk extends ValidateExtend
{
    protected $rule = [
        'version'    => 'require|checkVersion',
        'num'        => 'require|positiveInteger',
        'filename'   => 'require',
        'content'    => 'checkContent',
    ];

    protected $message = [
        'version.require'    => '版本号不能为空',
        'version.checkVersion' => '版本号不能比上一个版本小',
        'num.require'        => '构造号不能为空',
        'num.positiveInteger'=> '构造号只能是大于0的整数',
        'filename.require'   => '文件地址不能为空',
        'content'            => '更新内容超过500字',
        'action.require'     => '名称不能为空',
        'action.unique'      => '同样的记录已经存在!',
    ];

    protected $scene = [
        'add'  => ['version', 'num', 'content'],
        'edit' => [ 'num','content'],
    ];

    /**
     * 检测更新内容字数,500以内
     * @param $value
     * @return boolean
     */
    public function checkContent($value)
    {
        return mb_strlen($value) <= 500;
    }

    /**
     * 检测版本号是否比上一个版本小
     * @param $value
     * @return bool
     */
    public function checkVersion($value)
    {
        $lastVersion = Db::name('nw_android_sdk_config')->order('id', 'desc')->value('version');

        return version_compare($value, $lastVersion) == 1;
    }
}