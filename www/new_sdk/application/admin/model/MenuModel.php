<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 老猫 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\admin\model;

use think\Db;
use think\Model;

class MenuModel extends Model
{
    protected $table = 'nw_admin_auth_rule';

    /**
     * 按父ID查找菜单子项
     * @param int $parentId 父菜单ID
     * @param boolean $withSelf 是否包括他自己
     * @return mixed
     */
    public function adminMenu($parentId, $withSelf = false)
    {
        //父节点ID
        $parentId = (int)$parentId;

        // 菜单显示 管理员id为1
        if (mg_get_current_admin_id() == 1) {
            $result   = $this->where(['parent_id' => $parentId, 'status' => 1])->order(["list_order" => "ASC"])->select();
        } else {
            $roleIds = Db::name('nw_admin_role_user')->where(['user_id' => mg_get_current_admin_id()])->column('role_id');
            $authInfo = Db::name('nw_admin_role')->where(['id' => ['in', $roleIds]])->column('auth_ids');

            $authIds = [];
            foreach ($authInfo as $info) {
                $infoArr = explode(',', $info);
                foreach ($infoArr as $authId) {
                    array_push($authIds, $authId);
                }
            }

            $result   = $this->where(['parent_id' => $parentId, 'status' => 1, 'id' => ['in', $authIds]])->order(["list_order" => "ASC"])->select();
        }

        if ($withSelf) {
            $result2[] = $this->where(['id' => $parentId])->find();
            $result    = array_merge($result2, $result);
        }

        //权限检查
        if (mg_get_current_admin_id() == 1) {
            //如果是超级管理员 直接通过
            return $result;
        }

        $array = [];

        foreach ($result as $v) {
            list($app, $controller, $action) = explode('/', $v['auth_name']);
            //public开头的通过
            if (preg_match('/^public_/', $action)) {
                $array[] = $v;
            } else {
                if (preg_match('/^ajax_([a-z]+)_/', $action, $_match)) {
                    $action = $_match[1];
                }
/*
                $rule_name = $v['auth_name'];

                if (cmf_auth_check(mg_get_current_admin_id(), $rule_name)) {*/
                    $array[] = $v;
                //}
            }
        }

        return $array;
    }

    /**
     * 获取菜单 头部菜单导航
     * @param string $parentId 菜单id
     * @return mixed|string
     */
    public function subMenu($parentId = '', $bigMenu = false)
    {
        $array   = $this->adminMenu($parentId, 1);
        $numbers = count($array);
        if ($numbers == 1 && !$bigMenu) {
            return '';
        }
        return $array;
    }

    /**
     * 菜单树状结构集合
     */
    public function menuTree()
    {
        $data = $this->getTree(0);
        return $data;
    }

    /**
     * 取得树形结构的菜单
     * @param $myId
     * @param string $parent
     * @param int $Level
     * @return bool|null
     */
    public function getTree($myId, $parent = "", $Level = 1)
    {
        $data = $this->adminMenu($myId);
        $Level++;
        if (count($data) > 0) {
            $ret = NULL;
            foreach ($data as $a) {
                list($app, $controller, $action) = explode('/', $a['auth_name']);

                $id         = $a['id'];
                $name       = $app;
                $controller = ucwords($controller);
                //附带参数
                $params = "";
                if ($a['param']) {
                    $params = "?" . htmlspecialchars_decode($a['param']);
                }
                $array = [
                    "icon"   => $a['icon'],
                    "id"     => $id . $name,
                    "name"   => $a['name'],
                    "parent" => $parent,
                    "tab_type"=> $a['tab_type'],
                    "url"    => url("{$controller}/{$action}{$params}")
                ];


                $ret[$id . $name] = $array;
                $child            = $this->getTree($a['id'], $id, $Level);
                //由于后台管理界面只支持三层，超出的不层级的不显示
                if ($child && $Level <= 3) {
                    $ret[$id . $name]['items'] = $child;
                }

            }
            return $ret;
        }

        return false;
    }

    public function menu($parentId, $with_self = false)
    {
        //父节点ID
        $parentId = (int)$parentId;
        $result   = $this->where(['parent_id' => $parentId])->select();
        if ($with_self) {
            $result2[] = $this->where(['id' => $parentId])->find();
            $result    = array_merge($result2, $result);
        }
        return $result;
    }

    /**
     * 得到某父级菜单所有子菜单，包括自己
     * @param number $parentId
     */
    public function get_menu_tree($parentId = 0)
    {
        $menus = $this->where(["parent_id" => $parentId])->order(["list_order" => "ASC"])->select();

        if ($menus) {
            foreach ($menus as $key => $menu) {
                $children = $this->get_menu_tree($menu['id']);
                if (!empty($children)) {
                    $menus[$key]['children'] = $children;
                }
                unset($menus[$key]['id']);
                unset($menus[$key]['parent_id']);
            }
            return $menus;
        } else {
            return $menus;
        }

    }

}
