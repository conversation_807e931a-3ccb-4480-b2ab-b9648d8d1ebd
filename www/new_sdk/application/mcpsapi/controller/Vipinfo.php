<?php
/**
 * 游戏管理控制器
 */

namespace app\mcpsapi\controller;

use think\Db;
use think\Exception;
use app\common\logic\Member as MemberService;

class VipInfo extends Guild
{
    //游戏平台
    protected $_platform = [
        0 => '安卓',
        1 => 'IOS'
    ];
    //资讯类型
    protected $_infoType = [
        1 => '资讯',
        2 => '麻花网络',
        3 => '测评',
        4 => '攻略',
        5 => '视频'
    ];
    protected function _initialize()
    {
        parent::_initialize();
        $this->articleModel = Db::name('cy_article');
        $this->timeLine = config('ARTICLE_AND_CHANNEL_TIMELINE');
        $this->_channelid = session('guild_info')['channel_id'];
        $this->_menuLimit = model('Channel')->where(['id'=>$this->_channelid])->field('point_show,taptap,parent_id')->find();
    }
    /**
     * 获取最新5条公告
     * @return [type] [description]
     */
    public function getArticleList()
    {
        $list_rows  = input('pageSize',10);
        $page       = input('page',1);
        if (!isset($list_rows) || empty($list_rows)) {
            $list_rows = 10;
        }

        //公告 取为5条
        $articleList = Db::name('nw_game_article')->alias('a')
            ->where(['a.is_show' => 1, 'a.show_time' => ['LT', request()->time()] ])
            ->order('a.create_time', 'desc')
            ->paginate(['list_rows'=>$list_rows,'page'=>$page])->toArray();
        $readList = Db::name('nw_game_article_read')
                ->where(['channel_id'=>$this->_channelid])
                ->column('game_article_id','id');
        if ($articleList) {
            foreach ($articleList['data'] as $key => $value) {
                $articleList['data'][$key]['show_time'] = date("Y-m-d",$value['show_time']);
                if (in_array($value['id'], $readList)) {
                    $articleList['data'][$key]['status'] = 1;
                }else{
                    $articleList['data'][$key]['status'] = 0;
                }
            }
            return json(['data'=>$articleList,'code'=>20000,'msg'=>'获取公告成功']);
        }
        return json(['data'=>[],'code'=>20000,'msg'=>'暂无公告']);
    }
    /**
     * 获取所有未读公告
     * @return [type] [description]
     */
    public function getUnreadArticleList()
    {
        //所有公告
        $articleList = Db::name('nw_game_article')->alias('a')
            ->where(['a.is_show' => 1, 'a.show_time' => ['LT', request()->time()] ])
            ->order('a.create_time', 'desc')->select();
        $readList = Db::name('nw_game_article_read')
                ->where(['channel_id'=>$this->_channelid])
                ->column('game_article_id','id');
        $out = [];    
        foreach ($articleList as $key => $value) {
            if (!in_array($value['id'], $readList)) {
               $out[] = $value;
            }
        }
        return json(['data'=>$out,'code'=>20000,'msg'=>'获取未读公告成功']);
    }
    /**
     * 查看公告
     * @return [type] [description]
     */
    public function seeAoYouNotice(){
        $id = input('id',0,'intval');
        if (empty($id)){
            return json(['data'=>[],'code'=>10017,'msg'=>'公告参数错误']);
        }
        // 记录 该渠道已读了该 游戏活动
        $data = ['game_article_id'=>$id,'channel_id'=>$this->_channelid];
        $res = Db::name('nw_game_article_read')->where($data)->find();
        if (empty($res)){
            $data['create_time'] = time();
            Db::name('nw_game_article_read')->insertGetId($data);
            return json(['data'=>[],'code'=>20000,'msg'=>'添加阅读记录成功']);
        }
        return json(['data'=>[],'code'=>20000,'msg'=>'已阅读过该公告']);
    }
    /**
     * 获取资讯列表
     * @return [type] [description]
     */
    public function getInfoList()
    {
        $list_rows         = input('pageSize',10);
        $page              = input('page',1);
        $where             = $this->_getCondition();
        $infoList = Db::name('cy_article')->alias('article')->field(['article.zhiding','article.gameId','article.id','article.title','article.type','article.create_time','content.content'])->where($where)->join('cy_content content', 'article.id=content.id','left')->order('article.zhiding desc,article.id desc')->paginate(['list_rows'=>$list_rows,'page'=>$page])->toArray();
        if ($infoList) {
            foreach ($infoList['data'] as $key => $value) {
                $infoList['data'][$key]['create_time'] = date("Y-m-d H:i:s",$value['create_time']);
                $infoList['data'][$key]['type'] = $this->_infoType[$value['type']];
            }
            return json(['data'=>$infoList,'code'=>20000,'msg'=>'获取资讯成功','dataList'=>$this->gameList()]);
        }
        return json(['data'=>[],'code'=>20000,'msg'=>'暂无资讯','dataList'=>$this->gameList()]);
    }
    /**
     * 获取搜索菜单列表
     * @param string $value [description]
     */
    public function getInfoMenu()
    {
        $meue = [];
        $meue['game_platform'] = $this->_platform;
        $meue['infoType'] = $this->_infoType;
        $meue['game_list']     = $this->gameList();
        return json(['data'=>$meue,'code'=>20000,'msg'=>'获取搜索菜单列表成功']);
    }
    /**
     * 获取游戏列表
     * @return mixed
     */
    public function gameList(){
        return model('Common/Game')->getAllByCondition('id,name',['game_kind'=>2,'cooperation_status'=>['neq',0]],'','self');
    }
    // 公共搜索条件
    protected function _getCondition()
    {
        $type     = input('type', 0, 'intval');//资讯类型
        $gameid   = input('gameid', 0, 'intval');//游戏id
        $title    = input('title', '', 'trim');//标题
        // $status   = input('status', 0, 'intval');//阅读类型
        $condition = [];
        $condition['isdelete'] = 0;
        if (! empty($title)) {
            $condition['article.title'] = ['LIKE', '%' . $title . '%'];
        }

        if (! empty($type)) {
            $condition['article.type'] = $type;
        }

        // if (! empty($status)) {
        //     $condition['article.type'] = $status;
        // }

        if (! empty($gameid)) {
            $condition['article.gameid'] = $gameid;
        }    

        return $condition;
    }

    public function getChannelInfo(){
        // 渠道id
        $channelid = $this->_channelid;
        // 获取渠道信息
        $channelInfo = Db::name('nw_channel')->where(['id'=>$channelid])->find();
        return $channelInfo;
    }

    public function getGameList(){
        return Db::name('cy_game')->field('id,name')->order('name asc')->select();
    }

    protected function _getTypy(){
        return Db::table('cy_gametype')->column('name', 'id');
    }

}
