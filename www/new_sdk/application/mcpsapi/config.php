<?php
/**
 * api模块配置文件
 *
 */
return [
    // +----------------------------------------------------------------------
    // | 自定义设置
    // +----------------------------------------------------------------------

    //支付宝配置(使用麻花网络SDK中的新支付宝账号的参数)
    'alipayAop'    => [
        //应用ID,您的APPID。
        'app_id' => "2021001189642488",

        //商户私钥
        'merchant_private_key' => "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCgboqWARRnPhV3ZT/cdRUOOk8QH+6b5k0iu4KlVRyeb2z1V69cOmcd2d9pDvuF0YShZHViQAax2yG2z5KjTgEax6obOWdEjn80Nn6kn4evtMqZyQfJFsJSfOrf6mhHBgBIfX9Wc0lKXeb1B0O6xWnrzqYpqjqTl3pVkskegw1sAUfTvyZD4KKOvhofCQChyX/6Iszidw59gUGfPBrvH8VgiFI2arrz+m9ghxuBW9gskiAZEvoMNrV+ZsTfN3hV2WDZXR8xsw6fswhru9Qf3pOIPiffrXR8P8vf6dcURTmCdABXDRe9JhRdUOK4Apu30Ggds4EFdhJdQd6PjRd9fPAhAgMBAAECggEBAJIdu7j9SMDzSYfRvoUiWcf4kVPFUYPKEEzm7Ql56DPgliMp9nwQcSQ97/12eNybQ6neng6a873/L8iZaxuzCsyCN0rOUGCfS/mY/GUhKup7xd6kuqOKR/fq79x6WPj+3Tkom9Z377N8rMJUGHq+HEd5kdr2wJz15KSVqPlSMOJBHL2hluUn0+Ae/D/HMANcYbX/nMzJRnu5WmHiYWv2Wsj5hI4W+tBp8giKrkCWw6xiBbvFxvXwODKe3UfQ69zL+5K9Jb+6RfDIqOJkjHQ9/W9LlIcNiBzATzWGTuQjV2GkfLTmiO52CPx4t4goHk6fUvg8sBv07frlcZjiVZ/fueUCgYEA9Cv9o1ymzarNc4M7MKL2Xfosv9o7a3IfeDF/EGBG1ZWlsiNo7PB7h79kTv4hFgq70s4B4bm7byDLzfXIQxWdhgFd2FjfXitady7Tx6QrGGoSwRx7iFdrUh/TkQNkoZ8XtRMGN0S2o0P1I/W40io+SzB96s3g8rS+WDlFqertGc8CgYEAqDQUIWIkgc909RnJ14QKXWu/keVtoItF9/U4sFgHFhhiY6dowGYlS4HXUK6yirTEePSAc/1x3mUW1SNeMxp3aK3D/lbj7+H7AI5XmFYjFFVMiK4/boeIaYNms5xsltrFOp1USbG8Majot4dFLL057SrVa22l77jUyeYMJQrFAw8CgYEA4hZVnc0tmmRcJmhOB0Pt0ajThLXCrUOXxgg2umgbTqtpKKBHCJXIHDLo44fGBQ3dSl18OpFq74QzkjrRuuQp3qonRitMcHq+IntWl5X4XXvib7M640zmz70ufijhJhIUtpKt/8D9SAGl3C0rfFanJaH6Bc+qSbF4SmKFkcc40dsCgYBtbfxOv7f6kAqKTy3GvYu0vZ3TgEzzLsjlia9I4uwxTsnsBYUb7xiCaEW5ov5bSUGtY3ySeaf0RRY2shzd6HEZNDuXo9YFQaL3+KM6Y4fM+bWiEqMH+Lsh0jAl2DW5azj3alXk3/IM6NQGWsYBVlZyWvSgQKS9MiWV+Mmb68bCtwKBgQDvXhfl0f5K08dIj/BQCNg8FdLYZw1IK+4h3auesY0ShJs9sEkHIClXV2b520yhDl/LPWTwXAE9ax09ex0v4D+VfoeLIAxihtaumW8+5yN1Y6gErqWyuVEOep8lKJMwVuhPqs627nOCT4fsaJeF/lJ3Ucf7qAVe8Qvfehzm+TMF6Q==",
        
        //异步通知地址
        'notify_url' => "http://mcpsapi.weilongwl.com/Recharge_notify/notify_url.html",
        
        //同步跳转
        'return_url' => "http://mcps.weilongwl.com/#/substitutefor/subRecharge",

        //编码格式
        'charset' => "UTF-8",

        //签名方式
        'sign_type'=>"RSA2",

        //支付宝网关
        'gatewayUrl' => "https://openapi.alipay.com/gateway.do",

        //支付宝公钥,查看地址：https://openhome.alipay.com/platform/keyManage.htm 对应APPID下的支付宝公钥。
        'alipay_public_key' => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlzeAjrzLR8rJ6EOlb2AP4R4gztO/1EWh4zTqL/rO36CORsqQWWqiYflrCy2zRgoxjSUcs/bbpIz/V1pkVvt7hC0lMqeWwNEYn+KjpP1nITbifBvqIU6nJoXV4egvjF3PeCkQjwRe9ujmGKY5yyJv3ux8rzojeMreOAnrnM0qm9Q2ZCd8bcf3gRtHQdVL6vUZesJfL6uF9YCnS1VcQkZH2gwM84raWs6/h3j9OF+aBOfySA5HxuWymRaq1CcScXPD265hMMj6olqJBTbC5DCP25Hdcx9nr+sTd8r1azdmV106kILKX7lHiPADD/e7hSwC2CHPKkNSGctb9+Kk11NTmQIDAQAB",

    ],
    
    //快接微信h5支付
    'wxpay-h5-kj'   => [
         // 支付后返回地址
        'return_url'=> "http://" . $_SERVER['HTTP_HOST'] . "/recharge_notify/pay_success_kj.html",

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url'=> "http://" . $_SERVER['HTTP_HOST'] . "/recharge_notify/wxpayh5kj.html",
        
        //APP里调起H5支付，需要在webview中手动设置referer
        'referer'   => "http://www.weilongwl.com",
    ],
];
