<?php


use think\Env;

if(!function_exists('GetIP')) {
    // 取得当前IP
    function GetIP($type=0){
        if(!empty($_SERVER["HTTP_CLIENT_IP"])) {
            $cip = $_SERVER["HTTP_CLIENT_IP"];
        } else if(!empty($_SERVER["HTTP_X_FORWARDED_FOR"])) {
            $cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        } else if(!empty($_SERVER["REMOTE_ADDR"])) {
            $cip = $_SERVER["REMOTE_ADDR"];
        } else {
            $cip = "";
        }

        preg_match("/[\d\.]{7,15}/", $cip, $cips);
        $cip = $cips[0] ? $cips[0] : 'unknown';
        unset($cips);
        if ($type==1) $cip = myip2long($cip);
        return $cip;
    }
}

if(!function_exists('myip2long')) {
    //转换IP
    function myip2long($ip){
//		$ip_arr = split('\.',$ip);
        $ip_arr = explode('.', $ip);
        $iplong = (16777216 * intval($ip_arr[0])) + (65536 * intval($ip_arr[1])) + (256 * intval($ip_arr[2])) + intval($ip_arr[3]);
        $iplong = sprintf("%u",$iplong);
        return $iplong;
    }
}

/**
 * 获取游戏icon， 没有图片时，给个默认icon
 * @param $url
 * @return string
 */
function getGameIcon($url,$use_small_icon=1){
    if ( empty($url) ) {
        return  '/static/images/aoyou_game.png';
    }
    
    $smallIconExt = '?x-image-process=style/small';

    if ( strpos($url, 'platform/') === 0 || strpos($url, 'image/') === 0 ) {
        if(substr(STATIC_DOMAIN,-1)=='/' && substr($url,0,1)=='/'){
            return $use_small_icon?STATIC_DOMAIN.'/'.$url.$smallIconExt:STATIC_DOMAIN.'/'.$url;
        }
        else if(substr(STATIC_DOMAIN,-1)<>'/' && substr($url,0,1)<>'/'){
             return $use_small_icon?substr(STATIC_DOMAIN,0,strlen(STATIC_DOMAIN)-1).$url.$smallIconExt:substr(STATIC_DOMAIN,0,strlen(STATIC_DOMAIN)-1).$url;
        }
        else{
            return $use_small_icon?STATIC_DOMAIN.$url.$smallIconExt:STATIC_DOMAIN.$url;
        }
        return $use_small_icon?STATIC_DOMAIN.'/'.$url.$smallIconExt:STATIC_DOMAIN.'/'.$url;
    }

    if(substr(STATIC_DOMAIN,-1)=='/'){
        return $use_small_icon?STATIC_DOMAIN.'image/'.$url.$smallIconExt:STATIC_DOMAIN.'image/'.$url;
    }
    else{
        return $use_small_icon?STATIC_DOMAIN.'/image/'.$url.$smallIconExt:STATIC_DOMAIN.'/image/'.$url;
    }
}
/**
 * 获取游戏下载地址
 * @return string
 */
function getDownload($game) {
    if ($game['filename']) {
        return APK_DOWN_DOMAIN.'/sygame/'.$game['pinyin'].'/'.$game['filename'];
    } else {
        return '';
    }
}
/**
 * 获取开发厂商
 * @return string
 */
function getDeveloper($p_developer='') {
    return $p_developer ? $p_developer : '未知';
}
/**
 * 获取游戏类型
 * @return string
 */
function getTypename($p_type_name='') {
    return $p_type_name ? $p_type_name : '未知';
}
/**
 * 获取游戏题材
 * @return string
 */
function getSubjectname($p_subject_name='') {
    return $p_subject_name ? $p_subject_name : '未知';
}
/**
 * 账号密码加解密
 */
if(!function_exists('auth_code')) {
    function auth_code($string, $operation = 'DECODE', $key = '', $expiry = 0) {
        $ckey_length = 0;

        $key = md5($key ? $key : Env::get('auth_key') );
        $keya = md5(substr($key, 0, 16));
        $keyb = md5(substr($key, 16, 16));
        $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length): substr(md5(microtime()), -$ckey_length)) : '';

        $cryptkey = $keya.md5($keya.$keyc);
        $key_length = strlen($cryptkey);

        $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0).substr(md5($string.$keyb), 0, 16).$string;
        $string_length = strlen($string);

        $result = '';
        $box = range(0, 255);
        // $box = 100;

        $rndkey = array();
        for($i = 0; $i <= 255; $i++) {
            $rndkey[$i] = ord($cryptkey[$i % $key_length]);
        }

        for($j = $i = 0; $i < 256; $i++) {
            $j = ($j + $box[$i] + $rndkey[$i]) % 256;
            $tmp = $box[$i];
            $box[$i] = $box[$j];
            $box[$j] = $tmp;
        }

        for($a = $j = $i = 0; $i < $string_length; $i++) {
            $a = ($a + 1) % 256;
            $j = ($j + $box[$a]) % 256;
            $tmp = $box[$a];
            $box[$a] = $box[$j];
            $box[$j] = $tmp;
            $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
        }

        if($operation == 'DECODE') {
            if((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26).$keyb), 0, 16)) {
                return substr($result, 26);
            } else {
                return '';
            }
        } else {
            return $keyc.str_replace('=', '', base64_encode($result));
        }
    }
}

/**
 * 获取充值方式
 * @return string
 */
function getRechargeType($p_type=1) {
	$rechargeType = "";
    if($p_type == 1){
        $rechargeType = "申请";
    }
	elseif ($p_type == 2){
        $rechargeType = "直充";
    }
	else if($p_type == 3){
		$rechargeType = "结算币充值";
	}
	return $rechargeType;
}
/**
 * 获取充值支付类型
 * @return string
 */
function getRechargePaytype($p_paytype='') {
	$paytype = "";
    if($p_type == 'zfb'){
        $paytype = "支付宝";
    }
	elseif ($p_type == 'wxpay'){
        $paytype = "微信";
    }
	return $paytype;
}
/**
 * 获取充值支付类型
 * @return string
 */
function getRechargeStatus($p_status=0) {
	$rechargeStatus = "";
    if($p_status == 0){
        $rechargeStatus = "审核中";
    }
	elseif ($p_status == 1){
        $rechargeStatus = "已支付";
    }
	elseif ($p_status == 2){
        $rechargeStatus = "审核通过";
    }
	elseif ($p_status == 3){
        $rechargeStatus = "审核不通过";
    }
	return $rechargeStatus;
}

function chkTransferPlayer($channel_id,$account='',$game_id=0){
	$errorCode = 1;
	$errorMsg  = '校验成功';
	$retData	   = array();

	if(!$account || !$game_id){
		$errorCode = 0;
		$errorMsg = "玩家账号与游戏ID不能为空";
	}
	else{
		$memberInfo = model('Members')->where(['username' => $account])->field('id,username,flag')->find();
		$gameInfo = model('Game')->where(['id' => $game_id])->field('id,name,game_kind')->find();

		if(!$memberInfo){
			$errorCode = 0;
			$errorMsg = "'".$account."' 该玩家账号不存在;";
		}
		else if(!$gameInfo){
			$errorCode = 0;
			$errorMsg = "游戏不存在;";
		}
		else if($gameInfo['game_kind']<>2){
			$errorCode = 0;
			$errorMsg = "'".$gameInfo['name']."' 为非混服游戏,该游戏不支持平台币。";
		}
		else{
			$allChildIds = get_child_channel_arr($channel_id);
			$memberGameChannelInfo = model('MemberChannelGame')->where(['member_id' => $memberInfo['id'], 'game_id' => $game_id])->find();
			if(!$memberGameChannelInfo){
				$errorCode = 0;
				$errorMsg = "'".$account."' 该玩家在游戏'".$gameInfo['name']."'中尚未绑定推广员,您不能对其进行转账;";
			}
			else if(!in_array($memberGameChannelInfo['channel_id'],$allChildIds) && $memberGameChannelInfo['channel_id']<>$channel_id){
				$errorCode = 0;
				$errorMsg = "'".$account."' 该玩家在游戏'".$gameInfo['name']."'中所属推广员不在您的公会下,您不能对其进行转账;";
			}
			else{
				$retData['game_id']  = $game_id;
				$retData['userid']	 = $memberInfo['id'];
				$retData['username'] = $memberInfo['username'];
				$retData['cuser_id'] = $memberGameChannelInfo['channel_id'];
			}
		}
	}
	return array("errorCode"=>$errorCode,"errorMsg"=>$errorMsg,"retData"=>$retData);
}

function chkTransferChannel($channel_id,$account=''){
	$errorCode = 1;
	$errorMsg  = '校验成功';
	$retData	   = array();

	if(!$account){
		$errorCode = 0;
		$errorMsg = "收款账号不能为空";
	}
	else{
		$channelInfo = model('Channel')->where(['name' => $account])->field('id,name,status,level')->find();

		if(!$channelInfo){
			$errorCode = 0;
			$errorMsg = "'".$account."' 该收款账号不存在;";
		}
		else if($channelInfo['level']<>2){
			$errorCode = 0;
			$errorMsg = "'".$account."' 该收款账号非子公会账号,不能进行转账操作;";
		}
		else{
			$allChildIds = get_child_channel_arr($channel_id,1);
			if(!in_array($channelInfo['id'],$allChildIds)){
				$errorCode = 0;
				$errorMsg = "'".$account."' 该收款账号非您下属子公会,您不能对其进行转账;";
			}
			else{
				$retData['userid']	 = $channelInfo['id'];
				$retData['username'] = $channelInfo['name'];
			}
			//是否需要判断可以发币？？？？
		}
	}
	return array("errorCode"=>$errorCode,"errorMsg"=>$errorMsg,"retData"=>$retData);
}
