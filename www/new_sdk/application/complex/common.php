<?php

use think\Env;

/**
 * 获取响应数据
 *
 * @param string $url 请求地址
 * @param mixed $param 请求参数
 * @param string $method 请求方式
 * @param array $httpHeader 需要更改的表头协议
 * @return string
 */
function get_http_response($url,$param,$method='post',$httpHeader='') {
    $ch = curl_init();
    if ( 'get' == $method ) {
        curl_setopt($ch, CURLOPT_URL, "{$url}?{$param}");
    } else {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
    }

    if (strstr($url, 'https')){
        //https必须要跳过证书检查才能正常请求
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); //这个是重点,规避ssl的证书检查。
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 跳过host验证
    }

    if (!empty($httpHeader)){
        curl_setopt($ch, CURLOPT_HTTPHEADER, $httpHeader);
    }

    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    // 超时设置
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);

    $responseText = curl_exec($ch);
    curl_close($ch);
    $logMsg  = '请求接口连接:' . $url;
    $logMsg .= ', 请求参数:' . $param;
    $logMsg .= ', 响应结果:' . $responseText;

    // writeLogToElk($logMsg);

    // log_message($logMsg,'log',LOG_PATH . 'complexLoginlog/');
    trace('请求: ' . $logMsg, 'complex/common@get_http_response.result');
    return trim($responseText);
}

// 请求次数控制
function can_do_request($key,$times=10,$expire=3600) {
    if ( empty($key) ) {
        return '';
    }

    $times  = is_numeric($times) ? (int)$times : 10;
    $expire = is_numeric($expire) ? (int)$expire : 24*60*60;

    //$redis = initAndGetRedis();

    $redis = new \Redis();
    $redis->connect(Env::get('redis.host', '127.0.0.1'), Env::get('redis.port', 6379));
    if (Env::get('redis.password', '')) {
        $redis->auth(Env::get('redis.password', ''));
    }
    $redis->select(Env::get('redis.select', 0));

    if ( $redis->exists($key) ) {
        $count = $redis->get($key);
        if ( $count > $times ) {
            return 'over request times';
        } else {
            $redis->incr($key);
        }
    } else {
        $redis->setex($key, $expire, 1);
    }

    return '';
}


/**
 * 获取游戏指定的用户名连接符
 *
 * @param int $gameid 游戏ID
 * @return string
 */
function get_username_connector($gameid) {
    $specify = ['116','173','201'];

    if ( in_array($gameid, $specify) ) {
        return '#';
    } else {
        return '___';
    }
}

