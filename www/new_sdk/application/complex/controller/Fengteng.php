<?php

/**
 * 飞腾聚合子渠道对接
 * 
 */

namespace app\complex\controller;

use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class Fengteng extends Complex  {
    
    protected $channel_mark = 'fengteng';
    
    /**
     * 支付处理
     */
    public function pay()
    {
        if (empty($_POST)) {
            
            die('请求内容为空');
        }
        $params		= array();
        $params['m_order_no']   = $orderid = $_POST['m_order_no'];     //下级渠道的订单号
        $params['tm']		    = $_POST['tm'];
        $params['cchid']	    = $_POST['cchid'];
        $params['appid']        = $appid = trim($_POST['appid']);
        $params['paid_amt']     = $amount  = $_POST['paid_amt'];
        $params['paid_state']   = $_POST['paid_state'];
        $params['paid_time']    = $paytime = $_POST['paid_time'];
        $params['openid']       = $_POST['openid'];
		$params['server_id']    = $_POST['server_id'];
		$params['server_name']  = urldecode($_POST['server_name']);
		$params['role_id']      = $_POST['role_id'];
		$params['role_name']    = urldecode($_POST['role_name']);
		$params['product_id']   = $_POST['product_id'];
		$params['product_name'] = urldecode($_POST['product_name']);
		$params['cp_ext']       = $_POST['cp_ext'];
		$params['is_test']      = $_POST['is_test'];
        $params['cp_order_no']  = $attach = $_POST['cp_order_no'];      //这个才是自己聚合层(本级)的订单编号
        $params['sign']			= $sign	  = $_POST['sign'];
        
        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('FAIL:订单不存在或订单金额不一致');
        }
        
        $polyChannelGameModel = new PolychannelGame;
        
        $param = $polyChannelGameModel->where(['game_id'=>$this->payInfo['gameid'],'channel_id'=>$this->payInfo['channel_id']])->value('param');
        
        if(empty($param)){
            log_message('后台渠道游戏中，用于该游戏的验签参数还未设置,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('FAIL:用于验签的参数还未设置');
        }
        
        $param = unserialize($param);
            
        $appkey = $param[$appid];
        
        $thisSign = $this->sign($params,$appkey);
            
        if ($sign != $thisSign) {
            log_message('签名校验失败,参数:'.$thisSign.' 生成的签名thisSign='.$thisSign.' 对方的签名:'.$sign,'error',LOG_PATH . '../complexPaylog/');
            die('FAIL:签名校验失败 ' . $thisSign);
        }
        
        // 启动事务
        Db::startTrans();
        
        try{
            $this->updateOrder($attach,$orderid,$paytime);
            
            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            die('FAIL:订单生成失败'.$e->getMessage());
        }
        
   
        
        // 【产品确认：测试阶段暂时不用通知CP】通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
            
        die('SUCCESS');
    }
    
	/**
     * 二次登录验证
     */
    public function checklogin()
    {
		/*
		$_POST['tm'] = '100073';
		$_POST['cchid'] = '122211';
		$_POST['appid'] = '123213213';
		$_POST['access_token'] = time();
		*/
	
        if (empty($_POST)) {
            die('请求内容为空');
        }
        
		$params					= array();
		$params['appid']		= $appid	= trim($_POST['appid']);
        $params['cchid']		= trim($_POST['cchid']);
        $params['access_token']	= urlencode(trim($_POST['access_token']));
        $params['tm']			= trim($_POST['tm']);

		$appkeyArrs = array("10078"=>"9GXn6KD4W7YlhPm3","100073"=>"10bd288640193feb5c8c470dd215e09f");
		if(!isset($appkeyArrs{$appid})){
			die('游戏秘钥未配置');
		}
		else{
			$appkey = $appkeyArrs{$appid};
		}
        $sign  = $this->sign($params,$appkey);
		$params['sign']	= $sign;
        
		$checkUrl = 'http://sdkapi.wx.yuekenet.com/user/v1/token/verify/';

        $response = get_http_response($checkUrl, http_build_query($params));

		die($response);

		/*
	//	var_dump($response);
	//	$response = json_decode($response,true);
	//	var_dump($response);
 
		if ($response  == 'success'){
            die('success');
        }
		else{
			die($response);
        }
		die('failed');
		*/
    }

	private function sign($param, $key){
		$param = array_filter($param, function ($v, $k) {
			if ($k !== 'sign_type' && $k !== 'sign' && $v !== '') {
				return true;
			}
		}, ARRAY_FILTER_USE_BOTH);
		ksort($param);
		reset($param);
		$signStr = '';
		foreach ($param as $k => $v) {
			$signStr .= $k . '=' . $v . '&';
		}
		if (get_magic_quotes_gpc()) {
			$signStr = stripslashes($signStr);
		}
	//	echo $signStr . $key;
		return md5($signStr . $key);
	}

}
