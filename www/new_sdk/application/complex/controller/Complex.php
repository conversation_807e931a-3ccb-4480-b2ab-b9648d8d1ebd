<?php

/**
 * sdk接口公共控制器类
 *
 */
namespace app\complex\controller;

use app\common\controller\Base;
use app\common\model\ComplexPay as PayModel;
use app\common\model\PayCpinfo;
use app\common\model\ComplexMembers;
use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class Complex extends Base {

    protected $payInfo;     //订单支付信息，子类里面有调用
    
    /**
     * 初始化操作
     */
    public function _initialize() {
        
    }

    /**
     * 检验订单
     *
     * @param $orderid string 订单ID
     * @param $amount string 订单金额
     *
     * @return boolean 验签结果
     */
    protected function checkOrder($orderid,$amount)
    {
        $payModel = new PayModel;
        
        $payInfo = $payModel->field('userid,amount,gameid,channel_id')->where(['orderid'=>$orderid,'status'=>0])->find();

        if(empty($payInfo))
            return false;
        elseif(floatval($payInfo['amount'])!=floatval($amount)){
            return false;
        }
        
        $this->payInfo = $payInfo;
        
        return true;
    }

    /**
     * 更新订单信息
     *
     * @param $orderId string 订单号
     * @param $sub_orderid string 下级渠道订单号
     * @param $pay_time int 支付时间
     * 
     * @return bool
     */
    protected function updateOrder($orderid,$sub_orderid,$pay_time=NOW_TIMESTAMP) {
        
        $payModel       = new PayModel;
        $payCpinfoModel = new PayCpinfo;
        $memberModel    = new ComplexMembers;
        
        //消费订单信息
        $payInfo = $payModel->field('id,amount,userid,gameid,channel_id')->where(['orderid'=>$orderid])->find();
        
        if($payInfo){
            
            $payModel->save(['status'=>1,'sub_orderid'=>$sub_orderid,'pay_time'=>$pay_time],['orderid'=>$orderid]);
            
            $payCpinfoModel->save(['payflag'=>1],['orderid'=>$orderid]);
            
            //更新nw_complex_members表的total_pay_amount
            $memberModel->where(['id'=>$payInfo['userid']])->update(['total_pay_amount'=> Db::raw('total_pay_amount+'.$payInfo['amount'])]);
        }
    }

    /**
     * 判断请求次数是否过多，默认为10次
     * @param $attach    麻花网络订单号
     * @param $orderid   cp订单号
     * @param $msg       提示信息
     * @param $mark      渠道标识
     */
    protected function requestTimes($attach,$orderid,$msg,$mark){
        $res = can_do_request('complex_sdk_pay_callback:' . $mark . '_request_count_' . md5($attach));
        if ($res){
            log_message('请求次数超出限制,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die($msg);
        }
    }

    /**
     * 写入数据库，通知cp
     * @param $attach    麻花网络订单号
     * @param $orderid  cp订单号
     * @param $msg       提示信息
     * @param $pay_time  支付时间
     */
    protected function writeDb($attach,$orderid,$msg,$pay_time=''){
        // 启动事务
        Db::startTrans();

        try{
            if (empty($pay_time)){
                $this->updateOrder($attach,$orderid);
            }else{
                $this->updateOrder($attach,$orderid,$pay_time);
            }

            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            log_message('订单生成失败'.$e->getMessage(),LOG_PATH . '../complexPaylog/');
//            die('订单生成失败'.$e->getMessage());
            die($msg);
        }

        // 通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
    }

    /**
     * 验证订单金额是否一致，获取游戏配置参数
     * @param $attach     麻花网络
	 
	 
	 
	 
	 订单号
     * @param $amount    金额 单位 元
     * @param $orderid   cp订单号
     * @param $msg       提示信息
     * @return mixed     配置参数
     */
    protected function checkAndGetParam($attach,$amount,$orderid,$msg){
        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            //die('订单不存在或订单金额不一致');
            die($msg);
        }

        $polyChannelGameModel = new PolychannelGame;

        $param = $polyChannelGameModel->where(['game_id'=>$this->payInfo['gameid'],'channel_id'=>$this->payInfo['channel_id']])->value('param');

        if(empty($param)){
            log_message('后台渠道游戏中，用于该游戏的验签参数还未设置,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            //die('用于验签的参数还未设置');
            die($msg);
        }

        $param = unserialize($param);

        return $param;
    }

    /**
     * 验证出错时，写日志，返回信息给cp，终止程序
     * @param        $errMsg    返回给cp的信息
     * @param        $logMsg    记录日志的信息
     * @param string $path      日志存放路径
     * @param string $errLeve   日志类型
     */
    protected function errAction($errMsg,$logMsg,$path='',$errLeve='error'){
        if (empty($path)){
            $path = LOG_PATH.'../complexPaylog/';
        }
        log_message($logMsg,$errLeve,$path);
        die($errMsg);
    }
}
