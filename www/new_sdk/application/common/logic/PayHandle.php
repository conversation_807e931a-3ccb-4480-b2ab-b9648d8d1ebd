<?php

namespace app\common\logic;

use think\Exception;

class PayHandle
{
    // 获取支付类型的支付渠道
    public function getPayChannel($pay_scene, $game_id, $version)
    {
        try {
            $versionList = model('AndroidSdk')->getAllVersion()->toArray();
            $handleVersionList = array_column($versionList, 'id', 'version');
            if (empty($handleVersionList[$version])) {
                return ['code' => -112, 'msg' => '支付版本有误！', 'data' => []];
            }
            $where = [
                'sdk_versions' => [['like', '%,' . $handleVersionList[$version] . ',%'], ['eq', 'all'], 'or'],
                'game_ids' => [['like', '%,' . $game_id . ',%'], ['eq', 'all'], 'or'],
                'pay_scene' => $pay_scene,
                'status' => 1,
            ];
            $paySupconList = (new \app\common\model\PaySupconModel())->getCacheList($where);
            $payList = getRandomPaymentType($paySupconList);
            $newPayList = [
                'paytype' => $payList['pay_mark'],
                'payname' => $payList['paytype_name'],
                'pay_scene' => $payList['pay_scene'],
                'sort' => $payList['sort'],
            ];
            return ['code' => 200, 'msg' => 'success', 'data' => $newPayList];
        } catch (Exception $e) {
            return ['code' => -100, 'msg' => '支付渠道获取异常：' . $e->getMessage(), 'data' => []];
        }
    }

    // 获取支付渠道列表
    public function getPayChannelList($game_id, $version)
    {

        /**
         * ## 随机支付
         *
         * 1. 适配不同的支付方式
         * 2. 适配不同的游戏
         * 3. 适配不同的版本
         *
         */
        try {
            $versionList = model('AndroidSdk')->getAllVersion()->toArray();
            $handleVersionList = array_column($versionList, 'id', 'version');
            if (empty($handleVersionList[$version])) {
                $this->jsonResult('', 0, '当前版本号有误');
            }
            $where = [
                'sdk_versions' => [['like', '%,' . $handleVersionList[$version] . ',%'], ['eq', 'all'], 'or'],
                'game_ids' => [['like', '%,' . $game_id . ',%'], ['eq', 'all'], 'or'],
            ];
            $paySupconList = (new \app\common\model\PaySupconModel())->where($where)->field('id,paytype_name,probability,pay_scene,pay_mark,sort')->select()->toArray();

            // 支付类型设置权重
            $payScene = ['wx_h5', 'wx_app', 'ali_h5', 'ali_app'];
            // 初始化新的支付类型数组
            $organizedPayChannels = [];
            // 遍历支付渠道数组
            foreach ($paySupconList as $channel) {
                $scene = $channel['pay_scene'];
                if (in_array($scene, $payScene)) {
                    // 如果支付类型数组中不存在该支付场景，则初始化
                    if (!isset($organizedPayChannels[$scene])) {
                        $organizedPayChannels[$scene] = [];
                    }
                    // 将支付渠道添加到相应的支付场景中
                    $organizedPayChannels[$scene][] = $channel;
                }
            }
            $payList = [];
            if (!empty($organizedPayChannels['wx_h5'])) {
                $payList[] = getRandomPaymentType($organizedPayChannels['wx_h5']);
            }
            if (!empty($organizedPayChannels['ali_h5'])) {
                $payList[] = getRandomPaymentType($organizedPayChannels['ali_h5']);
            }
            if (!empty($organizedPayChannels['ali_app'])) {
                $payList[] = getRandomPaymentType($organizedPayChannels['ali_app']);
            }
            if ($payList) {
                usort($payList, function ($a, $b) {
                    return $a['sort'] <=> $b['sort'];
                });
            }
            $newPayList = [];
            foreach ($payList as $key => $value) {
                $newPayList[] = [
                    'paytype' => $value['pay_mark'],
                    'payname' => $value['paytype_name'],
                    'pay_scene' => $value['pay_scene'],
                    'sort' => $value['sort'],
                ];
            }

            // dump($payList);
            // 如果没有查询到配置的游戏，则走默认
            if ($newPayList) {
                $this->jsonResult($newPayList, 1, '成功');
            }

        } catch (Exception $e) {
            $this->jsonResult('', 0, '支付获取异常：' . $e->getMessage());
        }
    }
}