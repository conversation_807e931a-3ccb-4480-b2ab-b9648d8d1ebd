<?php

/**
 * 前台用户公共业务逻辑类
 * 
 */

namespace app\common\logic;

use think\Config;
use think\Db;
use think\Env;
use think\Exception;
use app\common\library\FileUpload;

class Member {

    /**
     * 同步用户信息到账户中心
     *
     * @param $username:用户名
     * @param $password:明文密码
     * @param $mobile:手机号码
     * @param $now_time:当前时间
     *
     * @return string mem_openid
     */
    public function syncMember($username, $password, $mobile,$now_time)
    {
        $mgMemberInfo = Db::connect('db_config_resource_center')->table('mg_member')->field('mem_id,mem_openid')->where(['mem_login_name'=>$username,'pl_id'=>2])->find();

        
        $data = ['mem_password' => password_hash($password, PASSWORD_DEFAULT),
                'mem_mobile' 	=> $mobile,
                'mem_updatetime'=> $now_time,
            ];
        
        // 账户不存在，做插入
        if(empty($mgMemberInfo)) {
            
            $data['pl_id']          = 2;
            $data['mem_openid']     = md5($now_time.rand(1000, 9999));
            $data['mem_login_name'] = $username;
            
            Db::connect('db_config_resource_center')->table('mg_member')->insert($data);
            
            $openid = $data['mem_openid'];
            
        }else{ // 存在，更新账户的手机号和密码
            
            Db::connect('db_config_resource_center')->table('mg_member')->where(['mem_id'=>$mgMemberInfo['mem_id']])->update($data);
            $openid = $mgMemberInfo['mem_openid'];
        }
        
        return $openid;
    }


    /**
     * 上传txt文件
     * @param $file
     * @return string|array
     */
    public function uploadFile($file)
    {
        if (! $file) {
           return '请上传txt文件! ';
        }

        $tmpname  = $file->getInfo()['tmp_name'];
        $filename = $file->getInfo()['name'];

        if (pathinfo($filename, PATHINFO_EXTENSION ) !== 'txt') {
            return '请上传txt文件! ';
        }

        $users = file($tmpname);

        $checkResult = $this->checkContent($users);

        if (true !== $checkResult) {
            return $checkResult;
        }

        $fileObject = new FileUpload();

        $fileObject->set('allowExt', 'txt'); // 设置允许上传类型

        $path = $fileObject->upload($file);

        if ($path) {
            return ['filename' => $filename, 'tmpname' => $path];
        } else {
            return '文件上传失败';
        }
    }

    /**
     * 检测txt文件
     * @param $users
     * @return boolean
     */
    public function checkContent($users)
    {
        if (empty($users)) {
            return '格式有误，请重新上传1';
        }

        $users = array_map('trim', $users);

        foreach ($users as $user) {
            if (! preg_match('/^[0-9a-zA-Z]+:[0-9a-zA-Z]+$/', $user)) {
               return '格式有误，请重新上传';
            }
        }

        return true;
    }

    /**
     * 用户注册
     * @param $data
     * @return boolean
     */
    public function registerUser($data)
    {
        $now_time = request()->time();
        $data['password']   = auth_code($data['password'], "ENCODE", Env::get('auth_key')); // 加密
        $data['reg_time']   = $now_time;
        $data['login_time'] = $now_time;
        $data['ip']         = request()->ip();

        // 启动事务
        Db::startTrans();

        try {
            $member_id = Db::table('cy_members')->insertGetId($data);

            if ($member_id) {
                // 插入玩家历史记录
                Db::table('cy_member_history')->insert([
                    'userid'      => $member_id,
                    'password'    => $data['password'],
                    'ip'          => request()->ip(),
                    'create_time' => $now_time
                ]);

                //插入用户扩展记录
                Db::table('cy_memberstwo')->insert([
                    'userid'      => $member_id,
                    'username'    => $data['username'],
                    'realname'    => -1,
                    'idcard'      => -1,
                    'create_time' => $now_time
                ]);

                // 登录时 绑定该游戏的归属渠道
                Db::table('cy_member_channel_game_rel')->insert([
                    'member_id' => $member_id,
                    'channel_id' => $data['channel_id'],
                    'game_id' => $data['gameid'],
                    'mcgr_createtime' => time(),
                    //	'update_time'		=> time(),
                    'mcgr_ip' => request()->ip()
                ]);
                $sub_username = strtolower(random(20)).'_'.$data['gameid'].Env::get('sdk_mark_type');
                Db::table('nw_subaccount')->insert([
                    'member_id' => $member_id,
                    'game_id' => $data['gameid'],
                    'channel_id' => $data['channel_id'],
                    'sub_username' => $sub_username,
                    'create_time' => time(),
                ]);

                // 另一端游戏提前绑定当前渠道
                $gameType = Db::table('cy_game')->where(['id' => $data['gameid']])->value('type');
                if ($gameType == 1) { // ios
                    $gameBandId = Db::table('nw_game_band')->where(['ios_game_id' => $data['gameid']])->value('android_game_id');
                } else if ($gameType == 2) { // 安卓
                    $gameBandId = Db::table('nw_game_band')->where(['android_game_id' => $data['gameid']])->value('ios_game_id');
                }
                if ($gameBandId) {
                    Db::table('cy_member_channel_game_rel')->insert([
                        'member_id' => $member_id,
                        'channel_id' => $data['channel_id'],
                        'game_id' => $gameBandId,
                        'mcgr_createtime' => time(),
                        //	'update_time'		=> time(),
                        'mcgr_ip' => request()->ip()
                    ]);

                    $sub_username = strtolower(random(20)).'_'.$gameBandId.Env::get('sdk_mark_type');
                    Db::table('nw_subaccount')->insert([
                        'member_id' => $member_id,
                        'game_id' => $gameBandId,
                        'channel_id' => $data['channel_id'],
                        'sub_username' => $sub_username,
                        'create_time' => time(),
                    ]);
                }

            } else {
                throw new Exception("注册失败!");
            }

            Db::commit();
            return true;

        } catch (Exception $e) {

            Db::rollback();
            return $e->getMessage();

        }
    }

    /**
     * 游戏-账号-角色查询
     * @param array $condition
     * @return \think\Paginator|\think\paginator\Collection
     */
    public function accountQuery(array $condition,$pageNum=10)
    {
        $infoList = Db::table('cy_role_info')->alias('info')
            ->join('cy_members user', 'info.userid = user.id')
            ->join('cy_game game', 'game.id = info.gameid')
            ->field(['game.name', 'user.username', 'info.serverid','info.servername', 'info.roleid','info.rolename', 'info.rolelevel', 'info.create_time'])
            ->where($condition)
            ->order('info.id', 'desc')
            ->paginate($pageNum, false, ['query' => input('get.')]);

        return $infoList;
    }

    /**
     * 游戏-账号-角色查询
     * @param array $condition
     * @return \think\Paginator|\think\paginator\Collection
     */
    public function queryUserRole(array $condition)
    {
        $roleInfo = Db::table('cy_role_info')
            ->field(['servername', 'rolename', 'rolelevel'])
            ->where($condition)
            ->order('id', 'asc')
            ->find();

        return $roleInfo;
    }
}
