<?php
namespace app\common\library;
use app\common\model\GameBindDouyinOpen;
use DouYinGameOpen\Init;
use DouYinGameOpen\User;
use think\Cache;
use think\Env;

/**
 * 抖音广告队列类
 * Class MakeReport
 */
class DyGameOpen
{
    private $_redis = null;
    public $dy_config = null;
    private $init_status = [];


    public function __construct(string $key, string $type)
    {
        $this->_redis = Cache::init()->handler();
        $this->init($key, $type);
    }

    /**
     * 根据类型初始化配置
     *
     * @param $key  string 参数值
     * @param $type string 参数类型=game_id|app_id
     *
     * @return array
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function init(string $key, string $type)
    {
        if($type == 'game_id'){
            $config = GameBindDouyinOpen::getInfoByGameId($key);
        }else{
            $config = GameBindDouyinOpen::getInfoByAppId($key);
        }

        if(!$config){
            $this->init_status = $this->handleResponse([], -100, '当前游戏未进行抖音平台绑定!');
        }
        $this->dy_config = $config;
    }

    private function handleResponse($data = [], $code = 100, $msg = 'success'): array
    {
        if($code == -100){
            $msg = 'error: '.$msg;
        }
        return ['data' => $data, 'code' => $code, 'msg' => $msg];
    }

    // -- 获取公共 --
    /**
     * 获取平台的 client_token
     * @return mixed
     */
    public function getClientToken(){
        if($this->init_status){
            return $this->init_status;
        }

        $cache_key = 'dy_game_open:client_token:'.$this->dy_config['app_id'];
        $client_token = $this->_redis->get($cache_key);

        if(!$client_token){
            $client_info = (new Init($this->dy_config))->client_token();
            $this->_redis->set($cache_key, $client_info['data']['access_token'], $client_info['data']['expires_in']-60);
            $client_token = $client_info['data']['access_token'];
        }

        return $this->handleResponse($client_token);
    }

    /**
     * 获取用户的 access_token
     * @param $code
     *
     * @return array
     */
    public function getAccessToken($code){
        if($this->init_status){
            return $this->init_status;
        }
        $access_info = (new Init($this->dy_config))->access_token($code);
        if($access_info['message'] == 'error' || !empty($dyResult['data']['description'])){
            return $this->handleResponse([], -100, $access_info['data']['description'].'-'.$access_info['data']['error_code']);
        }

        $result = [
            'access_token' => $access_info['data']['access_token'],
            'open_id' => $access_info['data']['open_id'],
        ];
        return $this->handleResponse($result);
    }

    /**
     * 刷新用户的 access_token
     * @param $access_token
     *
     * @return array
     */
    public function refreshToken($access_token){
        if($this->init_status){
            return $this->init_status;
        }

        // 请求刷新access_token
        $access_info = (new Init($this->dy_config))->refresh_token($access_token);

        if ($access_info['message'] == 'error' || !empty($access_info['data']['description'])) {
            return $this->handleResponse([], -100, '@刷新token失败：' . $access_info['data']['description'] . '-' . $access_info['data']['error_code']);
        }
        // 更新缓存
        $token_data = [
            'access_token' => $access_info['data']['access_token'],
            'refresh_token' => $access_info['data']['refresh_token'],
            'expires_in' => $access_info['data']['expires_in'],
            'refresh_expires_in' => $access_info['data']['refresh_expires_in'],
            'open_id' => $access_info['data']['open_id']
        ];

        return $this->handleResponse($token_data);
    }



    // -- 游戏绑定抖音账户 --
    /**
     * 根据 open_id 获取用户绑定意愿
     * @param $open_id
     *
     * @return array
     */
    public function getAuthInfoOpen($open_id){
        if($this->init_status){
            return $this->init_status;
        }

        $client_token = $this->getClientToken();
        if($client_token['code'] != 100){
            return $this->handleResponse([], -100, $client_token['msg']);
        }

        $result = (new User($this->dy_config))->get_auth_info_open($client_token['data'], $open_id);
        if($result['err_no'] > 0){
            return $this->handleResponse([], -100, $result['err_msg']);
        }
        // 用户是否同意进行账号绑定 = true/false
        return $this->handleResponse($result['data']);
    }



    // -- 抖音绑定游戏账户 --
    /**
     * 3.4.2 抖音授权登录根据 code 获取加密手机号
     *
     * @param $open_id
     *
     * @return array
     */
    public function getHashMobile($open_id){
        if($this->init_status){
            return $this->init_status;
        }

        $client_token = $this->getClientToken();
        if($client_token['code'] != 100){
            return $this->handleResponse([], -100, 'error: '.$client_token['msg']);
        }

        $result = (new User($this->dy_config))->get_user_hash_mobile($client_token['data'], $open_id);
        if($result['err_no'] > 0){
            return $this->handleResponse([], -100, $result['err_msg']);
        }
        return $this->handleResponse($result['data']['hash_mobile']);
    }

    /**
     * 3.4.7 游戏内授权后回调抖音游戏
     *
     * @param $is_agree
     * @param $uid
     * @param $scheme
     *
     * @return array
     */
    public function getJumpAuthCallback($is_agree, $uid, $scheme){
        if($this->init_status){
            return $this->init_status;
        }

        $client_token = $this->getClientToken();
        if($client_token['code'] != 100){
            return $this->handleResponse([], -100, $client_token['msg']);
        }

        $result = (new Init($this->dy_config))->jump_game_auth_callback($client_token['data'], $is_agree, $uid, $scheme);
        if($result['err_no'] > 0){
            return $this->handleResponse([], -100, $result['err_msg']);
        }
        // 用户是否同意进行账号绑定 = true/false
        return $this->handleResponse();

    }
}
