<?php
namespace app\common\library;
use think\Env;

/**
 * 报表生成服务类
 * Class MakeReport
 */
class MakeReport{
    private $_redis = null;

    public function __construct(){
        if(extension_loaded('redis')) {
            $this->_redis = new \Redis();
            $this->_redis->connect(Env::get('redis.host', '127.0.0.1'), Env::get('redis.port', 6379));

            if (Env::get('redis.password', '')) {
                $this->_redis->auth(Env::get('redis.password', ''));
            }
            
            $this->_redis->select(Env::get('redis.select', 0));
        } else {
            throw Exception('请开启redis扩展');
        }
    }

    /**
     * 添加报表生成任务
     * @param $action string  和python协商好的名称用来识别哪个报表
     * @param $sql
     * @param $uid int 用户id
     * @param $extend array 扩展参数
     * @param $count int sql查询结果总条数
     * @param int $expire 相同任务多久内不重复生成
     * 
     * @return boolean true:提交报表任务成功  ；false:提交报表失败
     */
    public function addTask($action, $sql, $uid, $extend = ['is_show'=>false], $count = 0, $expire = 3600){
        $task_data = array(
            'action'	=> $action,
            'sql'		=> $sql,
            'uid'		=> $uid,
            'time'      => request()->time(),
            'extend'    => $extend,
            'count'     => $count
        );

        $task_unique_id = strtolower(md5(sprintf(
            '%s|%s|%s|%s',
            $task_data['uid'],
            $task_data['action'],
            $task_data['sql'],
            json_encode($task_data['extend'])
        )));
        $task_unique_id = config('MAKE_REPORT_UNIQUE_KEY_PRE') . $task_unique_id;
        $has_task = $this->_redis->get($task_unique_id);

        // 防止重复生成报表
        if(empty($has_task)) {
            $this->_redis->lpush(config('MAKE_REPORT_KEY'), json_encode($task_data));
            $this->_redis->setex($task_unique_id, $expire,1);
            
            return true;
        }
        else{
            return false;
        }
    }
}