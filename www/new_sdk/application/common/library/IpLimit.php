<?php
/**
 * 国内ip限制
 */

namespace app\common\library;


use think\Env;
use think\Exception;

class IpLimit
{
    //白名单账号
    private $usernames = [
        'inlude', 'inlude1'
    ];

    public function validateIp($ip, $username = '')
    {
        if (in_array($username, $this->usernames)) {
            return 1;
        }
        $tmp = cache('IpLimit:validateIp:' . $ip);
        if ($tmp > 0 && $tmp != 3) {
            return $tmp;
        }

        //$tmp 返回值  1  国外  2  国内  3 访问异常

        $tmp = $this->tencentIp($ip);
        if ($tmp != 3) {
            cache('IpLimit:validateIp:' . $ip, $tmp, 86400 * 7);
            return $tmp;
        }
        $tmp = $this->freeapi($ip);
        if ($tmp != 3) {
            cache('IpLimit:validateIp:' . $ip, $tmp, 86400 * 7);
            return $tmp;
        }
        $tmp = $this->pconline($ip);
        if ($tmp != 3) {
            cache('IpLimit:validateIp:' . $ip, $tmp, 86400 * 7);
            return $tmp;
        }

        $tmp = $this->ipApi($ip);
        if ($tmp != 3) {
            cache('IpLimit:validateIp:' . $ip, $tmp, 86400 * 7);
            return $tmp;
        }

        return $tmp;
    }

    //ip-api
    private function ipApi($ip)
    {
        try {
            $url = sprintf('http://ip-api.com/json/%s?lang=zh-CN', $ip);
            $result = curl($url);
            $res = json_decode($result, true);
            if ($res['status'] == 'success') {
                if ($res['country'] == '中国') {
                    return 2;
                } else {
                    return 1;
                }
            } else {
                $this->curlDDIp('ipApi', $ip, $result);
                return 3;
            }
        } catch (Exception $e) {
            $this->curlDDIp('ipApi', $ip, $e->getMessage());
            return 3;
        }
    }

    //太平洋
    public function pconline($ip)
    {
        try {
            $url = sprintf('http://whois.pconline.com.cn/ipJson.jsp?ip=%s&json=true', $ip);
            $result = curl($url);
            $result = iconv('GBK', "UTF-8//IGNORE", $result);
            $res = json_decode($result, true);
            if (isset($res['proCode'])) {
                if (!in_array($res['proCode'], [110000, 120000, 130000, 140000, 150000, 210000, 220000, 230000, 310000, 320000, 330000,
                    340000, 350000, 360000, 370000, 410000, 420000, 430000, 440000, 450000, 460000, 500000, 510000, 520000, 530000,
                    540000, 610000, 620000, 630000, 640000, 650000
                ])) {
                    return 2;
                } else {
                    return 1;
                }
            } else {
                $this->curlDDIp('pconline', $ip, $result);
                return 3;
            }

        } catch (Exception $e) {
            $this->curlDDIp('pconline', $ip, $e->getMessage());
            return 3;
        }

    }
    //BX3BZ-JRKE4-4XOU7-XDFVB-VGEO3-LLBXQ
    //3JABZ-46O6D-M6R45-PPVSG-2BNXK-VSFL4
    public function tencentIp($ip)
    {

        try {
            $url = sprintf('https://apis.map.qq.com/ws/location/v1/ip?output=json&key=BX3BZ-JRKE4-4XOU7-XDFVB-VGEO3-LLBXQ&ip=%s', $ip);
            $result = curl($url);

            $res = json_decode($result, true);

            if ($res['status'] == 0) {
                if ($res['result']['ad_info']['nation'] == '中国') {
                    return 2;
                } else {
                    return 1;
                }
            } else {
                $url = sprintf('https://apis.map.qq.com/ws/location/v1/ip?output=json&key=3JABZ-46O6D-M6R45-PPVSG-2BNXK-VSFL4&ip=%s', $ip);
                $result = curl($url);

                $res = json_decode($result, true);

                if ($res['status'] == 0) {
                    if ($res['result']['ad_info']['nation'] == '中国') {
                        return 2;
                    } else {
                        return 1;
                    }
                } else {
                    $this->curlDDIp('tencentIp', $ip, $result);
                    return 3;
                }
            }

        } catch (Exception $e) {
            $this->curlDDIp('tencentIp', $ip, $e->getMessage());
            return 3;
        }
    }

    public function freeapi($ip)
    {
        try {
            $url = sprintf('http://freeapi.ipip.net/%s', $ip);
            $result = curl($url);

            $res = json_decode($result, true);
            if (isset($res[0])) {
                if ($res[0] == '中国') {
                    return 2;
                } else {
                    return 1;
                }
            } else {
                $this->curlDDIp('freeapi', $ip, $result);
                return 3;
            }

        } catch (Exception $e) {
            $this->curlDDIp('freeapi', $ip, $e->getMessage());
            return 3;
        }
    }

    public function curlDDIp($action, $ip, $msg)
    {
        $template = 'ip归属地获取失败：' . $action . '  ' . $ip . '  ' . $msg;
        $ddurl = Env::get('operat_url');
        curlDD($template, $ddurl, true);
    }
}
