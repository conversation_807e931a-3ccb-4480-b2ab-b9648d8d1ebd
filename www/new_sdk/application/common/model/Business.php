<?php

namespace app\common\model;


use think\Db;

class Business extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_business';
    protected $dateFormat = false;          //时间戳格式不自动转换

    public function getUpdateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }

    public function getChannelIds($admin_id, $level = 0)
    {


        $status = true;

        $admin = model('common/Admin')->alias('a')->join('nw_admin_role_user b', 'a.id=b.user_id')->where('a.id', $admin_id)->field('a.id,b.role_id')->find();

        if (!in_array($admin['role_id'], [12, 17, 18])) {
            return -1;
        }


        $rolelist = [];
        $role_id = $admin['role_id'];
        while ($status) {
            if ($role = Db::table('nw_admin_role')->cache('business:getChannelIds:parent_id' . $role_id, 60)->field('id,parent_id')->where('parent_id', $role_id)->find()) {
                $role_id = $role['id'];
                $rolelist[] = $role['id'];
            } else {
                $status = false;
            }
        }

        $list = Db::table('nw_admin_role_user')->cache('business:getChannelIds:role_' . md5(implode(',', $rolelist)), 60)->alias('a')->join('cy_business b', 'a.user_id=b.admin_id')->whereIn('a.role_id', $rolelist)->field('channel_ids')->select();

        $bus = $this->where('admin_id', $admin_id)->field('channel_ids')->find();
        $tmp = explode(',', $bus['channel_ids']);
        foreach ($list as $k => $v) {
            $tmp = array_merge($tmp, explode(',', $v['channel_ids']));
        }

        if ($level == 0) {
            if ($tmp && $tmp[0] != '') {
                return $tmp;
            } else {
                return -2;
            }
        } else if ($level == 1) { // 会长
            if (!$tmp && $tmp[0] != '') {
                return -2;
            }
            $channel = model('Channel')->cache('business:getChannelIds:level_B' . md5(implode(',', $tmp)), 60)->whereIn('parent_id', $tmp)->column('id');
            if ($channel) {
                return $channel;
            } else {
                return -2;
            }

        } else if ($level == 2) { // 子会长
            if (!$tmp && $tmp[0] != '') {
                return -2;
            }
            $where = [];
            foreach ($tmp as $k => $v) {
                $where[] = sprintf(' id_path like ",%s', $v) . '%" ';
            }

            $str = implode(' or ', $where);
            $channel = model('Channel')->cache('business:getChannelIds:level_B_' . md5(implode(',', $tmp)), 60)->where('level', $level)->whereRaw($str)->column('id');
            if ($channel) {
                return $channel;
            } else {
                return -2;
            }
        } else {//推广员
            if (!$tmp && $tmp[0] != '') {
                return -2;
            }

            $where = [];
            foreach ($tmp as $k => $v) {
                $where[] = sprintf(' id_path like ",%s', $v) . '%" ';
            }

            $str = implode(' or ', $where);
            $channel = model('Channel')->cache('business:getChannelIds:level_C' . md5(implode(',', $tmp)), 60)->where('level', $level)->whereRaw($str)->column('id');
            if ($channel) {
                return $channel;
            } else {
                return -2;
            }
        }


    }
}
