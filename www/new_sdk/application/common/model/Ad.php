<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/29
 * Time: 16:38
 */

namespace  app\common\model;

use think\Model;
use think\Db;
use think\Config;

class Ad extends Model
{
    protected $table = 'cy_ad';
    protected $dateFormat = false;


    /**
     * 获取页内广告
     */
    public function getPageAd(){
        $adInfo = Db::connect(config('database_slave'))->table('cy_ad')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->where('type',99)->order('sort asc,create_time asc')->find();
        return $adInfo;
    }
}

