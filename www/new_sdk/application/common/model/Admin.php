<?php
/**
 * 后台账户表
 * 
 */
namespace app\common\model;
use think\Cache;
use think\Db;

class Admin extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_admin';

    public function getAllByCondition($field = 'id,username', $condition = [],$order='id desc')
    {
        $list = Db::table($this->table)->field($field)->where(['type'=> 1 ,'status'=> 1])->where($condition)->order($order)->select();
        return $list;
    }
}