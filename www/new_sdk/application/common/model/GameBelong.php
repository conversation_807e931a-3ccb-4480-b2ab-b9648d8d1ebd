<?php
/**
 * 游戏归属绑定设置
 *
 */

namespace app\common\model;

use think\Cache;

class GameBelong extends \think\Model
{
    protected $pk                 = 'id';
    protected $table              = 'nw_game_belong';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    protected static function init()
    {
        self::afterInsert(function () {
            Cache::clear('game_belong_info');
        });
        
        self::afterUpdate(function () {
            Cache::clear('game_belong_info');
        });
        
        self::afterDelete(function () {
            Cache::clear('game_belong_info');
        });
    }
    
    public function getCacheInfo ($where = [], $field = '*')
    {
        return $this->cache(true, 180, "game_belong_info")->where($where)->field($field)->find();
    }
}