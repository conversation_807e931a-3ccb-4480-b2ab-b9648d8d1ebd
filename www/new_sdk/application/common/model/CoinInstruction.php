<?php

namespace app\common\model;

use think\Cache;

/**
 * 平台币说明设置表
 * <AUTHOR>
 */
class CoinInstruction extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'nw_coin_instruction';
    protected $dateFormat = false;          //时间戳格式不自动转换

    const CACHE_KEY = "clearable_coin_instruction";

    /**
     * 初始化
     */
    protected static function init()
    {
        CoinInstruction::afterInsert(function () {
            Cache::rm(self::CACHE_KEY);
        });

        CoinInstruction::afterUpdate(function () {
            Cache::rm(self::CACHE_KEY);
        });

        CoinInstruction::afterWrite(function () {
            Cache::rm(self::CACHE_KEY);
        });
    }

    /**
     * 获得平台币说明
     */
    public function getInstruction($name = '')
    {
        $info = Cache::get(self::CACHE_KEY);
        if (empty($info)) {
            $info = self::value('content');
            if (!empty($info)) {
                Cache::set(self::CACHE_KEY, $info);
            }
        }
        
        return $info;
    }

}