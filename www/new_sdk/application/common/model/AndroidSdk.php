<?php
/**
 * 安卓sdk版本配置表
 * Created by PhpStorm.
 * User: cqingt
 * Date: 2018/6/26
 * Time: 17:42
 */

namespace app\common\model;

class AndroidSdk extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'nw_android_sdk_config';

    // 阻止在view页面，时间格式自动转换
    protected $dateFormat = false;

    // 自动写入
    protected $autoWriteTimestamp = true;
    protected $resultSetType = 'collection';

    /**
     * 获取最后一个更新版本
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getLastVersionInfo()
    {
        return $this->field('version,num,filename')
            ->order('id', 'desc')
            ->find();
    }

    /**
     * 获取所有的版本号
     * @return mixed
     */
    public function getAllVersion()
    {
        return $this->field('id,version')->order('id', 'desc')->select();
    }
}