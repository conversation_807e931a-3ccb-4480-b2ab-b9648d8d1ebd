<?php

namespace app\common\model;

use think\Cache;
use think\Db;

/**
 * 渠道表
 * <AUTHOR>
 */
class ComplexChannel extends \think\Model
{
    protected $pk    = 'id';
    protected $table = 'nw_complex_channel';
    const CACHE_KEY_MARK_INFO = "complex_channel_mark_info:";

    protected static function init()
    {
        Channel::afterInsert(function () {
            Cache::clear('complex_channel_cache');
        });

        Channel::afterUpdate(function () {
            Cache::clear('complex_channel_cache');
        });

        Channel::afterWrite(function () {
            Cache::clear('complex_channel_cache');
        });
    }
    
    
    /**
     * 根据 渠道标识 获取聚合渠道数据
     *
     * @param $mark
     *
     * @return array|bool|\PDOStatement|string|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCacheInfoByMark($mark){
        return $this->where('mark', $mark)->cache(self::CACHE_KEY_MARK_INFO.$mark, 30)->field('id,name,mark,status')->find();
    }
}