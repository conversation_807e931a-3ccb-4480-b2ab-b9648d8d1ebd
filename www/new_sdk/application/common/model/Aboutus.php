<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/11/19
 * Time: 11:55
 */
namespace  app\common\model;

use think\Model;
use think\Cache;

class Aboutus extends Model
{
    protected $table = 'cy_aboutus';
    protected $dateFormat = false;
    protected $order = 'order asc,create_time desc';

    const  CACHE_KEY = "clearable_get_aboutus_list";

    /**
     * 获取首页列表
     */
    public function getAboutList()
    {
        $list = Cache::get(self::CACHE_KEY);

        if (empty($list)) {

            $list = $this->field('id,name,title')->order($this->order)->select();

            if (!empty($list)) {

                Cache::set(self::CACHE_KEY, $list, 3600);

            }
        }
        
        return $list;
    }

    /**
     * 删除缓存
     */
    public function delCache()
    {
        Cache::rm(self::CACHE_KEY);
    }
    /*
     *获取关于列表
     */
    public function getList($order = null)
    {
        $orderStr = "create_time DESC";
        if (!empty($order)){
            $orderStr = $order;
        }
        return $this->field('id,title,content,order,create_time,name')->order($orderStr)->paginate();
    }

    /**
     *获取关于列表
     */
    public function getAllList($order = null)
    {
        $orderStr = "create_time DESC";
        if (!empty($order)){
            $orderStr = $order;
        }
        return $this->field('id,title,content,order,create_time,name')->order($orderStr)->select();
    }
}

