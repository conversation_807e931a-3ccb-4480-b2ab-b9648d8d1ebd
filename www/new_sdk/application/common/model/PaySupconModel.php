<?php

namespace app\common\model;

use think\Cache;
use think\Model;

class PaySupconModel extends Model
{
    protected $pk         = 'id';
    protected $table      = 'cy_pay_supcon';
    protected $resultSetType = 'collection';
    
    protected static function init()
    {
        self::afterInsert(function () {
            Cache::clear('pay_supcon_list');
        });
        
        self::afterUpdate(function () {
            Cache::clear('pay_supcon_list');
        });
        
        self::afterDelete(function () {
            Cache::clear('pay_supcon_list');
        });
    }
    
    public function getCacheList ($where = [], $field = 'id,paytype_name,probability,pay_scene,pay_mark,sort')
    {
        return $this->cache(true, 300, "pay_supcon_list")->where($where)->field($field)->select()->toArray();
    }
}
