<?php

namespace app\common\model;

use think\Cache;

/**
 * 系统配置表
 * <AUTHOR>
 */
class Setting extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'cy_setting';
    protected $dateFormat = false;          //时间戳格式不自动转换

    const  CACHE_KEY = "clearable_get_setting:";
    const  CACHE_TAG = "clearable_get_setting";

    protected static function init()
    {
        Setting::afterUpdate(function () {
            Cache::clear(self::CACHE_TAG);
        });

        Setting::afterWrite(function () {
            Cache::clear(self::CACHE_TAG);
        });
    }

    public static function getSetting($name = '')
    {
        if (empty($name)) {
            return false;
        }

        $cache_key = self::CACHE_KEY.$name;
        $info = Cache::get($cache_key);
        if (empty($info)) {
            $info = self::where(['name' => $name])->value('value');
            if (empty($info)) {
                return false;
            }
            Cache::tag(self::CACHE_TAG)->set($cache_key, $info, 3600 * 24);
        }
        return $info;
    }

}