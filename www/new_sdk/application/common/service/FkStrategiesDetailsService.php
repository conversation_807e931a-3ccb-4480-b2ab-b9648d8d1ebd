<?php
namespace app\common\service;


// 风控策略规则管理
use think\Cache;
use think\Db;

class FkStrategiesDetailsService
{
    // 缓存前缀
    const CACHE_PREFIX = 'fk_strategies_details:';

    private $redis;

    public function __construct()
    {
        $this->redis = Cache::store('default')->handler();
        $this->redis->select(1);
    }
    /**
     * 检查频率规则
     * @param array $detail 规则详情
     * @param array $eventData 事件数据
     * @return array
     */
    public function checkFrequencyRule(array $detail, array $eventData)
    {

        // dump($detail['exception_validity_period']);
        if($detail['exception_validity_period'] > 0){
            $cacheKey = self::CACHE_PREFIX . 'frequency:' . $detail['rule_code'];
            $this->redis->hset($cacheKey, $eventData[$detail['exception_object_type']], 1);
            $contrast = $this->redis->hlen($cacheKey);
            $ttl = $this->redis->ttl($cacheKey);
            if($contrast === 0 || $ttl < 0) {
                // 设置过期时间，减少内存数据冗余
                $this->redis->expire($cacheKey, $detail['exception_validity_period']);
            }
        }else{
            $contrast = $eventData[$detail['exception_object_type']];
        }

        $result = false;
        // 根据逻辑类型判断
        switch (trim($detail['logic_type'])) {
            case 'gt': // 大于
                $result = $contrast > $detail['logic_value'];
                break;
            case 'gte': // 大于等于
                $result = $contrast >= $detail['logic_value'];
                break;
            case 'lt': // 小于
                $result = $contrast < $detail['logic_value'];
                break;
            case 'lte': // 小于等于
                $result = $contrast <= $detail['logic_value'];
                break;
            case 'eq': // 等于
                $result = $contrast == $detail['logic_value'];
                break;
        }

        return ['status' => $result, 'count' => $contrast];
    }

    /**
     * 检查统计规则
     * @param array $detail
     * @param array $eventData
     * @return array
     */
    public function checkStatisticsRule(array $detail, array $eventData)
    {
        $result = false;
        if($detail['exception_validity_period'] > 0){
            $cacheKey = self::CACHE_PREFIX . 'statistics:' . $detail['rule_code'].':'.$eventData[$detail['exception_object_type']];

            $this->redis->Incr($cacheKey, 1);
            $contrast = $this->redis->get($cacheKey);
            $ttl = $this->redis->ttl($cacheKey);
            if($contrast === 0 || $ttl < 0) {
                $this->redis->expire($cacheKey, $detail['exception_validity_period']);
            }
        }else{
            $contrast = $eventData[$detail['exception_object_type']];
        }

        // 根据逻辑类型判断
        switch ($detail['logic_type']) {
            case 'gt': // 大于
                $result = $contrast > $detail['logic_value'];
                 break;
            case 'gte': // 大于等于
                $result = $contrast >= $detail['logic_value'];
                break;
            case 'lt': // 小于
                $result = $contrast < $detail['logic_value'];
                break;
            case 'lte': // 小于等于
                $result = $contrast <= $detail['logic_value'];
                break;
            case 'eq': // 等于
                $result = $contrast == $detail['logic_value'];
                break;
        }
        // dump($result, $contrast, ' - ', $detail['logic_type'], $detail['logic_value']);

        return ['status' => $result, 'count' => $contrast];
    }
}