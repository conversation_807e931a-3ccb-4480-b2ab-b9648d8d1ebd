<?php
namespace app\common\factory\pay;

use Exception;
use think\Config;
use think\Request;
use think\Validate;

/**
 * 优亿支付(易宝)(原趣智连)
 */
class YyYbPay extends Pay
{
    // private $url = "https://mp.letuike.cn"; // 测试
    private $url = "https://mp.letuike.cn"; // 正式

    private $handleSign;

    /**
     * 发起下单
     *
     * @param $data 下单数据
     * @param $type 支付方式: payWxH5=微信H5, payZfbH5=支付宝
     *
     * @return array|void
     */
    public function pay($data, $type)
    {
        try {
            $this->action = 'pay';
            $pay_config = Config::get('yyyb_pay');

            $wap_url = 'http://' . HTTP_HOST_URL; // 支付成功后打开的网址跳回游戏
            if ($data['gameid']) {
                $wap_url .= '?gameid=' . $data['gameid'];
            }

            $bodyData = [
                "mchNo" => $pay_config['mch_no'],                           // 商户号
                "appId" => $pay_config['app_id'],                           // 应用ID
                "mchOrderNo" => $data['orderid'],                           // 商户生成的订单号
                "wayCode" => $type,                                         // 支付方式,如微信H5 WX_H5
                "amount" => strval(formatYuanToFen($data['real_amount'])),  // 支付金额,单位分
                "currency" => "cny",                                        // 三位货币代码,人民币:cny
                // "clientIp" => "xxx", // IP
                "subject" => $data['productname'],                          // 商品标题
                "body" => $data['productname'],                             // 商品描述
                "notifyUrl" => $data['notify_url'],                         // 异步通知URL
                "returnUrl" => $wap_url,                                    // 同步通知URL
                "extParam" => $data['attach'] ?? '',                        // 扩展参数
                "reqTime" => time(),                                        // 时间戳
                "version" => "1.0",                                         // 接口版本
                "signType" => "MD5",                                        // 签名类型：MD5
            ];
            $bodyData['sign'] = $this->sign($bodyData, $pay_config['app_secret']);

            $result = get_http_response($this->url . '/api/pay/unifiedOrder', $bodyData, 'post', array('Content-Type: application/x-www-form-urlencoded'));
            $return = json_decode($result, true);
            // {"code":0,"data":{"mchOrderNo":"WL17256111918516691501","orderState":1,"payData":"https://qr.alipay.com/bax047862ctw5ndudi085522","payDataType":"payurl","payOrderId":"P1831972229617868802"},"msg":"SUCCESS","sign":"F1F0E2AE3A74D4F48C127DFDC30E53E4"}

            $pay_return = [];
            if ($return['code'] === 0) {
                if ($type == 'payWxH5') {
                    $pay_return = [
                        'mweb_url' => $return['data']['payData'],
                    ];
                } else {
                    $pay_return = [
                        'mweb_url' => $return['data']['payData'],
                        'referer' => 'com.yiyou.zfb',
                    ];
                }
                return ['error' => false, 'data' => $pay_return];
            }
            return ['error' => true, 'data' => [], "msg" => 'yyyb..error: ' . $return['msg']];
        } catch (Exception $e) {
            return ['error' => true, 'data' => [], "msg" => "yyyb.error: " . $e->getMessage()];
        }


        // try {
        //     if ($type == 'payWxH5') {
        //         $res = $this->$type($data, 'WX_H5');
        //     } else if ($type == 'payZfbH5') {
        //         $res = $this->$type($data, 'ALI_H5');
        //     }
        // } catch (Exception $e) {
        //     return ['error' => true, 'data' => [], "msg" => '支付平台的支付方式有误_yyyb_001-' . $e->getMessage()];
        // }
        //
        // return $res;
    }

    // 回调
    public function notify($input)
    {
        $this->action = 'notify';
        $pay_config = Config::get('yyyb_pay');

        $data = Request::instance()->only(['payOrderId', 'mchNo', 'appId', 'mchOrderNo', 'ifCode', 'wayCode', 'amount', 'currency', 'state', 'clientIp', 'subject', 'body', 'channelOrderNo', 'errCode', 'errMsg', 'extParam', 'createdAt', 'successTime', 'reqTime', 'sign'], 'post');
        $rules = [
            'payOrderId' => 'require',
            'mchNo' => 'require',
            'appId' => 'require',
            'mchOrderNo' => 'require',
            'ifCode' => 'require',
            'wayCode' => 'require',
            'amount' => 'require',
            'currency' => 'require',
            'state' => 'require',
            'clientIp' => 'require',
            'subject' => 'require',
            'body' => 'require',
            'channelOrderNo' => 'require',
            // 'errCode' => 'require',
            // 'errMsg' => 'require',
            // 'extParam' => 'require',
            'createdAt' => 'require',
            'successTime' => 'require',
            'reqTime' => 'require',
            'sign' => 'require',
        ];

        $validate = new Validate($rules);
        if (!$validate->check($data)) {
            return ['error' => true, 'result' => $this->returnNotify('fail'), 'msg' => $data['mchOrderNo'] . " - fail:" . $validate->getError()];
        }
        $oldSign = $data['sign'];
        unset($data['sign']);

        //首先对获得的商户号进行比对
        if ($data['mchNo'] != $pay_config["mch_no"]) {
            ddMsg("warning", '', ['回调-yyyb_pay', '回调商户号不存在', json_encode(['orderid' => $input['mchOrderNo']], JSON_UNESCAPED_UNICODE)]);
            return ['error' => true, 'result' => $this->returnNotify('fail'), 'msg' => $data['mchOrderNo'] . " - fail; 当前商户号不存在！"];
        }
        //验签
        $sign = $this->sign($data, $pay_config['app_secret']);
        if ($sign == $oldSign) {
            return ['error' => false, 'result' => $this->returnNotify('success'), "msg" => $data['mchOrderNo'] . " - success"];
        }

        ddMsg("warning", '', ['回调-qzl_pay', '回调验签失败', json_encode(['orderid' => $input['mchOrderNo'], 'input' => "sign=".$sign, 'handle' => $this->handleSign], JSON_UNESCAPED_UNICODE)]);
        return ['error' => true, 'result' => $this->returnNotify('fail'), 'msg' => $data['mchOrderNo'] . ' - fail; sign: ' . $sign . '(input) - ' . $this->handleSign . '(handle)'];
    }

    //生成签名
    public function sign($paramArray, $appKey)
    {
        ksort($paramArray);
        reset($paramArray);

        $md5str = "";
        foreach ($paramArray as $key => $val) {
            if (strlen($key) && strlen($val)) {
                $md5str = $md5str . $key . "=" . $val . "&";
            }
        }

        $signStr = $md5str . "key=" . $appKey;
        $this->handleSign = $signStr;

        $sign = strtoupper(md5($signStr));  //签名
        return $sign;
    }
}